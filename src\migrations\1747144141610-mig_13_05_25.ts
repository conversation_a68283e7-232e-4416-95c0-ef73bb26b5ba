import { MigrationInterface, QueryRunner } from "typeorm";

export class Mig1305251747144141610 implements MigrationInterface {
    name = 'Mig1305251747144141610'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "p_map"."layer" ADD "applyDateFilter" boolean NOT NULL DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "p_map"."layer" DROP COLUMN "applyDateFilter"`);
    }

}
