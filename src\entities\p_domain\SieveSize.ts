import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';

@Entity({ schema: 'p_domain' })
export class SieveSize {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  sieveNumber?: string;

  @Column({ nullable: true, type: 'decimal' })
  minimumMassRequired?: number;

  @Column({ nullable: true })
  maxParticleSizeOnVisualInspection?: string;

  @Column({ nullable: true, type: 'decimal' })
  sieveSize?: number;

  @Column()
  isDefault?: number;

  @Column()
  sequence?: number;

  @Column()
  sieveGroup?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
