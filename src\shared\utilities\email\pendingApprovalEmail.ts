import { Approval } from '../../../entities/p_utils/Approval';
import approvalModel from '../../../modules/project360/models/approval.model';
import projectModel from '../../../modules/project360/models/project.model';
import { getAllUsers } from '../../server/platformApi/user';
import stringTemplate from 'string-template';
import { sendMail } from './email';
import ErrorLogModel from '../../../modules/project360/models/log/errorLog.model';

const pendingApprovalMail = async (projectId: string, defaultMail?: string) => {
  try {
    const approvalData = await approvalModel.findPedingByProjectId(projectId);
    const groupedByDceId: GroupedResult = groupByProjectAndUser(approvalData);
    const projectIds = Object.keys(groupedByDceId);
    const allProject = await projectModel.getAll();
    const body = `
    <html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pending Approvals Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 600px;
            margin: auto;
            border: 1px solid #ddd;
            padding: 20px;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        h2 {
            color: #078DEE;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #078DEE;
            color: white;
        }
        .footer {
            margin-top: 20px;
            font-size: 0.9em;
            color: #555;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin-top: 20px;
            color: white;
            background-color: #078DEE;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
    </head>
    <body>
    <div class="container">
        <h2>Daily Reminder: Pending Approvals in SmartInfra Hub for {project}</h2>
        <p>Hi {userName},</p>
        <p>This is a reminder from SmartInfra Hub regarding items pending your approval for <strong>{project}</strong>. Please review these items to keep the project on schedule.</p>

        <table>
            <tr>
                <th>Data Point</th>
                <th>Pending Approvals</th>
            </tr>
            {tableBody}
        </table>

        <p>To view and approve these items, please visit your </br><a href="{link}" class="btn">Approval Page in SmartInfra Hub</a>.</p>

        <div class="footer">
            <p>Your prompt attention is essential for timely processing. If you need assistance, please contact our support team.</p>
            <p>Thank you,<br>
            SmartInfra Hub Team<br>
            ${process.env.WEB_BASE_URL}</p>
            <p><em>This message was automatically generated by SmartInfra Hub. Please do not reply directly to this email.</em></p>
        </div>
    </div>
    </body>
    </html>
    `;
    const allUsers = await getAllUsers();
   for (const value of projectIds) {
      const projectDetails = allProject.find((item) => item.id == value);
      const users = Object.keys(groupedByDceId[value]);

      for (const user of users) {
        const userData = allUsers.find((item) => item.id == user);
        const approval = groupedByDceId[value][user];
        const dceAndCounts = countNames(approval);
        let tableBody = ``;
        Object.keys(dceAndCounts).forEach((item) => {
          tableBody += `<tr>
            <td>${item}</td>
            <td>${dceAndCounts[item]}</td>
          </tr>`;
        });
        if (tableBody != '') {
          const finalBody = stringTemplate(body, {
            project: projectDetails?.name,
            tableBody,
            userName: `${userData?.firstName} ${userData?.lastName}`,
            currentYear: new Date().getFullYear(),
            link: `${process.env.WEB_BASE_URL}/project360/projectExecution/approvals`,
          });
          await sendMail(
            `Pending Approval Alert for ${projectDetails?.name}`,
            userData?.email || '',
            finalBody
          );
          // Optional defaultMail send
          if (defaultMail) {
            await sendMail(
              `Pending Approval Alert for ${projectDetails?.name} for ${userData?.firstName}`,
              defaultMail,
              finalBody
            );
          }
          // Delay after each email to avoid SMTP throttling (adjust as needed)
          await new Promise((resolve) => setTimeout(resolve, 200));
        }
      }
    }
  } catch (error) {
    const errorModel = new ErrorLogModel();
    errorModel.addErrorLog({ error: (error as any).message });
  }
};

type GroupedResult = {
  [projectId: string]: {
    [approverId: string]: Approval[];
  };
};
const groupByProjectAndUser = (data: Approval[]) => {
  return data.reduce((acc: GroupedResult, item: Approval) => {
    const { projectId, approvers } = item;

    // Initialize the project object if it doesn't exist
    if (!acc[projectId]) {
      acc[projectId] = {};
    }

    // Loop through each approver and group by userId
    approvers?.forEach((approver) => {
      const { approverId } = approver;
      if (approverId !== undefined && approverId !== null) {
        // Initialize the user array if it doesn't exist
        if (!acc[projectId][approverId]) {
          acc[projectId][approverId] = [];
        }

        // Push the current item into the corresponding user array
        acc[projectId][approverId].push(item);
      }
    });

    return acc;
  }, {});
};

const countNames = (arr: Approval[]) => {
  return arr.reduce((accumulator: { [key: string]: number }, current) => {
    const name = current.dce?.name; // Extract the name
    // If the name already exists in the accumulator, increment its count
    if (name) {
      if (accumulator[name]) {
        accumulator[name] += 1;
      } else {
        // If it doesn't exist, initialize it with a count of 1
        accumulator[name] = 1;
      }
    }
    return accumulator;
  }, {}); // Start with an empty object
};

export default pendingApprovalMail;
