import { getManager } from 'typeorm';
import { SurveyFile } from '@entities/p_cs/SurveyFile';

export default class SurveyFileModel {
  async getBySurveyId(surveyId: string): Promise<SurveyFile[] | undefined> {
    try {
      const repo = getManager().getRepository(SurveyFile);
      const files = await repo.find({
        where: { surveyId, isDelete: false },
        relations: ['survey'], // optional
      });
      return files;
    } catch (error) {
      console.error('Error fetching SurveyFiles by surveyId:', error);
      throw error;
    }
  }
}
