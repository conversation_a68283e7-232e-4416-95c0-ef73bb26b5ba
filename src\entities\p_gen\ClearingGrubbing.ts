import { En<PERSON>ty, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn } from 'typeorm';
import { DCEColumns } from '@entities/common/DCEColumns';
import { Project } from './Project';
import { Area } from './Area';
import { Feature } from './Feature';
import { Purpose } from '@entities/p_meta/Purpose';
import { EventLog } from './EventLog';
import { WorkPackageActivity } from './WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';

@Entity({ schema: 'p_gen', name: 'clearing_grubbing' })
export class ClearingGrubbing extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  typeOfWork?: string;

  @Column({ type: 'decimal', nullable: true })
  area?: number;

  @Column({ type: 'timestamp', nullable: true })
  date?: Date;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;
}
