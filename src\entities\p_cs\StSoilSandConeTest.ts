// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { DepthInfoNGSC } from '../p_domain/DepthInfoNGSC';
import { Test } from '../p_meta/Test';
import { TestMethod } from '../p_meta/TestMethod';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { SandConeInfo } from '../p_domain/SandConeInfo';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { Sample } from './Sample';
import { ProjectSandCones } from '@entities/p_gen/ProjectSandCones';
import { EventLog } from '@entities/p_gen/EventLog';
import { StSoilNuclearGaugeTest } from './StSoilNuclearGaugeTest';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { TestAndDCEColumns } from '@entities/common/TestColumns';
import Structure from '@entities/p_gen/Structure';
import { Feature } from '@entities/p_gen/Feature';
import { StationAlignment } from '@entities/p_map/StationAlignment';
import { Area } from '@entities/p_gen/Area';
import { StSoilProctorTest } from './StSoilProctorTest';

@Entity({ schema: 'p_cs' })
export class StSoilSandConeTest extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  // TODO: Remove once we start using proctorId
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  proctorId?: string;

  @ManyToOne(() => StSoilProctorTest, { nullable: true })
  @JoinColumn({ name: 'proctorId' })
  proctor?: StSoilProctorTest;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  retest?: boolean;

  @Column({ nullable: true })
  failedTestId?: string;

  @ManyToOne(() => StSoilSandConeTest, { nullable: true })
  @JoinColumn({ name: 'failedTestId' })
  stSoilNuclearGaugeTest?: StSoilSandConeTest;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  stationAlignmentId?: string;

  @ManyToOne(() => StationAlignment, { nullable: true })
  @JoinColumn({ name: 'stationAlignmentId' })
  stationAlignment?: StationAlignment;

  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @Column({ type: 'decimal', nullable: true })
  requiredCompaction?: number;

  @Column({ type: 'decimal', nullable: true })
  actualCompaction?: number;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ type: 'decimal', nullable: true })
  optimumMoistureContent?: number;

  @Column({ type: 'decimal', nullable: true })
  maximumLabDensity?: number;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  sandType?: string;

  @Column({ type: 'timestamp', nullable: true })
  calibrationDate?: Date;

  @Column({ nullable: true })
  calibrationMethod?: string;

  @Column({ nullable: true })
  calibrationMethodSandConeApparatus?: string;

  @Column({ type: 'decimal', nullable: true })
  volumeOfSandConeApparatus?: number;

  @Column({ nullable: true })
  station?: string;

  @Column({ nullable: true })
  offset?: string;

  @Column({ nullable: true })
  depthId?: string;

  @ManyToOne(() => DepthInfoNGSC, { nullable: true })
  @JoinColumn({ name: 'depthId' })
  depthInfoNGSC?: DepthInfoNGSC;

  // TODO: Remove it once data is added to sandConeNo
  @Column({ nullable: true })
  sandConeId?: string;

  @ManyToOne(() => SandConeInfo, { nullable: true })
  @JoinColumn({ name: 'sandConeId' })
  sandConeInfo?: SandConeInfo;

  @Column({ nullable: true })
  sandConeNo?: string;

  @ManyToOne(() => SandConeInfo, { nullable: true })
  @JoinColumn({ name: 'sandConeNo' })
  projectSandCones?: ProjectSandCones;

  @Column({ nullable: true })
  nuclearGaugeTestId?: string;

  @ManyToOne(() => StSoilNuclearGaugeTest, { nullable: true })
  @JoinColumn({ name: 'nuclearGaugeTestId' })
  nuclearGaugeTests?: StSoilNuclearGaugeTest;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  testLocation?: string;

  @Column({ nullable: true })
  testSequence?: string;

  @Column({ nullable: true })
  lot?: string;

  @Column({ type: 'decimal', nullable: true })
  sandUnitWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  testHoleVolume?: number; //WeightOfSoilFromHole/SandUnitWeight

  @Column({ type: 'decimal', nullable: true })
  initialWeightOfSandAndJar?: number;

  @Column({ type: 'decimal', nullable: true })
  finalWeightOfSandAndJar?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfSandInHoleAndFunnel?: number; //initialWeightOfSandAndJar - finalWeightOfSandAndJar

  @Column({ type: 'decimal', nullable: true })
  initialWeightOfSandInFunnel?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfSandInHole?: number; //weightOfSandInHoleAndFunnel - initialWeightOfSandInFunnel

  @Column({ type: 'decimal', nullable: true })
  weightOfSoilFromHoleAndJar?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfJar?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfSoilFromHole?: number; //weightOfSoilFromHoleAndJar - weightOfJar

  @Column({ type: 'decimal', nullable: true })
  IPWetDensity?: number; //(weightOfSoilFromHole/TestHoleVolume)

  @Column({ type: 'decimal', nullable: true })
  moistureContent?: number;

  @Column({ type: 'decimal', nullable: true })
  wetWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  dryWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  panWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  volumeOfSandCone?: number;

  @Column({ type: 'decimal', nullable: true })
  IPDryDensity?: number; //(IPWetDensity/(1+moistureContent))

  @Column({ nullable: true })
  liftNumber?: string;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
