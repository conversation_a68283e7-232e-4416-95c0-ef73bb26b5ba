/* eslint-disable @typescript-eslint/no-unused-vars */
import { Sample } from '../../../entities/p_cs/Sample';
import sampleModel from '../../../modules/project360/models/sample.model';
import { getManager } from 'typeorm';

class ObjectToDatabase {
  async fieldDensityTest(data: any[]) {
    try {
      await Promise.all(
        data.map(async (value) => {
          try {
            if (value.sample) {
              const newSample = await sampleModel.addSample(value.sample);
              if (newSample && value.sampleTest) {
              }
            }
          } catch (error) {
            // Handle errors for each iteration
            console.error(`Error processing data: ${(error as any).message}`);
            throw error;
          }
        })
      );
    } catch (error) {
      // Handle errors for the entire operation
      throw error;
    }
  }

  async fieldDensityTestWithEntity(data: any[]) {
    const entityManager = getManager();

    try {
      await entityManager.transaction(async (transactionalEntityManager) => {
        await Promise.all(
          data.map(async (value) => {
            try {
              if (value.sample) {
                const newSample = await transactionalEntityManager.save(Sample, value.sample);
                if (newSample && value.sampleTest) {
                  value.sampleTest.sampleId = newSample.id;
                  value.sampleTest.status = 'Completed';
                }
              }
            } catch (error) {
              console.error(`Error processing data: ${(error as any).message}`);
              throw error; // Re-throw the error to indicate failure
            }
          })
        );
      });
    } catch (error) {
      console.error(`Error processing data: ${(error as any).message}`);
      throw error;
    }
  }

  async fieldEarthworkWithEntity(data: any[]) {
    const entityManager = getManager();

    try {
      await entityManager.transaction(async (transactionalEntityManager) => {
        await Promise.all(
          data.map(async (value) => {
            try {
              if (value.sample) {
                const newSample: Sample = await transactionalEntityManager.save(
                  Sample,
                  value.sample
                );
                if (newSample && value.testOne.sampleTest && value.testOne.sampleTest.testId) {
                  value.testOne.sampleTest.sampleId = newSample.id;
                  value.testOne.sampleTest.status = 'Completed';
                }
              }
            } catch (error) {
              console.error(`Error processing data: ${(error as any).message}`);
              throw error; // Re-throw the error to indicate failure
            }
          })
        );
      });
    } catch (error) {
      console.error(`Error processing data: ${(error as any).message}`);
      throw error;
    }
  }

  async fieldWaterWithEntity(data: any[]) {
    const entityManager = getManager();

    try {
      await entityManager.transaction(async (transactionalEntityManager) => {
        await Promise.all(
          data.map(async (value) => {
            try {
              if (value.sample) {
              }
            } catch (error) {
              console.error(error);
              throw error; // Re-throw the error to indicate failure
            }
          })
        );
      });
    } catch (error) {
      console.error(`Error processing data: ${(error as any).message}`);
      throw error;
    }
  }
}

const objectToDatabase = new ObjectToDatabase();
export default objectToDatabase;
