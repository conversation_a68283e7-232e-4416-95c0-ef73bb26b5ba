import { getManager, getRepository } from 'typeorm';
import { DataCaptureElements } from '../../../../entities/p_meta/DataCaptureElements';
import { EntityListInterface, entityList } from '../../../../shared/utilities/entity/entityList';
import { SubDataCaptureElements } from '../../../../entities/p_meta/SubDataCaptureElement';

class SubDCEModel {
  async findById(id: string) {
    try {
      return await getManager()
        .getRepository(SubDataCaptureElements)
        .findOne({ where: { id, isDelete: false }, relations: [] });
    } catch (error) {
      throw error;
    }
  }

  async findByEntity(entity: string) {
    try {
      return await getManager()
        .getRepository(SubDataCaptureElements)
        .findOne({ where: { entity, isDelete: false } });
    } catch (error) {
      throw error;
    }
  }

  async findByApprovalEntity(entity: string) {
    try {
      return await getManager()
        .getRepository(SubDataCaptureElements)
        .findOne({ where: { entity, isDelete: false } });
    } catch (error) {
      throw error;
    }
  }

  async findByDCEId(dceId: string) {
    try {
      const data = await getManager()
        .getRepository(SubDataCaptureElements)
        .find({
          where: { dceId, isDelete: false },
          relations: ['dce'],
        });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async findByGeoJson() {
    try {
      const data = await getManager()
        .getRepository(DataCaptureElements)
        .find({
          where: { isDelete: false, isGeoJson: true },
        });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getColumnsByDce(dce: SubDataCaptureElements) {
    if (dce?.entity && dce?.entity in entityList) {
      const entityClass: any = entityList[dce.entity as keyof EntityListInterface];
      const entityMetadata = getRepository(entityClass).metadata;
      return entityMetadata.columns.map((column) => {
        return column.propertyName;
      });
    }
  }
  getColumnsBySubDce = async (subDCE: SubDataCaptureElements) => {
    try {
      if (subDCE?.entity && subDCE?.entity in entityList) {
        const entityClass: any = entityList[subDCE.entity as keyof EntityListInterface];
        const entityMetadata = getRepository(entityClass).metadata;
        const query = `SELECT column_name as name, udt_name as type
                              FROM information_schema.columns
                              WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}' 
                              and column_name != 'longitude' and column_name != 'latitude' 
                              and column_name != 'easting' and column_name != 'northing'`;

        const out: { name: string; type: string }[] = await getManager().query(query);
        const columnsToRemove = [
          'projectId',
          'submittalVersionId',
          'workPackageActivityId',
          'createdUserId',
          'geom',
          'createdAt',
          'createdBy',
          'updatedAt',
          'updatedBy',
          'isDelete',
          'qcDate',
          'qcVerifier',
          'qaVerifier',
          'qaDate',
          'sampleTestId',
          'QMSId',
          'id',
          'eventLogId',
        ];
        return out.filter(
          (value) =>
            !columnsToRemove.includes(value.name) &&
            !subDCE.columnsToRemove?.split(',')?.includes(value.name)
        );
      }
    } catch (error) {
      throw error;
    }
  };
}

export default SubDCEModel;
