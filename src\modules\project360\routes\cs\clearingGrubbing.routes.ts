import express, { Router } from 'express';
import { ClearingGrubbing } from '@entities/p_gen/ClearingGrubbing';
import CrudController from 'src/modules/generic/crudDriver.controller';
import { authenticateToken } from 'src/shared/middlewares/auth.middleware';
import importController from '@controllers//import.controller';
const router: Router = express.Router();

const GenricController = new CrudController<ClearingGrubbing>(ClearingGrubbing);

router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'clearingGrubbing')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'clearingGrubbing')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'clearingGrubbing')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'clearingGrubbing')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'clearingGrubbing'));
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'clearingGrubbing')
);
router.get('/get/by/project/:entity/:id', authenticateToken, importController.getData);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'clearingGrubbing')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'clearingGrubbing')
);

export default router;
