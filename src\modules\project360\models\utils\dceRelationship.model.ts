import { Brackets, DeepPartial, getManager } from 'typeorm';
import RelationshipModel from '../meta/relationship.model';
import { DCERelationship } from '../../../../entities/p_utils/DCERelationship';
import { groupAllRelatedItems } from '../../../../shared/utilities/dce/dceRelationGrouping';
import testModel from '../meta/test.model';
import { ISampleTestRelation } from '../../../../shared/interface/relationshipInterface';
import addDataInTestTableForSampleTestRelation from '../../../../shared/utilities/relationship/addDataInTestTableForSampleTestRelation';
import dceModel from '../meta/dce.model';
import editDataInTestTableForSampleTestRelation from '../../../../shared/utilities/relationship/editDataInTestTableForSampleTestRelation';
import deleteDataInTestTableForSampleTestRelation from '../../../../shared/utilities/relationship/deleteDataInTestTableForSampleTestRelation';
import getTestTableForSampleTestRelation from '../../../../shared/utilities/relationship/getTestTableForSampleTestRelation';
import getTestSummaryForSample from '../../../../shared/utilities/relationship/testSummary';

class DCERelationshipModel {
  relationshipModel = new RelationshipModel();
  Repo = getManager().getRepository(DCERelationship);

  getRelationByDceAndTableId = async (
    dceId: string,
    tableId: string,
    projectId: string
  ): Promise<DCERelationship[]> => {
    try {
      const result = await this.Repo.createQueryBuilder('dcerelationship')
        .leftJoinAndSelect('dcerelationship.relationshipType', 'relationship')
        .where(
          new Brackets((qb) => {
            qb.where(
              'dcerelationship."sourceDCEId" = :dceId AND dcerelationship."isDelete" = false AND dcerelationship."projectId" = :projectId AND dcerelationship."sourceDataId" = :tableId',
              { dceId, projectId, tableId }
            ).orWhere(
              'dcerelationship."targetDCEId" = :dceId AND dcerelationship."isDelete" = false AND dcerelationship."projectId" = :projectId AND dcerelationship."targetDataId" = :tableId',
              { dceId, projectId, tableId }
            );
          })
        )
        .getMany();
      return result;
    } catch (error) {
      throw error;
    }
  };

  getRelationByDceAndRelationshipType = async (
    dceId: string,
    relationshipTypeId: string,
    projectId: string
  ): Promise<DCERelationship[]> => {
    try {
      const result = await this.Repo.createQueryBuilder('dcerelationship')
        .leftJoinAndSelect('dcerelationship.relationshipType', 'relationship')
        .where(
          new Brackets((qb) => {
            qb.where(
              'dcerelationship."sourceDCEId" = :dceId AND dcerelationship."isDelete" = false AND dcerelationship."projectId" = :projectId AND dcerelationship."relationshipTypeId" = :relationshipTypeId',
              { dceId, projectId, relationshipTypeId }
            ).orWhere(
              'dcerelationship."targetDCEId" = :dceId AND dcerelationship."isDelete" = false AND dcerelationship."projectId" = :projectId AND dcerelationship."relationshipTypeId" = :relationshipTypeId',
              { dceId, projectId, relationshipTypeId }
            );
          })
        )
        .getMany();
      return result;
    } catch (error) {
      throw error;
    }
  };

  getRelationByDceIds = async (projectId: string, dceIds: string) => {
    try {
      let dceIdFinal: string[] = [];
      if (dceIds !== '') {
        dceIdFinal = dceIds.split(',').map((id) => id.trim()); // Trim whitespace
      }

      const result = await this.Repo.createQueryBuilder('dcerelationship')
        .leftJoinAndSelect('dcerelationship.relationshipType', 'relationship')
        .where('dcerelationship.isDelete = false')
        .andWhere('dcerelationship.projectId = :projectId', { projectId })
        .andWhere('dcerelationship.sourceDCEId IN (:...dceIdFinal)', { dceIdFinal })
        .andWhere('dcerelationship.targetDCEId IN (:...dceIdFinal)', { dceIdFinal })
        .getMany();

      const grouped = groupAllRelatedItems(result);
      return grouped;
    } catch (error) {
      throw error;
    }
  };
  getRelationBySourceAndTarget = async (data: DeepPartial<DCERelationship>) => {
    try {
      return await this.Repo.findOne({
        where: {
          projectId: data.projectId,
          sourceDCEId: data.sourceDCEId,
          sourceDataId: data.sourceDataId,
          targetDataId: data.targetDataId,
          targetDCEId: data.targetDCEId,
          isDelete: false,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  async addNewRelation(data: DeepPartial<DCERelationship>[]) {
    try {
      return await this.Repo.save(data);
    } catch (error) {
      throw error;
    }
  }
  async editRelation(data: DeepPartial<DCERelationship>, id: string) {
    try {
      return await this.Repo.update({ id }, data);
    } catch (error) {
      throw error;
    }
  }

  // * Sample test related methods

  getRelationBySampleId = async (sampleId: string) => {
    try {
      const relationshipDetails = await this.relationshipModel.findSampleTestRelationById();
      const sampleDceDetails = await dceModel.findByEntity('sampleManagement');
      return await this.Repo.find({
        where: {
          relationshipTypeId: relationshipDetails?.id,
          sourceDataId: sampleId,
          sourceDCEId: sampleDceDetails?.id,
          isDelete: false,
        },
      });
    } catch (error) {
      throw error;
    }
  };

  getSampleTestsBySampleId = async (sampleId: string) => {
    const relationshipDetails = await this.relationshipModel.findSampleTestRelationById();
    const sampleDceDetails = await dceModel.findByEntity('sampleManagement');
    const relationShipData = await this.Repo.find({
      where: {
        relationshipTypeId: relationshipDetails?.id,
        sourceDataId: sampleId,
        sourceDCEId: sampleDceDetails?.id,
        isDelete: false,
      },
      relations: ['targetDCE'],
    });
    const data = await getTestSummaryForSample(sampleId, relationShipData);
    return data;
  };

  getRelationBySampleIdWithTestDetails = async (sampleId: string) => {
    try {
      const relationshipDetails = await this.relationshipModel.findSampleTestRelationById();
      const sampleDceDetails = await dceModel.findByEntity('sampleManagement');
      const relationShipData = await this.Repo.find({
        where: {
          relationshipTypeId: relationshipDetails?.id,
          sourceDataId: sampleId,
          sourceDCEId: sampleDceDetails?.id,
          isDelete: false,
        },
        relations: ['targetDCE'],
      });
      const data = await getTestTableForSampleTestRelation(sampleId, relationShipData);
      return data;
    } catch (error) {
      throw error;
    }
  };

  addSampleRelation = async (
    sampleId: string,
    sampleTestDetails: ISampleTestRelation[],
    createdUserId: string,
    createdBy: string
  ) => {
    try {
      const existingSampleTests = await this.getRelationBySampleId(sampleId);
      // Extract IDs from incoming sampleTestDetails
      const incomingIds = sampleTestDetails.filter((rel) => rel.id).map((rel) => rel.id);
      // Find relations to delete (present in DB but not in incoming)
      const toDelete = existingSampleTests.filter((rel) => !incomingIds.includes(rel.id));
      for (const relation of toDelete) {
        await this.Repo.update({ id: relation.id }, { isDelete: true });
      }
      const newSampleTestDetails = sampleTestDetails.filter((value) => !value.id);
      const testDetails = await testModel.findByIds(
        newSampleTestDetails.map((value) => value.testId)
      );
      const testDetailsWithDceData = newSampleTestDetails.map((value) => {
        const getTestDetails = testDetails.find((test) => test.id == value.testId);
        if (getTestDetails && getTestDetails.dce) {
          value.dce = getTestDetails.dce;
        }
        return value;
      });
      const addedTests = await addDataInTestTableForSampleTestRelation(
        sampleId,
        testDetailsWithDceData,
        createdUserId,
        createdBy
      );
      return addedTests;
    } catch (error) {
      throw error;
    }
  };

  editSampleRelation = async (
    sampleId: string,
    dceRelationShiptId: string,
    sampleTestDetails: ISampleTestRelation[],
    editedUserId: string,
    editedBy: string
  ) => {
    try {
      await this.Repo.update(dceRelationShiptId, {
        updatedBy: editedBy,
        additionalDetails: sampleTestDetails,
      });

      const updatedData = await this.Repo.findOne({
        where: { id: dceRelationShiptId, isDelete: true },
        relations: ['targetDCE'],
      });
      if (!updatedData) {
        throw new Error('Data not found');
      }
      await editDataInTestTableForSampleTestRelation(sampleId, updatedData, editedUserId, editedBy);
      return updatedData;
    } catch (error) {
      throw error;
    }
  };

  deleteRelationBySourceAndTargetDataId = async (sourceDataId: string, targetDataId: string) => {
    try {
      await this.Repo.update({ sourceDataId, targetDataId }, { isDelete: true });
      const updatedData = await this.Repo.findOne({ where: { sourceDataId, targetDataId } });
      return updatedData;
    } catch (error) {
      throw error;
    }
  };

  deleteSampleRelation = async (sampleId: string, dceRelationShiptId: string) => {
    try {
      await this.Repo.update({ id: dceRelationShiptId }, { isDelete: true });
      const updatedData = await this.Repo.findOne({
        where: { id: dceRelationShiptId, isDelete: true },
        relations: ['targetDCE'],
      });
      if (!updatedData || !updatedData.targetDCE) {
        throw new Error('Data not found');
      }
      await deleteDataInTestTableForSampleTestRelation(sampleId, updatedData.targetDCE);
      return updatedData;
    } catch (error) {
      throw error;
    }
  };
  relationCheck = async (
    sourceDCEId: string,
    sourceDataId: string,
    targetDCEId: string,
    targetDataId: string
  ) => {
    try {
      const data = await this.Repo.findOne({
        where: { sourceDCEId, sourceDataId, targetDCEId, targetDataId, isDelete: false },
        relations: ['targetDCE'],
      });

      return data ? true : false;
    } catch (error) {
      throw error;
    }
  };
}
const dceRelationshipModel = new DCERelationshipModel();
export default dceRelationshipModel;
