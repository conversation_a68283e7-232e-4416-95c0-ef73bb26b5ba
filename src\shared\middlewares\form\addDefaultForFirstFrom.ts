import { NextFunction, Request, Response } from 'express';
import errorMiddleware from '../error/error.middleware';
import ProjectFormModel from '@models/projectForm.model';

export const addDefaultForFirstFrom = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const model = new ProjectFormModel();
    const check = await model.checkFirstForm(req.body.projectId, req.body.dceId, req.body.mode);
    if (check) req.body.default = true;
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
