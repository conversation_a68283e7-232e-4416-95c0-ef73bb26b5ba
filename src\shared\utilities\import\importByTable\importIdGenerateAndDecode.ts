import ImportMappingModel from '../../../../modules/project360/models/utils/importMapping.model';

// Helper to pad numbers to 5 digits
const charMap = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
const base = charMap.length;

// Convert a string to a unique 5-digit number
export const stringToUniqueNumber = async (input: string, entity: string) => {
  let numericValue = 0;
  for (let i = 0; i < input.length; i++) {
    numericValue = (numericValue * base + charMap.indexOf(input[i])) % 100000;
  }
  const importMappingModel = new ImportMappingModel();
  const check = await importMappingModel.checkByEntityAndImportId(
    entity,
    String(numericValue).padStart(5, '0')
  );
  if (check) {
    return String(numericValue + 2).padStart(5, '0');
  }
  return String(numericValue).padStart(5, '0');
};

// Convert a 5-digit number back to a string
export const uniqueNumberToString = (number: string, length: number) => {
  let numericValue = parseInt(number, 10);
  let result = '';
  while (numericValue > 0 && length > 0) {
    const charIndex = numericValue % base;
    result = charMap[charIndex] + result;
    numericValue = Math.floor(numericValue / base);
    length--;
  }
  return result;
};
