import { PhotoVideo } from '../../../entities/p_gen/PhotoVideo';
import { Between, getManager } from 'typeorm';
import { Approval } from '../../../entities/p_utils/Approval';
import { getAuditDetails } from '@utils/auditLog/getAuditDetailsByEntity';
import ApprovalStatusModel from './meta/approvalStatus.model';
import { dceValidationBeforeApproval } from '@utils/Approval/dceValidation';

class PhotoVideoModel {
  async addAsTransaction(newPhotoVideo: PhotoVideo[], createdUserId: string, source: string) {
    try {
      return await getManager().transaction(async (transactionalEntityManager) => {
        try {
          const photoVideoRepository = transactionalEntityManager.getRepository(PhotoVideo);
          const approvalStatusModel = new ApprovalStatusModel();
          const status = await approvalStatusModel.getByNameStatusId('ready for submission');
          const addPhotoVideos = newPhotoVideo
            .map((photoVideo) => {
              photoVideo.approvalStatusId = status || '';
              photoVideo.source = source;
              return photoVideoRepository.create(photoVideo);
            })
            .filter((value) => value.path && value.path !== '');
          await Promise.all(
            addPhotoVideos.map((photoVideo) => photoVideoRepository.save(photoVideo))
          );
          if (addPhotoVideos.length > 0) {
            await getAuditDetails(createdUserId, 'photoVideo', addPhotoVideos[0], 'add');
            await dceValidationBeforeApproval('photoVideo', addPhotoVideos);
          }
          return addPhotoVideos;
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async getByprojectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(PhotoVideo)
        .find({
          where: { projectId, isDelete: false },
          order: { updatedAt: 'DESC' },
          relations: ['feature', 'generalProjectArea'],
        });
    } catch (error) {
      throw error;
    }
  }
  async getById(id: string) {
    try {
      return await getManager()
        .getRepository(PhotoVideo)
        .findOne({
          where: { id, isDelete: false },
          relations: ['feature', 'generalProjectArea', 'mediaType', 'mediaType.feature', 'purpose'],
        });
    } catch (error) {
      throw error;
    }
  }

  async getByprojectIdAndDateForPhotoNumber(projectId: string, dateTime: Date) {
    try {
      const startOfDay = new Date(dateTime);
      startOfDay.setHours(0, 0, 0, 0);

      const endOfDay = new Date(dateTime);
      endOfDay.setHours(23, 59, 59, 999);

      return await getManager()
        .getRepository(PhotoVideo)
        .count({
          where: [
            {
              projectId,
              dateTime: Between(startOfDay, endOfDay),
              isDelete: false,
            },
            {
              projectId,
              dateTime: startOfDay,
              isDelete: false,
            },
          ],
        });
    } catch (error) {
      throw error;
    }
  }

  async getByProjectIdForPhotoNumber(projectId: string) {
    const result = await getManager().query(
      `
      SELECT COALESCE(MAX("photoNumber"::int), 0) + 1 AS "nextPhotoNumber"
      FROM p_gen.photo_video
      WHERE "isDelete" = false AND "projectId" = $1
    `,
      [projectId]
    );
    return result[0]?.nextPhotoNumber || 1;
  }

  async deleteAsTransaction(id: string) {
    try {
      return await getManager().transaction(async (transactionalEntityManager) => {
        try {
          const photoVideoRepository = transactionalEntityManager.getRepository(PhotoVideo);
          const approvalRepository = transactionalEntityManager.getRepository(Approval);
          await photoVideoRepository.update(id, { isDelete: true });
          const approvalData = await approvalRepository.find({
            where: { tableName: 'p_gen.photo_video', tableId: id },
          });
          if (approvalData.length > 0) {
            await Promise.all(
              approvalData.map(async (value) => {
                await approvalRepository.update(value.id, { isDelete: true, status: 'Removed' });
              })
            );
          }
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }
}

const photoVideoModel = new PhotoVideoModel();

export default photoVideoModel;
