import puppeteer from 'puppeteer';
import * as path from 'path';
import sharp from 'sharp';

const SIHLogoPath = path.join(process.cwd(), 'public', 'images', 'SIHLogo.png');
const USACELogoPath = path.join(process.cwd(), 'public', 'images', 'USACELogo.png');

// Get the logo as a Base64 string
export async function getSIHLogoBase64(): Promise<string> {
  const logoHeight = 55;
  const backgroundColor = '#EFF5FB';
  const padding = 10;

  const logoBuffer = await sharp(SIHLogoPath).resize({ height: logoHeight }).toBuffer();

  const { width: logoWidth } = await sharp(logoBuffer).metadata();

  const finalBuffer = await sharp({
    create: {
      width: (logoWidth ?? 0) + padding * 2,
      height: logoHeight + padding * 2,
      channels: 4,
      background: backgroundColor,
    },
  })
    .composite([{ input: logoBuffer, gravity: 'center' }])
    .png()
    .toBuffer();

  return finalBuffer.toString('base64');
}

export async function createHTMLWithContent(
  station: string | null,
  offset: string,
  photoNumber: string,
  formattedDate: string,
  logoBase64: string
): Promise<string> {
  return `
        <!-- Outer Wrapper for Transparency -->
        <div style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background-color: transparent; margin: 20px; box-sizing: border-box;">
          <!-- Inner Container with White Background and Padding -->
          <div style="padding: 15px; border-radius: 15px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); background-color: #f9f9f9;">
            <!-- Logo -->
            <img src="data:image/png;base64,${logoBase64}" alt="Logo" style="height: 77px; width: 422px;" />
            
            <!-- Content -->
            <div style="margin-top: 10px;">
              <!-- Station, Offset, and Photo Number -->
              <div style="display: flex; align-items: center; justify-content: space-between; font-size: 25px; font-weight: 400; margin-bottom: 10px;">
                <span>${station ?? ''}, ${offset}</span>
                <div style="background: #EFF5FB; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 25px; font-weight: bold; padding: 10px;">
                  #${photoNumber}
                </div>
              </div>
              
              <!-- Formatted Date -->
              <div style="font-size: 25px; font-weight: 400; margin-bottom: 10px;">
                ${formattedDate}
              </div>
            </div>
          </div>
        </div>
  `;
}

export const renderHTMLToImageBuffer = async (
  htmlContent: string,
  width: number,
  height: number
): Promise<Buffer> => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  // Set the viewport size with padding to avoid top-left clipping
  await page.setViewport({ width, height });

  // Set the content with transparent background
  await page.setContent(`
    <html>
      <body style="margin: 0; padding: 0; display: flex; justify-content: center; align-items: center; background-color: transparent; box-sizing: border-box;">
        <div id="wrapper" style="display: inline-block;">
          ${htmlContent}
        </div>
      </body>
    </html>
  `);

  await page.waitForSelector('#wrapper');

  // Capture a screenshot of the rendered content
  const element = await page.$('#wrapper');
  if (!element) {
    throw new Error('Failed to find the wrapper element for rendering HTML.');
  }

  // Capture the element as an image
  const buffer = Buffer.from(await element.screenshot({ type: 'png', omitBackground: true })); // Transparent background
  await browser.close();

  return buffer;
};

// Main function to create the image buffer
export const createTextSVGWithLogo = async (
  station: string | null,
  offset: string,
  photoNumber: string,
  date: string,
  width: number,
  height: number
): Promise<Buffer> => {
  // Get the Base64 logo
  const logoBase64 = await getSIHLogoBase64();

  // Generate the HTML content
  const htmlContent = await createHTMLWithContent(station, offset, photoNumber, date, logoBase64);

  // Render the HTML to an image buffer
  return await renderHTMLToImageBuffer(htmlContent, width, height);
};

export const sihLogo = async (): Promise<Buffer> => {
  return await sharp(SIHLogoPath).resize({ height: 55 }).toBuffer();
};

export const usaceLogo = async (): Promise<Buffer> => {
  return await sharp(USACELogoPath).resize({ height: 150 }).toBuffer();
};
