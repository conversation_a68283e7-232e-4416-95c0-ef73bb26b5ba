import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { ProjectReportList } from '../../../../entities/p_gen/ReportList';
import { sftpUploadReportFileMiddleware } from '../../../../shared/middlewares/uploadFileSftp.middleware';

const router: Router = express.Router();

const GenericController = new CrudController<ProjectReportList>(ProjectReportList);

router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenericController.findByProjectId(req, res, '')
);
router.get('/:id', authenticateToken, GenericController.findById);
router.post('/', authenticateToken, sftpUploadReportFileMiddleware, (req, res) => GenericController.create(req, res, 'reportList'));
router.put('/:id', authenticateToken, sftpUploadReportFileMiddleware, (req, res) => GenericController.update(req, res, 'reportList'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenericController.multiSoftDelete(req, res, 'reportList')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res, 'reportList')
);

export default router;
