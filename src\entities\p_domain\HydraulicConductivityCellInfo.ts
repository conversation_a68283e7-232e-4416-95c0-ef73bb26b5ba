import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
} from 'typeorm';
import { HydraulicConductivityCellType } from './HydraulicConductivityCellType';

@Entity({ schema: 'p_domain' })
export class HydraulicConductivityCellInfo {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  cellNo?: number;

  @Column({ nullable: true })
  cellTypeId?: string;

  @ManyToOne(() => HydraulicConductivityCellType, { nullable: true }) 
  @JoinColumn({ name: 'cellTypeId' })
  hydraulicConductivityCellType?: HydraulicConductivityCellType;

  @Column({ nullable: true, type: 'decimal' })
  headPipeLength?: number;

  @Column({ nullable: true, type: 'decimal' })
  tailPipeLength?: number;

  @Column({ nullable: true, type: 'decimal' })
  headPipeArea?: number;

  @Column({ nullable: true, type: 'decimal' })
  tailPipeArea?: number;

  @Column({ nullable: true, type: 'decimal' })
  headPipeCorrection?: number;

  @Column({ nullable: true, type: 'decimal' })
  tailPipeCorrection?: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
