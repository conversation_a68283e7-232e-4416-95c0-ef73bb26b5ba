import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from '../p_gen/Project';
import { LayerParent } from './LayerParent';
import { Legend } from './Legend';
import { GeomLayer } from './GeomLayer';
import { GeoJsonConfiguration } from './GeoJSONConfig';
import { LayerFilter } from './LayerFilter';

@Entity({ schema: 'p_map' })
export class Layer {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true }) 
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  layerParentId?: string;

  @ManyToOne(() => LayerParent, { nullable: true }) 
  @JoinColumn({ name: 'layerParentId' })
  layerParent?: LayerParent;

  @Column({ nullable: true })
  projectId?: string;

  @Column({ nullable: true })
  labelColumn?: string;

  @Column({ nullable: true })
  layerType?: string;

  @Column({ nullable: true })
  label?: string;

  @Column({ nullable: true })
  layerKey?: string;

  @Column({ nullable: true })
  profileLayer?: string;

  @Column({ nullable: true })
  popupType?: string;

  @Column({ nullable: true })
  filterSortColumn?: string;

  // Map specific properties

  @Column({ nullable: true, default: false })
  enableForMap?: boolean;

  @Column({ nullable: true, default: false })
  displayOnMap?: boolean;

  @Column({ nullable: true })
  mapLabelColor?: string;

  @Column({ nullable: true })
  profileLabelColorFor2d?: string;

  @Column({ nullable: true })
  profileLabelColorFor3d?: string;

  @Column({ nullable: true })
  enablePopupForMap?: boolean;

  @Column({ nullable: true })
  mapLayerZoom?: string;

  @Column({ nullable: true })
  profileLabelZoom?: number;

  @Column({ nullable: true })
  mapOutlineColor?: string;

  @Column({ nullable: true })
  mapOutlineWidth?: number;

  @Column({ nullable: true, type: 'decimal' })
  layerOpacity?: number;

  @Column({ nullable: true })
  enableIcon?: boolean;

  @Column({ nullable: true })
  order?: number;

  @Column({nullable: true })
  enableLabelForMap?: boolean;

  @Column({ nullable: true })
  mapLabelZoom?: number;

  // 2D specific properties

  @Column({ nullable: true, default: false })
  enableFor2d?: boolean;

  @Column({ nullable: true, default: false })
  displayOn2d?: boolean;

  @Column({ nullable: true })
  colorFor2d?: string;

  @Column({ nullable: true })
  opacityFor2d?: number;

  @Column({ nullable: true })
  enablePopupFor2d?: boolean;
  
  @Column({ nullable: true })
  popupTypeFor2d?: string;

  @Column({nullable: true })
  enableLabelFo2d?: boolean;

  // 3D specific properties

  @Column({ nullable: true, default: false })
  enableFor3d?: boolean;

  @Column({ nullable: true, default: false })
  displayOn3d?: boolean;

  @Column({ nullable: true })
  colorFor3d?: string;

  @Column({ nullable: true })
  opacityFor3d?: number;

  @Column({ nullable: true })
  enablePopupFor3d?: boolean;

  @Column({ nullable: true })
  popupTypeFor3d?: string;

  @Column({nullable: true })
  enableLabelFor3d?: boolean;

  // Progress specific properties

  @Column({ nullable: true, default: false })
  enableForProgress?: boolean;

  @Column({ nullable: true, default: false })
  displayOnProgress?: boolean;

  @Column({ nullable: true })
  colorForProgress?: string;

  @Column({ nullable: true })
  opacityForProgress?: number;

  @Column({ nullable: true })
  enableMobile?: boolean;

  @Column({ default: false, nullable: true })
  excludeOnFilter?: boolean;

  @Column({ nullable: true, default: false })
  enableFilters?: boolean;

  @Column({ nullable: true, default: true })
  applyDateFilter?: boolean;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @OneToMany(() => Legend, (Legend) => Legend.layer)
  legend?: Legend[];

  @OneToMany(() => GeomLayer, (geom) => geom.layer)
  geomLayer?: GeomLayer[];

  @OneToMany(() => GeoJsonConfiguration, (layer) => layer.layer)
  popupConfig!: GeoJsonConfiguration[];

  @OneToMany(() => LayerFilter, (layer) => layer.layer, { cascade: true })
  filter!: LayerFilter[];

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
