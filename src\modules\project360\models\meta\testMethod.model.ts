import { getConnection, getManager } from 'typeorm';
import { TestMethod } from '../../../../entities/p_meta/TestMethod';
import projectModel from '../project.model';

class TestMethodModel {
  constructor() {}

  async getAll() {
    try {
      const testMethod = await getManager()
        .getRepository(TestMethod)
        .find({
          relations: ['standardAgency', 'specAgency'],
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByTestId(testId: string) {
    try {
      const testMethod = await getManager()
        .getRepository(TestMethod)
        .find({
          where: { testId: testId },
          relations: ['standardAgency', 'specAgency'],
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByTestStandard(testId: number, projectId: string) {
    try {
      const connection = await getConnection();
      const standardAgency = await projectModel.getSpecAgencyIdByProjectId(projectId);
      const testMethod = await connection
        .getRepository(TestMethod)
        .createQueryBuilder('testMethod')
        .where('testMethod.testId = :id', { id: testId })
        .andWhere('testMethod.specAgencyId = id', { ids: standardAgency })
        .leftJoinAndSelect('testMethod.standardAgency', 'standardAgency')
        .leftJoinAndSelect('testMethod.specAgency', 'specAgency')
        .getMany();

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByStandardCode(standardCode: string) {
    try {
      const testMethod = await getManager()
        .getRepository(TestMethod)
        .findOne({
          where: { standardCode: standardCode },
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }
}

const testMethodModel = new TestMethodModel();
export default testMethodModel;
