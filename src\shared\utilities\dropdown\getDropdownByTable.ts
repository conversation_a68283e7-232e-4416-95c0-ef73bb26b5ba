import { get<PERSON>anager } from 'typeorm';
import { DataCaptureElemetRelation } from '../../../entities/p_meta/DataCaptureElemetRelation';
import projectModel from '../../../modules/project360/models/project.model';
import siteModel from '../../../modules/project360/models/site.model';
import featuretModel from '../../../modules/project360/models/feature.model';
import filterByUpdateAt from '../custom/filterDataByUpdateAt';
import projectRoleModel from '../../../modules/project360/models/role and permission/projectRole.model';

export const getDropdownByTable = async (
  relationData: DataCaptureElemetRelation,
  projectId: string,
  userId: string,
  updatedAfter?: Date
) => {
  try {
    if (relationData.columnName == 'generalProjectAreaId') {
      let result = await siteModel.getPrimeByProjectId(projectId);
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      const final = result.map((value) => {
        const newobj = {
          name: value.name,
          children: value.area?.map((item) => {
            return {
              name: `${item.description}-${item.name}`,
              value: item.id,
            };
          }),
        };
        return newobj;
      });
      return final;
    }
    if (relationData.columnName == 'featureId') {
      let result = await featuretModel.findByProjectIdForDropdown(projectId);
      if (!result) {
        return [];
      }
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      return result;
    }
    if (relationData.columnName == 'generalProjectAreaId') {
      let result = siteModel.getPrimeByProjectId(projectId);
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      return result;
    }
    const routeParams = relationData?.routeParams?.split(',');
    const additionalParams = relationData?.additionalParams?.split(',');
    const defaultParams = parseStringToArray(relationData?.defaultValue || '');
    // if (routeParams?.length == 1 && routeParams[0] == 'projectId') {
    let joinClause = '';
    let selectClause = '';

    if (relationData.foreignKeyMapping) {
      const relation = relationData?.foreignKeyMapping?.table;
      const column = relationData?.relatedColumnToDisplay;
      const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
      joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
      selectClause = `${relation}.${column} AS name`;
    } else {
      selectClause = `${relationData.relatedColumnToDisplay} AS name`;
    }

    let query = `SELECT ${relationData.relatedTableName}.id as value, ${selectClause} FROM ${relationData.relatedTableName} ${joinClause}`;

    let whereCondition = 'WHERE';
    if (routeParams && routeParams[0] == 'projectId') {
      const condition = `${relationData.relatedTableName}."projectId" = '${projectId}'`;

      whereCondition = `${whereCondition} ${condition}`;
    }
    if (!relationData.relatedTableName) {
      return [];
    }

    if (projectId) {
      if (relationData.relatedDceId) {
        const purpsoe = await projectRoleModel.getByAccessPurposeIds(
          projectId,
          userId,
          'Full Access'
        );
        if (purpsoe.length <= 0) {
          return [];
        }
        if (whereCondition !== 'WHERE') {
          whereCondition = `${whereCondition} AND ${
            relationData.relatedTableName
          }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
        } else {
          whereCondition = `${whereCondition} ${
            relationData.relatedTableName
          }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
        }
      }
    }

    if (additionalParams && additionalParams?.length > 0) {
      if (projectId) {
        const project: any = await projectModel.getProjectId(projectId);
        if (additionalParams && additionalParams.length > 0) {
          additionalParams.map((value, index) => {
            if (project && value in project) {
              let condition = `${relationData.relatedTableName}."${value}" = '${project[value]}'`;
              if (index != additionalParams.length - 1) {
                condition = `${condition} AND`;
              }
              if (whereCondition !== 'WHERE') {
                whereCondition = `${whereCondition} AND ${condition}`;
              } else {
                whereCondition = `${whereCondition} ${condition}`;
              }
            }
          });
        }
      }
    }
    if (defaultParams.length > 0) {
      defaultParams.forEach((value) => {
        if (whereCondition !== 'WHERE') {
          whereCondition = `${whereCondition} AND ${relationData.relatedTableName}."${value.item}" = '${value.value}'`;
        } else {
          whereCondition = `${whereCondition} ${relationData.relatedTableName}."${value.item}" = '${value.value}'`;
        }
      });
    }

    if (updatedAfter) {
      if (whereCondition !== 'WHERE') {
        whereCondition = `${whereCondition} AND ${relationData.relatedTableName}."updatedAt" = '${updatedAfter}'`;
      } else {
        whereCondition = `${whereCondition} ${relationData.relatedTableName}."updatedAt" = '${updatedAfter}'`;
      }
    }
    if (whereCondition !== 'WHERE') {
      query = `${query} ${whereCondition} AND ${relationData.relatedTableName}."isDelete" = false`;
    } else {
      query = `${query} WHERE ${relationData.relatedTableName}."isDelete" = false`;
    }

    const out = getManager().query(query);

    return out;
  } catch (error) {
    throw error;
  }
};

export const getDropdownByTableWithParent = async (
  relationData: DataCaptureElemetRelation,
  projectId: string,
  userId: string,
  parentColumns: string,
  updatedAfter?: Date
) => {
  try {
    if (relationData.columnName == 'generalProjectAreaId') {
      let result = await siteModel.getPrimeByProjectId(projectId);
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      const final = result.map((value) => {
        const newobj = {
          name: value.name,
          children: value.area?.map((item) => {
            return {
              name: `${item.description}-${item.name}`,
              value: item.id,
            };
          }),
        };
        return newobj;
      });
      return final;
    }
    if (relationData.columnName == 'featureId') {
      let result = await featuretModel.findByProjectIdForDropdown(projectId);
      if (!result) {
        return [];
      }
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      return result;
    }
    if (relationData.columnName == 'generalProjectAreaId') {
      let result = siteModel.getPrimeByProjectId(projectId);
      if (updatedAfter) {
        result = filterByUpdateAt(updatedAfter, result);
      }
      return result;
    }
    const routeParams = relationData?.routeParams?.split(',');
    const additionalParams = relationData?.additionalParams?.split(',');
    const defaultParams = parseStringToArray(relationData?.defaultValue || '');
    // if (routeParams?.length == 1 && routeParams[0] == 'projectId') {
    let joinClause = '';
    let selectClause = '';

    if (relationData.foreignKeyMapping) {
      const relation = relationData?.foreignKeyMapping?.table;
      const column = relationData?.relatedColumnToDisplay;
      const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
      joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
      selectClause = `${relation}.${column} AS name`;
    } else {
      selectClause = `${relationData.relatedColumnToDisplay} AS name`;
    }

    let query = `SELECT ${relationData.relatedTableName}.id as value, ${selectClause}, "${parentColumns}" as "parentValue" FROM ${relationData.relatedTableName} ${joinClause}`;

    let whereCondition = 'WHERE';
    if (routeParams && routeParams[0] == 'projectId') {
      const condition = `${relationData.relatedTableName}."projectId" = '${projectId}'`;

      whereCondition = `${whereCondition} ${condition}`;
    }
    if (!relationData.relatedTableName) {
      return [];
    }

    if (projectId) {
      if (relationData.relatedDceId) {
        const purpsoe = await projectRoleModel.getByAccessPurposeIds(
          projectId,
          userId,
          'Full Access'
        );
        if (purpsoe.length <= 0) {
          return [];
        }
        if (whereCondition !== 'WHERE') {
          whereCondition = `${whereCondition} AND ${
            relationData.relatedTableName
          }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
        } else {
          whereCondition = `${whereCondition} ${
            relationData.relatedTableName
          }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
        }
      }
    }

    if (additionalParams && additionalParams?.length > 0) {
      if (projectId) {
        const project: any = await projectModel.getProjectId(projectId);
        if (additionalParams && additionalParams.length > 0) {
          additionalParams.map((value, index) => {
            if (project && value in project) {
              let condition = `${relationData.relatedTableName}."${value}" = '${project[value]}'`;
              if (index != additionalParams.length - 1) {
                condition = `${condition} AND`;
              }
              if (whereCondition !== 'WHERE') {
                whereCondition = `${whereCondition} AND ${condition}`;
              } else {
                whereCondition = `${whereCondition} ${condition}`;
              }
            }
          });
        }
      }
    }
    if (defaultParams.length > 0) {
      defaultParams.forEach((value) => {
        if (whereCondition !== 'WHERE') {
          whereCondition = `${whereCondition} AND ${relationData.relatedTableName}."${value.item}" = '${value.value}'`;
        } else {
          whereCondition = `${whereCondition} ${relationData.relatedTableName}."${value.item}" = '${value.value}'`;
        }
      });
    }

    if (updatedAfter) {
      if (whereCondition !== 'WHERE') {
        whereCondition = `${whereCondition} AND ${relationData.relatedTableName}."updatedAt" = '${updatedAfter}'`;
      } else {
        whereCondition = `${whereCondition} ${relationData.relatedTableName}."updatedAt" = '${updatedAfter}'`;
      }
    }
    if (whereCondition !== 'WHERE') {
      query = `${query} ${whereCondition} AND ${relationData.relatedTableName}."isDelete" = false`;
    } else {
      query = `${query} WHERE ${relationData.relatedTableName}."isDelete" = false`;
    }

    const out = getManager().query(query);

    return out;
  } catch (error) {
    throw error;
  }
};

const parseStringToArray = (input: string) => {
  if (!input || input == '') {
    return [];
  }
  return input.split(',').map((pair) => {
    const [item, value] = pair.split('=');
    return { item, value };
  });
};
