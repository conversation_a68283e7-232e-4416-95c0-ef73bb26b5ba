
import { Request, Response } from 'express';
import SurveyFileModel from '@models/map/surveyFile.model';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

export default class SurveyFileController {
  async getBySurveyId(req: Request, res: Response) {
    try {
      const model = new SurveyFileModel();
      const { id } = req.params;
      const data = await model.getBySurveyId(id);
      return res.status(200).json({
        isSucceed: true,
        data: data || [],
        msg: 'Data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}
