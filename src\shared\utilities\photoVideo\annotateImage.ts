import moment from 'moment-timezone';
import featuretModel from '../../../modules/project360/models/feature.model';
import { getCurrentDateInDDMMYYYYFormatForPhotoVideo } from '../custom/dateFormating';
import mediaTypeModel from '../../../modules/project360/models/mediaType.model';
import GeneralProjectAreaModel from '../../../modules/project360/models/gen/area.model';
import projectModel from '../../../modules/project360/models/project.model';
import stringTemplate from 'string-template';
import stakeholderModel from '../../../modules/project360/models/stakeholder.model';
// @ts-ignore
import piexifjs from 'piexifjs';
import sharp from 'sharp';
import mediaConfigModel from '../../../modules/project360/models/gen/mediaConfig.model';
import { createCompositeConfig } from './compositeConfig';


const annotateImage = async (
  imageData: any,
  text: string,
  photographer: any,
  projectId: string,
  data: any,
  
) => {
  try {
    // Load the image
    // const resizedImage = await resizeImageTo300DPI(imageData.data);
    const image = sharp(imageData);

    // Step 3: Set the orientation back to 1
    await image.withMetadata({ orientation: 1 });
    const metadata = await image.metadata();

    // Step 2: Determine rotation based on orientation
    let rotateDegrees = 0; // Initialize rotation degrees
    if (!metadata.orientation) {
      metadata.orientation = 1;
    }
    // Check the orientation and set rotateDegrees accordingly
    switch (metadata.orientation) {
      case 3: // 180 degrees
        rotateDegrees = 180;
        break;
      case 6: // 90 degrees clockwise
        rotateDegrees = 90;
        break;
      case 8: // 90 degrees counterclockwise
        rotateDegrees = -90;
        break;
      default:
        // Orientation 1 is already horizontal, no need to rotate
        rotateDegrees = 0;
        break;
    }
    // Step 3: Rotate the image
    if (rotateDegrees !== 0) {
      await image.rotate(rotateDegrees);
    }
    // Step 4: Reset the orientation to 1 (normal)
    await image.withMetadata({ orientation: 1 });

    let exifData = null;
    let captureDate = null;
    if (!metadata || !metadata.height) {
      throw new Error('Error in Image annotation');
    }
    try {
      exifData = piexifjs.load(imageData.toString('binary'));
      const updatedDateTime = data?.dateTime;
      const savedProjectId = data?.projectId; 
      let timeZone =null;
      if(updatedDateTime) { 
        const res = await projectModel.getProjectId(savedProjectId ?? '');
        timeZone = res?.timezone;
        
      }
      if (timeZone) {
        captureDate = moment.utc(updatedDateTime).tz(timeZone).format('YYYY-MM-DD HH:mm:ss');
      }
      
    } catch (error) {
      throw new Error('Invalid exif ');
    }

    const mediaConfig = await mediaConfigModel.getByProjectId(projectId);
    const stakeholder = await stakeholderModel.findPrimeByProjectId(projectId);
    const project = await projectModel.getProjectId(projectId);
    const photoNumber = data.photoNumber;
    const contractor = stakeholder?.name;
    const projectname = project?.name;
    const contractorNumber = project?.projectNumber;

    const generalProjectAreaModel = new GeneralProjectAreaModel();
    const generalProjectArea = await generalProjectAreaModel.getDescriptionById(
      data.generalProjectAreaId
    );
    const typeId = data.typeId;
    let typeDetails;
    if (typeId) {
      typeDetails = await mediaTypeModel.findBytId(typeId);
    }

    const elevation = isNaN(Number(data.elevation)) ? 0 : Number(data.elevation);
    const gpsAltitude = [Math.round(Math.abs(elevation) * 10000), 10000];
    const gpsAltitudeRef = elevation < 0 ? 1 : 0; // 0 = above sea level, 1 = below sea level

    const customMetadata = {
      description: data.description,
      gpsLatitude: parseFloat(data.latitude).toString(),
      gpsLongitude: parseFloat(data.longitude).toString(),
      gpsElevation: gpsAltitude,
      gpsAltitudeRef: gpsAltitudeRef,

      dateTimeOriginal: data.dateTime,
      Artist: photographer,
      userComment: typeDetails
        ? JSON.stringify({
            GeneralProjectArea: generalProjectArea,
            ImageDirection: data.imgDegrees,
            Feature: data.featureId != '' ? await featuretModel.findBynameId(data.featureId) : '',
            OrientationView: data.orientationView,
            MediaType: typeDetails?.name,
          })
        : JSON.stringify({
            GeneralProjectArea: generalProjectArea,
            ImageDirection: data.imgDegrees,
            Feature: data.featureId != '' ? await featuretModel.findBynameId(data.featureId) : '',
            OrientationView: data.orientationView,
          }),
    };

    const size =
      data.orientationView.toLowerCase() === 'portrait'
        ? { width: 1200, height: 1600 } // 9:16 portrait
        : { width: 1600, height: 1200 }; // 16:9 landscape

    const date = data.dateTime;

    const workElement =
      data.featureId != '' ? await featuretModel.findBynameId(data.featureId) : '';
    const newDescriptionObj = {
      projectname: projectname,
      contractorNumber: contractorNumber,
      contractor: contractor,
      date: getCurrentDateInDDMMYYYYFormatForPhotoVideo(date),
      fDate: moment
        .tz(captureDate || date || new Date(), 'America/New_York')
        .format('MMMM DD YYYY, hh:mm A'),
      station: data.station,
      offset: data.offset,
      imageDirection: data.imgDegrees,
      photoNumber: photoNumber,
      orientation: data.orientationView,
      workElement: workElement || '',
      generalProjectArea: generalProjectArea,
      additionaldescription: customMetadata.description,
      purpose: data?.purpose?.name,
    };

    // Base template with placeholders for project details
    const baseTemplate = `Project: {projectname}, Contract No: {contractorNumber}, Contractor {contractor}, Photo No: #{photoNumber}, General Project Area: {generalProjectArea}, Work Element: {workElement}, Purpose: {purpose}`;

    // Additional part to add to the template if there's additional description
    const additionalPart = newDescriptionObj.additionaldescription
      ? `, Description: {additionaldescription}`
      : '';

    // Replace placeholders in the combined template with actual values
    const newDescription = stringTemplate(`${baseTemplate}${additionalPart}`, {
      ...newDescriptionObj,
    });

    const compositeConfig = await createCompositeConfig(
      mediaConfig,
      data.station,
      newDescriptionObj,
      newDescriptionObj?.fDate,
      newDescription,
      metadata,
      size
    );

    const annotatedBuffer = await image
      .withMetadata({ orientation: 1 })
      .resize(size.width, size.height, { fit: 'cover' })
      .rotate(rotateDegrees)
      .composite(compositeConfig)
      .toBuffer();

    const annotatedSharp = sharp(annotatedBuffer);
    switch (metadata.orientation) {
      case 3: // 180 degrees
        rotateDegrees = -180;
        break;
      case 6: // 90 degrees clockwise
        rotateDegrees = -90;
        break;
      case 8: // 90 degrees counterclockwise
        rotateDegrees = 90;
        break;
      default:
        // Orientation 1 is already horizontal, no need to rotate
        rotateDegrees = 0;
        break;
    }
    if (rotateDegrees != 0) {
      await annotatedSharp.rotate(rotateDegrees);
    }

    const final = await annotatedSharp.toBuffer();
    const newExif = {
      '0th': { ...exifData['0th'] },
      Exif: { ...exifData['Exif'] },
      GPS: { ...exifData['GPS'] },
      Interop: { ...exifData['Interop'] },
      '1st': { ...exifData['1st'] },
      thumbnail: null,
    };
    newExif['GPS'][piexifjs.GPSIFD.GPSLatitude] = piexifjs.GPSHelper.degToDmsRational(
      customMetadata.gpsLatitude
    );
    newExif['GPS'][piexifjs.GPSIFD.GPSLongitude] = piexifjs.GPSHelper.degToDmsRational(
      customMetadata.gpsLongitude
    );
    newExif['GPS'][piexifjs.GPSIFD.GPSAltitude] = customMetadata.gpsElevation;
    newExif['GPS'][piexifjs.GPSIFD.GPSAltitudeRef] = customMetadata.gpsAltitudeRef;

    newExif['Exif'][piexifjs.ExifIFD.DateTimeOriginal] = customMetadata.dateTimeOriginal;
    newExif['0th'][piexifjs.ImageIFD.Artist] = customMetadata.Artist;
    newExif['Exif'][piexifjs.ExifIFD.UserComment] = customMetadata.userComment;

    const keywordsArray = newDescription.split(',') || customMetadata.description.split(',');
    const keywords = keywordsArray.join(';');
    const xpKeywordsBuffer = Buffer.from(keywords, 'utf16le');
    const xpKeywordsArray = Array.prototype.slice.call(xpKeywordsBuffer, 0);
    newExif['0th'][piexifjs.ImageIFD.XPKeywords] = xpKeywordsArray;

    newExif['0th'][piexifjs.ImageIFD.ImageDescription] =
      newDescription || customMetadata.description;

    const newExifBinary = piexifjs.dump(newExif);

    const newPhotoData = piexifjs.insert(newExifBinary, final.toString('binary'));
    const fileBuffer = Buffer.from(newPhotoData, 'binary');

    return {
      image: {
        ...imageData,
        data: fileBuffer,
      },
    };
  } catch (err) {
    console.error('Error processing image:', err);
    throw err;
  }
};

export default annotateImage;

export async function changeResolution(buffer: any) {
  try {
    const desiredPPI = 300;
    const image = sharp(buffer);

    const metadata = await image.metadata();

    // Calculate new dimensions based on desired PPI
    const newWidth = Math.round(((metadata.width || 100) * desiredPPI) / (metadata.density || 100));
    const newHeight = Math.round(
      ((metadata.height || 100) * desiredPPI) / (metadata.density || 100)
    );

    await image
      .resize(newWidth, newHeight, {
        fit: 'inside', // Ensure the image fits within the new dimensions
        withoutEnlargement: true, // Prevent upscaling
      })
      .toBuffer();

    metadata.width = newWidth;
    metadata.height = newHeight;

    return { buffer, metadata };
  } catch (error) {
    console.error('Error changing resolution:', error);
  }
}
