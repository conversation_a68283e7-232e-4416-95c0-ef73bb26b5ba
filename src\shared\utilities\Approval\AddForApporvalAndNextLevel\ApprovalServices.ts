import { DataCaptureElements } from '@entities/p_meta/DataCaptureElements';
import { Approval } from '@entities/p_utils/Approval';
import { ApprovalLevel } from '@entities/p_utils/ApprovalLevel';
import { ApprovalLevelInstance } from '@entities/p_utils/ApprovalLevelInstance';
import approvalModel from '@models/approval.model';
import approvalSetupModel from '@models/approvalSetup.model';
import approverSetupModel from '@models/approverSetup.model';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import { getAuditDetails } from '@utils/auditLog/getAuditDetailsByEntity';
import { entityList, EntityListInterface } from '@utils/entity/entityList';
import { editSensorByApprovalStatus } from 'src/shared/server/sensorApi/dce';
import { DeepPartial, EntityManager } from 'typeorm';

class ApprovalServices {
  handleMicroserviceApproval = async (
    subModule: any,
    value: any,
    approvalInstance: any,
    levelInstance: DeepPartial<ApprovalLevelInstance[]>,
    token: string | undefined,
    entityManager: any,
    submittedUser: string,
    entity: string,
    projectId: string,
    pendingApprovalId: string
  ) => {
    try {
      await editSensorByApprovalStatus(
        subModule?.microserviceDceId || '',
        value.id,
        {
          approvalStatusId: pendingApprovalId,
        },
        token
      );
      await entityManager.transaction(async (transactionalEntityManager: any) => {
        try {
          const result = await transactionalEntityManager.save(Approval, approvalInstance);
          levelInstance.map((item) => {
            item.approvalId = result.id;
            return item;
          });
          await transactionalEntityManager.save(ApprovalLevelInstance, levelInstance);

          if (entity) {
            await getAuditDetails(
              submittedUser,
              entity,
              { updatedBy: submittedUser, projectId: projectId, id: value.id },
              'approval'
            );
          }
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  };

  handleNonMicroserviceApproval = async (
    entity: string,
    entityManager: any,
    value: any,
    approvalInstance: any,
    levelInstance: DeepPartial<ApprovalLevelInstance[]>,
    submittedUser: string,
    projectId: string,
    pendingStatusId: string
  ) => {
    if (entity in entityList) {
      await entityManager.transaction(async (transactionalEntityManager: any) => {
        try {
          const entityValue: any = entityList[entity as keyof EntityListInterface];
          const updateData = { approvalStatusId: pendingStatusId };

          await transactionalEntityManager
            .getRepository(entityValue)
            .update(value.id || '', updateData as any);
          const result = await transactionalEntityManager.save(Approval, approvalInstance);

          levelInstance.map((item) => {
            item.approvalId = result.id;
            return item;
          });
          await transactionalEntityManager.save(ApprovalLevelInstance, levelInstance);
          if (entity) {
            await getAuditDetails(
              submittedUser,
              entity,
              { updatedBy: submittedUser, projectId: projectId, id: value.id },
              'approval'
            );
          }
        } catch (error) {
          throw error;
        }
      });
    }
  };

  processApproval = async (
    value: any,
    subModule: any,
    projectId: string,
    entity: string,
    entityMetadata: any,
    currentLevel: number,
    submittedUser: string,
    isMicroservice: boolean,
    token: string | undefined,
    entityManager: any,
    pendingStatusId: string,
    userIdForNotification: Set<string>
  ) => {
    if (subModule) {
      const qaqc = value.purposeId;
      if (qaqc) {
        const approvalSetup = await approvalSetupModel.checkSubmoduleWithPurposeType(
          subModule.id,
          projectId,
          qaqc
        );
        if (!approvalSetup) throw new Error(`Approval setup not found`);

        const levels = await approvalSetupModel.findApprovalLevelsBySetupId(approvalSetup.id);
        if (!levels) throw new Error(`Invalid approval setup no level found`);

        const upcomingApprovalLevel = levels.find((item) => item.level == currentLevel + 1);
        if (!upcomingApprovalLevel) throw new Error(`Invalid approval setup no level found`);

        const getApproverForLevel = await approverSetupModel.getApproverByLevel(
          upcomingApprovalLevel.id
        );
        if (getApproverForLevel) {
          const approvalInstance: any = {
            approvalSetupId: approvalSetup.id,
            status: 'Pending',
            projectId: projectId,
            tableName: `${entityMetadata.schema}.${entityMetadata.tableName}`,
            tableId: value.id,
            entity: entity,
            dceId: approvalSetup?.dceId,
            submittedBy: submittedUser,
            currentLevelId: upcomingApprovalLevel.id,
            Details: `Approval pending in level 1`,
          };
          const levelInstance: DeepPartial<ApprovalLevelInstance[]> = getApproverForLevel.map(
            (approver) => ({
              approverId: approver.userId,
              details: `Approval pending in level 1`,
              status: 'Pending',
              levelId: upcomingApprovalLevel.id,
            })
          );

          levelInstance.forEach(({ approverId }) => {
            if (approverId) userIdForNotification.add(approverId);
          });

          if (!isMicroservice) {
            await this.handleNonMicroserviceApproval(
              entity,
              entityManager,
              value,
              approvalInstance,
              levelInstance,
              submittedUser,
              projectId,
              pendingStatusId
            );
          } else {
            await this.handleMicroserviceApproval(
              subModule,
              value,
              approvalInstance,
              levelInstance,
              token,
              entityManager,
              submittedUser,
              entity,
              projectId,
              pendingStatusId
            );
          }
        }
      } else {
        throw new Error(`Approval setup not found`);
      }
      return userIdForNotification;
    } else {
      throw new Error(`Approval not configured for ${entity}`);
    }
  };

  handleApprove = async (
    transactionalEntityManager: EntityManager,
    approvalData: Approval,
    item: {
      approvalId: string;
      levelInstanceId: string;
    },
    approvedUserName: string,
    approvedUserId: string,
    isMicroservice: boolean,
    date: Date,
    dceData: DataCaptureElements,
    addToSFTP: boolean,
    approvalChanges: Partial<Approval>,
    comment: string
  ) => {
    const approvalStatusModel = new ApprovalStatusModel();
    const approvedStatus = await approvalStatusModel.getByNameStatusId('Approved');
    const nextLevel = await transactionalEntityManager.findOne(ApprovalLevel, {
      where: {
        approvalSetupId: approvalData.approvalSetupId,
        level: (approvalData?.level?.level || 0) + 1,
        isDelete: false,
        approvers: { isDelete: false },
      },
      relations: ['approvers'],
    });

    if (nextLevel) {
      const newLevels: DeepPartial<Approval>[] = [];
      nextLevel.approvers?.map((value) => {
        const obj: DeepPartial<ApprovalLevelInstance> = {
          approvalId: item.approvalId,
          approverId: value.userId,
          status: 'Pending',
          levelId: nextLevel.id,
          details: `Approval pending in level ${nextLevel.level}`,
        };
        newLevels.push(obj);
      });

      await transactionalEntityManager.save(ApprovalLevelInstance, newLevels);
      const approvalUpdate: Partial<Approval> = {
        details: `Approval pending in level ${nextLevel.level}`,
        currentLevelId: nextLevel.id,
        updatedBy: approvedUserName,
      };
      approvalChanges = approvalUpdate;

      await getAuditDetails(
        approvedUserId,
        approvalData.entity || '',
        {
          level: approvalData.level?.level,
          status: 'Approved',
          updatedBy: approvedUserName,
          projectId: approvalData.projectId,
          id: approvalData.tableId,
          comment,
        },
        'approved'
      );
    } else if (!nextLevel) {
      const approvalUpdate: Partial<Approval> = {
        details: `All Level Approved`,
        status: 'Approved',
      };
      approvalChanges = approvalUpdate;

      const signerLevel = await approvalModel.getLevelApprovalSetupByIdForSigner(
        approvalData?.approvalSetupId || ''
      );

      const allLevelInstanceOnApproval = await approvalModel.getLevelInstanceByApprovalId(
        item.approvalId
      );

      const signerLevelInstance = allLevelInstanceOnApproval.filter(
        (value) => value.levelId == signerLevel?.id
      );
      let name;
      signerLevelInstance.map((sign) => {
        if (sign.approvedUser) {
          name = `${signerLevelInstance[0].user?.firstName} ${signerLevelInstance[0].user?.lastName}`;
        }
      });
      if (!name) {
        const check = signerLevelInstance.find((sign) => sign.id == item.levelInstanceId);
        if (check) {
          name = approvedUserName;
        }
      }

      if (name && date && approvedStatus) {
        if (!isMicroservice) {
          await transactionalEntityManager.query(
            `
              UPDATE ${approvalData.tableName} SET "qcVerifier" = $1, "qcDate" = $2,"updatedAt" = $3,"updatedBy" = $4,"approvalStatusId" = $5  WHERE id = $6;
            `,
            [name, date, new Date(), name, approvedStatus, approvalData.tableId]
          );
        } else {
          try {
            await editSensorByApprovalStatus(
              dceData?.microserviceDceId || '',
              approvalData.tableId || '',
              {
                approvalStatusId: approvedStatus,
                qcVerifier: name,
                qcDate: date,
                updatedAt: new Date(),
                updatedBy: name,
              }
            );
          } catch (error) {
            throw error;
          }
        }
      } else {
        throw new Error('Signer not found');
      }
      addToSFTP = true;

      await getAuditDetails(
        approvedUserId,
        approvalData.entity || '',
        {
          level: approvalData.level?.level,
          status: 'Approved',
          updatedBy: approvedUserName,
          projectId: approvalData.projectId,
          id: approvalData.tableId,
          comment,
        },
        'approved'
      );
    }
    return { approvalChanges, addToSFTP };
  };

  handleReject = async (
    item: {
      approvalId: string;
      levelInstanceId: string;
    },
    transactionalEntityManager: EntityManager,
    approvedUserName: string,
    approvalData: Approval,
    approvalChanges: Partial<Approval>,
    isMicroservice: boolean,
    approvedUserId: string,
    dceData: DataCaptureElements,
    comment: string
  ) => {
    const status = 'Rejected';
    const allLevelInstance = await approvalModel.getLevelInstanceByApprovalId(item.approvalId);
    const approvalStatusModel = new ApprovalStatusModel();
    const rejectedStatus = await approvalStatusModel.getByNameStatusId('rejected');
    if (allLevelInstance.length > 0) {
      await Promise.all(
        allLevelInstance.map(async (value) => {
          await transactionalEntityManager.update(ApprovalLevelInstance, value.id, {
            status: 'Rejected',
            details: `Rejected By ${approvedUserName} level ${approvalData?.level?.level}`,
          });
        })
      );
    }
    const approvalUpdate: Partial<Approval> = {
      details: `Rejected By ${approvedUserName} level ${approvalData?.level?.level}`,
      status: 'Rejected',
      updatedBy: approvedUserName,
    };
    approvalChanges = approvalUpdate;
    await getAuditDetails(
      approvedUserId,
      approvalData.entity || '',
      {
        level: approvalData.level?.level,
        status,
        updatedBy: approvedUserName,
        projectId: approvalData.projectId,
        id: approvalData.tableId,
        comment,
      },
      'approved'
    );
    if (!isMicroservice) {
      await transactionalEntityManager.query(
        `
UPDATE ${approvalData.tableName} SET "approvalStatusId" = $1  WHERE id = $2;
`,
        [rejectedStatus, approvalData.tableId]
      );
    } else {
      try {
        await editSensorByApprovalStatus(
          dceData?.microserviceDceId || '',
          approvalData.tableId || '',
          {
            approvalStatusId: status,
          }
        );
      } catch (error) {
        throw error;
      }
    }
    return { approvalChanges };
  };
}

export default ApprovalServices;
