import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

import { Project } from '../p_gen/Project';
import { Layer } from './Layer';
import { LegendCriteria } from './LegendCriteria';

@Entity({ schema: 'p_map' })
export class Legend {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  color?: string;

  @Column({ nullable: true })
  size?: string;

  @Column({ nullable: true })
  path?: string;

  @Column({ nullable: true })
  opacity?: string;

  @Column({ nullable: true })
  fill?: string;

  @Column({ nullable: true })
  isDefault?: boolean;

  @Column({ nullable: true })
  layerId?: string;

  @ManyToOne(() => Layer, { nullable: true }) 
  @JoinColumn({ name: 'layerId' })
  layer?: Layer;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => LegendCriteria, (legendCriteria) => legendCriteria.legend, {
    cascade: true,
  })
  legendCriteria?: LegendCriteria[];
}
