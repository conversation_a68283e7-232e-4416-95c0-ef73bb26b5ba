import { errorMailService } from './errorMail.service';

process.on('uncaughtException', async (error) => {
  console.error('Uncaught Exception:', error);
  await errorMailService(error);
  process.exit(1); // Exit after sending email
});

// Handle unhandled promise rejections
process.on('unhandledRejection', async (reason: any) => {
  console.error('Unhandled Rejection:', reason);
  await errorMailService(reason);
  process.exit(1);
});
