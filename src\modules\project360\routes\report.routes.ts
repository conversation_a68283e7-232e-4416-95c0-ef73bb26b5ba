import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
// import CrudController from '../../generic/crudDriver.controller';
// import Submittal from '../../../entities/p_gen/Submittal';
import { reportDocumentMiddleware } from '../../../shared/middlewares/uploadFileSftp.middleware';
import Submittal from '../../../entities/p_gen/Submittal';
import CrudController from '../../generic/crudDriver.controller';
import reportController from '../controllers/report.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUDController = new CrudController<Submittal>(Submittal);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post(
  '/version',
  authenticateToken,
  reportDocumentMiddleware,
  reportController.addReprotVersionWithDocument
);
router.put('/:id', authenticateToken, (req, res) => CRUDController.update(req, res));
router.post('/', reportDocumentMiddleware, authenticateToken, reportController.addReprot);
router.get('/version/:id', authenticateToken, reportController.getAllVersionOfReprot);
router.get('/by/project/:id', authenticateToken, reportController.findByProjectId);
router.get('/bold/access/token', authenticateToken, reportController.getBoldAccessToken);
router.get('/:id/:version', authenticateToken, reportController.findByIdAndVersion);
router.get('/count/by/status/:id/:status', authenticateToken, reportController.countByStatus);
router.get('/qc/report/:projectId', authenticateToken, reportController.qcReport);
router.get('/inplace/density/:projectId', authenticateToken, reportController.inPlaceDensityReport);
router.get('/proctor/:sampleId/:projectId', authenticateToken, reportController.proctorReport);
router.delete('/:id', authenticateToken, (req, res) => CRUDController.softDelete(req, res));
router.get('/flowclsm/:projectId/:sampleId', authenticateToken, reportController.flowConsistencyReports);
router.get('/ums/:projectId/:sampleId', authenticateToken, reportController.ucsReport);
router.get('/compressiveStrength/:projectId/:sampleId', authenticateToken, reportController.compressiveStrengthReport);
router.get('/clsm/:projectId/:sampleId', authenticateToken, reportController.clsmReport);
router.get('/clsmLabAndFieldTestingReport/:projectId/:sampleId', authenticateToken, reportController.clsmLabAndFieldTestingReport);
export default router;
