import { AzureBlobStorageService } from '@utils/sftpBlobAndFiles/azureBlobStorageService';
import { NextFunction, Request, Response } from 'express';
import { sanitizeFileName, validFileExtensions } from '../uploadFileSftp.middleware';
import errorMiddleware from '../error/error.middleware';

export const commonMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const files = req.files;
    if (!req.headers['x-app-version']) {
      Object.keys(req.body).forEach((key) => {
        // Check if the key represents a stringified object or array
        if (
          req.body[key] &&
          typeof req.body[key] == 'string' &&
          (req.body[key]?.startsWith('{') || req.body[key].startsWith('['))
        ) {
          try {
            // Parse the string back into an object/array
            req.body[key] = JSON.parse(req.body[key]);
          } catch (e) {
            console.error(`Failed to parse JSON for ${key}:`, e);
          }
        }
      });
    }

    if (files) {
      const fieldNames = Object.keys(files);
      const uploadFiles: string[] = [];
      await Promise.all(
        fieldNames.map(async (fieldName) => {
          const fileOrFiles = files[fieldName];

          // Handle single file case
          if (!Array.isArray(fileOrFiles)) {
            const file = fileOrFiles;
            const azureBlobStorageService = new AzureBlobStorageService(
              process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
              process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
            );

            const fileExtension = file?.name.split('.').pop();

            if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
              return res.status(400).json({
                error:
                  'Invalid file type. Supported types: Photo (jpg, jpeg, png, gif), Video (mp4, avi, mov)',
              });
            }
            const directoryPath: string = `form/upload/${Date.now()}`;
            const filePath = `${directoryPath}/${sanitizeFileName(file?.name)}`;

            try {
              const finalFilePath = await azureBlobStorageService.uploadFileFromRequest(
                req.body.projectId,
                filePath,
                file
              );
              uploadFiles.push(finalFilePath);
            } catch (err) {
              return errorMiddleware(err, 500, res, req);
            }
          }
          // Handle array of files case
          else {
            for (const file of fileOrFiles) {
              const azureBlobStorageService = new AzureBlobStorageService(
                process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
                process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
              );

              const fileExtension = file?.name.split('.').pop();

              if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
                return res.status(400).json({
                  error:
                    'Invalid file type. Supported types: Photo (jpg, jpeg, png, gif), Video (mp4, avi, mov)',
                });
              }
              const directoryPath: string = `form/upload/${Date.now()}`;
              const filePath = `${directoryPath}/${sanitizeFileName(file?.name)}`;

              try {
                const finalFilePath = await azureBlobStorageService.uploadFileFromRequest(
                  req.body.projectId,
                  filePath,
                  file
                );
                uploadFiles.push(finalFilePath);
              } catch (err) {
                return errorMiddleware(err, 500, res, req);
              }
            }
          }
          // If the field is a signature, store it as a string instead of an array
          if (fieldName === 'signature' || fieldName.toLowerCase().includes('signature')) {
            // If there's only one file, use its path directly as a string
            req.body[fieldName] = uploadFiles.length > 0 ? uploadFiles[0] : '';
          } else {
            // For other files, keep the array format
            req.body[fieldName] = uploadFiles;
          }
        })
      );
    }
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }

  next();
};
