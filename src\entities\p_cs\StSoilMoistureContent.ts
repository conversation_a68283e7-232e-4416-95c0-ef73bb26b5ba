// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { Test } from '../p_meta/Test';
import { Sample } from './Sample';
import { TestVariant } from '../p_meta/Testvariant';
import { TestMethod } from '../p_meta/TestMethod';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilMoistureContent extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  passFail?: string;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  underWeightSpecimen?: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  multiLayeredSoil?: boolean;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  materialExcluded?: boolean;

  @ColumnInfo({
    customData: {
      name: 'Weight Of Water',
      fieldName: 'weightOfWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  weightOfWater?: number;

  @Column({ nullable: true })
  panNo?: string;

  @ColumnInfo({
    customData: {
      name: 'Reason for Exclusion',
      fieldName: 'reasonforExclusion',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  reasonforExclusion?: string;

  @ColumnInfo({
    customData: {
      name: 'Moisture Content',
      fieldName: 'moistureContent',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moistureContent?: number;

  //Should populate when ASTM D2216 is selected

  @ColumnInfo({
    customData: {
      name: 'Weight Of Container',
      fieldName: 'weightOfContainer',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  weightOfContainer?: number;

  @ColumnInfo({
    customData: {
      name: 'Weight Of Moist Specimen Container',
      fieldName: 'weightOfMoistSpecimenContainer',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  weightOfMoistSpecimenContainer?: number;

  @ColumnInfo({
    customData: {
      name: 'Weight Of Dry Specimen Container',
      fieldName: 'weightOfDrySpecimenContainer',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  weightOfDrySpecimenContainer?: number;

  @ColumnInfo({
    customData: {
      name: 'Weight Of Oven Dried Specimen',
      fieldName: 'weightOfOvenDriedSpecimen',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  weightOfOvenDriedSpecimen?: number;

  @ColumnInfo({
    customData: {
      name: 'Drying Temperature',
      fieldName: 'dryingTemperature',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  dryingTemperature?: number;

  // Should populate when ASTM D4643 is selected

  // @Column({type: 'decimal', nullable: true})
  // dryingPeriodInterval?: number;

  // @Column({type: 'decimal', nullable: true})
  // timePeriodInterval?: number;

  // @Column({type: 'decimal', nullable: true})
  // settingPeriodInterval?: number;

  // Should populate when ASTM D4643 and ASTM D4959 is selected

  // @Column({type: 'decimal', nullable: true})
  // massOfSpecimen?: number;

  // @Column({type: 'decimal', nullable: true})
  // initialMass?: number;

  // @Column({type: 'decimal', nullable: true})
  // finalMass?: number;

  // @Column({ nullable: true })
  // desiccatorUsed?: boolean;

  // @Column({ nullable: true })
  // heatSource?: string; //Only populated for ASTM D4959

  // @Column({ nullable: true })
  // comparisonTestMethod?: string;

  // @Column({ nullable: true })
  // microWaveOvenType?: string;

  // @Column({ nullable: true })
  // dryingSetting?: string;

  // @Column({ nullable: true })
  // dryingCycle?: string;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false, nullable: true })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
