const  normalizeCtNumber = (input: string): string | null => {
    if (!input) return null;
  
    const match = input.trim().toUpperCase().match(/^(CT|AT)[\W_]*([0-9]+)$/i);
    if (!match) return null;
  
    const prefix = match[1].toUpperCase();
    const number = match[2].padStart(5, '0');
  
    return `${prefix}-${number}`;
  }

 const getCellValue = (
  row: Record<string, any>,
  key: string
): string => {
  const normalizedKey = key.toLowerCase().replace(/\s|\(.*?\)/g, '');
  for (const k in row) {
    const normalizedRowKey = k.toLowerCase().replace(/\s|\(.*?\)/g, '');
    if (normalizedRowKey === normalizedKey) {
      const value = row[k]?.toString().trim() ?? '';
      return value; 
    }
  }
  return ''; 
};
export { normalizeCtNumber , getCellValue};