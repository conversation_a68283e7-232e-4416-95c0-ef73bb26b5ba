import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import axios from 'axios';

const router: Router = express.Router();

router.get('/token', authenticateToken, async (req, res) => {
  try {
    const userEmail = '<EMAIL>';

    let QMSGetTokenlink = `https://qms-dev.smartinfrahub.com/api/FieldSheet.aspx/GetAuthToken?email='${userEmail}'`;
    if (process.env.NODE_ENV === 'prod') {
      QMSGetTokenlink = `https://qms.smartinfrahub.com/api/FieldSheet.aspx/GetAuthToken?email='${userEmail}'`;
    }
    axios
      .get(QMSGetTokenlink, {
        headers: {
          'Content-Type': 'application/json; charset=utf-8',
        },
      })
      .then((resp) => {
        const parsed = JSON.parse(resp.data.d);
        let finalLink = `https://qms-dev.smartinfrahub.com/login.aspx?email=${userEmail}&token=${parsed.token}`;

        if (process.env.NODE_ENV === 'prod') {
          finalLink = `https://qms.smartinfrahub.com/login.aspx?email=${userEmail}&token=${parsed.token}`;
        }

        return res.json({ link: finalLink });
      })
      .catch((err) => {
        console.log(err);
      });
  } catch (error) {
    res.status(500).json({ error: (error as any).message });
  }
});

module.exports = router;
