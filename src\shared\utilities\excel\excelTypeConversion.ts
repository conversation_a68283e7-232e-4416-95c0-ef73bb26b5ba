import { Column } from 'exceljs';

const excelTypeConversion = (type: string, numberOfPoints?: number): Partial<Column> => {
  switch (type) {
    case 'int':
    case 'integer':
    case 'float':
    case 'double':
    case 'decimal':
    case 'numeric':
    case 'number':
      const formattedValue =
        numberOfPoints === null || numberOfPoints === undefined
          ? '0.00'
          : numberOfPoints === 0
          ? '0'
          : `0.${'0'.repeat(numberOfPoints)}`;
      return { style: { numFmt: formattedValue } };
    case 'date':
      return { style: { numFmt: 'mm-dd-yyyy' } };
    case 'timestamp':
    case 'timestamp without time zone':
    case 'timestamp with time zone':
    case 'datetime':
      return { style: { numFmt: 'mm-dd-yyyy hh:mm:ss AM/PM' } };
    case 'boolean':
      return {}; // or handle as "Yes"/"No"
    default:
      return {}; // string/text fallback
  }
};

export default excelTypeConversion;
