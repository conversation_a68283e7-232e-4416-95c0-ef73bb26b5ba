// import { get<PERSON><PERSON><PERSON>, In, <PERSON>Null, Not } from 'typeorm';

// class StSoilUCSTestModel {
//   private StSoilUCSTestRepositry = getManager().getRepository(StSoilUCSTest);

//   getTestsBySampleIds = async (sampleIds: string[]) => {
//     try {
//       const ucsTests = await this.StSoilUCSTestRepositry.find({
//         select: ['id', 'sampleId', 'noOfDays', 'unconfinedCompressiveStrength'],
//         where: {
//           sampleId: In(sampleIds),
//           unconfinedCompressiveStrength: Not(IsNull()),
//           noOfDays: Not(IsNull()),
//         },
//       });

//       return ucsTests;
//     } catch (error) {
//       throw error;
//     }
//   };
// }

// const stSoilUCSTestModel = new StSoilUCSTestModel();
// export default stSoilUCSTestModel;
