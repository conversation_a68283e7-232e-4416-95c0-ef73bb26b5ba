import { getManager, Like } from 'typeorm';
import { Station } from '../../../../entities/p_map/Station';
import { convertLatLongToEastingNorthing } from '../../../../shared/utilities/spatialCoordinates/eastingAndNothingConversion';

class StationModel {
  async getStationByProjectId(projectId: string) {
    try {
      const entityManager = getManager().getRepository(Station);
      const stations = await entityManager.find({
        where: { projectId, isDelete: false },
        order: { pointNumber: 'ASC' },
      });
      return stations;
    } catch (error) {
      throw error;
    }
  }
  async getStationsByAlignmentId(alignmentId: string) {
    try {
      const entityManager = getManager().getRepository(Station);
      const stations = await entityManager.find({
        where: { stationAlignmentId: alignmentId, isDelete: false },
        order: { pointNumber: 'ASC' },
      });
      return stations;
    } catch (error) {
      throw error;
    }
  }
  async getCoordinatesByStation(station: string, projectId: string) {
    try {
      const entityManager = getManager().getRepository(Station);
      const data = await entityManager.findOne({ where: { station, projectId } });
      if (data) {
        return data;
      } else {
        const parts = station.split('+');
        if (parts.length != 2) {
          throw new Error('Invalid station number');
        }
        const results = await entityManager.find({
          where: {
            station: Like(`${parts[0]}+%`),
          },
        });
        if (results.length < 0) {
          throw new Error('Station not found');
        }
        const targetValue = this.extractNumber(station);
        const nearestValue = results.reduce((prev, curr) => {
          const prevDiff = Math.abs(this.extractNumber(prev.station || '') - targetValue);
          const currDiff = Math.abs(this.extractNumber(curr.station || '') - targetValue);
          return currDiff < prevDiff ? curr : prev;
        });
        if (nearestValue && nearestValue.latitude && nearestValue.longitude) {
          const conversionResult: any = convertLatLongToEastingNorthing(
            nearestValue.latitude,
            nearestValue.longitude
          );
          if (conversionResult.northing && conversionResult.easting) {
            (nearestValue as any).easting = conversionResult.easting;
            nearestValue.northing = conversionResult.northing;
          }
        }
        return nearestValue;
      }
    } catch (error) {
      throw error;
    }
  }

  extractNumber = (str: string) => {
    return parseFloat(str.split('+')[1]);
  };
}

export default StationModel;
