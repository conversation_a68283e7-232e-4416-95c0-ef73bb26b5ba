import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilOrganicContent } from '../../../../../../entities/p_cs/StSoilOrganicContent';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilOrganicContent>(StSoilOrganicContent);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'organicContent')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'organicContent')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'organicContent')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'organicContent')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'organicContent')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'organicContent')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'organicContent')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'organicContent')
);

export default router;
