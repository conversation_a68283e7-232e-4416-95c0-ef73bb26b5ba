import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  AfterLoad,
} from 'typeorm';
import { Project } from '../p_gen/Project'; // Import the Projects entity
import { ProjectEquipment } from './ProjectEquipment';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

@Entity({ schema: 'p_gen' })
export class Resource {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  equipmentId?: string;

  @ManyToOne(() => ProjectEquipment, { nullable: true })
  @JoinColumn({ name: 'equipmentId' })
  equipment?: ProjectEquipment;

  @Column({ nullable: true })
  userId?: string;

  @Column()
  name?: string;

  @Column()
  type?: string;

  @Column()
  qualificationStatus?: string;

  @Column()
  availabilityStatus?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  user?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.userId) {
        const data = await getUserById(this.userId);
        this.user = data;
      } else {
        this.user = null;
      }
    } catch (error) {
      this.user = null;
    }
  }
}
