import { Request, Response } from 'express';
import photoVideoModel from '../models/photoVideo.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import { getMediaName } from '../../../shared/utilities/photoVideo/getMediaName';
import { getPhotoNumberByProjectId } from '../../../shared/utilities/photoVideo/getPhotoNumber';
import { photoVideoUploadFunction } from '../../../shared/utilities/photoVideo/photoVideoUpload';

class PhotoVideoController {
  async addPhotoVideo(req: Request, res: Response) {
    try {
      const fileData = req.files?.files;

      const bodyData = req.body;
      const userAgent = req.headers['x-custom-header'];
      let source = 'Web';
      if (userAgent) {
        const userAgentAsJson = JSON.parse(userAgent as string);
        source = userAgentAsJson.isMobile ? 'Mobile' : 'Web';
        if (userAgentAsJson.browser === 'Dart') {
          source = 'Mobile';
        }
      }
      if (fileData && bodyData) {
        bodyData.createdBy = (req as any).user.name;
        bodyData.updatedBy = (req as any).user.name;
        if (!bodyData.photographer) {
          bodyData.photographer = (req as any).user.id;
        }
        bodyData.dateTime = new Date(bodyData.dateTime);

        bodyData.photoNumber = await getPhotoNumberByProjectId(bodyData.projectId);
        bodyData.name = await getMediaName(bodyData);
        const result = await photoVideoUploadFunction(fileData, bodyData.projectId, bodyData);
        if (
          result.uploadedFiles.length <= 0 ||
          !result.uploadedFiles[0].path ||
          result.uploadedFiles[0].path === ''
        ) {
          throw new Error('No File is uploaded');
        }
        result.uploadedFiles.map((data) => {
          for (const key in data) {
            if (key == 'dropDown') {
              delete data.dropDown;
            }
            if (
              typeof data[key] === 'object' &&
              data[key] !== null &&
              !(data[key] instanceof Date)
            ) {
              delete data[key];
            }
            if (typeof data[key] === 'string' && data[key] === '') {
              data[key] = null;
            }
          }

          return data;
        });
        const addData = await photoVideoModel.addAsTransaction(
          result.uploadedFiles,
          (req as any).user.id,
          source
        );
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: addData,
          msg: message,
        });
      } else {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'file not found' });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getByproject(req: Request, res: Response) {
    try {
      const projectId = req.params.id;

      const data = await photoVideoModel.getByprojectId(projectId);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async deletePhotoVideoWithApproval(req: Request, res: Response) {
    try {
      const id = req.params.id;
      await photoVideoModel.deleteAsTransaction(id);
      const message = req.__('DeleteSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: [],
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getByIdt(req: Request, res: Response) {
    try {
      const projectId = req.params.id;

      const data = await photoVideoModel.getById(projectId);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

export default PhotoVideoController;
