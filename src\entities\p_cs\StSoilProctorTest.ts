import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';

import { Sample } from './Sample';
import { TestVariant } from '../p_meta/Testvariant';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { Project } from '../p_gen/Project';
import { CompactionRammer } from '../p_domain/CompactionRammer';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { StSoilProctorWorksheet } from './StSoilProctorWorksheet';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { EventLog } from '@entities/p_gen/EventLog';
import { ProctorMold } from '@entities/p_meta/ProctorMold';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilProctorTest extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  testNo?: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Id', // this column will be entered by the
      fieldName: 'sampleId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.sample WHERE "id" = $1`,
      getListQuery: `SELECT id,"QMSSampleYear" || '-' || "QMSLabSampleId" as name FROM p_cs.sample WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 75;`,
      listParams: 'id',
      listName: 'sampleList',
    },
  })
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  passFail?: string;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  proctorMoldId?: string;

  @ManyToOne(() => ProctorMold, { nullable: true })
  @JoinColumn({ name: 'proctorMoldId' })
  proctorMold?: ProctorMold;

  @ColumnInfo({
    customData: {
      name: 'Test Variant', // this column will be entered by the
      fieldName: 'testVariantId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.test_variant WHERE "standardCode" = $1`,
      getListQuery: `SELECT id,"variantAlias" as name FROM p_meta.test_variant where "testId" = '89674f8e-c735-4bce-8a11-f4f7908ecc73' OR "testId" = '04e266d9-b696-4be4-8fbb-cf55db5985f3' ORDER BY "updatedAt";`,
      listName: 'testVariantList',
    },
  })
  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Compaction Rammer',
      fieldName: 'compactionRammerId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.compaction_rammer WHERE name = $1',
      getListQuery:
        'Select id, alias as name FROM p_domain.compaction_rammer WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'compactionRammerList',
    },
  })
  @Column({ nullable: true })
  compactionRammerId?: string;

  @ManyToOne(() => CompactionRammer, { nullable: true })
  @JoinColumn({ name: 'compactionRammerId' })
  compactionRammer?: CompactionRammer;

  @ColumnInfo({
    customData: {
      name: 'Corrected Maximum Dry Unit Weight',
      fieldName: 'correctedMaximumDryUnitWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  correctedMaximumDryUnitWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  moldVolume?: number;

  @Column({ type: 'decimal', nullable: true })
  moldWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Corrected Optimum Moisture Content',
      fieldName: 'correctedOptimumMoistureContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  correctedOptimumMoistureContent?: number;

  @Column({ type: 'decimal', nullable: true })
  specificGravity?: number;

  @ColumnInfo({
    customData: {
      name: 'No 4 Sieve Passing Percentage',
      fieldName: 'no4SievePassingPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  no4SievePassingPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'No 4 Sieve Retained Percentage',
      fieldName: 'no4SieveRetainedPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  no4SieveRetainedPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'Sieve 38 Passing Percentage',
      fieldName: 'sieve38PassingPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve38PassingPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'Sieve 38 Retained Percentage',
      fieldName: 'sieve38RetainedPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve38RetainedPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'Sieve 34 Passing Percentage',
      fieldName: 'sieve34PassingPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve34PassingPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'Sieve 34 Retained Percentage',
      fieldName: 'sieve34RetainedPercentage',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve34RetainedPercentage?: number;

  @ColumnInfo({
    customData: {
      name: 'Optimum Moisture Content',
      fieldName: 'optimumMoistureContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  optimumMoistureContent?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Dry Unit Weight',
      fieldName: 'maximumDryUnitWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumDryUnitWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'no Of Points',
      fieldName: 'noOfPoints',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  noOfPoints?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Hammer Type',
  //     fieldName: 'hammerType',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: false,
  //   },
  // })
  // @Column({ nullable: true })
  // hammerType?: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Preparation Type',
      fieldName: 'samplePreparationType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  samplePreparationType?: string;

  @Column({ type: 'decimal', nullable: true })
  weightOfWetSoil?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfDrySoil?: number;

  @Column({ type: 'decimal', nullable: true })
  initialMoistureContent?: number;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false, nullable: true })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @OneToMany(() => StSoilProctorWorksheet, (worksheet) => worksheet.stSoilProctorTest, {
    cascade: true,
  })
  proctorWorksheet?: StSoilProctorWorksheet[];

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
