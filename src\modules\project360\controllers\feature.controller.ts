import { Request, Response } from 'express';
import FeatureModel from '../models/feature.model';
import featuretModel from '../models/feature.model';
import { formatAsNameValuePairs } from '@utils/custom/formatAsNameValuePairs';

class FeatureController {
  constructor() {}

  async findByProjectId(req: Request, res: Response) {
    try {
      const featureDate = await FeatureModel.findByProjectId(req.params.id);
      // getting the data from database with the given id
      // checking if data is found with the id
      if (featureDate) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: featureDate,
          msg: 'feature found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No feature data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async formattedFeatureData(req: Request, res: Response) {
    try {
      const featureDate = await FeatureModel.findByProjectId(req.params.id);
      const formattedData = formatAsNameValuePairs(featureDate || []);
      return res.status(200).json({
        isSucceed: true,
        data: formattedData,
        msg: 'features found',
      });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async addFeatrue(req: Request, res: Response) {
    try {
      if (!req.body.projectId) {
        const message = req.__('InvalidInputDataError');
        return res.status(400).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }
      const feature = await featuretModel.addFeature(req.body, (req as any).user.id || '');

      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: feature,
        msg: message,
      });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async findByProjectWithoutFormatId(req: Request, res: Response) {
    try {
      const featureDate = await FeatureModel.findByProjectWithoutformatId(req.params.id);
      // getting the data from database with the given id
      // checking if data is found with the id
      if (featureDate) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: featureDate,
          msg: 'feature found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No feature data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

const featureController = new FeatureController();
export default featureController;
