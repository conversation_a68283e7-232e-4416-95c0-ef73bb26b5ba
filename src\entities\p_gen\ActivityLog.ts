import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project'; // Import the Projects entity

@Entity({ schema: 'p_gen' })
export class ActivityLog {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'timestamp', nullable: true })
  date?: Date;

  @Column({ nullable: true })
  detail?: string;

  @Column({ nullable: true })
  tableDetails?: string;

  @Column({ nullable: true })
  table?: string;

  @Column({ nullable: true })
  comment?: string;

  @Column({ nullable: true })
  entity?: string;

  @Column({ nullable: true })
  tableId?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
