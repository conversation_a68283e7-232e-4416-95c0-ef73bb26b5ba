import * as ExcelJS from 'exceljs';
import {
  Between,
  EntityTarget,
  ObjectLiteral,
  Repository,
  getConnection,
  getManager,
} from 'typeorm';

import { convertLatLongToEastingNorthing } from '../../spatialCoordinates/eastingAndNothingConversion';
import { entityRelationList } from '../../entity/entityRelation';
import DCECoordinatesConfigModel from '../../../../modules/project360/models/dceCoordinatesConfig.model';
import dceModel from '../../../../modules/project360/models/meta/dce.model';
import changeGeoJsonUUIDsToValue from '../../map/changeGeoJsonUUIDsToValues';
import FindData from '../../custom/findByProjectIdWithDataAccess';
import dceConfigToExcelConversion, {
  convertSubdceToExcel,
} from '../../dce/dceConfigToDatagridConversion';
import DCEConfigurationModel from '../../../../modules/project360/models/dceConfiguration.model';
import { getUserRoleDetail } from '../../role/getUserRoleDetails';
import { checkIsSuperAdmin } from '../../../server/platformApi/role';
import SubDCEModel from '../../../../modules/project360/models/meta/SubDce.model';
import { camelCaseToTitleCase } from '../../custom/caseConversion';
import ImportData from './importData';
import geoJsonConversionBasedOnType from '../../map/geoJsonConversionBasedOnType';
import MetaDCEConfigurationModel from '@models/meta/adminDceConfiguration.model';
import { DCECoordinatesConfiguration } from '@entities/p_gen/DCECoordinatesConfig';
import excelTypeConversion from '@utils/excel/excelTypeConversion';
import { revertNormalizedAlias } from '@utils/custom/aliasFieldNameFallBack';
import UniversalFilterMappingModel from '@models/map/universalFilterMapping.model';
import DCERelationModel from '../../../../modules/project360/models/meta/dceRelation.model';
import { addLookupsToWorkbook } from '../addLookups';
type ColumnWithType = { name: string; type: string };

type SubDceTemplate =
  | { worksheet: string; entity: string; columns: ColumnWithType[]; id: string }
  | { worksheet: string; entity: string; columns: string[]; id: string };

class CreateTemplate<T extends ObjectLiteral> {
  private Entity: EntityTarget<T>;
  private repository: Repository<T>;

  constructor(entity: EntityTarget<T>) {
    this.Entity = entity;
    this.repository = getConnection().getRepository(entity);
  }
  async createExcel(table: string, entity: string, projectId: string, userId: string) {
    try {
      // Create a new workbook
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(table);
      const dceData = await dceModel.findByEntity(entity);
      if (dceData) {
        const dceConfigModel = new MetaDCEConfigurationModel();
        let dceConfig = await dceConfigModel.getData(dceData);
        const DCECoordinatesConfigurationRepository = getManager().getRepository(
          DCECoordinatesConfiguration
        );
        const coordinatesData = await DCECoordinatesConfigurationRepository.findOne({
          where: { dceId: dceData.id, projectId, isDelete: false },
        });
        const excludedColumns = ['longitude', 'latitude', 'easting', 'northing'];
        dceConfig = dceConfig.filter((value) => !excludedColumns.includes(value.columnName));
        const dceRelationModel = new DCERelationModel();
        // calling getdropdownbydce model to get the options for the all columns
        const allColumnsOptions = await dceRelationModel.getDropdownByDce(
          dceData.id,
          projectId,
          userId
        );
        if (coordinatesData) {
          if (coordinatesData.isLatLong) {
            dceConfig.push({
              columnName: 'longitude',
              alias: 'Longitude',
              editable: true,
              id: 'longitude',
              updatedBy: 'system',
              order: dceConfig.length + 1,
              hide: false,
            });
            dceConfig.push({
              columnName: 'latitude',
              alias: 'Latitude',
              editable: true,
              id: 'longitude',
              updatedBy: 'system',
              order: dceConfig.length,
              hide: false,
            });
          } else if (!coordinatesData.isLatLong) {
            dceConfig.push({
              columnName: 'easting',
              alias: 'Easting',
              editable: true,
              id: 'easting',
              updatedBy: 'system',
              order: dceConfig.length + 1,
              hide: false,
            });
            dceConfig.push({
              columnName: 'northing',
              alias: 'Northing',
              editable: true,
              id: 'northing',
              updatedBy: 'system',
              order: dceConfig.length,
              hide: false,
            });
          }
        }

        if (!dceConfig || dceConfig.length <= 0) {
          throw new Error('Template config not found');
        }
        const columnsToRemove = [
          'projectId',
          'submittalVersionId',
          'workPackageActivityId',
          'createdUserId',
          'geom',
          'createdAt',
          'createdBy',
          'updatedAt',
          'updatedBy',
          'isDelete',
          'qcDate',
          'qcVerifier',
          'qaVerifier',
          'qaDate',
          'sampleTestId',
          'QMSId',
          'approvalStatusId',
          'eventLogId',
        ];
        const aliasToColumnName: Record<string, string> = {};
        const row = dceConfig
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .filter(
            (column) => column.hide === false && !columnsToRemove.includes(column.columnName || '')
          )
          .map((col) => {
            if (typeof col.alias === 'string') {
              aliasToColumnName[col.alias] = col.columnName;
              return col.alias;
            }
            return '';
          });
        const subDCEItems = await this.addSubDceToTemplate(dceData.id || '');
        if (subDCEItems && subDCEItems.length > 0) {
          row.unshift('Related Id');
          subDCEItems.map((item) => {
            (item.columns as string[]).unshift('Related Id');
            const subWorksheet = workbook.addWorksheet(item.worksheet);
            subWorksheet.addRow(item.columns);
          });
        }
        worksheet.addRow(row);
        // Calling addLookupsToWorkbook to get
        const lookupRangesByField = addLookupsToWorkbook(workbook, allColumnsOptions);
        //  Find whether allcolumns has options or not
        const hasDropdownOptions = Object.keys(allColumnsOptions).some(
          (key) => (allColumnsOptions[key] ?? []).length > 0
        );

        if (hasDropdownOptions) {
          row.forEach((header, index) => {
            /** We are getting options with key as "columnName as "row" contains alias we stored alias, columns in aliasToColumnName 
             matching columns the adding options to alias and creating excel */
            const columnName = aliasToColumnName[header];
            if (!columnName) return;
            const normalizedKey = columnName.replace(/\s+/g, '').toLowerCase();
            const validationRange = lookupRangesByField[normalizedKey];
            if (validationRange) {
              const columnLetter = worksheet.getColumn(index + 1).letter;
              for (let rowNumber = 2; rowNumber <= 1000; rowNumber++) {
                worksheet.getCell(`${columnLetter}${rowNumber}`).dataValidation = {
                  type: 'list',
                  formulae: [validationRange],
                  showErrorMessage: true,
                  errorTitle: 'Invalid Entry',
                  error: 'Please select a valid option from the dropdown list.',
                };
              }
            }
          });
        }

        if (subDCEItems && subDCEItems.length > 0) {
          workbook.worksheets.forEach((ws) => {
            // Find the "Related Id" in the first row (or adjust if needed)
            const relatedIdCell = ws.getCell('A1'); // Assuming "Related Id" is in column A, row 1

            // Apply font styling
            relatedIdCell.font = {
              bold: true,
              color: { argb: 'FFFF0000' }, // Red color for highlighting
            };

            relatedIdCell.note = `This ID links the records to the associated worksheets.`;
          });
        }
        // Save the workbook to a file
        const excelBuffer = await workbook.xlsx.writeBuffer();
        console.log('Excel file created successfully!');

        return excelBuffer;
      }
    } catch (error) {
      throw error;
    }
  }

  async createDownloadExcel<K extends keyof T>(
    projectId: T[K],
    keyVariable: string,
    entityValue: any,
    user: any,
    query: any
  ) {
    try {
      // Create a workbook and add a worksheet
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(camelCaseToTitleCase(keyVariable));
      const findData = new FindData<typeof entityValue>(entityValue);
      const dceData = await dceModel.findByEntity(keyVariable);
      if (dceData) {
        const dceConfigModel = new DCEConfigurationModel();
        const dceConfigData = await dceConfigModel.getData(dceData, projectId);

        if (!dceConfigData || dceConfigData.length <= 0) {
          throw new Error('Template config not found');
        }
        const subDCEItems = await this.addSubDceToTemplate(dceData.id || '', true);
        const data = await findData.findByProjectIdWithDataAccess(
          keyVariable,
          user,
          projectId,
          query
        );

        const importData = new ImportData(entityValue);

        if (subDCEItems && subDCEItems.length > 0) {
          const filteredData: any[] = [];
          for (const item of subDCEItems) {
            for (const [index, value] of data.data.entries()) {
              const existingEntity = filteredData.find((fil) => fil.entity === item.entity);

              if (value[item.entity] && value[item.entity].length > 0) {
                // const updateSubData = await subDCEUUIDChange(value[item.entity], item.id, user?.id);
                const updateSubData = value[item.entity];

                if (existingEntity) {
                  existingEntity.data.push(...updateSubData); // Spread operator to merge arrays
                } else {
                  filteredData.push({
                    entity: item.entity,
                    data: [...updateSubData], // Clone the array to avoid mutation
                    index: index + 1,
                  });
                }
              }
            }
          }
          const columnInfo =
            filteredData && filteredData.length > 0
              ? await importData.getColumnInfo(filteredData, projectId)
              : [];
          for (const item of subDCEItems) {
            (item.columns as ColumnWithType[]).unshift({ name: 'Related Id', type: 'string' });
            const subWorksheet = workbook.addWorksheet(item.worksheet);
            const columns: ColumnWithType[] = item.columns as ColumnWithType[];

            const subDceData = data.data.flatMap((value: any, index: number) => {
              if (
                value[item.entity] &&
                Array.isArray(value[item.entity]) &&
                value[item.entity].length > 0
              ) {
                return value[item.entity].map((work: any) => {
                  work.relatedId = index + 1;
                  return work;
                });
              }
              return [];
            });

            const columnInfoUpdate = columnInfo.find((value) => value.entity == item.entity);
            const subDCEConvertedData = await convertSubdceToExcel(
              columnInfoUpdate?.config || [],
              subDceData,
              projectId,
              user.id
            );

            const subDceRow: any[] = [];
            subDCEConvertedData.forEach((current: any, index) => {
              if (!('Related Id' in current)) {
                current['Related Id'] = index + 1;
              }
              const rowValues = columns.map((key: ColumnWithType) => current[key.name as string]);
              subDceRow.push(rowValues);
            });

            subWorksheet.addTable({
              name: item.entity,
              ref: 'A1',
              headerRow: true,
              totalsRow: false,
              style: {
                theme: 'TableStyleMedium9',
                showRowStripes: true,
              },
              columns: columns.map((value) => ({
                name: value.name,
                filterButton: true,
                ...excelTypeConversion(value.type, 2),
              })),
              rows: subDceRow.length > 0 ? subDceRow : [columns.map(() => '')], // <= key update
            });

            subWorksheet.columns.forEach((column) => {
              let maxLength = 0;
              if (column.eachCell) {
                column.eachCell({ includeEmpty: true }, (cell) => {
                  const cellValue = cell.value?.toString() || '';
                  maxLength = Math.max(maxLength, cellValue.length);
                });
              }
              column.width = maxLength + 2;
            });
          }
        }

        let convertedData = await dceConfigToExcelConversion(
          dceConfigData || [],
          data.data,
          projectId,
          user.id
        );
        if (convertedData && convertedData.length > 0) {
          if (subDCEItems && subDCEItems.length > 0) {
            convertedData = convertedData.map((value, index) => {
              value['Related Id'] = index + 1;
              return value;
            });
          }
          const key = Object.keys(
            convertedData && convertedData.length > 0 ? convertedData[0] : {}
          );

          const finalKey = key
            .sort((a, b) => (a === 'Related Id' ? -1 : b === 'Related Id' ? 1 : 0)) // Move 'relatedId' to the first index
            .map((value) => {
              // Find the matching column type based on the column key
              const columnType = dceConfigData.find(
                (item) => (item.alias || item.columnName) === revertNormalizedAlias(value)
              );

              // If the column type is found, pass it to excelTypeConversion
              return {
                name: value,
                filterButton: true,
                ...excelTypeConversion(
                  columnType?.type || 'string',
                  columnType?.decimalPointsNeeded
                ),
              };
            }); // Convert to the required format

          const rowValues: any[] = convertedData.map((item: any) => {
            return key.map((key) => item[key]);
          });

          worksheet.addTable({
            name: 'MyTable',
            ref: 'A1', // Starting cell for the table
            headerRow: true,
            totalsRow: false,
            style: {
              theme: 'TableStyleMedium9', // Choose a table style
              showRowStripes: true,
            },
            columns: finalKey,
            rows: rowValues,
          });

          worksheet.columns.forEach((column) => {
            let maxLength = 0;
            if (column.eachCell) {
              column.eachCell({ includeEmpty: true }, (cell) => {
                const cellValue = cell.value?.toString() || ''; // Safe check for undefined
                maxLength = Math.max(maxLength, cellValue.length); // Update maxLength
              });
            }
            column.width = maxLength + 2; // Add a little padding to the column width
          });

          const buffer = await workbook.xlsx.writeBuffer();

          return buffer;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  addSubDceToTemplate = async (
    dceId: string,
    type: boolean = false
  ): Promise<SubDceTemplate[] | null> => {
    try {
      const subDCEModel = new SubDCEModel();
      const subDceData = await subDCEModel.findByDCEId(dceId);
      if (subDceData && subDceData.length > 0) {
        const data = await Promise.all(
          subDceData.map(async (value) => {
            try {
              const columns = await subDCEModel.getColumnsBySubDce(value);
              if (columns && columns.length > 0) {
                const worksheet = camelCaseToTitleCase(value.entity || '');
                if (type) {
                  return {
                    worksheet,
                    entity: value.entity,
                    id: value.id,
                    columns: columns.map((item) => {
                      if (item.name.includes('Id')) {
                        item.name = item.name.replace('Id', '');
                      }
                      return { name: camelCaseToTitleCase(item.name || ''), type: item.type };
                    }),
                  };
                } else {
                  return {
                    worksheet,
                    entity: value.entity,
                    id: value.id,
                    columns: columns.map((item) => {
                      if (item.name.includes('Id')) {
                        item.name = item.name.replace('Id', '');
                      }
                      return camelCaseToTitleCase(item.name || '');
                    }),
                  };
                }
              }
            } catch (error) {
              console.error(`Error processing value: ${value}`, error);
            }
            return null;
          })
        );

        return (data.filter((value) => value !== null) as SubDceTemplate[]) || null;
      }
      return null;
    } catch (error) {
      throw error;
    }
  };

  async getData(
    projectId: string,
    keyVariable: string,
    replaceUUID?: boolean,
    user?: any,
    filters?: {
      date: {
        startDate: Date;
        endDate: Date;
      };
    },
    layerId?: string
  ) {
    try {
      const dceData = await dceModel.findByEntity(keyVariable);
      if (dceData && dceData.isGeoJson) {
        let filterColumn: any = '';
        const filterModel = new UniversalFilterMappingModel();
        const filterModelData = await filterModel.findBylayerId(layerId!);

        if (filterModelData && filterModelData.length > 0) {
          const dateFilterColumn = filterModelData.find(
            (filterMapping) => filterMapping.filter?.name === 'Date'
          );
          filterColumn = dateFilterColumn?.columnName;
        }

        // Create a workbook and add a worksheet
        let whereCondition = { projectId, isDelete: false } as any;

        if (filters?.date && filterColumn) {
          whereCondition[filterColumn] = Between(filters.date.startDate, filters.date.endDate);
        }

        let relationNeed: string[] = [];
        if (keyVariable in entityRelationList) {
          relationNeed = entityRelationList[keyVariable].relation;
        }
        if (user) {
          const dataControl = await getUserRoleDetail(user, projectId);
          const isAdmin = await checkIsSuperAdmin(user.id);
          if (!dataControl || dataControl.length <= 0) {
            if (!isAdmin) {
              whereCondition = false;
            }
          } else {
            whereCondition = dataControl;
            if (filters?.date) {
              whereCondition.forEach((condition: any) => {
                if (filterColumn) {
                  condition[filterColumn] = Between(filters.date.startDate, filters.date.endDate);
                }
              });
            }
          }
        }

        const data = whereCondition
          ? await this.repository.find({ where: whereCondition, relations: relationNeed })
          : [];

        const coordinateConfigCheck = await this.coordinatesConversion(
          data,
          keyVariable,
          projectId
        );

        if (replaceUUID) {
          const uuidChangedData = await changeGeoJsonUUIDsToValue(
            keyVariable,
            coordinateConfigCheck,
            projectId
          );
          const finalData = this.jsonArrayToGeoJson(uuidChangedData, dceData.geoType);

          return finalData;
        }

        const finalData = this.jsonArrayToGeoJson(coordinateConfigCheck, dceData.geoType);

        return finalData;
      } else {
        throw new Error('DCE entity not found');
      }
    } catch (error) {
      throw error;
    }
  }

  async getDataForMap(projectId: string, keyVariable: string, replaceUUID?: boolean, user?: any) {
    try {
      const dceData = await dceModel.findByEntity(keyVariable);
      if (dceData && dceData.isGeoJson) {
        // Create a workbook and add a worksheet
        let whereCondition: any = { projectId, isDelete: false };

        let relationNeed: string[] = [];
        if (keyVariable in entityRelationList) {
          relationNeed = entityRelationList[keyVariable].relation;
        }
        if (user) {
          const dataControl = await getUserRoleDetail(user, projectId);
          const isAdmin = await checkIsSuperAdmin(user.id);
          if (!dataControl || dataControl.length <= 0) {
            if (!isAdmin) {
              whereCondition = false;
            }
          } else {
            whereCondition = dataControl;
          }
        }

        const data = whereCondition
          ? await this.repository.find({
              where: whereCondition,
              relations: relationNeed,
              take: 30,
              order: { createdAt: 'DESC' } as any,
            })
          : [];

        const coordinateConfigCheck = await this.coordinatesConversion(
          data,
          keyVariable,
          projectId
        );

        if (replaceUUID) {
          const uuidChangedData = await changeGeoJsonUUIDsToValue(
            keyVariable,
            coordinateConfigCheck,
            projectId
          );
          const finalData = this.jsonArrayToGeoJson(uuidChangedData, dceData.geoType);

          return finalData;
        }

        const finalData = this.jsonArrayToGeoJson(coordinateConfigCheck, dceData.geoType);

        return finalData;
      } else {
        throw new Error('DCE entity not found');
      }
    } catch (error) {
      throw error;
    }
  }

  private async coordinatesConversion(data: any[], entity: string, projectId: string) {
    try {
      const dceCordinateConfigurationModel = new DCECoordinatesConfigModel();
      let final: any[] = data;
      const dceData = await dceModel.findByEntity(entity);
      if (dceData) {
        const isLatlong = await dceCordinateConfigurationModel.getIsLatByDceAndProject(
          projectId,
          dceData.id
        );
        if (isLatlong === false) {
          const restult = data.map((value) => {
            if ('latitude' in value && 'longitude' in value) {
              if (value.latitude && value.longitude) {
                const conversionResult: any = convertLatLongToEastingNorthing(
                  value.latitude,
                  value.longitude
                );
                if (conversionResult.northing && conversionResult.easting) {
                  value.Easting = conversionResult.easting;
                  value.Northing = conversionResult.northing;
                }
              }
            }
            return value;
          });
          final = restult;
        }
      }
      return final;
    } catch (error) {
      throw error;
    }
  }

  jsonArrayToGeoJson(jsonArray: any[], geoJsonType?: string) {
    try {
      // Check if the input JSON array is valid
      if (!Array.isArray(jsonArray)) {
        throw new Error(
          'Invalid JSON array. Please provide an array of objects with latitude and longitude properties.'
        );
      }

      const geoJsonFeatures = geoJsonConversionBasedOnType(geoJsonType || 'Point', jsonArray);

      // Create a GeoJSON FeatureCollection
      const geoJsonCollection: GeoJsonFeatureCollection = {
        type: 'FeatureCollection',
        features: geoJsonFeatures,
      };

      if (geoJsonCollection.features.length > 0) {
        return geoJsonCollection;
      } else {
        return {};
      }
    } catch (error) {
      throw error;
    }
  }
}

export interface GeoJsonFeature {
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: Record<string, any>;
}

interface GeoJsonFeatureCollection {
  type: 'FeatureCollection';
  features: GeoJsonFeature[];
}

export default CreateTemplate;
