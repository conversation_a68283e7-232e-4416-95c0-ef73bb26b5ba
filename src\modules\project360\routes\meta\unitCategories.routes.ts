import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { UnitCategories } from '@entities/p_meta/UnitCategories';

const router: Router = express.Router();

const GenericController = new CrudController<UnitCategories>(UnitCategories);

// Mount the userRouter for CRUD operations at /auth/user/crud

router.get('/', GenericController.findAll);
router.get('/:id', authenticateToken, GenericController.findById);
router.post('/', authenticateToken, (req, res) => GenericController.create(req, res));
router.put('/:id', authenticateToken, (req, res) => GenericController.create(req, res));
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res)
);

export default router;
