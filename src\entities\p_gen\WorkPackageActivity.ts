import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  JoinTable,
  OneToMany,
} from 'typeorm';
import { WorkPackage } from './WorkPackage';
import { ProjectActivity } from './Activity';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { ProjectEquipment } from './ProjectEquipment';
import { EventLog } from './EventLog';
import { WorkPackageActivityTechnician } from './WorkPackageActivityTechnician';

@Entity({ schema: 'p_gen' })
export class WorkPackageActivity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  workPackageId?: string;

  @ManyToOne(() => WorkPackage, { nullable: true }) 
  @JoinColumn({ name: 'workPackageId' })
  workPackage?: WorkPackage;

  @Column({ nullable: true })
  activityId?: string;

  @ManyToOne(() => ProjectActivity, { nullable: true }) 
  @JoinColumn({ name: 'activityId' })
  activity?: ProjectActivity;

  @Column({ type: 'timestamp', nullable: true })
  startTime?: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @ManyToMany(
    () => DataCaptureElements,
    (dataCaptureElements) => dataCaptureElements.workPackageActivity
  )
  @JoinTable({ name: 'work_package_activity_dce' }) // Specify the custom table name
  dce?: DataCaptureElements[];

  // @ManyToMany(() => User)
  // @JoinTable({ name: 'work_package_activity_technician' }) // Specify the custom table name
  // technician?: User[];

  @OneToMany(() => WorkPackageActivityTechnician, (technician) => technician.workPackageActivity)
  technician?: WorkPackageActivityTechnician[];

  @ManyToMany(() => ProjectEquipment)
  @JoinTable({ name: 'work_package_activity_equipment' }) // Specify the custom table name
  equipment?: ProjectEquipment[];

  @OneToMany(() => EventLog, (event) => event.workPackageActivity)
  eventLog?: EventLog[];
}
