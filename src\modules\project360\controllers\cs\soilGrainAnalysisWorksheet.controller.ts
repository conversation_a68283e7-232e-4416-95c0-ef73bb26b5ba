import { Request, Response } from 'express';
import soilGrainAnalysisWorksheetModel from '../../models/cs/soilGrainAnalysisWorksheet.model';
import soilGrainAnalysisModel from '../../models/cs/soilGrainAnalysis.model';

class SoilGrainAnalysisWorksheetController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const soilGrainAnalysisWorksheetData = await soilGrainAnalysisWorksheetModel.findById(
        req.params.id
      );
      // checking if data is found with the id
      if (soilGrainAnalysisWorksheetData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: soilGrainAnalysisWorksheetData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async findByGrainAnalysisTestId(req: Request, res: Response) {
    try {
      const soilGrainAnalysisWorksheetData =
        await soilGrainAnalysisWorksheetModel.findBySoilGrainAnalysisId(req.params.id);
      // getting the data from database with the given id

      if (soilGrainAnalysisWorksheetData && soilGrainAnalysisWorksheetData.length > 0) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: soilGrainAnalysisWorksheetData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async create(req: Request, res: Response) {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }
      const grainSizeAnalysisId = req.body.grainSizeAnalysisId;
      if (!grainSizeAnalysisId) {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'bad Request' });
      }
      const soilDetails = await soilGrainAnalysisModel.findById(grainSizeAnalysisId);
      if (!grainSizeAnalysisId && !soilDetails?.weightOfDrySample) {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'bad Request' });
      }
      let weightRetained;
      if (
        req.body.weightRetained1 &&
        req.body.weightRetained2 &&
        req.body.weightRetained3 &&
        req.body.weightRetained4
      ) {
        weightRetained =
          req.body.weightRetained1 +
          req.body.weightRetained2 +
          req.body.weightRetained3 +
          req.body.weightRetained4;
        req.body.weightRetained = weightRetained;
      } else if (req.body.weightRetained1 && req.body.weightRetained2) {
        weightRetained = req.body.weightRetained1 + req.body.weightRetained2;
        req.body.weightRetained = weightRetained;
      } else if (req.body.weightRetained1 && req.body.weightRetained2 && req.body.weightRetained3) {
        weightRetained =
          req.body.weightRetained1 + req.body.weightRetained2 + req.body.weightRetained3;
        req.body.weightRetained = weightRetained;
      } else if (req.body.weightRetained) {
        weightRetained = req.body.weightRetained;
      } else {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'bad Request' });
      }
      if (soilDetails?.weightOfDrySample && req.body.weightRetained) {
        const percentageRetained = req.body.weightRetained / soilDetails?.weightOfDrySample;
        req.body.percentageRetained = percentageRetained;
      }

      // getting the data from database with the given id
      const soilGrainAnalysisWorksheetData = await soilGrainAnalysisWorksheetModel.add(req.body);
      // checking if data is found with the id
      // if true data will send as response
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: soilGrainAnalysisWorksheetData,
        msg: message,
      });
    } catch (error) {
      const message = req.__('DataInputFail');
      // error response
      return res
        .status(500)
        .json({ isSucceed: false, data: [], msg: (error as any).message || message });
    }
  }
}

const soilGrainAnalysisWorksheetController = new SoilGrainAnalysisWorksheetController();
export default soilGrainAnalysisWorksheetController;
