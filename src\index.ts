/* eslint-disable @typescript-eslint/no-var-requires */
import 'reflect-metadata';
import express from 'express';
import cors from 'cors';
import cron from 'node-cron';
import dotenv from 'dotenv';
import morgan from 'morgan';
import { createConnection } from 'typeorm';
import fileUpload from 'express-fileupload';
import i18n from './i18n';
import SFTPGetFile from './shared/utilities/sftpBlobAndFiles/getFileFromSftp';
import { getDBConfig } from './dbConnection';
import errorMiddleware from './shared/middlewares/error/error.middleware';
import { getAllUsers } from './shared/server/platformApi/user';
import checkAndSendMail from './shared/utilities/email/projectMailCheck';
import { setupGraphQL } from './graphql';
import './services/error.service';

const app = express();
dotenv.config();
process.env.TZ = 'UTC';
const PORT = process.env.PORT || 3000;
app.use(express.json());
app.use(cors());
app.use(i18n.init);
app.use(fileUpload());
app.use(
  morgan(':method :url :status :res[content-length] - :response-time ms', {
    immediate: true, // Logs before the response is sent
  })
);
app.use(morgan('combined'));

const version = 'api';
const config: any = getDBConfig();

if (process.env.DB_HOST == 'sih-pg-db.postgres.database.azure.com') {
  config.extra = {
    ssl: {
      ssl: true,
    },
  };
}

createConnection(config)
  .then(async () => {
    console.log(`Connected to database ${process.env.DB_DATABASE}`);

    // Setup GraphQL server
    await setupGraphQL(app);

    app.listen(PORT, () => {
      getAllUsers().then(() => {
        console.log(`Server is running on http://localhost:${PORT}`);
      });
    });
  })
  .catch((error) => {
    console.log('Error connecting to the database:', error);
  });

app.get(`/${version}/project360`, (req, res) => {
  const greeting = req.__('welcomeMessage');
  const response = `${greeting} date and time ${new Date()}`;
  res.send(response);
});

app.get(`/${version}/project360/get/file/:projectId`, (req, res) => {
  try {
    const remoteFilePath = req.query.path;
    const sftpGetFile = new SFTPGetFile();
    return sftpGetFile.getBlobFile(req, res, remoteFilePath as string);
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
});
// const file = require('./modules/project360/routes/file/getFile.routes.ts');
// app.get(`/${version}/project360/get/file/:projectId`, file);

const qms = require('./modules/project360/routes/qms.routes');
app.use(`/${version}/project360/qms`, qms);

// Import platformRoutes using named export
const project360 = require('./modules/project360/routes/index.routes.ts');

app.use(`/${version}/project360`, project360);

// Check for scheduled mail every 30 min
cron.schedule('*/30 * * * *', checkAndSendMail);
