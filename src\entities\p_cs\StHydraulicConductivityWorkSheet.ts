import { <PERSON>ti<PERSON>, Column, ManyToOne, JoinColumn } from 'typeorm';
import { StHydraulicConductivityTest } from './StHydraulicConductivityTest';
import { HydraulicConductivityCellInfo } from '../p_domain/HydraulicConductivityCellInfo';
import { Project } from '../p_gen/Project';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { EventLog } from '@entities/p_gen/EventLog';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';

@Entity({ schema: 'p_cs' })
export class StHydraulicConductivityWorkSheet extends SubDCEColumns {
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  HCTestId?: string;

  @ManyToOne(() => StHydraulicConductivityTest, { nullable: true })
  @JoinColumn({ name: 'HCTestId' })
  hydraulicConductivityTest?: StHydraulicConductivityTest;

  @Column({ nullable: true })
  cellInfoId?: string;

  @ManyToOne(() => HydraulicConductivityCellInfo, { nullable: true })
  @JoinColumn({ name: 'cellInfoId' })
  hydraulicConductivityCellInfo?: HydraulicConductivityCellInfo;

  @Column({ type: 'timestamp' })
  startDate?: Date;

  @Column({ type: 'timestamp' })
  endDate?: Date;

  @Column({ type: 'float' })
  startReading1?: number;

  @Column({ type: 'float' })
  endReading1?: number;

  @Column({ type: 'float' })
  startReading2?: number;

  @Column({ type: 'float' })
  endReading2?: number;

  @Column({ type: 'float' })
  startConfiningPressure?: number;

  @Column({ type: 'float' })
  endConfiningPressure?: number;

  @Column({ type: 'float' })
  startHeadwaterPressure?: number;

  @Column({ type: 'float' })
  endHeadwaterPressure?: number;

  @Column({ type: 'float' })
  startTailwaterPressure?: number;

  @Column({ type: 'float' })
  endTailwaterPressure?: number;

  @Column({ type: 'float' })
  startWaterTemperature?: number;

  @Column({ type: 'float' })
  endWaterTemperature?: number;

  @Column({ default: false })
  usedInCalc?: boolean;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
