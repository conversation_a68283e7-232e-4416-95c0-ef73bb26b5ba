import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';
import { ProjectReportCategory } from './ReportCategory';
import { ReportList } from '../p_meta/ReportList';
import { Stage } from '../p_meta/Stage';
import { Purpose } from '../p_meta/Purpose';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';

@Entity({ schema: 'p_gen', name: 'report_list' })
export class ProjectReportList {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @Column({ nullable: true })
  categoryId?: string;

  @ManyToOne(() => ProjectReportCategory, { nullable: true })
  @JoinColumn({ name: 'categoryId' })
  reportCategory?: ProjectReportCategory;

  @Column({ nullable: true })
  reportId?: string;

  @ManyToOne(() => ReportList, { nullable: true })
  @JoinColumn({ name: 'reportId' })
  reportList?: ReportList;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  fileName?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  filePath?: string;

  @Column({ nullable: true })
  stageId?: string;

  @ManyToOne(() => Stage, { nullable: true })
  @JoinColumn({ name: 'stageId' })
  stage?: Stage;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true }) 
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;
}
