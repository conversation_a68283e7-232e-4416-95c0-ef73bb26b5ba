import { NextFunction, Request, Response } from 'express';
import submittalController from '../../modules/project360/controllers/submittal.controller';
import errorMiddleware from './error/error.middleware';

export const createSubmittalBeforeFormMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (req.query.screen == 'submittal') {
      if (req.query.submittalId) {
        const submittalVersionId = await submittalController.addSubmittalVersionBeforeForm(
          req,
          req.query.submittalId as string
        );
        req.body.submittalVersionId = submittalVersionId;
        return next();
      } else {
        return res
          .status(400)
          .json({ isSucceed: false, data: [], msg: 'Invalid request submittalId Invalid' });
      }
    } else {
      next();
    }
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const createSubmittalBeforeFormImportMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    if (req.query.screen == 'submittal') {
      if (req.query.submittalId) {
        const submittal = await submittalController.addSubmittalVersionBeforeFormForImport(
          req,
          req.query.submittalId as string
        );
        req.body.submittal = submittal;
        return next();
      } else {
        return res
          .status(400)
          .json({ isSucceed: false, data: [], msg: 'Invalid request submittalId Invalid' });
      }
    } else {
      next();
    }
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
