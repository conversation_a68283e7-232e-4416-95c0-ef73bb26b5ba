import { gql } from 'apollo-server-express';

export const typeDefs = gql`
  # Custom scalar for Date handling
  scalar Date

  type ConcreteMixDesign {
    id: ID!
    mixId: String
  }

  type ConcreteBatchTicket {
    id: ID!
    projectId: String
    project: Project
    mixId: String
    concreteMixDesign: ConcreteMixDesign
    materialId: String
    material: ProjectMaterial
    materialTypeId: String
    materialType: MaterialType
    plantId: String
    mixerId: String
    batchNumber: Int
    ticketNumber: Int
    truckId: Int
    batchedDateTime: Date
    batchUsed: String
    batchPlacementLocation: String
    batchVolume: Float
    batchWeight: Float
    wcRequired: Float
    waterRequired: Float
    waterBatched: Float
    waterPercentVariance: Float
    purposeId: String
    purpose: Purpose
    createdAt: Date
    updatedAt: Date
  }

  type Purpose {
    id: ID!
    name: String
  }

  extend type Query {
    batchTicketById(id: ID!): ConcreteBatchTicket
    batchTicketsByProject(projectId: ID!): [ConcreteBatchTicket]
    batchTicketsByDateRange(
      projectId: ID!
      startDate: String!
      endDate: String!
    ): [ConcreteBatchTicket]
    batchTicketsByProject(
      projectId: ID!
      startDate: String
      endDate: String
      dateField: String
    ): [ConcreteBatchTicket]
  }
`;
