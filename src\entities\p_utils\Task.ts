import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  AfterLoad,
} from 'typeorm';
import { TaskComment } from './TaskComment';
import { TaskAttachment } from './TaskAttachment';
import { Bucket } from './Bucket';
import { TaskType } from './TaskType';
import { TaskPurpose } from './TaskPurpose';
import { TaskStatus } from './TaskStatus';
import { TaskPriority } from './TaskPriority';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';
import { Project } from '../p_gen/Project';

@Entity({ schema: 'p_utils' })
export class Task {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  subject?: string;

  @Column({ nullable: true })
  content?: string;

  @Column({ nullable: true })
  statusId?: string;

  @ManyToOne(() => TaskStatus, { nullable: true })
  @JoinColumn({ name: 'statusId' })
  status?: TaskStatus;

  @Column({ nullable: true })
  priorityId?: string;

  @ManyToOne(() => TaskPriority, { nullable: true })
  @JoinColumn({ name: 'priorityId' })
  priority?: TaskPriority;

  @Column({ nullable: true })
  assignedUserId?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  agentId?: string;

  @Column({ nullable: true })
  categoryId?: string;

  @Column({ nullable: true })
  typeId?: string;

  @ManyToOne(() => TaskType, { nullable: true })
  @JoinColumn({ name: 'typeId' })
  type?: TaskType;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => TaskPurpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: TaskPurpose;

  @OneToMany(() => TaskComment, (comment) => comment.task, {
    nullable: true,
  })
  comments?: TaskComment[];

  @OneToMany(() => TaskAttachment, (attachment) => attachment.task)
  attachments?: TaskAttachment[];

  @Column({ nullable: true })
  bucketId?: string;

  @ManyToOne(() => Bucket, (bucket) => bucket.task)
  @JoinColumn({ name: 'bucketId' })
  bucket?: Bucket;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  assignedUser?: IUser | null;

  createdUser?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    this.assignedUser = null;
    this.createdUser = null;
    try {
      if (this.assignedUserId) {
        const data = await getUserById(this.assignedUserId);
        this.assignedUser = data;
      }
      if (this.createdUserId) {
        const data = await getUserById(this.createdUserId);
        this.createdUser = data;
      }
    } catch (error) {
      this.assignedUser = null;
      this.createdUser = null;
    }
  }
}
