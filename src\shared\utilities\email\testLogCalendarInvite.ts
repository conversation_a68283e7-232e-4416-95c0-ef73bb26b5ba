import { sendCalendarInvite } from './calendarInvite';
import projectModel from '../../../modules/project360/models/project.model';
import { getManager } from 'typeorm';
import { WorkPackage } from '../../../entities/p_gen/WorkPackage';
import workPackageModel from '@models/workPackage/workPackage.model';
import moment from 'moment-timezone';
import ctLogConfigModel from '@models/ctLogConfig.model';
import GeneralProjectAreaModel from '../../../modules/project360/models/gen/area.model';
import projectMaterialModel from '../../../modules/project360/models/projectMaterial.model';
import { getUserById } from '../../../shared/server/platformApi/user';
import { ProjectActivity } from '../../../entities/p_gen/Activity';
import { CTLog } from '../../../entities/p_gen/CTLog';

/**
 * Extends the CTLog type with additional fields needed for calendar invites
 */
interface TestLogCalendarData extends Partial<CTLog> {
  isMultipleTests?: boolean;
  firstTestNo?: string;
  lastTestNo?: string;
  updatedUserId?: string;
  sequence?: number;
}

/**
 * Sends a calendar invite for a test log and creates a work package entry
 * @param data The test log data
 * @param emailListId The email list ID to send the invite to
 * @param isEdit Whether this is an edit operation
 * @returns A promise that resolves when the invite is sent and work package is created
 */
export async function sendTestLogCalendarInvite(
  data: TestLogCalendarData,
  emailListId: string,
  isEdit: boolean = false
): Promise<{ success: boolean; message: string; workPackageId?: string }> {
  try {
    // Variable to store the workPackageId to return
    let workPackageId: string | undefined;

    // Handle multiple test numbers if available
    let testNoText = '';

    if (data.isMultipleTests && data.firstTestNo && data.lastTestNo) {
      // For multiple tests, show the range of test numbers
      testNoText = ` - ${data.firstTestNo} to ${data.lastTestNo}`;
    } else {
      // For single test, show the test number as before
      const testNo = data.testNo || '';
      testNoText = testNo ? `- ${testNo}` : '';
    }

    // Get comments for description
    const comments = data.comments || '';

    // Get project details if projectId is available
    let projectName = '';
    let projectTimezone = '';
    if (data.projectId) {
      try {
        const projectData = await projectModel.getProjectId(data.projectId);
        if (projectData) {
          if (projectData.alias) {
            projectName = projectData.alias;
          } else {
            projectName = projectData.name || '';
          }
          if (projectData.timezone) {
            projectTimezone = projectData.timezone;
          }
        }
      } catch (error) {
        console.error('Error fetching project details:', error);
      }
    }

    // Add project name to subject if available
    const projectText = projectName ? `${projectName} - ` : '';

    // Get CTLogConfig name if ctLogConfigId is available
    let ctLogConfigName = 'Test Log'; // Default value
    if (data.ctLogConfigId) {
      try {
        const ctLogConfigData = await ctLogConfigModel.findById(data.ctLogConfigId);
        if (ctLogConfigData && ctLogConfigData.name) {
          ctLogConfigName = ctLogConfigData.name;
        }
      } catch (error) {
        console.error('Error fetching CTLogConfig details:', error);
      }
    }

    // Get General Project Area name if generalProjectAreaId is available
    let generalProjectAreaName = '';
    if (data.generalProjectAreaId) {
      try {
        const generalProjectAreaModel = new GeneralProjectAreaModel();
        const areaName = await generalProjectAreaModel.getDescriptionById(
          data.generalProjectAreaId
        );
        if (areaName) {
          generalProjectAreaName = areaName;
        }
      } catch (error) {
        console.error('Error fetching General Project Area details:', error);
      }
    }

    // Get Material Type and Material names if materialId is available
    let materialTypeName = '';
    let materialName = '';
    if (data.materialId) {
      try {
        const materialData = await projectMaterialModel.findById(data.materialId);
        if (materialData) {
          if (materialData.materialType && materialData.materialType.name) {
            materialTypeName = materialData.materialType.name;
          }
          if (materialData.name) {
            materialName = materialData.name;
          }
        }
      } catch (error) {
        console.error('Error fetching Material details:', error);
      }
    }

    let startDate = data.scheduleFrom
      ? new Date(data.scheduleFrom)
      : new Date(Date.now() + 24 * 60 * 60 * 1000);

    let endDate = data.scheduleTo ? new Date(data.scheduleTo) : new Date(startDate);

    // If only scheduleFrom is provided, set endDate to end of the same day
    if (data.scheduleFrom && !data.scheduleTo) {
      endDate.setHours(23, 59, 59, 999);
    }

    // We'll use the original dates for the calendar invite
    // and convert to UTC for database storage
    const displayStartDate = new Date(startDate);
    const displayEndDate = new Date(endDate);

    if (projectTimezone) {
      // Create moments in the project timezone
      const startMoment = moment.tz(startDate, projectTimezone);
      const endMoment = moment.tz(endDate, projectTimezone);

      // Convert to UTC for database storage
      const startDateUTC = startMoment.utc().toDate();
      const endDateUTC = endMoment.utc().toDate();

      startDate = startDateUTC;
      endDate = endDateUTC;
    } else {
      // If no timezone is specified, convert to UTC directly for database storage
      startDate = moment(startDate).utc().toDate();
      endDate = moment(endDate).utc().toDate();
    }

    // Build description with additional fields
    let descriptionText = comments || '';

    // Create an array of description parts to join later
    const descriptionParts = [];

    // Add test number information to description
    if (data.isMultipleTests && data.firstTestNo && data.lastTestNo) {
      descriptionParts.push(`Test Numbers: ${data.firstTestNo} to ${data.lastTestNo}`);
    } else if (data.testNo) {
      descriptionParts.push(`Test Number: ${data.testNo}`);
    }

    // Add General Project Area, Material Type, and Material to description if available
    if (generalProjectAreaName) {
      descriptionParts.push(`General Project Area: ${generalProjectAreaName}`);
    }

    if (materialTypeName) {
      descriptionParts.push(`Material Type: ${materialTypeName}`);
    }

    if (materialName) {
      descriptionParts.push(`Material: ${materialName}`);
    }

    // Add the original comments if they exist
    if (descriptionText) {
      descriptionParts.push(descriptionText);
    }

    // Join all parts with double newlines for proper spacing
    descriptionText = descriptionParts.join('\n\n');

    // Get work package if this is an edit and workPackageId exists
    let workPackage: WorkPackage | null = null;
    let sequence = 0;
    let workPackageNo: string | undefined;

    if (isEdit && data.workPackageId) {
      try {
        workPackage = await workPackageModel.getById(data.workPackageId);

        if (workPackage) {
          // Increment sequence for calendar update
          sequence = (workPackage.sequence || 0) + 1;
          workPackageNo = workPackage.workPackageNo;

          // Update the sequence in the database
          const workPackageRepository = getManager().getRepository(WorkPackage);

          await workPackageRepository.update(
            { id: workPackage.id },
            {
              sequence,
              startDate,
              endDate,
              updatedBy: data.updatedBy || 'System',
            }
          );
        }
      } catch (error) {
        console.error('Error fetching or updating work package:', error);
      }
    }

    // If this is an edit and we have a workPackage with activities, update the activities too
    if (isEdit && workPackage && data.workPackageId) {
      try {
        const workPackageActivityRepository = getManager().getRepository('WorkPackageActivity');

        // Update all activities associated with this work package
        await workPackageActivityRepository.update(
          { workPackageId: data.workPackageId },
          {
            startTime: startDate,
            endTime: endDate,
            updatedBy: data.updatedBy || 'System',
          }
        );
      } catch (error) {
        console.error('Error updating work package activities:', error);
      }
    }

    let assignedToEmail: string | undefined;

    if (data.sampledBy) {
      try {
        const userData = await getUserById(data.sampledBy);

        if (userData && userData.email) {
          assignedToEmail = userData.email;
        } else {
          console.log('User has no email or user not found');
        }
      } catch (error) {
        console.error('Error fetching user details for sampledBy:', error);
      }
    } else {
      console.log('No sampledBy field provided in the data');
    }

    // Create a work package entry only if this is not an edit operation
    if (data.projectId && !isEdit) {
      try {
        const workPackageRepository = getManager().getRepository(WorkPackage);
        const activityRepository = getManager().getRepository(ProjectActivity);

        // Get the next work package number
        let workPackageNo;
        try {
          workPackageNo = await workPackageModel.getNextWorkPackageNo();
        } catch (error) {
          console.error('Error generating workPackageNo:', error);
        }

        // Create work package name based on whether it's multiple tests or a single test
        let workPackageName = '';
        if (data.isMultipleTests && data.firstTestNo && data.lastTestNo) {
          workPackageName = `${ctLogConfigName}: ${data.firstTestNo} to ${data.lastTestNo}`;
        } else {
          workPackageName = `${ctLogConfigName}: ${data.testNo || ''}`;
        }

        // Get dceId from CTLogConfig
        let dceId: string | null = null;
        if (data.ctLogConfigId) {
          try {
            const ctLogConfigData = await ctLogConfigModel.findById(data.ctLogConfigId);
            if (ctLogConfigData && ctLogConfigData.dceId) {
              dceId = ctLogConfigData.dceId;
            }
          } catch (error) {
            console.error('Error fetching dceId from CTLogConfig:', error);
          }
        }

        // Find activity based on projectId and dceId
        let activityId = null;
        if (dceId && data.projectId) {
          try {
            // Query activities for this project that have this DCE
            const activities = await activityRepository.find({
              where: { projectId: data.projectId, isDelete: false },
              relations: ['dataCaptureElements'],
            });

            // Find the first activity that has this DCE
            const activity = activities.find(
              (activity) => activity.dataCaptureElements?.some((dce) => dce.id === dceId)
            );

            if (activity) {
              activityId = activity.id;
            }
          } catch (error) {
            console.error('Error finding activity for dceId:', error);
          }
        }

        const workPackageData = {
          workPackageNo: workPackageNo,
          title: `${projectText}${ctLogConfigName}${testNoText}`,
          name: workPackageName,
          description: descriptionText,
          startDate: startDate,
          endDate: endDate,
          generalProjectAreaId: data.generalProjectAreaId,
          featureId: data.featureId,
          structureId: data.structureId,
          emailListId: emailListId,
          latitude: data.latitude,
          longitude: data.longitude,
          easting: data.easting,
          northing: data.northing,
          elevation: data.elevation,
          projectId: data.projectId,
          createdBy: data.createdBy || 'System',
          updatedBy: data.updatedBy || 'System',
          createdUserId: data.createdUserId || undefined,
        };

        if (activityId && dceId) {
          // Create work package with activity
          const workPackageActivityData = [
            {
              technicianIds: data.sampledBy ? [data.sampledBy] : [],
              equipmentIds: [],
              dceIds: [dceId],
              workPackageActivity: {
                activityId: activityId,
                startTime: startDate,
                endTime: endDate,
                projectId: data.projectId,
                createdBy: data.createdBy || 'System',
                updatedBy: data.updatedBy || 'System',
              },
            },
          ];

          // Use the add function to create work package with activity
          const addedWorkPackage = await workPackageModel.add(
            workPackageData,
            workPackageActivityData
          );
          if (addedWorkPackage && addedWorkPackage.id) {
            workPackageId = addedWorkPackage.id;
          }
        } else {
          const workPackage = workPackageRepository.create(workPackageData);
          const savedWorkPackage = await workPackageRepository.save(workPackage);
          workPackageId = savedWorkPackage.id;
        }
      } catch (error) {
        console.error('Error creating work package:', error);
      }
    }

    // If this is an edit operation, return the existing workPackageId
    if (isEdit && data.workPackageId) {
      workPackageId = data.workPackageId;
    }

    const eventOptions = {
      summary: `${projectText}${ctLogConfigName}${testNoText}`,
      description: descriptionText,
      comments: descriptionText,
      location: data.location || undefined,
      timezone: projectTimezone || undefined,
      allDay: false,
      start: displayStartDate || startDate,
      end: displayEndDate || endDate,
      organizer: {
        name: 'Smart Structures',
        email: process.env.SMTP_EMAIL || '<EMAIL>',
      },
      // Add workPackageNo as UID and sequence for calendar updates
      workPackageId: workPackageId || workPackageNo,
      sequence: sequence,
      isEdit: isEdit,
    };

    // Send the calendar invite with the sampler's email as an additional recipient
    await sendCalendarInvite(emailListId, eventOptions, assignedToEmail);

    return {
      success: true,
      message: isEdit
        ? 'Calendar invitation updated successfully'
        : 'Calendar invitation sent successfully and work package created with associated activity',
      workPackageId: workPackageId,
    };
  } catch (error) {
    console.error('Error in sendTestLogCalendarInvite:', error);
    throw error;
  }
}
