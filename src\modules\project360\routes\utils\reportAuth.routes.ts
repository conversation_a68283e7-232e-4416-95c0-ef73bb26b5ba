import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import ReportAuthController from '../../controllers/utils/reportAuth.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const controller = new ReportAuthController();

router.get('/get/new/:projectId', authenticateToken, controller.addToken);

export default router;
