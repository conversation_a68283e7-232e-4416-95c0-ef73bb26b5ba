import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { TestConfiguration } from './TestConfig';

@Entity({ schema: 'p_gen' })
export class TestConfigurationRule {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: false })
  testConfigId?: string;

  @ManyToOne(() => TestConfiguration, { nullable: true }) 
  @JoinColumn({ name: 'testConfigId' })
  testConfig?: TestConfiguration;

  @Column({ nullable: true })
  logic?: string; // values like AND, OR etc

  @Column({ nullable: true })
  column1?: string; // first conlumn in condition

  @Column({ nullable: true })
  condition?: string; // values like >, <, == etc

  @Column({ nullable: true })
  valueOrColumn?: string; // if the second value in the condition is a column or a constant value

  @Column({ nullable: true })
  column2?: string; // secend conlumn in condition

  @Column({ nullable: true })
  value?: string; // if valueOrColumn == 'value' use this column as the 2 value in condition

  @Column({ nullable: true })
  order!: number; // order of the condition

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
