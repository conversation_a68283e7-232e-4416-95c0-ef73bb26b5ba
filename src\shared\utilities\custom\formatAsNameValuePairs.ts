export const formatAsNameValuePairs = (
  items: any[],
  level = 0
): { name: string; value: string }[] => {
  let result: { name: string; value: string }[] = [];
  const indent = `${'|--'.repeat(level)} `;

  items.forEach((item) => {
    result.push({ name: `${indent}${item.name}`, value: item.id });

    if (item.children && item.children.length > 0) {
      result = result.concat(formatAsNameValuePairs(item.children, level + 1));
    }
  });

  return result;
};
