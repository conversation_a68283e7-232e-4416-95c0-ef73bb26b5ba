import { IsNull, getManager } from 'typeorm';
import { ProjectOrgStructure } from '../../../entities/p_auth/OrgStructure';
import { buildHierarchyOrgStructure } from '../../../shared/utilities/custom/createHierarchy';

class ProjectOrgStructureModel {
  constructor() {}
  async addOrgStructure(newOrgStructure: ProjectOrgStructure) {
    try {
      const projectOrgStructureRepository = getManager().getRepository(ProjectOrgStructure);
      const addedStakeholder = projectOrgStructureRepository.create(newOrgStructure);
      await projectOrgStructureRepository.save(addedStakeholder);
      return addedStakeholder;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectId(projectId: string) {
    try {
      // Replace this with your recursive query to fetch the hierarchical data
      // const orgStructures = await getManager()
      //   .getRepository(ProjectOrgStructure)
      //   .createQueryBuilder("orgStructure")
      //   .where("orgStructure.projectId = :projectId", { projectId })
      //   .leftJoinAndSelect("orgStructure.Stakeholder", "stakeholder")
      //   .orderBy("orgStructure.parentId", "ASC", "NULLS FIRST")
      //   .getMany();

      const orgStructures = await getManager()
        .getRepository(ProjectOrgStructure)
        .createQueryBuilder('orgStructure')
        .where('orgStructure.projectId = :projectId', { projectId })
        .andWhere('orgStructure.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('orgStructure.stakeholder', 'stakeholder')
        .leftJoinAndSelect('stakeholder.stakeUsers', 'stakeUser')
        .leftJoin('stakeUser.role', 'role')
        .addSelect(['role.name', 'role.description'])
        .getMany();

      // Custom sorting function based on roleType
      const customOrder = ['Owner', 'Contractor', 'QA', 'Subcontractor'];
      orgStructures.sort((a, b) => {
        const roleA = a.roleType || '';
        const roleB = b.roleType || '';
        return customOrder.indexOf(roleA) - customOrder.indexOf(roleB);
      });

      if (orgStructures && orgStructures.length > 0) {
        const hierarchyData = buildHierarchyOrgStructure(orgStructures);
        return hierarchyData;
      }

      return null;
    } catch (error) {
      throw error;
    }
  }

  async findByRole(role: string, projectId: string) {
    try {
      return await getManager()
        .getRepository(ProjectOrgStructure)
        .find({
          where: { roleType: role, projectId: projectId, isDelete: false },
        });
    } catch (error) {
      throw error;
    }
  }

  async findByRoleWithoutParent(role: string, projectId: string) {
    try {
      return await getManager()
        .getRepository(ProjectOrgStructure)
        .find({
          where: { roleType: role, projectId: projectId, isDelete: false, parentId: IsNull() },
        });
    } catch (error) {
      throw error;
    }
  }

  async findContractorRole(projectId: string) {
    try {
      return await getManager()
        .getRepository(ProjectOrgStructure)
        .find({
          where: { roleType: 'Contractor', projectId: projectId, isDelete: false },
          relations: ['stakeholder', 'stakeholder'],
        });
    } catch (error) {
      throw error;
    }
  }

  async EditParentId(id: string, newParentId: string) {
    try {
      return await getManager()
        .getRepository(ProjectOrgStructure)
        .createQueryBuilder()
        .update(ProjectOrgStructure)
        .set({ parentId: newParentId })
        .where('id = :id', { id })
        .execute();
    } catch (error) {
      throw error;
    }
  }
}

const projectOrgStructureModel = new ProjectOrgStructureModel();
export default projectOrgStructureModel;
