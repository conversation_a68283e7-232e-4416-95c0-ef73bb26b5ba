import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

import { DataCaptureElements } from './DataCaptureElements';

@Entity({ schema: 'p_meta', name: 'form' })
export class DefaultForm {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'varchar', nullable: true })
  mode?: string | null;

  @Column({ type: 'varchar', nullable: true })
  defaultMode?: string | null;

  @Column({ type: 'json', nullable: true })
  form?: any;

  @Column({ nullable: true, default: false })
  default?: boolean;

  @Column({ nullable: true, default: false })
  isCustom?: boolean;

  @Column({ nullable: true })
  formKey?: string;

  @Column({ nullable: true })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
