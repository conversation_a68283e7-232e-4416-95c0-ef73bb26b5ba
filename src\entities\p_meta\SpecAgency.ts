import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StandardAgency } from './StandardAgency'; // Import the StandardAgency entity

@Entity({ schema: 'p_meta' })
export class SpecAgency {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  agencyName?: string;

  // @Column()
  // referenceStandardAgencyId?: string;

  // @ManyToOne(() => StandardAgency, { nullable: true }) 
  // @JoinColumn({ name: "referenceStandardAgencyID" })
  // referenceStandardAgency?: StandardAgency;

  @Column()
  country?: string;

  @Column()
  website?: string;

  @Column()
  contactDetails?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
