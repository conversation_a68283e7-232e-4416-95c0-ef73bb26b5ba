import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  OneToMany,
  ManyToOne,
  JoinColumn,
  AfterLoad,
} from 'typeorm';
import { ProjectActivity } from '../p_gen/Activity';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { dceConfiguration } from '../p_gen/dceConfiguration';
import { Route } from '../p_auth/Route';
import { ISensorDCE } from '../../shared/server/sensorApi/interface/ISensorDCE';
import { getSensorDCEById } from '../../shared/server/sensorApi/dce';

@Entity({ schema: 'p_meta' })
export class DataCaptureElements {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  entity?: string;

  @ManyToMany(() => ProjectActivity, (activity) => activity.dataCaptureElements)
  activities?: ProjectActivity[];

  @Column({ default: false })
  isGeoJson?: boolean;

  @Column({ nullable: true })
  geoJsonType?: string;

  @Column({ nullable: true })
  component?: string;

  @Column({ nullable: true, default: false })
  isMicroservice?: boolean;

  @Column({ nullable: true })
  microservice?: string;

  @Column({ nullable: true })
  microserviceDceId?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @ManyToMany(() => WorkPackageActivity, (workPackageActivity) => workPackageActivity.dce)
  workPackageActivity?: WorkPackageActivity[];

  @OneToMany(() => dceConfiguration, (config) => config.dce)
  dceConfig?: dceConfiguration[];

  @Column({ default: 'Point' })
  geoType?: string;

  @Column({ nullable: true })
  routeId?: string;

  @Column({ nullable: true })
  isMobile?: boolean;

  @ManyToOne(() => Route, { nullable: true })
  @JoinColumn({ name: 'routeId' })
  route?: Route;

  microserviceDce?: ISensorDCE | null;

  @AfterLoad()
  async afterLoad() {
    this.microserviceDce = null;
    try {
      if (this.microserviceDceId) {
        const data = await getSensorDCEById(this.microserviceDceId);
        this.microserviceDce = data;
      }
    } catch (error) {
      this.microserviceDce = null;
    }
  }
}
