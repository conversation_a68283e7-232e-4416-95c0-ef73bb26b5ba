import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  AfterLoad,
} from 'typeorm';
import { File } from './File';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

@Entity({ schema: 'p_gen' })
export class FilePermission {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  fileId?: string;

  @Column({ nullable: true })
  userId?: string;

  @ManyToOne(() => File, (file) => file.filePermission, { nullable: true })
  @JoinColumn({ name: 'fileId' })
  file?: File;

  @Column({ type: 'varchar' })
  permission?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  user?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.userId) {
        const data = await getUserById(this.userId);
        this.user = data;
      } else {
        this.user = null;
      }
    } catch (error) {
      this.user = null;
    }
  }
}
