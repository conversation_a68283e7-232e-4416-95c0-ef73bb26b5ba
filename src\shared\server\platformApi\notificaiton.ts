import axios from 'axios';
import { setHeaderPlatformServerKeyHeader } from '../setHeader';

export const sendNotification = async (
  userIds: string[],
  message: string,
  triggerName: string,
  projectId?: string
): Promise<boolean> => {
  try {
    await axios.put(
      `${process.env.PLATFORM_BASE_URL}/utils/notification/send/notification`,
      {
        userIds,
        message,
        triggerName,
        projectId,
      },
      setHeaderPlatformServerKeyHeader()
    );

    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
};
