import { Request, Response } from 'express';
import submittalModel from '../models/submittal.model';
import Submittal from '../../../entities/p_gen/Submittal';
import SubmittalDocument from '../../../entities/p_gen/SubmittalDocument';
import {
  ensureFolderExists,
  transferFileBetweenSFTPs,
} from '../../../shared/utilities/Approval/transferFileBetweenSFTPs';
import sftpConfidModel from '../models/sftpConfig.model';
import path from 'path';
import stringTemplate from 'string-template';
import SubmittalVersion from '../../../entities/p_gen/SubmittalVersion';
import { submittalPushInterface } from '../../../shared/middlewares/uploadFileSftp.middleware';
import SubmittalRevision from '../../../entities/p_gen/SubmittalRevision';
import subSpecificationModel from '../models/subSpecification.model';
import { DataFromEntityInterface } from '../../../shared/utilities/entity/customDecorator';

import { EntityListInterface, entityList } from '../../../shared/utilities/entity/entityList';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import dceModel from '../models/meta/dce.model';
import SubmittalMethod from '../../../shared/utilities/submittal/submittalMethod';

class SubmittalController {
  constructor() {}

  async findByRevisionId(req: Request, res: Response) {
    try {
      const data = await submittalModel.findByIdRevision(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByVersionId(req: Request, res: Response) {
    try {
      const data = await submittalModel.findByIdVersion(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findById(req: Request, res: Response) {
    try {
      const data = await submittalModel.findById(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByProjectId(req: Request, res: Response) {
    try {
      const data = await submittalModel.findByProjectId(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findSubmittalVersion(req: Request, res: Response) {
    try {
      const data = await submittalModel.findSubmittalVersionWithDocumnets(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getSubmittalVersionNumber(req: Request, res: Response) {
    try {
      const submittalUtilites = new SubmittalMethod();
      const submital = await submittalModel.findById(req.params.submittalId);
      const { version } = await submittalUtilites.getVersion(req.params.submittalId, submital);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: { version: version },
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getSubmittalRevisionNumber(req: Request, res: Response) {
    try {
      let revisionVersion: string = '0';

      const submitalVersion = await submittalModel.findAllRevisionByVersion(
        req.params.submittalVersionId
      );

      if (submitalVersion && submitalVersion) {
        const allRevisionVersion = submitalVersion?.map((value) => value.version);
        if (allRevisionVersion && allRevisionVersion?.length > 0) {
          const lastVersion = SubmittalController.getLastVersion(allRevisionVersion);
          revisionVersion = SubmittalController.incrementVersion(lastVersion);
        }
      }
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: { revision: revisionVersion },
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  private static incrementVersion(originalNumber: string): string {
    const parts = originalNumber.split('.');

    if (parts.length === 1) {
      parts.push('1');
    } else {
      parts[parts.length - 1] = (parseInt(parts[parts.length - 1]) + 1).toString();
    }

    return parts.join('.');
  }

  private static getLastVersion(versions: string[]) {
    return versions.reduce((maxVersion, currentVersion) => {
      return maxVersion > currentVersion ? maxVersion : currentVersion;
    });
  }

  async addSubmittalVersionAndRevisionWithDocument(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      const submittalUtilites = new SubmittalMethod();
      const submital = await submittalModel.findById(req.body.submittalId);
      const { revisionVersion, version } = await submittalUtilites.getVersion(
        req.body.submittalId,
        submital
      );
      const newSubmitalVersion: any = {
        updatedBy,
        createdBy,
        submittalId: req.body.submittalId,
        internalCodeId: req.body.internalCodeId,
        submissionDate: req.body.submissionDate,
        transmittalNo: req.body.transmittalNo,
        currentStep: req.body.currentStep || '1',
        keyword: req.body.keyword,
        status: 'Pending',
        version: version,
      };
      const newSubmitalRevision: SubmittalRevision = {
        updatedBy,
        createdBy,
        description: req.body.description,
        ownerCodeId: req.body.ownerCodeId,
        internalCodeId: req.body.internalCodeId,
        submissionDate: req.body.submissionDate,
        status: 'Pending',
        version: revisionVersion,
      };

      const submittalChange: Partial<Submittal> = {
        id: req.body.submittalId,
        lastSubmissionDate: req.body.submissionDate,
        lastOwnerCodeId: null as any,
        currentVersion: version.toString(),
        status: 'Pending',
      };

      const files: submittalPushInterface[] = req.body.filePath || [];
      const document = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy,
          createdBy,
          documentURL: value.path,
          documentType: 'Submittal',
        };
        return newDocument;
      });
      // Moving to sftp
      if (document && document.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          newSubmitalRevision?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submital?.projectId || ''
        );
        const addPath: string[] = [];
        await Promise.all(
          document.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submital) {
              const destination = sftpConfig.path;

              const data: any = {
                subCode: submital.specification?.subSpecCode?.replace(/-/g, ''),
                type: submital.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submital?.description?.replace(/[ .]/g, ''),
                item: submital.itemNo,
                specNumber: submital.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  newSubmitalVersion?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  newSubmitalVersion?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: newSubmitalVersion?.keyword,
                revisionNumber: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };

              // const versionForData =
              //   newSubmitalVersion.keyword &&
              //   newSubmitalVersion.keyword != 'undefined' &&
              //   newSubmitalVersion.keyword != 'null'
              //     ? `${newSubmitalVersion?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         newSubmitalVersion.version
              //       )}`
              //     : `v${String(newSubmitalVersion.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submital?.description?.replace(/[ .]/g, ''),
                docType: 'Submittal',
                item: submital.itemNo,
                version: version,
                revesion: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };
              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                finalPath,
                submital?.projectId || '',
                submital.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submital.projectId || '',
                submital.purposeId || ''
              );
              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }

      await submittalModel.addSubmittalAndDocumentAndChangeStatus(
        submittalChange,
        document,
        newSubmitalVersion,
        newSubmitalRevision,
        req.body.projectId
      );

      // getting the data from database with the given id
      if (newSubmitalRevision) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: newSubmitalRevision,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addSubmittalVersionAndRevisionWithDocumentByAttachingForm(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      const createdUserId = (req as any).user.id;
      let attachments: any = '';
      if (req.body.attachment) {
        attachments = JSON.parse(req.body.attachment);
      }

      const submittalUtilites = new SubmittalMethod();
      const submital = await submittalModel.findById(req.body.submittalId);
      if (!submital) {
        throw new Error('Submittal not found');
      }
      const { query, auditLogs } = submittalUtilites.getAttachmentQuerys(
        submital,
        attachments,
        updatedBy,
        createdUserId
      );
      const { revisionVersion, version } = await submittalUtilites.getVersion(
        req.body.submittalId,
        submital
      );

      const newSubmitalVersion: any = {
        updatedBy,
        createdBy,
        submittalId: req.body.submittalId,
        internalCodeId: req.body.internalCodeId,
        submissionDate: req.body.submissionDate,
        currentStep: req.body.currentStep || '1',
        transmittalNo: req.body.transmittalNo,
        keyword: req.body.keyword,
        status: 'Pending',
        version: version,
      };
      const newSubmitalRevision: SubmittalRevision = {
        updatedBy,
        createdBy,
        description: req.body.description,
        ownerCodeId: req.body.ownerCodeId,
        internalCodeId: req.body.internalCodeId,
        submissionDate: req.body.submissionDate,
        status: 'Pending',
        version: revisionVersion,
      };

      const submittalChange: Partial<Submittal> = {
        id: req.body.submittalId,
        lastSubmissionDate: req.body.submissionDate,
        currentVersion: version.toString(),
        status: 'Pending',
      };

      const files: submittalPushInterface[] = req.body.filePath || [];
      const document = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy,
          createdBy,
          documentURL: value.path,
          documentType: 'Submittal',
        };
        return newDocument;
      });

      if (document && document.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          newSubmitalRevision?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submital?.projectId || ''
        );
        const addPath: string[] = [];
        await Promise.all(
          document.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submital) {
              const destination = sftpConfig.path;

              const data: any = {
                subCode: submital.specification?.subSpecCode?.replace(/-/g, ''),
                type: submital.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submital?.description?.replace(/[ .]/g, ''),
                item: submital.itemNo,
                specNumber: submital.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  newSubmitalVersion?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  newSubmitalVersion?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: newSubmitalVersion?.keyword?.replace(/[^a-zA-Z0-9]/g, ''),
                revisionNumber: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };

              // const versionForData =
              //   newSubmitalVersion.keyword &&
              //   newSubmitalVersion.keyword != 'undefined' &&
              //   newSubmitalVersion.keyword != 'null'
              //     ? `${newSubmitalVersion?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         newSubmitalVersion.version
              //       )}`
              //     : `v${String(newSubmitalVersion.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submital?.description?.replace(/[ .]/g, ''),
                docType: 'Submittal',
                item: submital.itemNo,
                version: version,
                revesion: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };
              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                `${finalPath}`,
                submital?.projectId || '',
                submital.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submital.projectId || '',
                submital.purposeId || ''
              );

              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }

      await submittalModel.addSubmittalAndDocumentAndChangeStatus(
        submittalChange,
        document,
        newSubmitalVersion,
        newSubmitalRevision,
        req.body.projectId,
        query,
        auditLogs
      );

      // getting the data from database with the given id
      if (newSubmitalRevision) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: newSubmitalRevision,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addSubmittalVersionBeforeForm(req: Request, submittalId: string): Promise<string> {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      let version = 1.1;
      // let revisionVersion: string = '1.1.1';
      if (submittalId) {
        const submital = await submittalModel.findById(submittalId);
        const submitalVersion = await submittalModel.findByIdLastVersion(submittalId);
        if (submital && submital.itemNo && !submitalVersion) {
          version = Number(submital.itemNo) + 0.1;
        }

        if (submitalVersion && submitalVersion.version) {
          version = Number((Number(submitalVersion.version) + 0.1).toFixed(1));

          // revisionVersion = SubmittalController.incrementVersion(`${version}.0`);
        } else {
          // revisionVersion = SubmittalController.incrementVersion(`${version}.0`);
        }
      }
      const newSubmitalVersion: any = {
        updatedBy,
        createdBy,
        submittalId,
        currentStep: req.body.currentStep || '2',
        status: 'Form Submitted',
        version: version,
      };

      const submittalChange: Partial<Submittal> = {
        id: submittalId,
        currentVersion: version.toString(),
        status: 'Form Submitted',
      };

      const versionId = await submittalModel.addSubmittalAndDocumentAndChangeStatusBeforForm(
        submittalChange,
        // document,
        newSubmitalVersion
        // newSubmitalRevision
      );

      return versionId;
    } catch (error) {
      throw error;
    }
  }

  async addSubmittalVersionBeforeFormForImport(req: Request, submittalId: string) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      let version = 1.1;
      // let revisionVersion: string = '1.1.1';
      if (submittalId) {
        const submital = await submittalModel.findById(submittalId);
        const submitalVersion = await submittalModel.findByIdLastVersion(submittalId);

        if (submital && submital.itemNo && !submitalVersion) {
          version = Number(submital.itemNo) + 0.1;
        }

        if (submitalVersion && submitalVersion.version) {
          version = Number((Number(submitalVersion.version) + 0.1).toFixed(1));

          // revisionVersion = SubmittalController.incrementVersion(`${version}.0`);
        } else {
          // revisionVersion = SubmittalController.incrementVersion(`${version}.0`);
        }
      }
      const newSubmitalVersion: any = {
        updatedBy,
        createdBy,
        submittalId,
        currentStep: req.body.currentStep || '2',
        status: 'Form Submitted',
        version: version,
      };

      const submittalChange: Partial<Submittal> = {
        id: submittalId,
        currentVersion: version.toString(),
        status: 'Form Submitted',
      };
      return { submitalVersion: newSubmitalVersion, submittal: submittalChange };
    } catch (error) {
      throw error;
    }
  }

  async addRevisionWithDocument(req: Request, res: Response) {
    const addPath: string[] = [];
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      let revisionVersion: string = '0';
      const submittal = await submittalModel.findById(req.body.submittalId);
      const submittalVersionDetails = await submittalModel.findByIdVersion(
        req.body.submittalVersionId
      );
      if (req.body.submittalId) {
        const submitalVersion = await submittalModel.findAllRevisionByVersion(
          req.body.submittalVersionId
        );

        if (submitalVersion && submitalVersion) {
          const allRevisionVersion = submitalVersion?.map((value) => value.version);
          if (allRevisionVersion && allRevisionVersion?.length > 0) {
            const lastVersion = SubmittalController.getLastVersion(allRevisionVersion);
            revisionVersion = SubmittalController.incrementVersion(lastVersion);
          }
        }
      }
      const newSubmitalRevision: SubmittalRevision = {
        updatedBy,
        createdBy,
        submittalVersionId: req.body.submittalVersionId,
        internalCodeId: req.body.internalCodeId,
        ownerCodeId: req.body.ownerCodeId,
        submissionDate: req.body.submissionDate,
        status: 'Pending',
        version: revisionVersion,
      };

      const submittalRevisionChange: Partial<SubmittalVersion> = {
        id: req.body.submittalVersionId,
        submissionDate: req.body.submissionDate,
        internalCodeId: req.body.internalCodeId,
        status: 'Pending',
        transmittalNo: req.body.transmittalNo,
      };
      const submittalChange: Partial<Submittal> = {
        id: req.body.submittalId,
        lastSubmissionDate: req.body.submissionDate,
        status: 'Pending',
      };

      const files: submittalPushInterface[] = req.body.filePath || [];
      const document = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy,
          createdBy,
          documentURL: value.path,
          documentType: 'Submittal',
        };
        return newDocument;
      });

      if (document && document.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          newSubmitalRevision?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submittal?.projectId || ''
        );

        await Promise.all(
          document.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submittal) {
              const destination = sftpConfig.path;

              const data: any = {
                subCode: submittal.specification?.subSpecCode?.replace(/-/g, ''),
                type: submittal.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submittal?.description?.replace(/[ .]/g, ''),
                item: submittal.itemNo,
                specNumber: submittal.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  submittalVersionDetails?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  submittalVersionDetails?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, ''),
                revisionNumber: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };

              // const versionForData =
              //   submittalVersionDetails?.keyword &&
              //   submittalVersionDetails.keyword != 'undefined' &&
              //   submittalVersionDetails.keyword != 'null' &&
              //   submittalVersionDetails?.version
              //     ? `${submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         submittalVersionDetails?.version
              //       )}`
              //     : `v${String(submittalVersionDetails?.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submittal?.description?.replace(/[ .]/g, ''),
                docType: 'Submittal',
                item: submittal.itemNo,
                version: submittalVersionDetails?.version,
                revesion: revisionVersion?.split('.')[revisionVersion?.split('.').length - 1],
              };
              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                finalPath,
                submittal?.projectId || '',
                submittal.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submittal.projectId || '',
                submittal.purposeId || ''
              );
              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }
      await submittalModel.addRevisionAndDocumentAndChangeStatus(
        submittalChange,
        document,
        submittalRevisionChange,
        newSubmitalRevision,
        req.body.projectId
      );

      // getting the data from database with the given id
      if (newSubmitalRevision) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: newSubmitalRevision,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addRevisionWithDocumentAfterForm(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      let revisionVersion: string = '0';

      if (req.body.submittalId) {
        const submitalVersion = await submittalModel.findAllRevisionByVersion(
          req.body.submittalVersionId
        );
        const versionDetails = await submittalModel.findByIdVersion(req.body.submittalVersionId);
        if (versionDetails && versionDetails?.status != 'Form Submitted') {
          return res.status(500).json({ isSucceed: false, data: [], msg: 'Item already submited' });
        }
        if (submitalVersion && submitalVersion) {
          const allRevisionVersion = submitalVersion?.map((value) => value.version);

          if (allRevisionVersion && allRevisionVersion?.length > 0) {
            const lastVersion = SubmittalController.getLastVersion(allRevisionVersion);
            revisionVersion = SubmittalController.incrementVersion(lastVersion);
          } else {
            revisionVersion = `${versionDetails?.version}.1`;
          }
        }
      }

      const newSubmitalRevision: SubmittalRevision = {
        updatedBy,
        createdBy,
        submittalVersionId: req.body.submittalVersionId,
        submissionDate: req.body.submissionDate,
        internalCodeId: req.body.internalCodeId,
        status: 'Pending',
        version: revisionVersion,
      };

      const submittalRevisionChange: Partial<SubmittalVersion> = {
        id: req.body.submittalVersionId,
        submissionDate: req.body.submissionDate,
        internalCodeId: req.body.internalCodeId,
        currentStep: req.body.currentStep || '2',
        transmittalNo: req.body.transmittalNo,
        status: 'Pending',
      };

      const submittalChange: Partial<Submittal> = {
        id: req.body.submittalId,
        lastSubmissionDate: req.body.submissionDate,
        status: 'Pending',
      };

      const files: submittalPushInterface[] = req.body.filePath || [];
      const document = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy,
          createdBy,
          documentURL: value.path,
          documentType: 'Submittal',
        };
        return newDocument;
      });

      await submittalModel.addRevisionAndDocumentAndChangeStatus(
        submittalChange,
        document,
        submittalRevisionChange,
        newSubmitalRevision,
        req.body.projectId
      );

      // getting the data from database with the given id
      if (newSubmitalRevision) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: newSubmitalRevision,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addSubmittalProjectLevel(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;

      const submital: Submittal = {
        updatedBy,
        description: req.body.description,
        createdBy,
        projectId: req.body.projectId,
        stakeholderId: req.body.stakeholderId,
        specificationId: req.body.specificationId,
        submittalTypeId: req.body.submittalTypeId,
        submittalListId: req.body.submittalListId,
        classificationId: req.body.classificationId,
        reviewCodeId: req.body.reviewCodeId,
        sdNo: req.body.sdNo,
        itemNo: req.body.itemNo,
        purposeId: req.body.purposeId,
        section: req.body.section,
        drawingSheetNumber: req.body.drawingSheetNumber,
        status: 'Not Started',
        moveToSFTP: req.body.moveToSFTP || false,
      };
      const formIds = req.body.formId;
      const specification = await subSpecificationModel.findById(req.body.specificationId);
      await submittalModel.addSubmittalAtProject(submital, (req as any).user.id || '', formIds);
      if (specification) {
      }

      // getting the data from database with the given id
      if (submital) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: submital,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async updateApprovalStatus(req: Request, res: Response) {
    // const addedPath: string[] = [];
    try {
      const { reviewerComments, ownerCodeId, submittalRevisionId, submittalId } = req.body;
      let status = '';
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;

      if (ownerCodeId) {
        const actionCodeStatus = await submittalModel.findActionCodeStatus(ownerCodeId);
        if (actionCodeStatus?.status) {
          status = actionCodeStatus?.status;
        }
      }

      const submittalDetails = await submittalModel.findById(submittalId);
      const submittalRevisionDetails = await submittalModel.findByIdRevision(submittalRevisionId);
      const submittalVersionDetails = await submittalModel.findByIdVersion(
        submittalRevisionDetails?.submittalVersionId || ''
      );
      if (submittalRevisionDetails && submittalRevisionDetails?.status != 'Pending') {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'Item already approved or rejected',
        });
      }
      const updateDate: Partial<SubmittalVersion> = {
        id: submittalRevisionDetails?.submittalVersionId,
        ownerCodeId,
        reviewerComments,
        responseDate: req.body.responseDate || new Date(),
        status,
        updatedBy,
      };

      const updateDataRevision: Partial<SubmittalRevision> = {
        id: submittalRevisionDetails?.id,
        ownerCodeId,
        reviewerComments,
        responseDate: req.body.responseDate || new Date(),
        status,
        updatedBy,
      };

      const updateDataForSubmittal: Partial<Submittal> = {
        id: submittalId,
        status,
        lastResponseDate: req.body.responseDate || new Date(),
        lastOwnerCodeId: ownerCodeId,
        updatedBy,
      };
      let document: SubmittalDocument[] = [];
      if (req.body.filePath) {
        const files: submittalPushInterface[] = req.body.filePath || [];
        document = files.map((value) => {
          const newDocument: SubmittalDocument = {
            updatedBy,
            createdBy,
            isFeedback: true,
            documentURL: value.path,
            submittalRevisionId: submittalRevisionDetails?.id,
            documentType: 'Feedback',
          };
          return newDocument;
        });
      }

      if (document && document.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          submittalRevisionDetails?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submittalDetails?.projectId || ''
        );
        const addPath: string[] = [];
        await Promise.all(
          document.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submittalDetails) {
              const destination = sftpConfig.path;
              const data: any = {
                subCode: submittalDetails?.specification?.subSpecCode?.replace(/-/g, ''),
                type: submittalDetails?.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submittalDetails?.description?.replace(/[ .]/g, ''),
                item: submittalDetails.itemNo,
                specNumber: submittalDetails.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  submittalVersionDetails?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  submittalVersionDetails?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, ''),
                revisionNumber:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };

              // const versionForData =
              //   submittalVersionDetails?.keyword &&
              //   submittalVersionDetails.keyword != 'undefined' &&
              //   submittalVersionDetails.keyword != 'null' &&
              //   submittalVersionDetails?.version
              //     ? `${submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         submittalVersionDetails?.version
              //       )}`
              //     : `v${String(submittalVersionDetails?.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submittalDetails?.description?.replace(/[ .]/g, ''),
                docType: 'Feedback',
                item: submittalDetails.itemNo,
                version: submittalVersionDetails?.version,
                revesion:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };

              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                finalPath,
                submittalDetails?.projectId || '',
                submittalDetails.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submittalDetails?.projectId || '',
                submittalDetails.purposeId || ''
              );
              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }

      const submital = await submittalModel.updateAcceptStatus(
        updateDataForSubmittal,
        updateDate,
        document,
        updateDataRevision
      );
      // getting the data from database with the given id
      if (submital) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: submital,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async update(req: Request, res: Response) {
    try {
      const id: string = req.params.id;
      const data = req.body;
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      const result = await submittalModel.update(id, data);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data updated' });
      } else {
        res.status(404).json({
          response: { isSucceed: false, data: [], msg: 'Entity not found' },
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async countByStatus(req: Request, res: Response) {
    try {
      const pending = await submittalModel.countByStatus(req.params.id, 'Pending');
      const accepted = await submittalModel.countByStatus(req.params.id, 'Accepted');
      const needRevision = await submittalModel.countByStatus(req.params.id, 'Need Revision');
      const notStarted = await submittalModel.countByStatus(req.body.id, 'Not Started');
      const finalData: any = {};
      finalData.pending = pending;
      finalData.accepted = accepted;
      finalData.needRevision = needRevision;
      finalData.notStarted = notStarted;
      finalData.total = pending + needRevision + accepted + notStarted;

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: finalData,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getVersionDocumnetByVersionId(req: Request, res: Response) {
    try {
      const data = await submittalModel.findSubmittalVersionById(req.body.id);

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByRevision(req: Request, res: Response) {
    try {
      const data = await submittalModel.findByIdRevision(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getAttachmentBySubmittalVersion(req: Request, res: Response) {
    try {
      const submittalVersionFormDetails = await submittalModel.findByIdVersionForAttchments(
        req.params.id
      );
      if (submittalVersionFormDetails) {
        const obj: any = {};
        for (const element of submittalVersionFormDetails) {
          if (!submittalVersionFormDetails || !element.submittalFormList.dce?.entity) {
            throw new Error('Attachment not found');
          }
          const entity = element.submittalFormList.dce?.entity;
          if (entity in entityList) {
            const entityValue: any = entityList[entity as keyof EntityListInterface];

            const result = await submittalModel.findAttachedTableData(
              req.params.id,
              entityValue,
              entity
            );
            if (result && result?.length > 0) {
              obj[entity.charAt(0).toUpperCase() + entity.slice(1)] = result || [];
            }
          }
        }
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: obj || {},
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: {},
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  private static getCurrentDateInDDMMYYYYFormat(date: Date): string {
    const currentDate: Date = new Date(date) || new Date();

    // Get the day, month, and year components
    const day: string = String(currentDate.getDate()).padStart(2, '0');
    const month: string = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const year: string = String(currentDate.getFullYear());

    // Concatenate the components in the desired format
    const formattedDate: string = day + month + year;

    return formattedDate;
  }

  private static reverseMapKeysToFieldNames(
    data: any[],
    columnInfo: DataFromEntityInterface[]
  ): any[] {
    return data.map((item) => {
      const newItem: any = {};
      for (const key in item) {
        const column = columnInfo.find((c) => c.fieldName === key);
        if (column && column.inTemplate) {
          newItem[column.name] = item[key];
        } else {
          newItem[key] = item[key];
        }
      }
      newItem.rowNumber = item.rowNumber;
      return newItem;
    });
  }

  private static addOneToLastDigit(num: string) {
    // Convert number to string
    const numStr = num.toString();

    // Split the number into integer and decimal parts
    // eslint-disable-next-line prefer-const
    let [integerPart, decimalPart] = numStr.split('.');

    // If there is no decimal part or the decimal part is '9', increment the integer part
    // if (!decimalPart || decimalPart === '9') {
    //   integerPart = (parseInt(integerPart) + 1).toString();
    //   decimalPart = '0'; // Reset the decimal part to '0'
    // } else {
    // Increment the last digit of the decimal part
    const lastDigit = decimalPart.split('.').pop();
    const newLastDigit = Number(lastDigit) + 1;
    decimalPart = decimalPart.slice(0, -1) + newLastDigit.toString();
    // }

    // Reconstruct the number
    const updatedNumStr = decimalPart === '0' ? integerPart : `${integerPart}.${decimalPart}`;

    // Convert the updated string back to a number
    return updatedNumStr;
  }

  editVersion = async (req: Request, res: Response) => {
    try {
      const { keyword, projectId, transmittalNo } = req.body;
      const { submittalVersionId } = req.params;
      let attachments: any = '';
      if (req.body.attachment) {
        attachments = JSON.parse(req.body.attachment);
      }
      const versionDetails = await submittalModel.findSubmittalVersionById(submittalVersionId);
      if (!versionDetails) {
        throw new Error('Submittal version not found');
      }
      const submital = await submittalModel.findById(versionDetails?.submittalId || '');
      if (!submital) {
        throw new Error('Submittal not found');
      }
      const submittalUtilites = new SubmittalMethod();
      let documentToUpdate: {
        updatedDocument: SubmittalDocument[];
        newPath: string;
        oldPath: string;
      } | null = null;
      if (keyword && keyword !== versionDetails.keyword) {
        const documentsInVersion =
          await submittalModel.getAllDocumentsBySubmittalVersion(submittalVersionId);
        if (documentsInVersion && documentsInVersion.length > 0) {
          documentToUpdate = submittalUtilites.getAllVersionDocumentForNameChange(
            keyword,
            documentsInVersion,
            versionDetails?.keyword || '',
            versionDetails.version || 1
          );
        }
      }
      const { query } = submittalUtilites.getAttachmentQuerys(
        submital,
        attachments,
        (req as any).user.name,
        (req as any).user.id
      );
      const tableNames = submittalUtilites.getAttachmentTableNames(submital, attachments);
      await submittalModel.editVersion(
        submittalVersionId,
        keyword,
        projectId,
        new Date(),
        transmittalNo,
        query,
        tableNames,
        documentToUpdate
      );
      const message = req.__('UpdatedSuccess');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  editRevision = async (req: Request, res: Response) => {
    try {
      const { revision, documentToDelete, filePath } = req.body;
      const parsedDocumentToDelete =
        documentToDelete && documentToDelete != '[]' ? JSON.parse(documentToDelete) : [];
      const parsedRevision = JSON.parse(revision) || {};
      const { revisionId } = req.params;
      const createdBy = (req as any).user.name;
      const files: submittalPushInterface[] = filePath || [];
      const submittalRevisionDetails = await submittalModel.findByIdRevision(revisionId);
      const submittalVersionDetails = await submittalModel.findByIdVersion(
        submittalRevisionDetails?.submittalVersionId || ''
      );
      const submital = await submittalModel.findById(submittalVersionDetails?.submittalId || '');
      if (parsedDocumentToDelete && parsedDocumentToDelete.length > 0) {
        const submittalUtilites = new SubmittalMethod();
        await submittalUtilites.deleteSubmittalFileFromSFTP(
          parsedDocumentToDelete,
          submital?.projectId || '',
          submital?.purposeId || ''
        );
      }
      const documents = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy: createdBy,
          createdBy,
          documentURL: value.path,
          documentType: 'Submittal',
          submittalRevisionId: revisionId,
        };
        return newDocument;
      });
      if (documents && documents.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          parsedRevision?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submital?.projectId || ''
        );
        const addPath: string[] = [];
        await Promise.all(
          documents.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submital) {
              const destination = sftpConfig.path;

              const data: any = {
                subCode: submital.specification?.subSpecCode?.replace(/-/g, ''),
                type: submital.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submital?.description?.replace(/[ .]/g, ''),
                item: submital.itemNo,
                specNumber: submital.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  submittalVersionDetails?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  submittalVersionDetails?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, ''),
                revisionNumber:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };

              // const versionForData =
              //   submittalVersionDetails?.keyword &&
              //   submittalVersionDetails.keyword != 'undefined' &&
              //   submittalVersionDetails.keyword != 'null'
              //     ? `${submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         submittalVersionDetails?.version
              //       )}`
              //     : `v${String(submittalVersionDetails?.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submital?.description?.replace(/[ .]/g, ''),
                docType: 'Submittal',
                item: submital.itemNo,
                version: submittalVersionDetails?.version,
                revesion:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };
              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                finalPath,
                submital?.projectId || '',
                submital.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submital.projectId || '',
                submital.purposeId || ''
              );

              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }
      await submittalModel.editRevision(
        parsedRevision,
        revisionId,
        parsedDocumentToDelete,
        documents
      );
      const message = req.__('UpdatedSuccess');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  editFeedBack = async (req: Request, res: Response) => {
    try {
      const { revision, documentToDelete, filePath } = req.body;
      const { revisionId } = req.params;
      const parsedDocumentToDelete =
        documentToDelete && documentToDelete != '[]' ? JSON.parse(documentToDelete) : [];
      const parsedRevision = JSON.parse(revision) || {};
      const createdBy = (req as any).user.name;
      const files: submittalPushInterface[] = filePath || [];
      const submittalRevisionDetails = await submittalModel.findByIdRevision(revisionId);
      const submittalVersionDetails = await submittalModel.findByIdVersion(
        submittalRevisionDetails?.submittalVersionId || ''
      );
      const submital = await submittalModel.findById(submittalVersionDetails?.submittalId || '');
      if (parsedDocumentToDelete && parsedDocumentToDelete.length > 0) {
        const submittalUtilites = new SubmittalMethod();
        await submittalUtilites.deleteSubmittalFileFromSFTP(
          parsedDocumentToDelete,
          submital?.projectId || '',
          submital?.purposeId || ''
        );
      }
      const documents = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy: createdBy,
          createdBy,
          documentURL: value.path,
          isFeedback: true,
          documentType: 'Feedback',
          submittalRevisionId: revisionId,
        };
        return newDocument;
      });
      if (documents && documents.length > 0) {
        const date = SubmittalController.getCurrentDateInDDMMYYYYFormat(
          parsedRevision?.submissionDate || new Date()
        );
        const submodule = await dceModel.findByEntity('submittal');
        const sftpConfig = await sftpConfidModel.findByDCEId(
          submodule?.id || '',
          submital?.projectId || ''
        );
        const addPath: string[] = [];
        await Promise.all(
          documents.map(async (value, index) => {
            if (sftpConfig && sftpConfig.path && submital) {
              const destination = sftpConfig.path;

              const data: any = {
                subCode: submital.specification?.subSpecCode?.replace(/-/g, ''),
                type: submital.submittalType?.sdDescription?.split(' ').join(''),
                submittal: submital?.description?.replace(/[ .]/g, ''),
                item: submital.itemNo,
                specNumber: submital.specification?.subSpecCode?.replace(/-/g, ''),
                transmittalNumber:
                  submittalVersionDetails?.transmittalNo?.split('-')?.[1]?.split('.')?.[0] ||
                  submittalVersionDetails?.transmittalNo?.split('-')?.[0]?.split('.')?.[0],
                keyword: submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, ''),
                revisionNumber:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };

              // const versionForData =
              //   submittalVersionDetails?.keyword &&
              //   submittalVersionDetails.keyword != 'undefined' &&
              //   submittalVersionDetails.keyword != 'null'
              //     ? `${submittalVersionDetails?.keyword?.replace(/[^a-zA-Z0-9]/g, '')}_v${String(
              //         submittalVersionDetails?.version
              //       )}`
              //     : `v${String(submittalVersionDetails?.version)}`;
              // data.version = versionForData;
              // destination = `${sftpConfig.path}/{version}`;

              const finalPath = stringTemplate(destination, data);
              const fileName = sftpConfig.fileName;
              const fileNameData = {
                date,
                submittal: submital?.description?.replace(/[ .]/g, ''),
                docType: 'Feedback',
                item: submital.itemNo,
                version: submittalVersionDetails?.version,
                revesion:
                  submittalRevisionDetails?.version?.split('.')[
                    submittalRevisionDetails?.version?.split('.').length - 1
                  ],
              };
              const templateName = stringTemplate(fileName as string, fileNameData);
              const currentDocument = path.basename(value?.documentURL || '');
              const fileExtension = currentDocument.split('.').pop();
              const finalName = `${templateName}_f${index + 1}.${fileExtension}`;
              await ensureFolderExists(
                finalPath,
                submital?.projectId || '',
                submital.purposeId || ''
              );
              await transferFileBetweenSFTPs(
                value?.documentURL || '',
                `${finalPath}/${finalName}`,
                submital.projectId || '',
                submital.purposeId || ''
              );

              addPath.push(`${finalPath}/${finalName}`);
              value.sftpPath = `${finalPath}/${finalName}`;
            }
            return value;
          })
        );
      }
      await submittalModel.editRevision(
        parsedRevision,
        revisionId,
        parsedDocumentToDelete,
        documents
      );
      const message = req.__('UpdatedSuccess');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

const submittalController = new SubmittalController();
export default submittalController;
