import { Request, Response } from 'express';
import stakeUserModel from '../models/stakeUser.model';
import stakeholderModel from '../models/stakeholder.model';
import {
  addUserByInvite,
  addUserByInviteRoleBack,
  checkEmailExist,
  refreshUserById,
  sendInviteLink,
} from '../../../shared/server/platformApi/user';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import { sendMail } from '../../../shared/utilities/email/email';
import projectModel from '../models/project.model';

class StakeUserController {
  constructor() {}

  async findByStakeholderId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const stakeUserData = await stakeUserModel.findByStakeholderId(Number(req.params.id));
      // checking if data is found with the id
      if (stakeUserData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeUserData,
          msg: 'stakeUser found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No stakeUser data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async inviteUser(req: Request, res: Response) {
    try {
      const { email, firstName, lastName, roleId, stakeholderId, designation, customMessage } =
        req.body;
      const stakeholderDetails = await stakeholderModel.findById(stakeholderId);
      const projectDetails = await projectModel.getProjectId(stakeholderDetails?.projectId || '');
      const emailExist = await checkEmailExist(req.headers.authorization || '', email);

      if (emailExist && emailExist.user) {
        if (
          await stakeUserModel.checkUserInProject(
            emailExist.user.id,
            stakeholderDetails?.projectId || ''
          )
        ) {
          throw new Error('User already added in project');
        }
        const newStakeuser: any = {
          roleId,
          userId: emailExist.user.id,
          designation,
          createdUserId: (req as any).user.id,
          updatedBy: (req as any).user.name,
          createdBy: (req as any).user.name,
          stakeholderId,
        };
        await stakeUserModel.addStakeUser(newStakeuser);

        const body = `
        <p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">Hi,</p>

<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">
    You have been added to a project <strong style="color: #007bff;">${projectDetails?.name} WORKS</strong> in 
    <strong style="color: #37adf4;">SmartInfra Hub</strong>.
</p>

${customMessage ? `<p style="font-size: 14px; color: #555;">${customMessage}</p>` : ''}

<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">
    To visit <strong>SmartInfra Hub</strong> and access your project, please click on the following link:
</p>

<p style="text-align: center; margin: 20px 0;">
    <a href="${process.env.WEB_BASE_URL}" 
       style="background-color: #37adf4; color: #ffffff; padding: 12px 20px; text-decoration: none; 
              border-radius: 5px; font-size: 16px; font-weight: bold; display: inline-block;font-family: Arial, sans-serif;">
        Visit SmartInfra Hub
    </a>
</p>

<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">Best regards,</p>
<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;"><strong>SmartInfra Hub</strong></p>

            `;
        sendMail(
          'You have been add to new project in SmartInfra Hub.',
          emailExist.user.email,
          body
        );
        return res.status(200).json({
          isSucceed: true,
          data: '',
          msg: 'User added',
        });
      }

      const userBody = {
        email,
        firstName,
        lastName,
        organizationId: stakeholderDetails?.organizationId,
      };

      const addedUser = await addUserByInvite(req.headers.authorization || '', userBody);
      try {
        const newStakeuser: any = {
          roleId,
          userId: addedUser.user.id,
          designation,
          createdUserId: (req as any).user.id,
          updatedBy: (req as any).user.name,
          createdBy: (req as any).user.name,
          stakeholderId,
        };
        await sendInviteLink(req.headers.authorization || '', { ...addedUser.user, customMessage });
        refreshUserById(addedUser.user.id, addedUser.user);
        await stakeUserModel.addStakeUser(newStakeuser);
      } catch (error) {
        await addUserByInviteRoleBack(req.headers.authorization || '', {
          userId: addedUser.user.id,
        });
        throw error;
      }

      return res.status(200).json({
        isSucceed: true,
        data: '',
        msg: 'User added',
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByUserListProjectId(req: Request, res: Response) {
    try {
      const stakeholderData = req.query.dropdown
        ? await stakeUserModel.findByUserListProjectIdForDropdown(req.params.id)
        : await stakeUserModel.findByUserListProjectId(req.params.id);

      // checking if data is found with the id
      if (stakeholderData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeholderData,
          msg: 'stakeholder found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No stakeholder data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async findAllUserListProject(req: Request, res: Response) {
    try {
      const stakeholderData = stakeUserModel.findAllUserListProject();

      // checking if data is found with the id
      if (stakeholderData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeholderData,
          msg: 'Data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async softDelete(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const data = await stakeUserModel.delete(id);
      if (data && data.user && data.stakeholder?.project) {
        const body = `
        <p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">Hi,</p>

<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">
    You have been <strong style="color: #dc3545;">removed</strong> from the project 
    <strong style="color: #37adf4;">${data.stakeholder?.project?.name} WORKS</strong> in 
    <strong style="color: #37adf4;">SmartInfra Hub</strong>.
</p>



<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">
    If you believe this was a mistake or need further assistance, please contact the administrator.
</p>

<p style="text-align: center; margin: 20px 0;">
    <a href="${process.env.WEB_BASE_URL}" 
       style="background-color: #37adf4; color: #ffffff; padding: 12px 20px; text-decoration: none; 
              border-radius: 5px; font-size: 16px; font-weight: bold; display: inline-block;">
        Visit SmartInfra Hub
    </a>
</p>

<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;">Best regards,</p>
<p style="font-size: 16px; color: #333; font-family: Arial, sans-serif;"><strong>SmartInfra Hub</strong></p>`;

        await sendMail(
          'You Have Been Removed from a Project in SmartInfra Hub',
          `${data.user.email}`,
          body
        );
      }

      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: [],
        msg: 'No data found',
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async refreshUserData(req: Request, res: Response) {
    try {
      refreshUserById(req.params.id, req.body);
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: [],
        msg: 'Data updated',
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async getUserRolesAndStakeHoldersByProjectId(req: Request, res: Response) {
    try {
      const { id} = req.params;
      const data = await stakeUserModel.getUserInfoByProjectId(id);
      if (!data || data.length === 0) {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'Data found',
      });
    } catch (error) {
     return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getPageRoutesByUserIdAndProjectId(req: Request, res: Response) {
    try {
      const { userId, projectId } = req.params;
      const data = await stakeUserModel.getPageRoutesByUserIdAndProjectId(userId, projectId);
      if (!data) {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'Data found',
      });
    } catch (error) {
     return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }


}
const stakeUserController = new StakeUserController();
export default stakeUserController;
