import controller from "@controllers//map/testResultPopup.controller";
import express, { Router } from "express";
import { authenticateToken } from "src/shared/middlewares/auth.middleware";

const router: Router = express.Router();

router.get('/by/layer/:layerId', authenticateToken, controller.getByLayerId);
router.post('/', authenticateToken, controller.add);
router.put('/by/layer', authenticateToken, controller.update);

export default router;