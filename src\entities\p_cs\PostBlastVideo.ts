import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { BlastReport } from './BlastReport';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class PostBlastVideo extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Blast Report Id', // this column will be entered by the
      fieldName: 'blastReportId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.blast_report WHERE "designBlastId" = $1`,
      getListQuery:
        'SELECT id,"designBlastId" as name FROM p_cs.blast_report WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 20;',
      listParams: 'id',
      listName: 'blastReportList',
    },
  })
  @Column({ nullable: true })
  blastReportId?: string;

  @ManyToOne(() => BlastReport, { nullable: true })
  @JoinColumn({ name: 'blastReportId' })
  blastReport?: BlastReport;

  @ColumnInfo({
    customData: {
      name: 'Video Name',
      fieldName: 'videoName',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  videoName?: string;

  @ColumnInfo({
    customData: {
      name: 'Video Date Time',
      fieldName: 'dateTime',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ type: 'timestamp', nullable: true })
  dateTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Blast/Throw Direction',
      fieldName: 'direction',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  direction?: string;

  @ColumnInfo({
    customData: {
      name: 'View Camera Two',
      fieldName: 'viewCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  viewCamTwo?: string;

  @ColumnInfo({
    customData: {
      name: 'View Camera One',
      fieldName: 'viewCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  viewCamOne?: string;

  @ColumnInfo({
    customData: {
      name: 'Videographer',
      fieldName: 'videographer',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  videographer?: string;

  @ColumnInfo({
    customData: {
      name: 'Video Description',
      fieldName: 'description',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  description?: string;

  @ColumnInfo({
    customData: {
      name: 'Longitude Camera One',
      fieldName: 'longitudeCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitudeCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Latitude Camera One',
      fieldName: 'latitudeCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitudeCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting Camera One',
      fieldName: 'eastingCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  eastingCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing Camera One',
      fieldName: 'northingCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northingCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation Camera One',
      fieldName: 'elevationCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevationCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Video Direction Camera One',
      fieldName: 'videoDirectionCamOne',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  videoDirectionCamOne?: number;

  @ColumnInfo({
    customData: {
      name: 'Blast Video 1 File Path',
      fieldName: 'blastVideo1FilePath',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastVideo1FilePath?: string;

  @Column({ nullable: true })
  blastVideo1SFTPFilePath?: string;

  @ColumnInfo({
    customData: {
      name: 'Longitude Camera Two',
      fieldName: 'longitudeCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitudeCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Latitude Camera Two',
      fieldName: 'latitudeCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitudeCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting Camera Two',
      fieldName: 'eastingCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  eastingCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing Camera Two',
      fieldName: 'northingCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northingCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation Camera Two',
      fieldName: 'elevationCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevationCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Video Direction Camera Two',
      fieldName: 'videoDirectionCamTwo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  videoDirectionCamTwo?: number;

  @ColumnInfo({
    customData: {
      name: 'Blast Video 2 File Path',
      fieldName: 'blastVideo2FilePath',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastVideo2FilePath?: string;

  @Column({ nullable: true })
  blastVideo2SFTPFilePath?: string;

  @ColumnInfo({
    customData: {
      name: 'Post-Blast Report File Path',
      fieldName: 'reportPath',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  reportPath?: string;

  @Column({ nullable: true })
  sftpReportPath?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
