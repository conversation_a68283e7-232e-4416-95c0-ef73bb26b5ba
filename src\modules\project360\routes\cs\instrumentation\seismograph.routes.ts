import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { Seismograph } from '../../../../../entities/p_cs/Seismograph';

const router: Router = express.Router();

const GenricController = new CrudController<Seismograph>(Seismograph);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'seismograph')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'seismograph')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'seismograph')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'seismograph')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'seismograph'));
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'seismograph')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'seismograph')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'seismograph')
);

export default router;
