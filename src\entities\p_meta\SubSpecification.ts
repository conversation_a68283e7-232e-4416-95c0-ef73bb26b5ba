import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>umn,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SpecAgency } from './SpecAgency';

@Entity({ schema: 'p_meta' })
export class SubSpecification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  subSpecCode?: string;

  @Column()
  subSpecName?: string;

  @Column()
  version?: number;

  @Column({ nullable: true })
  specAgencyId?: string;

  @JoinColumn({ name: 'specAgencyId' })
  @ManyToOne(() => SpecAgency, { nullable: true }) 
  specAgency?: SpecAgency;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy?: string;

  @Column({ default: false })
  isDelete?: boolean;
}
