import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { DCERelationship } from '../../../../entities/p_utils/DCERelationship';
import DCERelationshipController from '../../controllers/utils/dceRelationship.contorller';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUD = new CrudController<DCERelationship>(DCERelationship);
const controller = new DCERelationshipController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.put('/:id', authenticateToken, (req, res) => CRUD.update(req, res));
router.post('/', authenticateToken, (req, res) => CRUD.create(req, res));
router.delete('/:id', authenticateToken, (req, res) => CRUD.softDelete(req, res));
router.get(
  '/by/dce/relation/:projectId/:dceId/:relationshipTypeId',
  authenticateToken,
  controller.findByDceAndRelationshipType
);
router.get('/by/dce/:projectId/:dceId/:tableId', authenticateToken, controller.findByDceAndTableId);
router.get('/by/dces/:projectId', authenticateToken, controller.findByDcesAndProject);
router.get(
  '/get/sample/test/relation/:sampleId',
  authenticateToken,
  controller.getSampleTestRelation
);
router.get(
  '/get/sample/test/summary/:sampleId',
  authenticateToken,
  controller.getSampleTestSummary
);
router.get(
  '/get/sample/test/relation/test/data/:sampleId',
  authenticateToken,
  controller.getSampleTestRelationTestData
);
router.post('/add/sample/test/relation', authenticateToken, controller.addSampleTestRelation);
router.put('/edit/sample/test/relation/:id', authenticateToken, controller.editSampleTestRelation);
router.delete(
  '/delete/sample/test/relation/:sampleId/:id',
  authenticateToken,
  controller.deleteSampleTestRelation
);

export default router;
