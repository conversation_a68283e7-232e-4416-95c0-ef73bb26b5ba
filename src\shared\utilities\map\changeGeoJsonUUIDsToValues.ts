import { getManager, getRepository } from 'typeorm';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import DCERelationModel from '../../../modules/project360/models/meta/dceRelation.model';
import { entityList, EntityListInterface } from '../entity/entityList';
import { typeConversionNormal } from '../surveyJs/convertTableMetaDataToSurveyJsJson';
import isUUID from '@utils/custom/isUUID';
import getCustomDropdownValues from '@utils/dropdown/getCustomDropdownValues';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';

const changeGeoJsonUUIDsToValue = async (entity: string, data: any[], projectId: string) => {
  try {
    const finalOut: any[] = [];
    const approvalStatusModel = new ApprovalStatusModel();
    const approvalStatus = await approvalStatusModel.getAllStatus();
    const approvalStatusMap = new Map(approvalStatus.map((item) => [item.id, item.name]));
    if (entity in entityList) {
      const dce = await dceModel.findByEntity(entity);
      if (dce) {
        const entity = dce.entity;
        const entityClass: any = entityList[entity as keyof EntityListInterface];
        const entityMetadata = getRepository(entityClass).metadata;
        const query = `SELECT column_name as name, udt_name as type
                            FROM information_schema.columns
                            WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

        const out: { name: string; type: string }[] = await getManager().query(query);
        const dceRelationModel = new DCERelationModel();
        const relationData = await dceRelationModel.getByDceId(dce.id);
        const columnType: any = {};
        for (const iterator of out) {
          const type = typeConversionNormal(iterator.type);
          columnType[iterator.name] = type;
        }

        const uuidCColumns = Array.from(
          new Set([
            ...Object.keys(columnType).filter((key) => columnType[key] === 'uuid'),
            ...relationData.map((value) => value.columnName),
          ])
        );
        if (uuidCColumns.includes('projectId')) {
        }
        if (uuidCColumns.length > 0) {
          let project = '';
          if (uuidCColumns.includes('projectId')) {
            const queryForProject = `SELECT name From p_gen.project WHERE id='${projectId}'`;
            const projectFromDb: { name: string }[] = await getManager().query(queryForProject);
            project = projectFromDb[0].name;
          }

          // Implementing batching
          const BATCH_SIZE = 100;
          const batches = [];

          for(let i = 0; i < data.length; i += BATCH_SIZE) {
            batches.push(data.slice(i, i + BATCH_SIZE));
          }

          for(const batch of batches) {
            const batchResults = await Promise.allSettled(
              batch.map(async (obj) => {
                try {
                 const objCopy = {...obj };
                  const columnBatches = [];

                  for (let i = 0; i < uuidCColumns.length; i += 10) {
                    columnBatches.push(uuidCColumns.slice(i, i + 10)); 
                  }

                  for (const columns of columnBatches) {
                    const columnResults = await Promise.allSettled(
                      columns.map(async (key) => {
                        try {
                          if (!key) return;

                          if (key === 'projectId') {
                            objCopy[key] = project;   
                          } else if(objCopy.hasOwnProperty(key) && objCopy[key]) {
                            const relevantRelation = relationData.find(
                              (item) => item.columnName === key && item.dceId === dce.id
                            );
                            
                            if(relevantRelation){
                              if(relevantRelation.custom && relevantRelation.custom === 'userByProject'){
                                if((isUUID(objCopy[key]) && key === 'photographer') || isUUID(objCopy[key])){
                                  try {
                                    const user = await getCustomDropdownValues(relevantRelation.custom, {projectId}, objCopy[key])
                                    objCopy[key] = user || null; 
                                  } catch (error) {
                                    objCopy[key] = null; 
                                  }
                                } 
                              } else {
                                if(isUUID(objCopy[key])){
                                  try {
                                    const dataQuery = `SELECT ${relevantRelation.relatedColumnToDisplay} AS name FROM ${relevantRelation.relatedTableName} WHERE id = '${objCopy[key]}'`;
                                    const result = await getManager().query(dataQuery);
                                    objCopy[key] = result[0]?.name || null; 
                                  } catch (error) {
                                    console.error(`Error executing query for ${key}:`, error);
                                    objCopy[key] = null;
                                  }
                                }
                              }
                            }
                          } else if (key === 'approvalStatusId') {
                            objCopy[key] = approvalStatusMap.get(objCopy[key]) || null;
                          }
                        } catch (error) {
                          console.error(`Error processing column ${key}:`, error);
                         } 
                      }) 
                    ) ;

                    columnResults.forEach((result, index) => {
                      if (result.status === 'rejected') {
                        console.error(`Error processing column ${columns[index]}:`, result.reason);
                      }
                    });
                  }

                  return objCopy; 
                } catch(error) {
                  console.error('Error processing object:', error);
                  return obj; // Return original object if processing fails
                }
              })
            );

            batchResults.forEach((result, index) => {
              if (result.status === 'fulfilled') {
                finalOut.push(result.value);
              } else {
                console.error(`Error processing object at index ${index}:`, result.reason);
              }
            });
          }


            if (finalOut.length > 0) {
              return finalOut;
            }
        }
      }
    }
    return data;
  } catch (error) {
    throw error;
  }
};

export default changeGeoJsonUUIDsToValue;
