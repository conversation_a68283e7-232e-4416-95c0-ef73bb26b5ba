import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  Column,
  AfterLoad,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { getTriggerById } from '../../shared/server/platformApi/trigger';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

@Entity({ schema: 'p_utils' })
export class EmailNotificationConfiguration {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  userIds?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  triggerId?: string;

  @Column({ nullable: true })
  frequency?: string;

  @Column({ nullable: true, type: 'timestamp' })
  nextDate?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ default: true })
  enable?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  trigger?: any | null;
  user?: IUser[] | [];

  @AfterLoad()
  async afterLoad() {
    try {
      this.user = [];
      this.trigger = null;
      if (this.triggerId) {
        const data = await getTriggerById(this.triggerId);
        this.trigger = data;
      }
      if (this.userIds) {
        const userId = this.userIds.split(',');
        if (userId.length > 0) {
          const result = await Promise.all(
            userId.map(async (value) => {
              return await getUserById(value);
            })
          );
          if (result.length > 0) {
            this.user = result;
          }
        }
      }
    } catch (error) {
      this.user = [];
      this.trigger = null;
    }
  }
}
