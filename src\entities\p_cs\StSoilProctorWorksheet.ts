import { Entity, Column, <PERSON>ToOne, JoinColumn } from 'typeorm';
import { StSoilProctorTest } from './StSoilProctorTest';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';

@Entity({ schema: 'p_cs' })
export class StSoilProctorWorksheet extends SubDCEColumns {
  @Column({ nullable: true })
  testSequenceNo?: string;

  @Column({ nullable: true })
  panId?: string;

  @Column({ type: 'decimal', nullable: true })
  targetMoisture?: number;

  @Column({ type: 'decimal', nullable: true })
  sampleWeightTargetMoisture?: number;

  @Column({ type: 'decimal', nullable: true })
  waterAdded?: number;

  @Column({ type: 'decimal', nullable: true })
  wetSoilPan?: number;

  @Column({ type: 'decimal', nullable: true })
  drySoilPan?: number;

  @Column({ type: 'decimal', nullable: true })
  panWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfWetSoil?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfDrySoil?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfWater?: number;

  @Column({ type: 'decimal', nullable: true })
  moistureContent?: number;

  @Column({ type: 'decimal', nullable: true })
  wetWeightMoldSoil?: number;

  @Column({ type: 'decimal', nullable: true })
  wetWeightSoil?: number;

  @Column({ type: 'decimal', nullable: true })
  wetDensity?: number;

  @Column({ type: 'decimal', nullable: true })
  dryDensity?: number;

  @Column({ nullable: true })
  proctorTestId?: string;

  @ManyToOne(() => StSoilProctorTest, { nullable: true })
  @JoinColumn({ name: 'proctorTestId' })
  stSoilProctorTest?: StSoilProctorTest;
}
