import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { FeatureType } from '../p_domain/FeatureType';
import { Material } from '../p_meta/Material';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class FeatureConstruction extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Description',
      fieldName: 'description',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  fcDescription?: string;

  @ColumnInfo({
    customData: {
      name: 'Type',
      fieldName: 'featuretypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.feature_type WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  featureTypeId?: string;

  @ManyToOne(() => FeatureType, { nullable: true })
  @JoinColumn({ name: 'featureTypeId' })
  FeatureType?: FeatureType;

  @ColumnInfo({
    customData: {
      name: 'Material',
      fieldName: 'materialId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_gen.material WHERE name = $1 AND "projectId" = $2',
    },
  })
  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => Material, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: Material;

  @ColumnInfo({
    customData: {
      name: 'Comment',
      fieldName: 'comment',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
