import { NextFunction, Request, Response } from 'express';
import postblastVideoModel from '../../modules/project360/models/cs/postBlastVideo.mode';
import { AzureBlobStorageService } from '../utilities/sftpBlobAndFiles/azureBlobStorageService';
import projectFilelModel from '../../modules/project360/models/projectFile.model';

export const deletePostBlastVideo = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const postBlastVideoDetails = await postblastVideoModel.findById(req.params.id);
    if (
      postBlastVideoDetails &&
      postBlastVideoDetails.projectId &&
      (req as any).deleteFile.length > 0
    ) {
      if (
        (req as any).deleteFile.includes('blastVideo1FilePath') &&
        postBlastVideoDetails?.blastVideo1FilePath
      ) {
        await azureBlobStorageService.deleteBlob(
          postBlastVideoDetails?.projectId,
          postBlastVideoDetails?.blastVideo1FilePath
        );
      }
      if (
        (req as any).deleteFile.includes('blastVideo2FilePath') &&
        postBlastVideoDetails?.blastVideo2FilePath
      ) {
        await azureBlobStorageService.deleteBlob(
          postBlastVideoDetails?.projectId,
          postBlastVideoDetails?.blastVideo2FilePath
        );
      }
      if ((req as any).deleteFile.includes('reportPath') && postBlastVideoDetails?.reportPath) {
        await azureBlobStorageService.deleteBlob(
          postBlastVideoDetails?.projectId,
          postBlastVideoDetails?.reportPath
        );
      }
    }
    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to delete the file from server.' });
  }
};

export const deleteProjectFile = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const data = await projectFilelModel.findById(req.params.id);
    if (data && data.filePath && data.projectId) {
      await azureBlobStorageService.deleteBlob(data.projectId, data.filePath);
    } else {
      return res.status(401).json({ error: 'Item not found' });
    }
    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to delete the file from server.' });
  }
};
