const convertBase64ToBlob = (fileData: any) => {
  const base64Content = fileData.content.split(',')[1]; // Remove "data:application/pdf;base64," prefix

  // Decode Base64 string to binary data
  const binaryString = atob(base64Content);

  // Convert binary string to a Uint8Array
  const binaryLength = binaryString.length;
  const bytes = new Uint8Array(binaryLength);
  for (let i = 0; i < binaryLength; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }

  // Create a Blob
  const blob = new Blob([bytes], { type: fileData.type });
  return blob;
};

export default convertBase64ToBlob;
