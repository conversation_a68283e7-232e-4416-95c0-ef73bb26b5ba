import { Request, Response } from 'express';
import errorMiddleware from '../../../../../shared/middlewares/error/error.middleware';
import BoreholeModel from '../../../models/cs/borehole.model';

class BoreholeController {
  private model = new BoreholeModel();

  getBoreholesByStationsRange = async (req: Request, res: Response) => {
    try {
      const { stationStart, stationEnd, projectId } = req.params;

      const data = await this.model.getBoreholesByStationsRange(
        Number(stationStart),
        Number(stationEnd),
        projectId
      );
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getAllBoreholesWithVerificationPurpose = async (req: Request, res: Response) => {
    try {
      const data = await this.model.getBoreholesWithVerificationPurpose();
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default BoreholeController;
