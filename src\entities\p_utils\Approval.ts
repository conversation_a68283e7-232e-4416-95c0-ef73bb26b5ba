import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ApprovalLevel } from './ApprovalLevel';
import { ApprovalLevelInstance } from './ApprovalLevelInstance';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { ProjectActivity } from '../p_gen/Activity';

@Entity({ schema: 'p_utils' })
export class Approval {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  tableName?: string;

  @Column({ nullable: true })
  qurey?: string;

  @Column({ nullable: true })
  tableId?: string;

  @Column({ nullable: true })
  entity?: string;

  @Column({ nullable: true })
  details?: string;

  @Column({ nullable: true, type: 'uuid' })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  activityId?: string;

  @ManyToOne(() => ProjectActivity, { nullable: true }) 
  @JoinColumn({ name: 'activityId' })
  activity?: ProjectActivity;

  @Column({ nullable: true })
  currentLevelId?: string;

  @ManyToOne(() => ApprovalLevel, { nullable: true })
  @JoinColumn({ name: 'currentLevelId' })
  level?: ApprovalLevel;

  @Column({ nullable: true })
  approvalSetupId?: string;

  @Column({ nullable: true })
  projectId!: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  submittedBy?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  updatedBy?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => ApprovalLevelInstance, (instance) => instance.approval)
  approvers?: ApprovalLevelInstance[];
}
