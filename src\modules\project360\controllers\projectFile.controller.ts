import { Request, Response } from 'express';
import SftpClient from 'ssh2-sftp-client';
import { closeSftpConnection } from '../../../shared/utilities/sftpBlobAndFiles/sftpConnection';
import projectFilelModel from '../models/projectFile.model';
import filePermissionModel from '../models/filePermission.model';
import { AzureBlobStorageService } from '../../../shared/utilities/sftpBlobAndFiles/azureBlobStorageService';
import { File } from '@entities/p_gen/File';
import { FilePermission } from '@entities/p_gen/FilePermission';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class ProjectFileController {
  constructor() {}
  // find by id

  // async listSftpDirectory(req: Request, res: Response) {
  //   try {
  //     const sftp: SftpClient = await connectSftp(); // Establish the SFTP connection
  //     if (!req.query.path) {
  //       res.status(404).json({
  //         error: 'Invaild path',
  //       });
  //     }
  //     const remoteDir = `/root/project${req.query.path}`;
  //     const fileList = await sftp.list(remoteDir as string); // Use await to handle the promise
  //     closeSftpConnection(); // Close the connection after getting the directory listing
  //     fileList.map((value) => {
  //       (value as any).modifyTime = new Date(value.modifyTime);
  //       (value as any).accessTime = new Date(value.accessTime);
  //       if (value.type == 'd') {
  //         (value as any).type = 'folder';
  //       } else if (value.type == '-') {
  //         (value as any).type = 'file';
  //       }
  //       if (value.size > 1024 && value.size < 1048576) {
  //         const sizeInMegabytes = value.size / 1024;
  //         (value as any).size = `${sizeInMegabytes.toFixed(2)} KB`;
  //       } else if (value.size > 1048576 && value.size < **********) {
  //         const sizeInMB = value.size / (1024 * 1024);
  //         (value as any).size = `${sizeInMB.toFixed(2)} MB`;
  //       } else if (value.size > **********) {
  //         const sizeInGB = value.size / Math.pow(1024, 3);
  //         (value as any).size = `${sizeInGB.toFixed(2)} GB`;
  //       }
  //       return value;
  //     });

  //     res.json({
  //       isSucceed: true,
  //       data: fileList,
  //       msg: 'data found',
  //     }); // Send the directory listing as a JSON response
  //   } catch (error) {
  //     closeSftpConnection(); // Ensure the connection is closed in case of an error
  //     res.status(500).json({
  //       error: (error as any).message || 'Failed to list directory on SFTP server.',
  //     });
  //   }
  // }

  async listSftpDirectoryNew(req: Request, res: Response) {
    try {
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );
      const fileDetials = await projectFilelModel.findByProjectId(req.params.projectId);
      const filterChange: any[] = [];
      if (fileDetials.length > 0) {
        for (const value of fileDetials) {
          const file = await azureBlobStorageService.getBlobDetails(
            req.params.projectId,
            value.filePath
          );
          const final = ProjectFileController.extractBlobDetails(
            file,
            value.name || '',
            value.filePath
          );
          if (final.ogSize > 1024 && final.ogSize < 1048576) {
            const sizeInMegabytes = final.ogSize / 1024;
            (final as any).size = `${sizeInMegabytes.toFixed(2)} KB`;
          } else if (final.ogSize > 1048576 && final.ogSize < **********) {
            const sizeInMB = final.ogSize / (1024 * 1024);
            (final as any).size = `${sizeInMB.toFixed(2)} MB`;
          } else if (final.ogSize > **********) {
            const sizeInGB = final.ogSize / Math.pow(1024, 3);
            (final as any).size = `${sizeInGB.toFixed(2)} GB`;
          }
          final.type = 'file';
          (final as any).id = value.id;
          (final as any).fileType =
            value.filePath.split('.')[value.filePath.split('.').length - 1] || '';
          filterChange.push(final);
        }

        return res.json({
          isSucceed: true,
          data: filterChange,
          msg: 'data found',
        }); // Send the directory listing as a JSON response
      } else {
        return res.json({
          isSucceed: true,
          data: [],
          msg: 'data not found',
        });
      }
    } catch (error) {
      res.status(500).json({
        error: (error as any).message || 'Failed to list directory on SFTP server.',
      });
    }
  }

  static extractBlobDetails(blobDetails: any, name: string, path: string): BlobDetails {
    const type = blobDetails.contentType;
    const ogSize = blobDetails.contentLength;
    const lastModified = new Date(blobDetails.lastModified);
    const accessedOn = new Date(blobDetails.date);

    return { type, name, path, ogSize, lastModified, accessedOn };
  }

  async listByProjectId(req: Request, res: Response) {
    if (!req.query.path) {
      res.status(404).json({
        error: 'Invaild path',
      });
    }
    let remoteDir = `/${req.params.projectId}/projectfile`;
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    try {
      if (req.query.path != '/') {
        remoteDir = `${remoteDir}${req.query.path}/`;
      }

      const fileList = await azureBlobStorageService.listBlobs(
        req.params.projectId,
        '/',
        remoteDir
      );

      fileList.map((value) => {
        if (value.kind === 'blob') {
          (value as any).type = 'file';
        } else if (value.kind === 'prefix') {
          (value as any).type = 'folder';
        }

        const properties = (value as any).properties || (value as any).Properties;
        const contentLength = properties.contentLength || properties['Content-Length'];
        if (contentLength) {
          if (contentLength > 1024 && contentLength < 1048576) {
            const sizeInMegabytes = contentLength / 1024;
            (value as any).size = `${sizeInMegabytes.toFixed(2)} KB`;
          } else if (contentLength > 1048576 && contentLength < **********) {
            const sizeInMB = contentLength / (1024 * 1024);
            (value as any).size = `${sizeInMB.toFixed(2)} MB`;
          } else if (contentLength > **********) {
            const sizeInGB = contentLength / Math.pow(1024, 3);
            (value as any).size = `${sizeInGB.toFixed(2)} GB`;
          }
        }

        return value;
      });

      res.json({
        isSucceed: true,
        data: fileList,
        msg: 'data found',
      }); // Send the directory listing as a JSON response
    } catch (error) {
      closeSftpConnection(); // Ensure the connection is closed in case of an error
      res.status(500).json({
        error: (error as any).message || 'Failed to list directory on SFTP server.',
      });
    }
  }

  // async listSftpDirectoryByUser(req: Request, res: Response) {
  //   const sftp: SftpClient = await connectSftp(); // Establish the SFTP connection
  //   const proejctId = req.params.projectId;
  //   const userId = req.params.userId;
  //   if (!req.query.path) {
  //     res.status(404).json({
  //       error: 'Invaild path',
  //     });
  //   }

  //   const remoteDir = `/root/project${req.query.path}`;
  //   try {
  //     const userPermission = await filePermissionModel.findByUserIdAndProject(userId, proejctId);

  //     userPermission.filter((obj) => obj.file?.filePath?.startsWith(remoteDir));
  //     const fileAccess = userPermission.map((value) => {
  //       const fileNeeded = {
  //         name: value.file?.name,
  //         fileId: value.fileId,
  //         path: value.file?.filePath,
  //       };
  //       return fileNeeded;
  //     });
  //     const fileList = await sftp.list(remoteDir as string);
  //     closeSftpConnection(); // Close the connection after getting the directory listing
  //     // const fileAccessmap = new Map(fileAccess.map(obj => [obj.name, obj.fileId]));

  //     const filteFileList = fileList.filter((obj1) => {
  //       const tst = fileAccess.some((obj2) => obj2.name == obj1.name);
  //       return tst;
  //     });

  //     await Promise.all(
  //       filteFileList.map(async (value) => {
  //         await Promise.all(
  //           fileAccess.map(async (obj) => {
  //             if (value.name == obj.name && obj.fileId) {
  //               const user = await filePermissionModel.findPermissionAndUserByFileId(obj.fileId);
  //               (value as any).path = obj.path;
  //               (value as any).id = obj.fileId;
  //               (value as any).user = user;
  //             }
  //           })
  //         );
  //       })
  //     );

  //     filteFileList.map((value) => {
  //       (value as any).modifyTime = new Date(value.modifyTime);
  //       (value as any).accessTime = new Date(value.accessTime);
  //       if (value.type == 'd') {
  //         (value as any).type = 'folder';
  //       } else if (value.type == '-') {
  //         (value as any).type = 'file';
  //       }
  //       if (value.size > 1024 && value.size < 1048576) {
  //         const sizeInMegabytes = value.size / 1024;
  //         (value as any).size = `${sizeInMegabytes.toFixed(2)} KB`;
  //       } else if (value.size > 1048576 && value.size < **********) {
  //         const sizeInMB = value.size / (1024 * 1024);
  //         (value as any).size = `${sizeInMB.toFixed(2)} MB`;
  //       } else if (value.size > **********) {
  //         const sizeInGB = value.size / Math.pow(1024, 3);
  //         (value as any).size = `${sizeInGB.toFixed(2)} GB`;
  //       }
  //       return value;
  //     });

  //     res.json({
  //       isSucceed: true,
  //       data: filteFileList,
  //       msg: 'data found',
  //     }); // Send the directory listing as a JSON response
  //   } catch (error) {
  //     console.error(error);
  //     closeSftpConnection(); // Ensure the connection is closed in case of an error
  //     res.status(500).json({
  //       error: (error as any).message || 'Failed to list directory on SFTP server.',
  //     });
  //   }
  // }

  async createSftpDirectory(req: Request, res: Response) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const sftp = new SftpClient();

    try {
      return res.status(503).json({
        isSucceed: false,
        data: [],
        msg: 'Currently unavailable',
      });
    } catch (error) {
      closeSftpConnection();
      console.error(error);
      res.status(500).json({ error: 'Failed to create directory on SFTP server' });
    }
  }

  async addFile(req: Request, res: Response) {
    try {
      // getting value from request
      const { files } = req.body as { files: File[] };

      if (!files || files.length <= 0) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request body',
        });
      }

      // adding to database
      const fileData = await projectFilelModel.addFile(files);
      const filePermission: Partial<FilePermission>[] = fileData.map((file) => ({
        fileId: file.id,
        userId: (req as any).user.id,
        permission: 'Owner',
        createdBy: (req as any).user.name,
        updatedBy: (req as any).user.name,
      }));

      await filePermissionModel.addPermission(filePermission);
      // sending success response
      return res.status(200).json({
        isSucceed: true,
        data: fileData,
        msg: 'file added',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getPermissionByfileId(req: Request, res: Response) {
    try {
      const { id } = req.params;

      const data = await filePermissionModel.findByFileId(Number(id));

      res.json({
        isSucceed: true,
        data: data,
        msg: 'Permission found',
      });
    } catch (error) {
      console.error(error);
      res.status(500).json({ error: 'Failed to create directory on SFTP server' });
    }
  }
}

interface BlobDetails {
  type: string;
  name: string;
  path: string;
  ogSize: number;
  lastModified: Date;
  accessedOn: Date;
}

const projectFileController = new ProjectFileController();
export default projectFileController;
