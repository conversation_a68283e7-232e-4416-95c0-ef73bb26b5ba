import DCECoordinatesConfigModel from '../../../modules/project360/models/dceCoordinatesConfig.model';
import { convertLatLongToEastingNorthing } from './eastingAndNothingConversion';

export const convertLatAndLongToEastingAndNorthingOnGetDataByProjectId = async (
  data: any[],
  dceId: string,
  projectId: string
) => {
  try {
    const dceCoordinatesModel = new DCECoordinatesConfigModel();
    const dceCoordinatesData = await dceCoordinatesModel.getByDceAndProject(dceId, projectId);
    const restult = data.map((value) => {
      if ('latitude' in value && 'longitude' in value && !value.easting && !value.northing) {
        if (value.latitude && value.longitude && !dceCoordinatesData?.isLatLong) {
          const conversionResult: any = convertLatLongToEastingNorthing(
            value.latitude,
            value.longitude
          );
          if (conversionResult.northing && conversionResult.easting) {
            value.easting = conversionResult.easting;
            value.northing = conversionResult.northing;
          }
        }
      }
      return value;
    });
    return restult;
  } catch (error) {
    throw error;
  }
};
