import emailListModel from '../../../modules/project360/models/gen/emailList.model';
import ical, { ICalCalendarMethod, ICalAttendeeRole, ICalAttendeeStatus } from 'ical-generator';
import nodemailer from 'nodemailer';
import * as path from 'path';
import * as fs from 'fs/promises';
import moment from 'moment-timezone';

// Path to the SIH logo
const SIHLogoPath = path.join(process.cwd(), 'public', 'images', 'SIHLogo.png');

// Function to get the SIH logo as a base64 string
async function getSIHLogoBase64(): Promise<string> {
  try {
    const logoBuffer = await fs.readFile(SIHLogoPath);
    return logoBuffer.toString('base64');
  } catch (error) {
    console.error('Error reading SIH logo:', error);
    return ''; // Return empty string if logo can't be read
  }
}

interface CalendarEventOptions {
  summary: string;
  description: string;
  comments?: string; // Comments will be used as description if provided
  location?: string;
  start: Date;
  end: Date;
  timezone?: string; // Timezone for the event
  allDay?: boolean; // Whether this is an all-day event
  organizer: {
    name: string;
    email: string;
  };
  sequence: number;
  workPackageId?: string;
  isEdit?: boolean;
}

// Default event options if none are provided
const defaultEventOptions: CalendarEventOptions = {
  summary: 'Project360 Meeting',
  description: 'Project discussion and updates',
  start: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
  end: new Date(
    Date.now() + 24 * 60 * 60 * 1000 + 23 * 60 * 60 * 1000 + 59 * 60 * 1000 + 59 * 1000
  ), // Tomorrow end of day
  allDay: true, // Default to all-day events
  organizer: {
    name: 'Project360 System',
    email: process.env.SMTP_EMAIL || '<EMAIL>',
  },
  sequence: 0,
};

export async function sendCalendarInvite(
  emailListId: string,
  eventOptions?: Partial<CalendarEventOptions>,
  additionalToEmail?: string
): Promise<{ success: boolean; message: string } | Error> {
  try {
    // Get the SIH logo as a base64 string
    const logoBase64 = await getSIHLogoBase64();
    // Merge provided options with defaults
    const fullEventOptions: CalendarEventOptions = {
      ...defaultEventOptions,
      ...eventOptions,
    };

    // If comments are provided, use them as the description
    if (fullEventOptions.comments) {
      // Make sure comments are used as the description for both the email and the calendar event
      fullEventOptions.description = fullEventOptions.comments;
    }

    // Get email list from database using the provided emailListId
    const emailList = await emailListModel.findById(emailListId);

    if (!emailList || !emailList.recipient) {
      throw new Error('Email list not found or has no recipients');
    }

    // Check if there are any recipients
    const toRecipients = emailList.recipient.to || [];
    const ccRecipients = emailList.recipient.cc || [];
    const bccRecipients = emailList.recipient.bcc || [];

    if (toRecipients.length === 0 && ccRecipients.length === 0 && bccRecipients.length === 0) {
      throw new Error('Email list has no recipients (to, cc, or bcc)');
    }

    // Add additional email to toRecipients if provided and not already in any recipient list
    if (
      additionalToEmail &&
      !toRecipients.includes(additionalToEmail) &&
      !ccRecipients.includes(additionalToEmail) &&
      !bccRecipients.includes(additionalToEmail)
    ) {
      toRecipients.push(additionalToEmail);
    }

    // Format recipients for email
    const to = Array.isArray(toRecipients) ? toRecipients.join(',') : toRecipients;
    const cc = Array.isArray(ccRecipients) ? ccRecipients.join(',') : ccRecipients;
    const bcc = Array.isArray(bccRecipients) ? bccRecipients.join(',') : bccRecipients;

    // Create iCal event
    const calendar = ical({
      prodId: { company: 'smart-structures', product: 'project360' },
      name: 'Project360 Calendar',
      method: ICalCalendarMethod.REQUEST, // Add method REQUEST for better compatibility with calendar clients
    });

    // Prepare start and end dates with timezone information if available
    const startDate = fullEventOptions.start;
    const endDate = fullEventOptions.end;

    // Create event with timezone information
    const eventData: any = {
      uid:
        fullEventOptions.workPackageId ||
        `project360-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      summary: fullEventOptions.summary,
      description: fullEventOptions.description,
      location: fullEventOptions.location,
      organizer: {
        name: fullEventOptions.organizer.name,
        email: fullEventOptions.organizer.email,
      },
      allDay: fullEventOptions.allDay !== undefined ? fullEventOptions.allDay : true,
      sequence: fullEventOptions.sequence || 0,
    };
    eventData.uid = fullEventOptions.workPackageId;

    // If timezone is provided, use it for the event
    if (fullEventOptions.timezone) {
      // Use moment to create dates with timezone information
      eventData.start = moment(startDate).tz(fullEventOptions.timezone);
      eventData.end = moment(endDate).tz(fullEventOptions.timezone);
      eventData.timezone = fullEventOptions.timezone;
    } else {
      // If no timezone is provided, use the dates as is
      eventData.start = startDate;
      eventData.end = endDate;
    }

    // Add attendees to the event if there are recipients
    if (toRecipients.length > 0) {
      eventData.attendees = toRecipients.map((email: string) => ({
        email,
        rsvp: true,
        role: ICalAttendeeRole.REQ,
        status: ICalAttendeeStatus.NEEDSACTION,
      }));
    }

    // Add CC recipients as optional attendees if there are any
    if (ccRecipients.length > 0) {
      const optionalAttendees = ccRecipients.map((email: string) => ({
        email,
        rsvp: true,
        role: ICalAttendeeRole.OPT,
        status: ICalAttendeeStatus.NEEDSACTION,
      }));

      eventData.attendees = eventData.attendees
        ? [...eventData.attendees, ...optionalAttendees]
        : optionalAttendees;
    }
    // Create and add the event to the calendar
    const event = calendar.createEvent(eventData);
    event.uid(eventData.uid);

    // Generate iCal string
    const icalString = calendar.toString();

    // Create HTML body with styled template (blue header with logo)
    const htmlBody = `
      <html>
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${fullEventOptions.summary}</title>
      </head>
      <body style="font-family: Arial, sans-serif; color: #333; margin: 0; padding: 20px;">
        <table cellpadding="0" cellspacing="0" border="0" width="100%" style="max-width: 600px; margin: auto; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <tr>
            <td style="background-color: #078DEE; color: white; padding: 15px; border-radius: 5px 5px 0 0;">
              <table width="100%">
                <tr>
                  <td width="100" style="vertical-align: middle;">
                    ${
                      logoBase64
                        ? `<img src="data:image/png;base64,${logoBase64}" alt="Smart Structures Logo" style="max-height: 35px; max-width: 100px; background-color: white; padding: 4px; border-radius: 4px;">`
                        : ''
                    }
                  </td>
                  <td style="text-align: center; vertical-align: middle;">
                    <h2 style="color: white; margin: 0; padding: 10px 0;">${
                      fullEventOptions.summary
                    }</h2>
                  </td>
                  <td width="45"></td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td class="content" style="padding: 20px;">
              <div class="details">
                <p><strong>When:</strong> ${
                  fullEventOptions.allDay
                    ? `All day on ${fullEventOptions.start.toLocaleDateString()}`
                    : `${
                        fullEventOptions.timezone
                          ? moment(fullEventOptions.start)
                              .tz(fullEventOptions.timezone)
                              .format('MM-DD-YYYY hh:mm A')
                          : fullEventOptions.start.toLocaleString()
                      } - ${
                        fullEventOptions.timezone
                          ? moment(fullEventOptions.end)
                              .tz(fullEventOptions.timezone)
                              .format('MM-DD-YYYY hh:mm A')
                          : fullEventOptions.end.toLocaleString()
                      }`
                }</p>
                ${
                  fullEventOptions.location
                    ? `<p><strong>Where:</strong> ${fullEventOptions.location}</p>`
                    : ''
                }
                <p><strong>Description:</strong> <br>${fullEventOptions.description.replace(
                  /\n/g,
                  '<br>'
                )}</p>
              </div>

              <p>Please find the calendar invitation attached to this email. You can add this event to your calendar by opening the attachment.</p>
            </td>
          </tr>
          <tr>
            <td class="footer" style="padding: 20px; text-align: center;">
              <p>This invitation was sent by Smart Structures.</p>
              <p>If you have any questions, please contact the organizer.</p>
            </td>
          </tr>
        </table>
      </body>
      </html>
    `;

    // Create email transporter
    const mailTransporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: Number(process.env.SMTP_PORT),
      secure: false,
      auth: {
        user: process.env.SMTP_EMAIL,
        pass: process.env.SMTP_PASSWORD,
      },
      tls: {
        ciphers: 'SSLv3',
        rejectUnauthorized: false,
      },
    });

    // Send email with calendar attachment
    await mailTransporter.sendMail({
      from: process.env.SMTP_EMAIL,
      to: to,
      cc: cc || undefined,
      bcc: bcc || undefined,
      subject: fullEventOptions.summary,
      html: htmlBody,
      alternatives: [
        {
          contentType: `text/calendar; charset=UTF-8; method=${ICalCalendarMethod.REQUEST}`,
          content: icalString,
        },
      ],
      icalEvent: {
        filename: `${fullEventOptions.summary.replace(/[^a-zA-Z0-9-_]/g, '_')}${
          fullEventOptions.isEdit ? `_${fullEventOptions.sequence || 0}_updated` : ''
        }.ics`,
        method: ICalCalendarMethod.REQUEST,
        content: icalString,
      },
    });

    return { success: true, message: 'Calendar invitation sent successfully' };
  } catch (error) {
    console.error('Error sending calendar invite:', error);
    throw error as Error;
  }
}
