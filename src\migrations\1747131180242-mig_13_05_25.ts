import { MigrationInterface, QueryRunner } from "typeorm";

export class Mig1305251747131180242 implements MigrationInterface {
    name = 'Mig1305251747131180242'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "p_map"."layer" ALTER COLUMN "enableForMap" SET DEFAULT false`);
        await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_nuclear_gauge_test" ADD CONSTRAINT "FK_74c271e44a3f5a4ddd3fe45a2e8" FOREIGN KEY ("proctorId") REFERENCES "p_cs"."st_soil_proctor_test"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" ADD CONSTRAINT "FK_30b3af19f2990a5db17b710fb63" FOREIGN KEY ("proctorId") REFERENCES "p_cs"."st_soil_proctor_test"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" DROP CONSTRAINT "FK_30b3af19f2990a5db17b710fb63"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_nuclear_gauge_test" DROP CONSTRAINT "FK_74c271e44a3f5a4ddd3fe45a2e8"`);
        await queryRunner.query(`ALTER TABLE "p_map"."layer" ALTER COLUMN "enableForMap" SET DEFAULT true`);
    }

}
