import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { PanelInformation } from '../../../../../entities/p_cs/PanelInformation';
import PanelInformationController from '../../../controllers/cs/panelInformation/panelInformation.controller';

const router: Router = express.Router();

const GenricController = new CrudController<PanelInformation>(PanelInformation);
const controller = new PanelInformationController();

router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'panelInformation')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'panelInformation')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'panelInformation')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'panelInformation')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'panelInformation')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'panelInformation')
);
router.get(
  '/by/stations/:stationStart/:stationEnd',
  authenticateToken,
  controller.getPanelsByStartandEndStation
);

export default router;
