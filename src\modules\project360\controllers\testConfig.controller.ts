import { Request, Response } from 'express';
import testConfigModel from '../models/testConfig.model';
import sampleModel from '../models/sample.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';

class TestConfigController {
  constructor() {}

  findByProjectId = async (req: Request, res: Response) => {
    try {
      const testConfigData = await testConfigModel.findByProjectInOrderId(req.params.id);
      // getting the data from database with the given id

      // checking if data is found with the id
      if (testConfigData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testConfigData,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  findBySampleId = async (req: Request, res: Response) => {
    try {
      const sampleData = await sampleModel.findById(req.params.id);

      if (sampleData?.materialId && sampleData?.featureId && sampleData?.projectId) {
        const testConfigData = await testConfigModel.findByProjectMaterialFeature(
          sampleData?.projectId,
          sampleData?.materialId,
          sampleData?.featureId
        );
        // getting the data from database with the given id

        // checking if data is found with the id
        if (testConfigData) {
          const message = req.__('DataFoundMessage');
          // if true data will send as response
          return res.status(200).json({
            isSucceed: true,
            data: testConfigData,
            msg: message,
          });
        } else {
          const message = req.__('DataNotFoundMessage');
          // if false send error as response
          return res.status(200).json({
            isSucceed: true,
            data: [],
            msg: message,
          });
        }
      } else {
        const message = req.__('GetTestConfigError');
        return res.status(400).json({ isSucceed: false, data: [], msg: message });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  findBySampleIdAndTest = async (req: Request, res: Response) => {
    try {
      const sampleData = await sampleModel.findById(req.params.sampleId);

      if (sampleData?.materialId && sampleData?.featureId && sampleData?.projectId) {
        const testConfigData = await testConfigModel.findByProjectMaterialFeatureTest(
          sampleData?.projectId,
          sampleData?.materialId,
          sampleData?.featureId,
          req.params.testId
        );
        // getting the data from database with the given id

        // checking if data is found with the id
        if (testConfigData) {
          const message = req.__('DataFoundMessage');
          // if true data will send as response
          return res.status(200).json({
            isSucceed: true,
            data: testConfigData,
            msg: message,
          });
        } else {
          const message = req.__('DataNotFoundMessage');
          // if false send error as response
          return res.status(200).json({
            isSucceed: true,
            data: [],
            msg: message,
          });
        }
      } else {
        const message = req.__('GetTestConfigError');
        return res.status(400).json({ isSucceed: false, data: [], msg: message });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  findById = async (req: Request, res: Response) => {
    try {
      const testConfigData = await testConfigModel.findById(req.params.id);
      // getting the data from database with the given id

      // checking if data is found with the id
      if (testConfigData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testConfigData,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  addTestConfig = async (req: Request, res: Response) => {
    try {
      const { rule, ...testConfig } = req.body;
      const result = await testConfigModel.addTestConfig(testConfig, rule);
      const message = req.__('DataInputSuccess');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: result || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

const testConfigController = new TestConfigController();
export default testConfigController;
