import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import dceController from '../../controllers/meta/dce.contorller';
import { DataCaptureElements } from '../../../../entities/p_meta/DataCaptureElements';
import { commonMiddleware } from 'src/shared/middlewares/common/dceSubmitFileUpload.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const genericController = new CrudController<DataCaptureElements>(DataCaptureElements);

// Mount the userRouter for CRUD operations at /auth/user/crud

router.post('/', authenticateToken, (req, res) => genericController.create(req, res));
router.get('/', authenticateToken, dceController.getAll);
router.get('/types/by/:id', authenticateToken, dceController.getTypesById);
router.get('/by/activity/:id', authenticateToken, dceController.findByActivityId);
router.get('/by/entity/:id', authenticateToken, dceController.findByEntity);
router.get('/for/geojson', authenticateToken, dceController.findByGeoJson);
router.get('/:id', authenticateToken, genericController.findById);
router.get('/columns/by/dce/:id', authenticateToken, dceController.getColumnNameByDceId);
router.get('/get/relation/url/by/dce/:id', authenticateToken, dceController.getColumnNameByDceId);
router.put('/:id', authenticateToken, (req, res) => genericController.update(req, res));
router.post(
  '/submit/form/entity/:entity',
  authenticateToken,
  commonMiddleware,
  dceController.submitFormByDCEByEntity
);

router.put(
  '/edit/form/entity/:entity/:id',
  authenticateToken,
  commonMiddleware,
  dceController.editFormByDCEByEntity
);
router.post(
  '/submit/form/:dceId',
  authenticateToken,
  commonMiddleware,
  dceController.submitFormByDCE
);
router.post('/multiple/dce/submit', authenticateToken, dceController.multipleDCESubmit);
router.put('/edit/form/:dceId/:id', authenticateToken, dceController.editFormByDCE);
router.get('/get/validation/:entity/:id', authenticateToken, dceController.getValidationResult);
router.get(
  '/get/form/data/by/:dceId/:column/:value',
  authenticateToken,
  dceController.getFormByDCE
);

export default router;
