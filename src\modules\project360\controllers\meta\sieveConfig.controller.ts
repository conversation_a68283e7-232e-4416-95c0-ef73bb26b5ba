import sieveConfigModel from '@models/meta/sieveConfig.model';
import { Request, Response } from 'express';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class SieveConfigController {
  constructor() {}

  async findByMateriaIdAndTestMethodId(req: Request, res: Response) {
    try {
      const testMethodId = req.params.testMethodId;
      const materialId = req.params.materialId;
      const sieveData = await sieveConfigModel.getByMateriaIdAndTestMethodId(
        testMethodId,
        materialId
      );
      if (sieveData?.length > 0) {
        return res.status(200).json({
          isSucceed: true,
          data: sieveData,
          msg: 'Data found',
        });
      } else {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const sieveConfigController = new SieveConfigController();
export default sieveConfigController;
