import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';

import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from './Project';
import { DCEValidation } from './DCEValidation';
import { Route } from '../p_auth/Route';
import { Units } from '@entities/p_meta/Units';
import { DCEConfiguration } from '@entities/p_meta/AdminDCEConfiguration';

@Entity({ schema: 'p_gen', name: 'dce_configuration' })
export class dceConfiguration {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  adminDceConfigId?: string;

  @ManyToOne(() => DCEConfiguration, { nullable: true })
  @JoinColumn({ name: 'adminDceConfigId' })
  adminDceConfig?: DCEConfiguration;

  @Column({ type: 'varchar', nullable: true })
  columnName?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'varchar', nullable: true })
  alias?: string;

  @Column({ nullable: true })
  unitId?: string;

  @ManyToOne(() => Units, { nullable: true })
  @JoinColumn({ name: 'unitId' })
  units?: Units;

  @Column({ nullable: true })
  width?: number;

  @Column({ default: false, nullable: true })
  required?: boolean;

  @Column({ default: false, nullable: true })
  hide?: boolean;

  @Column({ default: false, nullable: true })
  isURL?: boolean;

  @Column({ default: true, nullable: true })
  editable?: boolean;

  @Column({ default: false, nullable: true })
  isRelation?: boolean;

  @Column({ nullable: true })
  routeId?: string;

  @ManyToOne(() => Route, { nullable: true })
  @JoinColumn({ name: 'routeId' })
  route?: Route;

  @Column({ nullable: true })
  type?: string;

  @Column({ nullable: true })
  order?: number;

  @Column({ nullable: true })
  decimalPointsNeeded?: number;

  @Column({ nullable: true })
  valueForSingleSelect?: string;

  @Column({ nullable: true, default: false })
  isMultiSelect?: boolean;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToOne(() => DCEValidation, (profile) => profile.dceConfig)
  validation?: DCEValidation;
}
