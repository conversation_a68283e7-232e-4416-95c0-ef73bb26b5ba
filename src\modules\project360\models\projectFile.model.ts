import { getManager } from 'typeorm';
import { File } from '../../../entities/p_gen/File';

class ProjectFilelModel {
  constructor() {}

  async findByProjectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(File)
        .find({
          where: { projectId: projectId, isDelete: false },
        });
    } catch (error) {
      throw error;
    }
  }

  async findById(id: string) {
    try {
      return await getManager()
        .getRepository(File)
        .findOne({
          where: { id, isDelete: false },
        });
    } catch (error) {
      throw error;
    }
  }

  async addFile(newFile: File[]) {
    try {
      const projectFileRepository = getManager().getRepository(File);
      const addedAttachment = projectFileRepository.create(newFile);
      await projectFileRepository.save(addedAttachment);
      return addedAttachment;
    } catch (error) {
      throw error;
    }
  }
}

const projectFilelModel = new ProjectFilelModel();
export default projectFilelModel;
