import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
// import crudDriver from "../../generic/crudDriver.controller";
import CrudController from '../../generic/crudDriver.controller';
import { Feature } from '../../../entities/p_gen/Feature';
import featureController from '../controllers/feature.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const FeatureDetailController = new CrudController<Feature>(Feature);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.put('/:id', authenticateToken, (req, res) => FeatureDetailController.update(req, res));
router.delete('/:id', authenticateToken, (req, res) =>
  FeatureDetailController.softDelete(req, res)
);
router.get('/:id', authenticateToken, FeatureDetailController.findById);
router.post('/', authenticateToken, featureController.addFeatrue);
router.get('/by/project/:id', authenticateToken, featureController.findByProjectId);
router.get(
  '/dropdown/formatted/by/project/:id',
  authenticateToken,
  featureController.formattedFeatureData
);
router.get(
  '/by/project/plain/:id',
  authenticateToken,
  featureController.findByProjectWithoutFormatId
);
router.delete('/:id', authenticateToken, (req, res) =>
  FeatureDetailController.softDelete(req, res)
);

export default router;
