import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  PrimaryGeneratedColumn,
  OneToMany,
} from 'typeorm';

import SubmittalVersion from './SubmittalVersion';
import SubmittalDocument from './SubmittalDocument';
import { ActionCode } from '../p_domain/ActionCode';

@Entity({ schema: 'p_gen' })
class SubmittalRevision {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  version!: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, (submittal) => submittal.revision)
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  ownerCodeId?: string;

  @ManyToOne(() => ActionCode, { nullable: true }) 
  @JoinColumn({ name: 'ownerCodeId' })
  ownerCode?: ActionCode;

  @Column({ nullable: true })
  reviewerComments?: string;

  @Column({ nullable: true })
  submissionDate?: Date;

  @Column({ nullable: true })
  responseDate?: Date;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  internalCodeId?: string;

  @ManyToOne(() => ActionCode, { nullable: true }) 
  @JoinColumn({ name: 'internalCodeId' })
  internalCode?: ActionCode;

  @Column({ nullable: true })
  description?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => SubmittalDocument, (document) => document.revision)
  documents?: SubmittalDocument[];
}

export default SubmittalRevision;
