import express, { Router } from 'express';

import { Surveying } from '@entities/p_cs/Surveying';
import CrudController from 'src/modules/generic/crudDriver.controller';
import { authenticateToken } from 'src/shared/middlewares/auth.middleware';
import importController from '@controllers//import.controller';
const router: Router = express.Router();

const GenricController = new CrudController<Surveying>(Surveying);

router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'surveying')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'surveying')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'surveying')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'surveying')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'surveying'));
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'surveying')
);
router.get('/get/by/project/:entity/:id', authenticateToken, importController.getData);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'surveying')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'surveying')
);

export default router;
