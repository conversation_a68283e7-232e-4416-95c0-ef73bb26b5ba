import ExcelJS from 'exceljs';

export function addLookupsToWorkbook(
  workbook: ExcelJS.Workbook,
  allColumnsOptions: Record<string, string[]>
) {
  //Find whether the existing Lookups sheet
  let lookupsSheet = workbook.getWorksheet('Lookups');
  // Create the sheet if it doesn't exist
  if (!lookupsSheet) {
    lookupsSheet = workbook.addWorksheet('Lookups');
  }
  let lookupColumnIndex = 1;
  const lookupRangesByField: Record<string, string> = {};
  const normalizedAllColumnOptions: Record<string, string[]> = {};
  for (const [fieldKey, options] of Object.entries(allColumnsOptions)) {
    const normalizedField = fieldKey.replace(/\s+/g, '').toLowerCase();
    normalizedAllColumnOptions[normalizedField] = options;
  }

  for (const [field, options] of Object.entries(normalizedAllColumnOptions)) {
    if (options.length === 0) {
      lookupColumnIndex++;
      continue;
    }
    // Generate Excel column letter (A, B, C, ...) from the index
    const colLetter = String.fromCharCode(64 + lookupColumnIndex);
    // Set the field name as the header in the Lookups sheet
    lookupsSheet.getCell(`${colLetter}1`).value = field;
    // Add each option under the corresponding field column
    options.forEach((option, rowIndex) => {
      lookupsSheet!.getCell(`${colLetter}${rowIndex + 2}`).value = option;
    });
    // Store the Excel range for this field to use in data validation (e.g., "Lookups!$A$2:$A$10")
    lookupRangesByField[field] = `Lookups!$${colLetter}$2:$${colLetter}$${options.length + 1}`;
    lookupColumnIndex++;
  }
  lookupsSheet.state = 'veryHidden';
  return lookupRangesByField;
}
