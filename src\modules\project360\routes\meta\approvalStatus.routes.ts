import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import approvalStatusController from '../../controllers/meta/approvalStatus.controller';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';

const router: Router = express.Router();

const CrudFunctionController = new CrudController<ApprovalStatus>(ApprovalStatus);

router.post('/', authenticateToken, (req, res) =>
  CrudFunctionController.create(req, res, 'dceRelation')
);
router.put('/:id', authenticateToken, (req, res) =>
  CrudFunctionController.update(req, res, 'dceRelation')
);
router.get('/:id', authenticateToken, CrudFunctionController.findById);
router.get('/', authenticateToken, approvalStatusController.getAllStatus);

export default router;
