import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { OverbreakType } from '../p_domain/OverbreakType';
import { Movement } from '../p_domain/Movement';
import { FlyRockAssessment } from '../p_domain/FlyRockAssessment';
import { BlastReport } from './BlastReport';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class BlastEvaluation extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Blast Report Id', // this column will be entered by the
      fieldName: 'blastReportId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.blast_report WHERE "designBlastId" = $1`,
      getListQuery:
        'SELECT id,"designBlastId" as name FROM p_cs.blast_report WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 20;',
      listParams: 'id',
      listName: 'blastReportList',
    },
  })
  @Column({ nullable: true })
  blastReportId?: string;

  @ManyToOne(() => BlastReport, { nullable: true })
  @JoinColumn({ name: 'blastReportId' })
  blastReport?: BlastReport;

  @ColumnInfo({
    customData: {
      name: 'Blast Evaluation Date',
      fieldName: 'evaluationDate',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  evaluationDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Overbreak Type',
      fieldName: 'overbreakTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.overbreak_type WHERE name = $1',
      getListQuery:
        'Select id, alias as name FROM p_domain.overbreak_type WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'overbreakTypeList',
    },
  })
  @Column({ nullable: true })
  overbreakTypeId?: string;

  @ManyToOne(() => OverbreakType, { nullable: true })
  @JoinColumn({ name: 'overbreakTypeId' })
  overbreakType?: OverbreakType;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @ColumnInfo({
    customData: {
      name: 'Minimum Observed Overbreak',
      fieldName: 'minimumObservedOverbreak',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumObservedOverbreak?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Observed Overbreak',
      fieldName: 'maximumObservedOverbreak',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumObservedOverbreak?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Fragmentation Size',
      fieldName: 'minimumFragmentationSize',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumFragmentationSize?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Fragmentation Size',
      fieldName: 'maximumFragmentationSize',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumFragmentationSize?: number;

  @ColumnInfo({
    customData: {
      name: 'Largest Fragmentation Size',
      fieldName: 'largestFragmentationSize',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  largestFragmentationSize?: number;

  @ColumnInfo({
    customData: {
      name: 'Movement Of Mass',
      fieldName: 'movementOfMassId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.movement WHERE name = $1',
      getListQuery: 'Select id, alias as name FROM p_domain.movement WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'movementList',
    },
  })
  @Column({ nullable: true })
  movementOfMassId?: string;

  @ManyToOne(() => Movement, { nullable: true })
  @JoinColumn({ name: 'movementOfMassId' })
  movement?: Movement;

  @ColumnInfo({
    customData: {
      name: 'Movement Compass Direction',
      fieldName: 'movementDirection',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  movementDirection?: string;

  @ColumnInfo({
    customData: {
      name: 'Flyrock Condition',
      fieldName: 'flyrockAssessConditionId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.fly_rock_assessment WHERE name = $1',
      getListQuery:
        'Select id, alias as name FROM p_domain.fly_rock_assessment WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'flyRockAssessmentList',
    },
  })
  @Column({ nullable: true })
  flyrockAssessConditionId?: string;

  @ManyToOne(() => FlyRockAssessment, { nullable: true })
  @JoinColumn({ name: 'flyrockAssessConditionId' })
  flyRockAssessment?: FlyRockAssessment;

  @ColumnInfo({
    customData: {
      name: 'Flyrock Distance',
      fieldName: 'flyrockDistance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flyrockDistance?: number;

  @ColumnInfo({
    customData: {
      name: 'Misfires (Yes/No)',
      fieldName: 'misfires',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  misfires?: string;

  @ColumnInfo({
    customData: {
      name: 'No. of Holes Misfired',
      fieldName: 'misfiredHoles',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  misfiredHoles?: string;

  @ColumnInfo({
    customData: {
      name: 'Misfired Region',
      fieldName: 'misfiredPart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  misfiredPart?: string;

  @ColumnInfo({
    customData: {
      name: 'Technical Reason for Misfire',
      fieldName: 'misfireReason',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  misfireReason?: string;

  @ColumnInfo({
    customData: {
      name: 'Rifling (Yes/No)',
      fieldName: 'rifling',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  rifling?: string;

  @ColumnInfo({
    customData: {
      name: 'No. of Holes Experienced Rifling',
      fieldName: 'rifleHoles',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  rifleHoles?: number;

  @ColumnInfo({
    customData: {
      name: 'Predicted PPV Exceeds Actual PPV (Yes/No)',
      fieldName: 'seismographExceedance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  seismographExceedance?: string;

  @ColumnInfo({
    customData: {
      name: 'Blaster In-Charge',
      fieldName: 'blasterInCharge',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blasterInCharge?: string;

  @ColumnInfo({
    customData: {
      name: 'Blasting Specialist',
      fieldName: 'blastingSpecialist',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastingSpecialist?: string;

  @ColumnInfo({
    customData: {
      name: 'Blasting QC Verifier',
      fieldName: 'blastingQC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastingQC?: string;

  @ColumnInfo({
    customData: {
      name: 'Joint Post Blast Evaluator',
      fieldName: 'blastEvaluator',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastEvaluator?: string;

  @ColumnInfo({
    customData: {
      name: 'USACE Geologist',
      fieldName: 'geologistUSACE',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  geologistUSACE?: string;

  @ColumnInfo({
    customData: {
      name: 'USACE QA Verifier',
      fieldName: 'qaUSACE',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaUSACE?: string;

  @ColumnInfo({
    customData: {
      name: 'Other Assessment Details',
      fieldName: 'assessment',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  assessment?: string;

  @ColumnInfo({
    customData: {
      name: 'proposedChanges',
      fieldName: 'proposedChanges',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  proposedChanges?: string;

  @ColumnInfo({
    customData: {
      name: 'Seismic Data Reviewer',
      fieldName: 'seismicDataReviewer',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  seismicDataReviewer?: string;

  @ColumnInfo({
    customData: {
      name: 'Blast Video Reviewer',
      fieldName: 'blastVideoReviewer',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastVideoReviewer?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
