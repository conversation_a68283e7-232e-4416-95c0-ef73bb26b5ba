import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from './Project';
import { Purpose } from '../p_meta/Purpose';
import { SandConeInfo } from '../p_domain/SandConeInfo';

@Entity({ schema: 'p_gen' })
export class ProjectSandCones {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  sandConeId?: string;

  @ManyToOne(() => SandConeInfo, { nullable: true }) 
  @JoinColumn({ name: 'sandConeId' })
  sandConeInfo?: SandConeInfo;

  @Column()
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true }) 
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
