import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import EventLogModel from '../../models/workPackage/eventLog.model';

class EventLogController {
  async getByWorkPackageActivityId(req: Request, res: Response) {
    try {
      const model = new EventLogModel();
      const data = await model.getByWorkPackageActivityIKd(req.params.id);

      return res.json({ isSucceed: true, data, msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getByUser(req: Request, res: Response) {
    try {
      const model = new EventLogModel();
      const data = await model.getByUser(req.params.id);

      return res.json({ isSucceed: true, data, msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async checkByUser(req: Request, res: Response) {
    try {
      const model = new EventLogModel();
      const data = await model.getByUser(req.params.id);
      const out: { isCheckIn: boolean; checkInId: string; startTime?: Date } = {
        isCheckIn: true,
        checkInId: '',
      };
      if (data.length > 0) {
        if (!data[0].endTime) {
          out.isCheckIn = false;
          out.checkInId = data[0].id;
          out.startTime = data[0].startTime;
        }
      }

      return res.json({ isSucceed: true, data: out, msg: 'Data found' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addEventLog(req: Request, res: Response) {
    try {
      const model = new EventLogModel();
      const user = (req as any).user;
      const eventLogData = await model.getByUser(user.id, req.body?.projectId);
      let newLog = false;
      if (eventLogData.length <= 0) {
        newLog = true;
      }
      const data = await model.addEventLog(req.body, newLog);
      return res.json({ isSucceed: true, data: data || [], msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default EventLogController;
