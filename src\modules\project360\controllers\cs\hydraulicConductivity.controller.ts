import { Request, Response } from 'express';
import hydraulicConductivityModel from '../../models/cs/hydraulicConductivity.model';
import { HCTestCalculationObj } from '../../../../shared/utilities/cs/testCalculationFunctions';

class HydraulicConductivityController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const hydraulicConductivityData = await hydraulicConductivityModel.findById(req.params.id);
      // checking if data is found with the id
      if (hydraulicConductivityData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: hydraulicConductivityData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async create(req: Request, res: Response) {
    try {
      const {
        initialDiameter,
        initialLength,
        initialWaterContent,
        finalDiameter,
        initialMass,
        finalLength,
        finalMass,
        finalWaterContent,
      } = req.body;
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }

      if (
        !initialDiameter ||
        !initialLength ||
        !initialWaterContent ||
        !finalDiameter ||
        !initialMass ||
        !finalLength ||
        !finalMass ||
        !finalWaterContent
      ) {
        const message = req.__('InvalidInputDataError');
        return res.status(500).json({ isSucceed: false, data: [], msg: message });
      }

      const initialArea = HCTestCalculationObj.CalculateAreaByDiameter(Number(initialDiameter));
      req.body.initialArea = initialArea;
      req.body.specimenArea = initialArea;

      const initialVolume = HCTestCalculationObj.CalculateVolume(initialArea, initialLength);
      req.body.initialVolume = initialVolume;
      req.body.specimenLength = initialLength;

      const initialWetUnitWgt = HCTestCalculationObj.CalculateWetUnitWgt(
        initialMass,
        initialVolume
      );
      req.body.initialWetUnitWgt = initialWetUnitWgt;

      const initialDryUnitWgt = HCTestCalculationObj.CalculateDryUnitWgt(
        initialWetUnitWgt,
        initialWaterContent
      );

      req.body.initialDryUnitWgt = initialDryUnitWgt;

      const finalArea = HCTestCalculationObj.CalculateAreaByDiameter(Number(finalDiameter));
      req.body.finalArea = finalArea;

      const finalVolume = HCTestCalculationObj.CalculateVolume(finalArea, finalLength);
      req.body.finalVolume = finalVolume;

      const finalWetUnitWgt = HCTestCalculationObj.CalculateWetUnitWgt(finalMass, finalVolume);
      req.body.finalWetUnitWgt = finalWetUnitWgt;

      const finalDryUnitWgt = HCTestCalculationObj.CalculateDryUnitWgt(
        finalWetUnitWgt,
        finalWaterContent
      );
      req.body.finalDryUnitWgt = finalDryUnitWgt;

      // getting the data from database with the given id
      const hydraulicConductivityData = await hydraulicConductivityModel.add(req.body);
      // checking if data is found with the id
      // if true data will send as response
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: hydraulicConductivityData,
        msg: message,
      });
    } catch (error) {
      const message = req.__('DataInputFail');
      // error response
      return res
        .status(500)
        .json({ isSucceed: false, data: [], msg: (error as any).message || message });
    }
  }
}

const hydraulicConductivityController = new HydraulicConductivityController();
export default hydraulicConductivityController;
