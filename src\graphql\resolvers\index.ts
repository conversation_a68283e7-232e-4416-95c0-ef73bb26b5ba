// Import all resolvers
import { compressiveStrengthResolvers } from './stCompressiveStrength';
import { DateScalar } from '../scalars/date';
import { concreteBatchTicketResolvers } from './concreteBatchTicket';
import { pvCLSMPlacementResolvers } from './pvCLSMPlacement';
import { pvCutMasterResolvers } from './pvCutMaster';

// Combine all resolvers
export const resolvers = {
  Date: DateScalar, // Add the Date scalar resolver
  Query: {
    ...compressiveStrengthResolvers.Query,
    ...concreteBatchTicketResolvers.Query,
    ...pvCLSMPlacementResolvers.Query,
    ...pvCutMasterResolvers.Query,
  },
  Mutation: {
    // Add mutation resolvers here when needed
  },
};
