import { gql } from 'apollo-server-express';

// Define the base schema with Query and Mutation types
const baseTypeDefs = gql`
  type Query {
    _empty: String
  }

  type Mutation {
    _empty: String
  }
`;

// Import all schema definitions
import { typeDefs as compressiveStrengthTypeDefs } from './stCompressiveStrength';
import { typeDefs as concreteBatchTicketTypeDefs } from './concreteBatchTicket';
import { typeDefs as pvCLSMPlacementTypeDefs } from './pvCLSMPlacement';
import { typeDefs as pvCutMasterTypeDefs } from './pvCutMaster';

// Combine all schema definitions
export const typeDefs = [
  baseTypeDefs,
  compressiveStrengthTypeDefs,
  concreteBatchTicketTypeDefs,
  pvCLSMPlacementTypeDefs,
  pvCutMasterTypeDefs,
];
