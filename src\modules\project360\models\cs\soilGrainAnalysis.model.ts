import { getManager } from 'typeorm';
import { StSoilGrainAnalysis } from '../../../../entities/p_cs/StSoilGrainAnalysis';

class SoilGrainAnalysisModel {
  constructor() {}

  async findById(SampleId: string) {
    try {
      return await getManager()
        .getRepository(StSoilGrainAnalysis)
        .findOne({
          where: { id: SampleId, isDelete: false },
          relations: ['worksheet', 'hydrometer', 'breakerSieveGrainSize', 'hydrometer.readingTime'],
        });
    } catch (error) {
      throw error;
    }
  }

  async add(newSoilGrainAnalysist: StSoilGrainAnalysis) {
    try {
      const soilGrainAnalysisRepository = getManager().getRepository(StSoilGrainAnalysis);
      const addedSoilGrainAnalysis = soilGrainAnalysisRepository.create(newSoilGrainAnalysist);
      await soilGrainAnalysisRepository.save(addedSoilGrainAnalysis);
      return addedSoilGrainAnalysis;
    } catch (error) {
      throw error;
    }
  }
}

const soilGrainAnalysisModel = new SoilGrainAnalysisModel();
export default soilGrainAnalysisModel;
