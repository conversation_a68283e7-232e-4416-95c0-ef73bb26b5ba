import { BaseEntityWithAudit } from '@entities/common/AuditColumns';
import { Entity, Column, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Material } from './Material';
import { TestMethod } from './TestMethod';
import { SieveSize } from '@entities/p_domain/SieveSize';

@Entity({ schema: 'p_meta' })
export class SieveConfig extends BaseEntityWithAudit {
  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => Material, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: Material;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column()
  sieveSizeId!: string;

  @ManyToOne(() => SieveSize)
  @JoinColumn({ name: 'sieveSizeId' })
  sieveSize!: SieveSize;
}
