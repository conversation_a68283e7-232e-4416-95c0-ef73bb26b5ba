const qcReportQuery = [
  {
    name: 'CLSM Testing Summary',
    query: `
SELECT s."testNo" AS "CT#", -- Test number from p_cs.sample
     cc."castDate" AS "Cast Date", -- Cast date from p_cs.st_compressive_strength,
     cbt."ticketNumber" AS "Batch Ticket", -- Batch ticket from p_cs.concrete_batch_ticket
     cs."slumpAvg" AS "Flow (Inches)", -- Slump average from p_cs.st_concrete_slump
     cd."density" AS "Unit Weight (pcf)", -- Density from p_cs.st_concrete_density
     cd."yield" AS "Yield (CY)", -- Yield from p_cs.st_concrete_density
     cd."relativeYield" AS "Relative Yield", -- Relative yield from p_cs.st_concrete_density
     cd."cementContent" AS "Cement Content (lbs/CY)", -- Cement content from p_cs.st_concrete_density
     cd."airContent" AS "Air Content %", -- Air content from p_cs.st_concrete_density
     cc."dateTested" AS "Test Date", -- Test date from p_cs.st_compressive_strength
     cc."specimenAge" AS "Sample Age (days)", -- Sample age from p_cs.st_compressive_strength
    CASE WHEN cc."specimenAge" <= 7 THEN cc."compressiveStrength" END AS "Compressive Strength (psi)(<= 7 Days)", -- Compressive strength for <= 7 days
    CASE WHEN cc."specimenAge" = 14 THEN cc."compressiveStrength" END AS "Compressive Strength (psi)(< 14 Days)", -- Compressive strength for < 14 days
    CASE WHEN cc."specimenAge" >= 28 THEN cc."compressiveStrength" END AS "Compressive Strength (psi)(< 28 Days)" -- Compressive strength for < 28 days
FROM
    p_cs.sample s
-- Left join with p_cs.st_concrete_slump to get slump data
LEFT JOIN
    p_cs.st_concrete_slump cs ON s.id = cs."sampleId"
-- Left join with p_cs.concrete_batch_ticket to get batch ticket
LEFT JOIN
    p_cs.concrete_batch_ticket cbt ON cs."batchId" = cbt.id
-- Left join with p_cs.st_concrete_density to get density data
LEFT JOIN
    p_cs.st_concrete_density cd ON s.id = cd."sampleId"
-- Left join with p_cs.st_compressive_strength to get compressive strength data
LEFT JOIN
    p_cs.st_compressive_strength cc ON s.id = cc."sampleId"
-- Left join with p_domain.sample_status to get status
WHERE
    s."materialId" = 'd6070c09-d40b-4010-bcb1-0a13204ec094' 
AND cc."dateTested" BETWEEN {from} AND {to}
AND s."projectId" = {projectId}
ORDER BY 
    s."testNo";`,
  },
  {
    name: 'COW Verification Borings',
    query: `
SELECT
    ss."name" AS "Structure #",  -- Source name from p_gen.sample_source
    bh."boreholeNumber" AS "Verification Boring ID",  -- Borehole number from p_cs.borehole
    ucs."testNo" AS "Client Test #",  -- Test number from p_qms.st_soil_ucs
    sp."label" AS "Sample #",  -- Specimen number from p_cs.sample_specimen
    bh.northing AS "Northing",  -- Northing from p_cs.borehole
    bh.easting AS "Easting",  -- Easting from p_cs.borehole
    bh.station AS "Station",  -- Station from p_cs.borehole
    bh."drillStartTime" AS "Drilling Start Date",  -- Drill start time from p_cs.borehole
    bh."drillEndTime" AS "Drilling End Date",  -- Drill end time from p_cs.borehole
    bh.orientation AS "Hole Orientation (Vertical/Inclined)",  -- Orientation from p_cs.borehole
    bh.angle AS "Inclination (Degrees)",  -- Angle from p_cs.borehole
    bh."holeTopElevation" AS "Platform/Grade Elev. (NAVD88)",  -- Hole top elevation from p_cs.borehole
    cm."uniqueLabel" AS "Cut ID",  -- Cut ID from p_cs.pv_cut_master
    cm."mixDesign" AS "Mix No.",  -- Mix design from p_cs.pv_cut_master
    ci."panelStartDate" AS "Date Cut Placed",  -- Panel start date from p_cs.pv_cut_information
    s."sampledDate" AS "UCS Sampled Date",  -- Sampled date from p_cs.sample
    ucs."dateTested" AS "UCS Testing Date",  -- Date tested from p_qms.st_soil_ucs
    ucs."specimenAge" AS "UCS Testing Age (days)",  -- Specimen age from p_qms.st_soil_ucs
    ucs."unconfinedCompressiveStrength" AS "UCS (psi)",  -- Unconfined compressive strength from p_qms.st_soil_ucs
    ucs."waterContent" AS "Moisture Content (%)",  -- Water content from p_qms.st_soil_ucs
    ucs."wetDensity" AS "Wet Density (pcf)",  -- Wet density from p_qms.st_soil_ucs
    ucs."dryDensity" AS "Dry Density (pcf)",  -- Dry density from p_qms.st_soil_ucs
    NULL AS "10-Point Moving Average",  -- Placeholder (not in database)
    NULL AS "RHT Testing Date (Completion)",  -- Placeholder (not in database)
    NULL AS "Rising Head Permeability, k (cm/s)",  -- Placeholder (not in database)
    NULL AS "Backfill Date",  -- Placeholder (not in database)
    NULL AS "Water level prior to Decommissioning",  -- Placeholder (not in database)
    bh."comments" AS "Remarks"
FROM
    p_cs.sample s
    -- Left join with p_gen.sample_source to get structure #
    LEFT JOIN p_gen.sample_source ss ON s."sourceId" = ss.id
    -- Left join with p_cs.borehole to get borehole details
    LEFT JOIN p_cs.borehole bh ON s."boreholeId" = bh.id
    -- Left join with p_qms.st_soil_ucs to get test details (ensures only samples with UCS data are included)
    LEFT JOIN p_qms.st_soil_ucs ucs ON s.id = ucs."sampleId"
    -- Left join with p_cs.sample_specimen to get specimen number
    LEFT JOIN p_cs.sample_specimen sp ON s.id = sp."sampleId"
    -- Left join with p_cs.pv_cut_information to get cut details
    LEFT JOIN p_cs.pv_cut_information ci ON s."boreholeId" = ci."cutMasterId"
    -- Left join with p_cs.pv_cut_master to get cut ID and mix design
    LEFT JOIN p_cs.pv_cut_master cm ON ci."cutMasterId" = cm.id
WHERE
    s."materialId" = 'c3e4f092-4fe3-4393-bf28-12d5acea863a'
AND ucs."dateTested" BETWEEN {from} AND {to}
AND s."projectId" = {projectId};
    `,
  },

  {
    name: 'COW Wet Grab Samples',
    query: `
SELECT 
    ROW_NUMBER() OVER (ORDER BY s."testNo", sp."label") AS "S. No",
    sp."label" AS "Sample Unique Label",
    'Post Placement' AS "Sample Type",
    st."name" AS "Sample Size (D * L) (in.)",
    ucs."castDate" AS "Date Cast",
    cm."uniqueLabel" AS "Cut Unique Label",
    cm."mixDesign" AS "Mix No.",
    cm."panelStartDate" AS "Date Cut Placed",
    s."testNo" AS "Client Test #",
    s.northing AS "Northing",
    s.easting AS "Easting",
    s.station AS "Station",
    cm."platformLevel" AS "Platform/Grade Elev. (NAVD88)",
    ucs."dateTested" AS "Testing Date",
    s."depth" AS "Sample Depth (ft)",
    s."elevation" AS "Sample Elev. (ft)",
    ucs."specimenAge" AS "Testing Age (days)",
    ucs."unconfinedCompressiveStrength" AS "UCS (psi)",
    ucs."waterContent" AS "Moisture Content (%)",
    ucs."wetDensity" AS "Wet Density (pcf)",
    ucs."dryDensity" AS "Dry Density (pcf)",
    NULL AS "Permeability, k (cm/s)",
    ucs."comments" AS "Remarks"
FROM 
    p_cs.sample s
JOIN 
    p_cs.sample_specimen sp ON s.id = sp."sampleId"
LEFT JOIN 
    p_qms.st_soil_ucs ucs ON sp.id = ucs."specimenId"
LEFT JOIN 
    p_domain.specimen_type st ON sp."specimenTypeId" = st.id
LEFT JOIN 
    p_cs.pv_cut_master cm ON s."panelNumberId" = cm.id
WHERE 
    s."materialId" = 'c3af1409-9b99-4188-89ca-eba7b52cf62f'
AND 
    ucs."dateTested" BETWEEN {from} AND {to}
AND 
    s."projectId" = {projectId}
ORDER BY 
    s."testNo", sp."label";
    `,
  },
];

export { qcReportQuery };
