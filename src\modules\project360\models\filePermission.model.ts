import { getManager, getRepository } from 'typeorm';
import { FilePermission } from '../../../entities/p_gen/FilePermission';

class FilePermissionModel {
  constructor() {}
  async findByUserIdAndProject(userId: string, projectId: string) {
    try {
      // return await getManager()
      //   .getRepository(Feature)
      //   .find({ where: { proejctId: projectId } });

      const filePermissions = await getManager()
        .getRepository(FilePermission)
        .createQueryBuilder('filePermission')
        .innerJoinAndSelect('filePermission.file', 'file')
        .where('filePermission.userId = :userId', { userId: userId })
        .andWhere('file.projectId = :projectId', { projectId: projectId }) // Add this line
        .andWhere('filePermission.isDelete = :isDelete', { isDelete: false })
        .getMany();

      return filePermissions;
    } catch (error) {
      throw error;
    }
  }

  async addPermission(newFile: Partial<FilePermission>[]) {
    try {
      const projectFileRepository = getManager().getRepository(FilePermission);
      const addedAttachment = projectFileRepository.create(newFile);
      await projectFileRepository.save(addedAttachment);
      return addedAttachment;
    } catch (error) {
      throw error;
    }
  }

  // async findPermissionAndUserByFileId(fileId: string) {
  //   try {
  //     // const filePermissionRepository = getRepository(FilePermission);

  //     // const filePermissions = await filePermissionRepository
  //     //   .createQueryBuilder('filePermission')
  //     //   .innerJoinAndSelect('filePermission.user', 'user')
  //     //   .where('filePermission.fileId = :fileId', { fileId })
  //     //   .andWhere('filePermission.isDelete = :isDelete', { isDelete: false })
  //     //   .select([
  //     //     'filePermission.permission',
  //     //     'user.email',
  //     //     'user.id',
  //     //     'user.firstName',
  //     //     'user.lastName',
  //     //   ])
  //     //   .getMany();

  //     // return filePermissions.map((filePermission) => {
  //     //   // const userData = filePermission.user;
  //     //   const userDataWithPermission = {
  //     //     ...userData,
  //     //     permission: filePermission.permission,
  //     //   };
  //     //   return userDataWithPermission;
  //     // });
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async findByFileId(fileId: number) {
    try {
      const filePermissionRepository = getRepository(FilePermission);

      const filePermissions = await filePermissionRepository
        .createQueryBuilder('filePermission')
        .innerJoinAndSelect('filePermission.user', 'user')
        .where('filePermission.fileId = :fileId', { fileId })
        .andWhere('filePermission.isDelete = :isDelete', { isDelete: false })
        .select(['filePermission', 'user.email', 'user.id', 'user.firstName', 'user.lastName'])
        .getMany();

      return filePermissions;
    } catch (error) {
      throw error;
    }
  }
}

const filePermissionModel = new FilePermissionModel();
export default filePermissionModel;
