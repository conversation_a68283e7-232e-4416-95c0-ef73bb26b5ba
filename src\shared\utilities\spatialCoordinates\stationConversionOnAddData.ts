import dceModel from '../../../modules/project360/models/meta/dce.model';
import { entityList, EntityListInterface } from '../entity/entityList';
import { IDataInput } from '../import/importByTable/importData';
import getStationAndOffset from '../photoVideo/getData';

const stationConversionOnAddData = async (projectId: string, data: any, entity: any) => {
  try {
    const checkStationExist = await dceModel.checkColumnExistByDce(entity, 'station');
    const checkOffsetExist = await dceModel.checkColumnExistByDce(entity, 'offset');
    if ((checkStationExist || checkOffsetExist) && projectId && data.longitude && data.latitude) {
      const { station, offset, stationAlignmentId } = await getStationAndOffset(projectId, {
        longitude: data.longitude,
        latitude: data.latitude,
      });
      if (checkStationExist && !data.station) {
        data.station = station;
      }
      if (checkOffsetExist && !data.offset) {
        data.offset = offset;
      }
      if(checkStationExist && !data.stationAlignmentId) {
        data.stationAlignmentId = stationAlignmentId;
      }
    }

    return data;
  } catch (error) {
    throw error;
  }
};

export const stationConversionOnImportData = async (
  projectId: string,
  datas: IDataInput[],
  entityString: string
) => {
  try {
    const entity: any = entityList[entityString as keyof EntityListInterface];
    const checkStationExist = await dceModel.checkColumnExistByDce(entity, 'station');
    const checkOffsetExist = await dceModel.checkColumnExistByDce(entity, 'offset');
    const finalOut: IDataInput[] = [];
    for (const items of datas) {
      if (items.index == 0) {
        const newData: any = [];
        for (const data of items.data) {
          if (
            (checkStationExist || checkOffsetExist) &&
            projectId &&
            data.longitude &&
            data.latitude
          ) {
            const { station, offset } = await getStationAndOffset(projectId, {
              longitude: data.longitude,
              latitude: data.latitude,
            });
            if (checkStationExist && !data.station) {
              data.station = station;
            }
            if (checkOffsetExist && !data.offset) {
              data.offset = offset;
            }
          }
          newData.push(data);
        }
        finalOut.push({ entity: items.entity, data: newData, index: items.index });
      } else {
        finalOut.push({ entity: items.entity, data: items.data, index: items.index });
      }
    }

    return finalOut;
  } catch (error) {
    throw error;
  }
};

export default stationConversionOnAddData;
