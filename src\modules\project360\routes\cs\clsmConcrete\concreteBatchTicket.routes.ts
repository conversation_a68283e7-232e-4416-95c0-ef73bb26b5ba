import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { ConcreteBatchTicket } from '../../../../../entities/p_cs/ConcreteBatchTicket';

const router: Router = express.Router();

const GenricController = new CrudController<ConcreteBatchTicket>(ConcreteBatchTicket);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'concreteBatchTicket')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'concreteBatchTicket')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'concreteBatchTicket')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'concreteBatchTicket')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'concreteBatchTicket')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'concreteBatchTicket')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'concreteBatchTicket')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'concreteBatchTicket')
);

export default router;
