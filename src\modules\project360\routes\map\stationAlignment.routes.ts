import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { StationAlignment } from '@entities/p_map/StationAlignment';

const router: Router = express.Router();

const genericController = new CrudController<StationAlignment>(StationAlignment);

router.get('/by/project/:id', authenticateToken, (req, res)=>genericController.findByProjectId(req, res, "stationAlignment"));

export default router;
