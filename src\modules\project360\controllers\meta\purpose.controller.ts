import { Request, Response } from 'express';
import purposeModel from '../../models/meta/purpose.model';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';

class PurposeController {
  async findByDCEId(req: Request, res: Response) {
    try {
      const { projectId } = req.params;
      const { view } = req.query;
      const userId = (req as any).user.id;
      const { map } = req.query;
      let result = [];
      if (map) {
        result = await purposeModel.getByUserIdForMapFilter(userId, projectId, view ? true : false);
      } else {
        result = await purposeModel.getByUserIdForDropdown(userId, projectId, view ? true : false);
      }
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default PurposeController;
