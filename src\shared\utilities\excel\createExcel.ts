import * as ExcelJS from 'exceljs';

interface createExcelProp {
  sheetName: string;
  data: Record<string, string>[];
}

const createExcel = async (excelData: createExcelProp[]) => {
  try {
    const workbook = new ExcelJS.Workbook();

    excelData.forEach((item) => {
      const worksheet = workbook.addWorksheet(item.sheetName);

      if (item.data.length > 0) {
        const keys = Object.keys(item.data[0]);

        // Add headers manually
        worksheet.addRow(keys);

        // Add data rows
        item.data.forEach((row) => {
          worksheet.addRow(keys.map((key) => row[key]));
        });
        worksheet.addTable({
          name: item.sheetName.replace(/\s+/g, '_'), // Ensure valid sheet name
          ref: 'A1',
          headerRow: true,
          totalsRow: false,
          style: {
            theme: 'TableStyleMedium9',
            showRowStripes: true,
          },
          columns: keys.map((value) => ({ name: value, filterButton: true })),
          rows: item.data.map((row) => keys.map((key) => row[key])),
        });
      } else {
        worksheet.getCell('A1').value = 'No data available';
      }
    });

    const buffer = await workbook.xlsx.writeBuffer();
    return buffer;
  } catch (error) {
    throw error;
  }
};

export default createExcel;
