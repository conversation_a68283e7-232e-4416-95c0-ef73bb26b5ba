import { Request, Response } from 'express';
import FormModel from '../../../models/meta/form configuration/form.model';
import errorMiddleware from '../../../../../shared/middlewares/error/error.middleware';

class FormController {
  private model = new FormModel();

  findByDceId = async (req: Request, res: Response) => {
    try {
      const data = await this.model.findByDceId(req.params.dceId);

      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByDCEId = async (req: Request, res: Response) => {
    try {
      const data = await this.model.getJSONByDCE(req.params.dceId, req.params.mode);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  public update = async (req: Request, res: Response) => {
    try {
      const id: number | string = req.params.id;
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }

      const data = req.body;
      const result = await this.model.update(id, data);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data updated' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  editFormMode = async (req: Request, res: Response) => {
    try {
      const { mode }: { mode: { formId: string; mode: string[] }[] } = req.body;
      const { dceId } = req.params;
      await this.model.editModeByDce(dceId, mode);
      const message = req.__('UpdatedSuccess');
      return res.json({ isSucceed: true, data: [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  createFormByDce = async (req: Request, res: Response) => {
    try {
      const data = await this.model.createByDce(req.params.dceId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  createFormByMultipleDce = async (req: Request, res: Response) => {
    try {
      const { dceIds } = req.body;
      const data = await this.model.createByMultipleDce(dceIds);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  createFormBySubDce = async (req: Request, res: Response) => {
    try {
      const data = await this.model.createBySubDce(req.params.dceId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByFormId = async (req: Request, res: Response) => {
    try {
      const { formId, dceId } = req.params;
      const data = await this.model.getJSONByFormId(formId, dceId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getAllCustomForms = async (req: Request, res: Response) => {
    try {
      const { formId, dceId } = req.params;
      const data = await this.model.getJSONByFormId(formId, dceId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getCustomFormByFormKey = async (req: Request, res: Response) => {
    try {
      const { formKey } = req.params;
      const data = await this.model.getCustomFormByFormKey(formKey);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getModeByFormId = async (req: Request, res: Response) => {
    try {
      const { formId } = req.params;
      const data = await this.model.getModeByForm(formId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getModeFormsByDCE = async (req: Request, res: Response) => {
    try {
      const { dceId } = req.params;
      const data = await this.model.getFormByModeAndDce(dceId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default FormController;
