import { entityList, EntityListInterface } from '@utils/entity/entityList';
import {
  moveUnknownPrimitivesToMetadata,
  sanitizeRequestData,
} from '@utils/validation/sanitizeRequestData';
import { EntityTarget, getManager } from 'typeorm';

const editDataWithSubDCE = async (
  SubDceDat: { entity: string; data: any[] }[],
  data: { [key: string]: any },
  dceEntity: EntityTarget<any>,
  id: string
) => {
  try {
    const entityManager = getManager();
    await entityManager.transaction(async (transactionalEntityManager) => {
      const updatedData = await moveUnknownPrimitivesToMetadata(data, dceEntity);
      await transactionalEntityManager.update(dceEntity, id, updatedData);
      for (const element of SubDceDat) {
        if (element.entity in entityList) {
          const subEntity: any = entityList[element.entity as keyof EntityListInterface] as any;
          if (subEntity) {
            for (const item of element.data) {
              const itemWithoutNull = sanitizeRequestData(item);
              const updatedData = await moveUnknownPrimitivesToMetadata(itemWithoutNull, subEntity);
              if (item.id) {
                await transactionalEntityManager.update(subEntity, item.id, updatedData);
              } else {
                await transactionalEntityManager.save(subEntity, updatedData);
              }
            }
          }
        }
      }
    });
    return;
  } catch (error) {
    throw error;
  }
};

export default editDataWithSubDCE;
