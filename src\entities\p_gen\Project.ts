import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  ManyToMany,
  JoinTable,
  BeforeInsert,
  Point,
} from 'typeorm';

export enum ProjectStatus {
  Planned = 'Planned',
  InProgress = 'In Progress',
  Completed = 'Completed',
  OnHold = 'On Hold',
  construction = 'Construction',
}
import { Phase } from './Phase';
import { StandardAgency } from '../p_meta/StandardAgency';
import { SpecAgency } from '../p_meta/SpecAgency';
import { Config } from '../p_utils/Config';
import { Task } from '../p_utils/Task';
import { WorkPackage } from './WorkPackage';

@Entity({ schema: 'p_gen' })
export class Project {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  alias?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  startDate?: Date;

  @Column({ nullable: true })
  endDate?: Date;

  @Column({ nullable: true })
  projectNumber?: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  contact?: string;

  @Column({ nullable: true })
  division?: string;

  @Column({ nullable: true })
  districtCounty?: string;

  @Column({ nullable: true })
  stateId?: number;

  @Column({ nullable: true })
  countryId?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ nullable: true })
  currency?: string;

  @Column({ nullable: true })
  budget?: string;

  @Column({ nullable: true })
  estmHours?: string;

  @Column({
    type: 'enum',
    enum: ProjectStatus,
    nullable: true,
  })
  status?: ProjectStatus;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column()
  organizationId?: string;

  @Column({ nullable: true })
  folderPath?: string;

  @Column({ nullable: true })
  container!: string;

  @Column({ nullable: true })
  progress?: string;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  metrics!: string;

  @OneToMany(() => Task, (task) => task.project)
  task?: Task;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  phaseId?: string;

  @Column({ nullable: true })
  timezone?: string;

  @ManyToOne(() => Phase, (phase) => phase.projects)
  @JoinColumn({ name: 'phaseId' })
  phase?: Phase;

  @Column({ nullable: true })
  specAgencyId?: string;

  @ManyToOne(() => SpecAgency, { nullable: true })
  @JoinColumn({ name: 'specAgencyId' })
  specAgency?: SpecAgency;

  @OneToMany(() => Config, (config) => config.project)
  config?: Config[];

  @OneToMany(() => WorkPackage, (workpackage) => workpackage.project)
  workPackage?: WorkPackage[];

  @Column({ nullable: true })
  sftpToken?: string;

  @Column({ nullable: true })
  spatialReferenceCode?: string;

  @Column({ nullable: true })
  spatialReferenceParams?: string;

  @ManyToMany(() => StandardAgency, { eager: true })
  @JoinTable({ name: 'project_standard_agency' })
  standardAgency?: StandardAgency[];

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  logoPath?: string;

  @Column({ type: 'jsonb', nullable: true })
  edbAccessDetails?: any;

  @Column({ nullable: true })
  quickRefGuidPath?: string;

  @Column({ nullable: true })
  dataSummaryLink?: string;

  @Column({ nullable: true })
  schedulePDFPath?: string;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
