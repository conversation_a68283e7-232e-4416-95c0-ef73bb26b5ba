import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { BlastEvaluation } from '../../../../../entities/p_cs/BlastEvaluation';

const router: Router = express.Router();

const GenricController = new CrudController<BlastEvaluation>(BlastEvaluation);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'blastEvaluation')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'blastEvaluation')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'blastEvaluation')
);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'blastEvaluation')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'blastEvaluation')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'blastEvaluation')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'blastEvaluation')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'blastEvaluation')
);

export default router;
