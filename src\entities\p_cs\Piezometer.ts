import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { SoilSymbol } from '../p_domain/SoilSymbol';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class Piezometer extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @ColumnInfo({
    customData: {
      name: 'Piezometer Id',
      fieldName: 'piezometerId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  piezometerId?: string;

  @ColumnInfo({
    customData: {
      name: 'Piezometer Type',
      fieldName: 'piezometerType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  piezometerType?: string;

  @ColumnInfo({
    customData: {
      name: 'Longitude (deg)',
      fieldName: 'longitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Latitude (deg)',
      fieldName: 'latitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting (ft)',
      fieldName: 'easting',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing (ft)',
      fieldName: 'northing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Soil Symbol',
      fieldName: 'soilSymbolId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.soil_symbol WHERE name = $1',
      getListQuery: 'Select id, alias as name FROM p_domain.soil_symbol WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'soilSymbolList',
    },
  })
  @Column({ nullable: true })
  soilSymbolId?: string;

  @ManyToOne(() => SoilSymbol, { nullable: true })
  @JoinColumn({ name: 'soilSymbolId' })
  soilSymbol?: SoilSymbol;

  @ColumnInfo({
    customData: {
      name: 'Soil Description',
      fieldName: 'soilDescription',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  soilDescription?: string;

  @ColumnInfo({
    customData: {
      name: 'Top of Riser Elevation (ft)',
      fieldName: 'topRiserElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  topRiserElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Piezometer Tip Elevation (ft)',
      fieldName: 'piezometerTipElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  piezometerTipElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Tip/Bottom of Cap Depth (ft)',
      fieldName: 'tipDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  tipDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Filter Elevation Range (ft)',
      fieldName: 'filterElevationRange',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  filterElevationRange?: number;

  @ColumnInfo({
    customData: {
      name: 'Extreme Elevation (ft)',
      fieldName: 'extremeElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  extremeElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Ground Elevation (ft)',
      fieldName: 'groundElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  groundElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Casing Top Elevation (ft)',
      fieldName: 'casingTopElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  casingTopElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Protective Casing Stick Up above Ground (ft)',
      fieldName: 'protectiveCasingStickUp',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  protectiveCasingStickUp?: number;

  @ColumnInfo({
    customData: {
      name: 'Surface Seal Base Depth (ft)',
      fieldName: 'sealDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sealDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Static Water Level Elevation (ft)',
      fieldName: 'staticWaterLevelElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  staticWaterLevelElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen Interval Top Elevation (ft)',
      fieldName: 'screenIntervalTopElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  screenIntervalTopElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen Interval Bottom Elevation (ft)',
      fieldName: 'screenIntervalBottomElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  screenIntervalBottomElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen Top Elevation (ft)',
      fieldName: 'screenTopElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  screenTopElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Blank Casing Length (ft)',
      fieldName: 'blankCasingLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  blankCasingLength?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen Bottom Elevation (ft)',
      fieldName: 'screenBottomElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  screenBottomElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Sand Pack Bottom/Top Hole Seal Elevation (ft)',
      fieldName: 'sandPackBaseElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandPackBaseElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Bottom Hole Seal Depth (ft)',
      fieldName: 'holeSealDepthBottom',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  holeSealDepthBottom?: number;

  @ColumnInfo({
    customData: {
      name: 'Base Hole Depth (ft)',
      fieldName: 'baseHoleDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  baseHoleDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Protective Casing (Yes/No)',
      fieldName: 'protectiveCasing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  protectiveCasing?: string;

  @ColumnInfo({
    customData: {
      name: 'Concrete Pad (Yes/No)',
      fieldName: 'concretePad',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  concretePad?: string;

  @ColumnInfo({
    customData: {
      name: 'Locking Cap (Yes/No)',
      fieldName: 'lockingCap',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  lockingCap?: string;

  @ColumnInfo({
    customData: {
      name: 'Annular Seal Type',
      fieldName: 'annularSealType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  annularSealType?: string;

  @ColumnInfo({
    customData: {
      name: 'Annular Backfill Type',
      fieldName: 'annularBackfillType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  annularBackfillType?: string;

  @ColumnInfo({
    customData: {
      name: 'Annular Backfill Top Depth (ft)',
      fieldName: 'annularBackfillTopDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  annularBackfillTopDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Annular Backfill Bottom Depth (ft)',
      fieldName: 'annularBackfillBottomDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  annularBackfillBottomDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Casing Type',
      fieldName: 'casingType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  casingType?: string;

  @ColumnInfo({
    customData: {
      name: 'Casing Diameter (in)',
      fieldName: 'casingDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  casingDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen Interval Seal Type',
      fieldName: 'screenIntervalSealType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  screenIntervalSealType?: string;

  @ColumnInfo({
    customData: {
      name: 'Sand Filter Pack Type',
      fieldName: 'sandFilterPackType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  sandFilterPackType?: string;

  @ColumnInfo({
    customData: {
      name: 'Sand Filter Pack Diameter',
      fieldName: 'sandFilterPackDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandFilterPackDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Gravel Filter Pack Type',
      fieldName: 'gravelFilterPackType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  gravelFilterPackType?: string;

  @ColumnInfo({
    customData: {
      name: 'Gravel Filter Pack Diameter (in)',
      fieldName: 'gravelFilterPackDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  gravelFilterPackDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Screen MaterialType',
      fieldName: 'screenMaterialType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  screenMaterialType?: string;

  @ColumnInfo({
    customData: {
      name: 'Screen Slot Size (in)',
      fieldName: 'screenSlotSize',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  screenSlotSize?: number;

  @ColumnInfo({
    customData: {
      name: 'Bottom Hole Seal Type',
      fieldName: 'bottomHoleSealType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  bottomHoleSealType?: string;

  @ColumnInfo({
    customData: {
      name: 'Backfill Type',
      fieldName: 'backfillType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  backfillType?: string;

  @ColumnInfo({
    customData: {
      name: 'Backfill Thickness (ft)',
      fieldName: 'backfillThickness',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  backfillThickness?: number;

  @ColumnInfo({
    customData: {
      name: 'Installation Date',
      fieldName: 'installDate',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  installDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Bottom Cap Type',
      fieldName: 'bottomCapType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  bottomCapType?: string;

  @ColumnInfo({
    customData: {
      name: 'Bottom Cap Diameter (in)',
      fieldName: 'bottomCapDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bottomCapDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Casing Orientation',
      fieldName: 'casingOrientation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  casingOrientation?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
