import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  CreateDate<PERSON><PERSON>umn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

import { DCEValidation } from './DCEValidation';

@Entity({ schema: 'p_gen' })
export class DCEValidationCriteria {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  dceValidationId?: string;

  @ManyToOne(() => DCEValidation, { nullable: true }) 
  @JoinColumn({ name: 'dceValidationId' })
  dceValidation?: DCEValidation;

  @Column({ nullable: true })
  logic?: string;

  @Column({ nullable: true })
  operator?: string;

  @Column({ nullable: true })
  order!: number;

  @Column({ nullable: true })
  columnValue?: string;

  @Column({ nullable: true })
  conditionParameter?: string;

  @Column({ nullable: true })
  enterValue?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
