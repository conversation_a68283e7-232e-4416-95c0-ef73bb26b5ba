import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  DeepPartial,
} from 'typeorm';

import { DataCaptureElements } from './DataCaptureElements';
import { SubDataCaptureElements } from './SubDataCaptureElement';

export interface IForeignKeyMapping {
  table: string;
  joinColumn: string;
}

@Entity({ schema: 'p_meta' })
export class DataCaptureElemetRelation {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  relatedColumnToDisplay?: string;

  @Column({ nullable: true })
  columnName?: string;

  @Column({ nullable: true })
  routeParams?: string;

  @Column({ nullable: true })
  additionalParams?: string;

  @Column({ nullable: true })
  dceId?: string;

  @Column({ nullable: true })
  relatedTableName?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  relatedDceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'relatedDceId' })
  relatedDce?: DataCaptureElements;

  @Column({ nullable: true })
  subDceId?: string;

  @ManyToOne(() => SubDataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'subDceId' })
  SubDce?: SubDataCaptureElements;

  @Column({ nullable: true, type: 'jsonb' })
  foreignKeyMapping?: DeepPartial<IForeignKeyMapping>;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  defaultValue?: string;

  @Column({ nullable: true })
  custom?: string;

  @Column({ nullable: true })
  isMultiple?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
