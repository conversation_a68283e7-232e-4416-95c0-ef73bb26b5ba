export function getDBConfig() {
  const dbConfig = {
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 5432,
    username: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || '1234',
    database: process.env.DB_DATABASE || 'smart_local_uuid',
    synchronize: false,
    logging: false,
    dropSchema: false,
    force: false,
    entities: ['src/entities/**/*.ts'],
    migrations: ['src/migrations/**/*.ts'],
    subscribers: ['src/subscribers/**/*.ts'],
    cli: {
      entitiesDir: 'src/entities',
      migrationsDir: 'src/migrations',
      subscribersDir: 'src/subscribers',
    },
    extra: {
      max: 10, // Max connections in the pool (adjust based on workload)
      // ssl: true,
    },
  };
  return dbConfig;
}
