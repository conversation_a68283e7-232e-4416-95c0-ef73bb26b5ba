import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { StHydraulicConductivityTest } from '../../../../entities/p_cs/StHydraulicConductivityTest';

const router: Router = express.Router();

// Create a generic router for the User entity
const CrudFunctionController = new CrudController<StHydraulicConductivityTest>(
  StHydraulicConductivityTest
);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/:id', authenticateToken, CrudFunctionController.findById);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.findByProjectId(req, res, 'hydraulicConductivity')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.getDataCountByProjectId(req, res, 'hydraulicConductivity')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  CrudFunctionController.findByWorkActivityId(req, res, 'hydraulicConductivity')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  CrudFunctionController.sendForApproval(req, res, 'hydraulicConductivity')
);
router.post('/', authenticateToken, (req, res) =>
  CrudFunctionController.create(req, res, 'hydraulicConductivity')
);
router.put('/:id', authenticateToken, (req, res) =>
  CrudFunctionController.update(req, res, 'hydraulicConductivity')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  CrudFunctionController.multiSoftDelete(req, res, 'hydraulicConductivity')
);
router.delete('/:id', authenticateToken, (req, res) =>
  CrudFunctionController.softDelete(req, res, 'hydraulicConductivity')
);

export default router;
