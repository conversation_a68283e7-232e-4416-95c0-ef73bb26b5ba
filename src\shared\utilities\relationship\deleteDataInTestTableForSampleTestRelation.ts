import { getManager, In } from 'typeorm';
import { DataCaptureElements } from '../../../entities/p_meta/DataCaptureElements';

import { entityList, EntityListInterface } from '../entity/entityList';

const deleteDataInTestTableForSampleTestRelation = async (
  sampleId: string,
  targetDce: DataCaptureElements
) => {
  try {
    const entityString = targetDce.entity;
    if (entityString && entityString in entityList) {
      const entity = entityList[entityString as keyof EntityListInterface] as any;
      const repo = getManager().getRepository(entity);
      const dataToEdit = await repo.find({ where: { sampleId, isDelete: false } });
      await repo.update({ id: In(dataToEdit.map((value) => value.id)) }, { isDelete: false });
    }
  } catch (error) {
    throw error;
  }
};

export default deleteDataInTestTableForSampleTestRelation;
