import {
  EntityTarget,
  Repository,
  DeepPartial,
  ObjectLiteral,
  getConnection,
  getManager,
  In,
} from 'typeorm';
import SubmittalVersion from '../../entities/p_gen/SubmittalVersion';
import Submittal from '../../entities/p_gen/Submittal';
import { getAuditDetails } from '../../shared/utilities/auditLog/getAuditDetailsByEntity';
import stationConversionOnAddData from '../../shared/utilities/spatialCoordinates/stationConversionOnAddData';
import { addNewRelation, editRelation } from '@utils/relationship/relationshipChangesOnDropdown';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import { dceValidationBeforeApproval } from '@utils/Approval/dceValidation';
import addForApproval from '@utils/Approval/AddForApporvalAndNextLevel/addForApproval';
import SubDCEModel from '@models/meta/SubDce.model';
import dceModel from '@models/meta/dce.model';

export class GenericModel<T extends ObjectLiteral> {
  private repository: Repository<T>;
  private newEntity: EntityTarget<T>;
  private static dceToSubDceMap: Record<string, { entity: string }[]> = {};

  constructor(entity: EntityTarget<T>) {
    this.repository = getConnection().getRepository(entity);
    this.newEntity = entity;
  }

  async create(data: DeepPartial<T>, createdUserId: string, entity?: string): Promise<T> {
    try {
      let dataToAdd = data;
      if (entity) {
        const approvalStatusModel = new ApprovalStatusModel();
        const approvalStatusId = await approvalStatusModel.getUserStatusId();
        (dataToAdd as any).approvalStatusId = approvalStatusId;
        dataToAdd = await stationConversionOnAddData(
          (data as any)?.projectId || '',
          data,
          this.newEntity
        );
      }
      const entityData = this.repository.create(dataToAdd);
      const savedEntity = await this.repository.save(entityData);
      if (entity) {
        await addNewRelation(entity, savedEntity);
        await dceValidationBeforeApproval(entity, [savedEntity]);
        await getAuditDetails(createdUserId, entity, savedEntity, 'add');
      }
      return savedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while creating the entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async createWithSubmittal(
    data: DeepPartial<T>[],
    submittalVersion?: SubmittalVersion,
    submittalChange?: Partial<Submittal>
  ): Promise<T[]> {
    try {
      const entityManager = getManager();
      const result = await entityManager.transaction(async (transactionalEntityManager) => {
        if (submittalVersion && submittalChange) {
          const submittal = await transactionalEntityManager.save(
            SubmittalVersion,
            submittalVersion
          );
          await transactionalEntityManager.update(Submittal, submittalChange.id, submittalChange);
          data.map((value) => {
            (value as any).submittalVersionId = submittal.id;
          });
        }
        const entity = this.repository.create(data);
        const savedEntity = await transactionalEntityManager.save(entity);
        return savedEntity;
      });
      return result;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while creating the entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async findById(id: number | string): Promise<T | undefined> {
    try {
      const result = await this.repository.createQueryBuilder().where('id = :id', { id }).getOne();
      return result || undefined;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while finding entity by ID:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async getByProjectId(projectId: string): Promise<T[] | undefined> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .where('"projectId" = :projectId', { projectId })
        .getMany();
      return result || undefined;
    } catch (error) {
      console.error('Error occurred while finding entity by Project ID:', error);
      throw error;
    }
  }

  async findBySpecId(id: number | string): Promise<T[] | undefined> {
    try {
      const result = await this.repository
        .createQueryBuilder()
        .where('"specAgencyId" = :id', { id })
        .getMany();
      return result || undefined;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while finding entity by ID:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async getDataCountByProjectId(
    id: string,
    dataControl?: any
  ): Promise<
    {
      approvalStatusName: string | undefined;
      id: string;
      color: string | undefined;
      count: number;
    }[]
  > {
    try {
      let whereCondition: any = dataControl;
      if (!dataControl || dataControl.length <= 0) {
        whereCondition = { projectId: id, isDelete: false };
      }
      const approvalStatusModel = new ApprovalStatusModel();
      const allStatus = await approvalStatusModel.getAllStatus();

      const data = await this.repository.find({
        where: whereCondition,
        select: ['approvalStatusId', 'id', 'projectId'],
      });

      const groupedData = data.reduce(
        (acc, bh) => {
          const statusName = bh.approvalStatusId || 'Unknown';
          acc[statusName] = (acc[statusName] || 0) + 1;
          return acc;
        },
        {} as Record<string, number>
      );

      const allCounts = Object.entries(groupedData).map(([approvalStatusName, count]) => ({
        approvalStatusName,
        count,
      }));

      const result = allStatus.map((status) => {
        const item = allCounts.find((item) => item.approvalStatusName === status.id);
        return {
          approvalStatusName: status.name,
          id: status.id,
          color: status.color,
          count: item ? item.count : 0,
        };
      });

      return result;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while finding entity by ID:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  private async getSubDceEntitiesForDce(dceId: string): Promise<{ entity: string }[]> {
    // Check if we already have the mapping cached
    if (GenericModel.dceToSubDceMap[dceId]) {
      return GenericModel.dceToSubDceMap[dceId];
    }

    // If not cached, fetch and cache it
    try {
      const subDCEModel = new SubDCEModel();
      const subDCEs = await subDCEModel.findByDCEId(dceId);

      const subDceEntities = subDCEs.map((subDce) => ({
        entity: subDce.entity || '',
      }));

      // Cache the result
      GenericModel.dceToSubDceMap[dceId] = subDceEntities;

      return subDceEntities;
    } catch (error) {
      console.error('Error fetching SubDCE entities:', error);
      return [];
    }
  }

  async findByProjectId(
    id: string,
    relation?: string[],
    isSubmittal?: boolean,
    dataControl?: any,
    updatedAfter?: Date,
    isSubmittalEdit?: boolean,
    submittalVersionId?: string,
    entity?: string
  ): Promise<T[] | undefined> {
    try {
      let whereCondition: any = dataControl;
      if (!dataControl || dataControl.length <= 0) {
        whereCondition = { projectId: id, isDelete: false };
      }

      let sortField;
      switch (entity) {
        case 'ctLog':
          sortField = 'testNo';
          break;
        case 'sampleManagement':
          sortField = 'sampleNo';
          break;
        default:
          sortField = 'createdAt';
      }

      const data = await this.repository.find({
        where: whereCondition,
        relations: relation,
        order: { [sortField]: 'DESC' } as any,
      });

      const result = data.map(({ metadata, ...rest }) => ({
        ...rest,
        ...(typeof metadata === 'object' && metadata !== null ? metadata : {}),
      }));

      // Sort subdce entities by order column if entity is provided
      if (entity) {
        const dce = await dceModel.findByEntity(entity);

        if (dce && dce.id) {
          // Use the cached mapping instead of querying every time
          const subDceEntities = await this.getSubDceEntitiesForDce(dce.id);

          // Process each result item to sort its subdce arrays
          for (const item of result) {
            for (const subDce of subDceEntities) {
              const subEntityName = subDce.entity;
              if (subEntityName && item[subEntityName] && Array.isArray(item[subEntityName])) {
                // Sort the array by order field
                item[subEntityName].sort((a: any, b: any) => {
                  // Handle missing order values
                  const orderA = a.order !== undefined ? a.order : Number.MAX_SAFE_INTEGER;
                  const orderB = b.order !== undefined ? b.order : Number.MAX_SAFE_INTEGER;
                  return orderA - orderB;
                });
              }
            }
          }
        }
      }

      if (updatedAfter) {
        const givenDate = new Date(updatedAfter);
        const filteredData = result.filter((item) => item.updatedAt >= givenDate);
        return filteredData || undefined;
      }

      if (isSubmittal && result.length > 0) {
        if (isSubmittalEdit == true && submittalVersionId) {
          const newEditFilter = result.filter(
            (value) => value.submittalVersionId == submittalVersionId || !value.submittalVersionId
          );
          return newEditFilter;
        }
        const newResult = result.filter((value) => !value.submittalVersionId);
        return newResult;
      }

      return result || undefined;
    } catch (error) {
      console.error('Error occurred while finding entity by ID:', error);
      throw error;
    }
  }
  async findByWorkActivityId(id: string, relation?: string[]): Promise<T[] | undefined> {
    try {
      const whereCondition: any = { workPackageActivityId: id, isDelete: false };

      const result = await this.repository.find({
        where: whereCondition,
        relations: relation,
        order: { updatedAt: 'DESC' } as any,
      });

      return result || undefined;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while finding entity by ID:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async update(
    id: number | string,
    data: DeepPartial<T>,
    createdUserId: string,
    entity?: string
  ): Promise<T | undefined> {
    try {
      let dataToAdd = data;
      const dataBeforeUpdate = await this.findById(id);

      if (entity) {
        dataToAdd = await stationConversionOnAddData(
          (data as any)?.projectId || '',
          data,
          this.newEntity
        );
      }
      // Attempt to update the entity
      await this.repository.update(id, dataToAdd as any);

      // Try to find the updated entity
      const updatedEntity = await this.findById(id);

      if (entity) {
        await editRelation(entity, updatedEntity, dataBeforeUpdate);
        await dceValidationBeforeApproval(entity, [updatedEntity]);
        await getAuditDetails(createdUserId, entity, updatedEntity, 'edit');
      }

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async sendForApproval(ids: string[], projectId: string, entity: string, submittedUser: string) {
    try {
      const approvalStatusModel = new ApprovalStatusModel();
      const sendForApprovalCheckStatusId =
        await approvalStatusModel.getSendForApprovalCheckStatusId();
      const tableData = await this.repository.find({
        where: {
          id: In(ids), // Fetch all ids in a single query
          approvalStatus: In(sendForApprovalCheckStatusId), // Combine the approval status condition
          isDelete: false,
        } as any,
      });
      // const approvals = new ApprovalMethods();
      if (tableData.length > 0) {
        const entityMetadata = this.repository.metadata;
        await addForApproval(tableData, entity, projectId, entityMetadata, 0, submittedUser);
      }
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      throw error;
    }
  }

  async delete(id: number): Promise<boolean> {
    try {
      const result = await this.repository.delete(id);
      return result.affected === 1;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while deleting entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async findAll(page: number, pageSize: number): Promise<T[]> {
    try {
      if (page > 0 && pageSize > 0) {
        const entities = await this.repository.find({
          skip: (page - 1) * pageSize,
          take: pageSize,
        });
        return entities;
      } else {
        const entities = await this.repository.find({
          skip: (page - 1) * pageSize,
          take: pageSize,
        });

        return entities;
      }
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while retrieving entities:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  // eslint-disable-next-line @typescript-eslint/ban-types
  async getCount(): Promise<Number> {
    try {
      const entities = await this.repository.count();
      return entities;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while retrieving entities:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async softDelete(
    id: number | string,
    createdUserId: string,
    entity?: string
  ): Promise<T | undefined> {
    try {
      // Attempt to update the entity
      await this.repository.update(id, { isDelete: true } as any);

      // Try to find the updated entity
      const updatedEntity = await this.findById(id);

      if (entity) {
        await getAuditDetails(createdUserId, entity, updatedEntity, 'delete');
      }

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }
}
