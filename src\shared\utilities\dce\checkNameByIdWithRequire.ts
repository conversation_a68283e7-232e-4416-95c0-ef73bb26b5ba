import DCEConfigurationModel from '../../../modules/project360/models/dceConfiguration.model';
type ValidationError = {
  columnName: string;
  message: string;
};
type ValidatedRecord<T> = T & { validationErrors?: ValidationError[] };

const checkNameByIdWithRequireForValidationErrors = async <T extends Record<string, any>>(
  dceId: string,
  data: T[],
  projectId: string
): Promise<ValidatedRecord<T>[]> => {
  try {
    const dceModel = new DCEConfigurationModel();

    // Fetch only required, non-deleted DCE config fields for the project and form (dceId)
    const dceElements = await dceModel.findDceElementByIdandProjectId(dceId, projectId);
    // If no DCE rules, return data as is
    if (!dceElements || dceElements.length === 0) {
      return data;
    }
    // Loop through each input record to validate against required DCE fields
    const validatedRecords = data.map((item) => {
      const validationErrors = dceElements
        .map((field) => {
          const { columnName, alias } = field;
          if (!columnName || !alias) return null;
          if (!(columnName in item)) return null;
          // Check if the column exists in the item
          const value = item[columnName];
          if (value === null || value === undefined || value === '') {
            return {
              columnName,
              message: `${alias} is required but missing or empty`,
            };
          }
          return null;
        })
        .filter(Boolean) as ValidationError[];

      // If any errors, attach to record; otherwise keep it as-is
      return validationErrors.length > 0 ? { ...item, validationErrors } : item;
    });

    return validatedRecords;
  } catch (error) {
    console.error('Validation error:', error);
    throw error;
  }
};

export { checkNameByIdWithRequireForValidationErrors };
