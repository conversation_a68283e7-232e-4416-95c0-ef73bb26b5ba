import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, getManager } from 'typeorm';
import sftpConfidModel from '../../../modules/project360/models/sftpConfig.model';
import * as path from 'path';
import { ensureFolderExists, transferFileBetweenSFTPs } from './transferFileBetweenSFTPs';
import Submittal from '../../../entities/p_gen/Submittal';
import { PostBlastVideo } from '../../../entities/p_cs/PostBlastVideo';
import SubmittalVersion from '../../../entities/p_gen/SubmittalVersion';
import { BlastDamageSurvey } from '../../../entities/p_cs/BlastDamageSurvey';
import stringTemplate from 'string-template';
import { PhotoVideo } from '../../../entities/p_gen/PhotoVideo';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import {
  convertToTimeZone,
  getCurrentDateInDDMMYYYYFormat,
  getCurrentDateInDDMMYYYYFormatForPhotoVideo,
  getDateInyyyymmFormat,
} from '../custom/dateFormating';
import annotateImage from '../photoVideo/annotateImage';
import SFTPGetFile from '../sftpBlobAndFiles/getFileFromSftp';
import isUUID from '../custom/isUUID';
import { getUserById } from '../../server/platformApi/user';
import { AzureBlobStorageService } from '../sftpBlobAndFiles/azureBlobStorageService';
import photoVideoModel from '../../../modules/project360/models/photoVideo.model';
import projectModel from '@models/project.model';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';

export const photoVideoSftp = async (dceId: string, tableId: string, projectId: string) => {
  try {
    const sftpData = await sftpConfidModel.findByDCEId(dceId, projectId);
    if (sftpData) {
      const tableData = await getManager()
        .getRepository(PhotoVideo)
        .findOne({ where: { id: tableId }, relations: ['mediaType'] });
      if (tableData?.path && tableData?.projectId && sftpData?.fileName && sftpData.path) {
        const timezone = await projectModel.getTimezoneByProjectId(projectId);
        const fileName: string = path.basename(tableData.path);
        console.log(fileName.split('.')[1]);
        const date = getCurrentDateInDDMMYYYYFormat(
          convertToTimeZone(tableData?.dateTime || tableData.createdAt || new Date(), timezone)
        );
        const data = {
          date: date,
          description: tableData.description || 'new photo'.split('.').join('').replace(/ /g, '_'),
          mediaType: tableData.mediaType?.name?.split('.').join('').replace(/ /g, '_'),
          generalProjectArea: tableData?.generalProjectArea?.name
            ? tableData?.generalProjectArea?.name.split('.').join('').replace(/ /g, '_')
            : '',
          photoNumber: tableData.photoNumber?.split('.').join('').replace(/ /g, '_'),
        };
        const fileExtension = tableData?.path.split('.').pop();
        const newFileName = stringTemplate(sftpData?.fileName, data);
        const uploadPath = `${stringTemplate(sftpData.path, {
          date: getDateInyyyymmFormat(
            convertToTimeZone(tableData?.dateTime || tableData.createdAt || new Date(), timezone)
          ),
        })}/${newFileName}.${fileExtension}`;

        await ensureFolderExists(
          sftpData.path,
          tableData?.projectId,
          (tableData as any).purposeId || ''
        );

        await transferFileBetweenSFTPs(
          tableData.path,
          uploadPath,
          tableData?.projectId,
          (tableData as any).purposeId || ''
        );

        await getManager()
          .getRepository(PhotoVideo)
          .update(tableId, { sftpPath: `${sftpData.path}/${newFileName}.${fileExtension}` });

        return uploadPath;
      }
    }
    return;
  } catch (error) {
    console.log(error);
  }
};
export const photoVideoSftpWithData = async (
  subModuleId: string,
  addedPhotoVideo: PhotoVideo[],
  transactionalEntityManager: EntityManager,
  projectId: string
) => {
  try {
    const sftpData = await sftpConfidModel.findByDCEId(subModuleId, projectId);
    if (sftpData) {
      await Promise.all(
        addedPhotoVideo.map(async (tableData) => {
          if (tableData?.path && tableData?.projectId && sftpData?.fileName && sftpData.path) {
            const fileExtension = tableData?.path.split('.').pop();
            // const newFileName = stringTemplate(sftpData?.fileName, data);

            const uploadPath = `${sftpData.path}/${tableData.name}.${fileExtension}`;

            await ensureFolderExists(
              sftpData.path,
              tableData?.projectId,
              tableData.purposeId || ''
            );

            await transferFileBetweenSFTPs(
              tableData.annotationImagePath || tableData.path,
              uploadPath,
              tableData?.projectId,
              tableData.purposeId || ''
            );

            await transactionalEntityManager.getRepository(PhotoVideo).update(tableData.id, {
              sftpPath: `${sftpData.path}/${tableData.name}.${fileExtension}`,
            });
          }
        })
      );
    }
    return;
  } catch (error) {
    throw error;
  }
};
export const photoVideoSftpWithDataAfterAnnotation = async (subModuleId: string, id: string) => {
  try {
    const tableData = await photoVideoModel.getById(id);
    const sftpData = await sftpConfidModel.findByDCEId(subModuleId, tableData?.projectId || '');
    if (tableData?.path && tableData?.projectId) {
      const fileExtension = tableData?.path.split('.').pop();
      const fileType = tableData.fileType;

      const sftpGetFile = new SFTPGetFile();
      let photographer = tableData.photographer;
      if (photographer && isUUID(photographer)) {
        const photographerDetails = await getUserById(photographer);
        photographer = `${photographerDetails.firstName} ${photographerDetails.lastName}`;
      }
      const file = await sftpGetFile.getBlobFileByProject(tableData.path, tableData.projectId);
      let fileFinalPath = tableData.path;
      if (fileType === 'photo') {
        const annotatedImage = await annotateImage(
          file,
          '',
          tableData.photographer,
          tableData.projectId,
          { ...tableData }
        );
        const currentDate = new Date();
        const currentDay = currentDate.getDate();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();
        const dayDirectoryPath: string = `media/${fileType}/${currentYear}/${
          currentMonth + 1
        }/${currentDay}`;
        const fileName: string = `${Date.now()}`;
        const filePath = `${dayDirectoryPath}/annotated/${fileName}.${fileExtension?.toLowerCase()}`;
        const azureBlobStorageService = new AzureBlobStorageService(
          process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
          process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
        );
        fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          tableData.projectId,
          `${filePath}`,
          annotatedImage.image
        );
        await getManager().getRepository(PhotoVideo).update(tableData.id, {
          annotationImagePath: fileFinalPath,
        });
      }

      if (sftpData && sftpData?.fileName && sftpData.path) {
        const sftpFolderPath = `${stringTemplate(sftpData.path, {
          date: getDateInyyyymmFormat(tableData?.dateTime || tableData.createdAt || new Date()),
        })}`;
        const uploadPath = `${sftpFolderPath}/${tableData.name}.${fileExtension}`;

        await ensureFolderExists(sftpFolderPath, tableData?.projectId, tableData.purposeId || '');

        await transferFileBetweenSFTPs(
          fileFinalPath,
          uploadPath,
          tableData?.projectId,
          tableData.purposeId || ''
        );

        await getManager()
          .getRepository(PhotoVideo)
          .update(tableData.id, {
            sftpPath: `${sftpData.path}/${tableData.name}.${fileExtension}`,
          });
      }
    }
    return;
  } catch (error) {
    console.log(error);
  }
};

export const postBlastVideoAfterSubmittalSftp = async (
  submittal: Submittal,
  version: SubmittalVersion,
  sftpDestinationPath: string,
  projectId: string,
  approverName: string,
  submittalVersionId: string
) => {
  try {
    const dceDetails = await dceModel.findByEntity('postBlastVideo');

    const sftpPath: any = {};
    const dceId = dceDetails?.id;
    if (dceId) {
      const tableData = await getManager()
        .getRepository(PostBlastVideo)
        .findOne({
          where: { submittalVersionId, isDelete: false },
          relations: ['blastReport'],
        });
      const date = getCurrentDateInDDMMYYYYFormat(version.submissionDate || new Date());
      if (tableData?.blastVideo1FilePath) {
        const currentDocument = path.basename(tableData.blastVideo1FilePath);
        const fileExtension = currentDocument.split('.').pop();
        if (sftpDestinationPath) {
          const finalPath = `${sftpDestinationPath}/PostBlastVideo`;
          let finalName = `${date}_video_1.${fileExtension}`;
          if (tableData?.blastReport?.designBlastId) {
            const name = `camOneVideo_${date}_${tableData?.blastReport?.designBlastId}.${fileExtension}`;
            finalName = name;
          }

          if (tableData?.blastReport?.finalBlastId) {
            const name = `camOneVideo_${date}_${tableData?.blastReport?.finalBlastId}.${fileExtension}`;
            finalName = name;
          }
          await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
          await transferFileBetweenSFTPs(
            tableData.blastVideo1FilePath,
            `${finalPath}/${finalName}`,
            projectId,
            tableData.purposeId || ''
          );
          sftpPath.videoOne = `${finalPath}/${finalName}`;
        }
      }

      if (tableData?.blastVideo2FilePath) {
        const currentDocument = path.basename(tableData.blastVideo2FilePath);
        const fileExtension = currentDocument.split('.').pop();
        if (sftpDestinationPath) {
          const finalPath = `${sftpDestinationPath}/PostBlastVideo`;
          let finalName = `camTwoVideo_${date}_video.${fileExtension}`;
          if (tableData?.blastReport?.designBlastId) {
            const name = `camTwoVideo_${date}_${tableData?.blastReport?.designBlastId}.${fileExtension}`;
            finalName = name;
          }

          if (tableData?.blastReport?.finalBlastId) {
            const name = `camTwoVideo_${date}_${tableData?.blastReport?.finalBlastId}.${fileExtension}`;
            finalName = name;
          }
          await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
          await transferFileBetweenSFTPs(
            tableData.blastVideo2FilePath,
            `${finalPath}/${finalName}`,
            projectId,
            tableData.purposeId || ''
          );
          sftpPath.videoTwo = `${finalPath}/${finalName}`;
        }
      }

      if (tableData?.reportPath) {
        const currentDocument = path.basename(tableData.reportPath);
        const fileExtension = currentDocument.split('.').pop();
        if (sftpDestinationPath) {
          const finalPath = `${sftpDestinationPath}/PostBlastReport`;
          let finalName = `reprot_${date}.${fileExtension}`;
          if (tableData?.blastReport?.designBlastId) {
            const name = `reprot_${date}_${tableData?.blastReport?.designBlastId}.${fileExtension}`;
            finalName = name;
          }

          if (tableData?.blastReport?.finalBlastId) {
            const name = `reprot_${date}_${tableData?.blastReport?.finalBlastId}.${fileExtension}`;
            finalName = name;
          }
          await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
          await transferFileBetweenSFTPs(
            tableData.reportPath,
            `${finalPath}/${finalName}`,
            projectId,
            tableData.purposeId || ''
          );
          sftpPath.report = `${finalPath}/${finalName}`;
        }
      }
      const id = tableData?.id;
      if (id) {
        const approvalStatusModel = new ApprovalStatusModel();
        const approvedStatus = await approvalStatusModel.getByNameStatusId('Approved');
        await getManager()
          .getRepository(PostBlastVideo)
          .update(id, {
            qcVerifier: approverName,
            qcDate: new Date(),
            approvalStatusId: approvedStatus || '',
            blastVideo1SFTPFilePath: sftpPath.videoOne,
            blastVideo2SFTPFilePath: sftpPath.videoTwo,
            sftpReportPath: sftpPath.report,
          });
      }
    }

    return;
  } catch (error) {
    console.log(error);
  }
};

export const postBlastVideoSftp = async (dceId: string, tableId: string) => {
  try {
    const sftpPath: any = {};
    const tableData = await getManager()
      .getRepository(PostBlastVideo)
      .findOne({
        where: { id: tableId, isDelete: false },
        relations: ['blastReport'],
      });
    const projectId = tableData?.projectId;
    const date = getCurrentDateInDDMMYYYYFormatForPhotoVideo(tableData?.dateTime || new Date());
    const sftpData = await sftpConfidModel.findByDCEId(dceId, tableData?.projectId || '');
    let blastId = '';

    if (tableData?.blastReport?.designBlastId) {
      blastId = tableData?.blastReport?.designBlastId?.replace(/\s/g, '');
    }

    if (tableData?.blastReport?.finalBlastId) {
      blastId = tableData?.blastReport?.finalBlastId?.replace(/\s/g, '');
    }

    if (sftpData?.path) {
      const sftpTeemplateData = { id: blastId };
      const finalPath = stringTemplate(sftpData?.path, sftpTeemplateData);
      sftpData.path = finalPath;
    }
    if (sftpData?.path && sftpData?.fileName && projectId) {
      if (tableData?.blastVideo1FilePath) {
        const currentDocument = path.basename(tableData.blastVideo1FilePath);
        const fileExtension = currentDocument.split('.').pop();

        const data = {
          name: tableData?.videoName?.replace(/\s/g, ''),
          date: date,
          videoDirection: tableData.videoDirectionCamOne,
        };

        const finalName = stringTemplate(sftpData?.fileName, data);
        await ensureFolderExists(sftpData?.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.blastVideo1FilePath,
          `${sftpData?.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoOne = `${sftpData?.path}/${finalName}.${fileExtension}`;
      }

      if (tableData?.blastVideo2FilePath) {
        const currentDocument = path.basename(tableData.blastVideo2FilePath);
        const fileExtension = currentDocument.split('.').pop();

        const data = {
          name: tableData.videoName?.replace(/\s/g, ''),
          date: date,
          videoDirection: tableData.videoDirectionCamTwo,
        };
        const finalName = stringTemplate(sftpData?.fileName, data);
        await ensureFolderExists(sftpData?.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.blastVideo2FilePath,
          `${sftpData?.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoTwo = `${sftpData?.path}/${finalName}.${fileExtension}`;
      }

      if (tableData?.reportPath) {
        const currentDocument = path.basename(tableData.reportPath);
        const fileExtension = currentDocument.split('.').pop();

        // const data = {
        //   name: 'report',
        //   date: date,

        //   id: blastId,
        // };
        // const finalName = stringTemplate(sftpData?.fileName, data);
        const finalName = `report_${date}`;
        await ensureFolderExists(sftpData?.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.reportPath,
          `${sftpData?.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.report = `${sftpData?.path}/${finalName}.${fileExtension}`;
      }
      const id = tableData?.id;
      if (id) {
        await getManager().getRepository(PostBlastVideo).update(id, {
          blastVideo1SFTPFilePath: sftpPath.videoOne,
          blastVideo2SFTPFilePath: sftpPath.videoTwo,
          sftpReportPath: sftpPath.report,
        });
      }
    }

    return;
  } catch (error) {
    console.log(error);
  }
};

export const blastDamageDurveyAfterSubmittalSftp = async (
  submittal: Submittal,
  version: SubmittalVersion,
  sftpDestinationPath: string,
  projectId: string,
  approverName: string,
  submittalVersionId: string
) => {
  try {
    const dceDetails = await dceModel.findByEntity('blastDamageSurvey');
    const sftpPath: any = {};
    const dceId = dceDetails?.id;
    console.log(dceId);
    const tableData = await getManager()
      .getRepository(BlastDamageSurvey)
      .findOne({
        where: { submittalVersionId, isDelete: false },
      });
    const date = getCurrentDateInDDMMYYYYFormat(version.submissionDate || new Date());
    if (tableData?.photoLink) {
      const currentDocument = path.basename(tableData.photoLink);
      const fileExtension = currentDocument.split('.').pop();
      if (sftpDestinationPath) {
        const finalPath = `${sftpDestinationPath}/BlastDamageSurvey`;
        const finalName = `${date}_photo.${fileExtension}`;
        await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.photoLink,
          `${finalPath}/${finalName}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.photoLink = `${finalPath}/${finalName}`;
      }
    }

    if (tableData?.videoLinkOne) {
      const currentDocument = path.basename(tableData.videoLinkOne);
      const fileExtension = currentDocument.split('.').pop();
      if (sftpDestinationPath) {
        const finalPath = `${sftpDestinationPath}/PostBlastVideo`;
        const finalName = `videoOne_${date}.${fileExtension}`;
        await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.videoLinkOne,
          `${finalPath}/${finalName}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoLinkOne = `${finalPath}/${finalName}`;
      }
    }

    if (tableData?.videoLinkTwo) {
      const currentDocument = path.basename(tableData.videoLinkTwo);
      const fileExtension = currentDocument.split('.').pop();
      if (sftpDestinationPath) {
        const finalPath = `${sftpDestinationPath}/PostBlastVideo`;
        const finalName = `videoTwo_${date}.${fileExtension}`;
        await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.videoLinkTwo,
          `${finalPath}/${finalName}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoLinkTwo = `${finalPath}/${finalName}`;
      }
    }

    if (tableData?.reportLink) {
      const currentDocument = path.basename(tableData.reportLink);
      const fileExtension = currentDocument.split('.').pop();
      if (sftpDestinationPath) {
        const finalPath = `${sftpDestinationPath}/PostBlastReport`;
        const finalName = `reprot_${date}.${fileExtension}`;
        await ensureFolderExists(finalPath, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.reportLink,
          `${finalPath}/${finalName}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.reportLink = `${finalPath}/${finalName}`;
      }
    }
    const id = tableData?.id;
    if (id) {
      const approvalStatusModel = new ApprovalStatusModel();
      const approvedStatus = await approvalStatusModel.getByNameStatusId('Approved');
      await getManager()
        .getRepository(BlastDamageSurvey)
        .update(id, {
          qcVerifier: approverName,
          qcDate: new Date(),
          approvalStatusId: approvedStatus || '',
          sftpPhotoLink: sftpPath.photoLink,
          sftpReportLink: sftpPath.reportLink,
          sftpVideoLinkOne: sftpPath.videoLinkOne,
          sftpVideoLinkTwo: sftpPath.videoLinkTwo,
        });
    }

    return;
  } catch (error) {
    console.log(error);
  }
};

export const blastDamageSurveySftp = async (dceId: string, tableId: string) => {
  try {
    const sftpPath: any = {};
    const tableData = await getManager()
      .getRepository(BlastDamageSurvey)
      .findOne({
        where: { id: tableId, isDelete: false },
        relations: ['blastReport'],
      });
    const projectId = tableData?.projectId;
    const sftpData = await sftpConfidModel.findByDCEId(dceId, projectId || '');
    let blastId = '';
    if (tableData?.blastReport?.designBlastId) {
      blastId = tableData?.blastReport?.designBlastId;
    }

    if (tableData?.blastReport?.finalBlastId) {
      blastId = tableData?.blastReport?.finalBlastId;
    }
    if (sftpData?.path && sftpData?.fileName && projectId) {
      const date = getCurrentDateInDDMMYYYYFormat(tableData?.blastDate || new Date());

      if (tableData?.photoLink) {
        const currentDocument = path.basename(tableData.photoLink);
        const fileExtension = currentDocument.split('.').pop();
        const data = {
          name: 'photo',
          date: date,
          id: blastId,
        };
        const finalName = stringTemplate(sftpData?.fileName, data);
        await ensureFolderExists(sftpData?.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.photoLink,
          `${sftpData?.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.photoLink = `${sftpData?.path}/${finalName}.${fileExtension}`;
      }

      if (tableData?.videoLinkOne) {
        const currentDocument = path.basename(tableData.videoLinkOne);
        const fileExtension = currentDocument.split('.').pop();
        const data = {
          name: 'videoOne',
          date: date,
          id: blastId,
        };
        const finalName = stringTemplate(sftpData?.fileName, data);

        await ensureFolderExists(sftpData?.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.videoLinkOne,
          `${sftpData?.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoLinkOne = `${sftpData?.path}/${finalName}.${fileExtension}`;
      }

      if (tableData?.videoLinkTwo) {
        const currentDocument = path.basename(tableData.videoLinkTwo);
        const fileExtension = currentDocument.split('.').pop();
        const data = {
          name: 'videoTwo',
          date: date,
          id: blastId,
        };
        const finalName = stringTemplate(sftpData?.fileName, data);

        await ensureFolderExists(sftpData.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.videoLinkTwo,
          `${sftpData.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.videoLinkTwo = `${sftpData.path}/${finalName}.${fileExtension}`;
      }

      if (tableData?.reportLink) {
        const currentDocument = path.basename(tableData.reportLink);
        const fileExtension = currentDocument.split('.').pop();
        const data = {
          name: 'reprot',
          date: date,
          id: blastId,
        };
        const finalName = stringTemplate(sftpData?.fileName, data);
        await ensureFolderExists(sftpData.path, projectId, tableData.purposeId || '');
        await transferFileBetweenSFTPs(
          tableData.reportLink,
          `${sftpData.path}/${finalName}.${fileExtension}`,
          projectId,
          tableData.purposeId || ''
        );
        sftpPath.reportLink = `${sftpData.path}/${finalName}.${fileExtension}`;
      }
      const id = tableData?.id;
      if (id) {
        await getManager().getRepository(BlastDamageSurvey).update(id, {
          sftpPhotoLink: sftpPath.photoLink,
          sftpReportLink: sftpPath.reportLink,
          sftpVideoLinkOne: sftpPath.videoLinkOne,
          sftpVideoLinkTwo: sftpPath.videoLinkTwo,
        });
      }
    }

    return;
  } catch (error) {
    console.log(error);
  }
};
