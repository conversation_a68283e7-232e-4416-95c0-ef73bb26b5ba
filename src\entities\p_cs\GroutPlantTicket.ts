import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { GroutMixDesign } from './GroutMixDesign';
import { GroutDrillHole } from './GroutDrillHole';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class GroutPlantTicket extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @ColumnInfo({
    customData: {
      name: 'Mix Id',
      fieldName: 'mixId',
      needed: true,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column()
  mixId?: string;

  @ManyToOne(() => GroutMixDesign, { nullable: true })
  @JoinColumn({ name: 'mixId' })
  groutMixDesign?: GroutMixDesign;

  @ColumnInfo({
    customData: {
      name: 'Hole Id',
      fieldName: 'holeId',
      needed: true,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column()
  holeId?: string;

  @ManyToOne(() => GroutDrillHole, { nullable: true })
  @JoinColumn({ name: 'holeId' })
  groutDrillHole?: GroutDrillHole;

  @ColumnInfo({
    customData: {
      name: 'Batch Plant Id',
      fieldName: 'plantId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  plantId?: string;

  @ColumnInfo({
    customData: {
      name: 'Ticket Number',
      fieldName: 'ticketNumber',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  ticketNumber?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Date Time',
      fieldName: 'batchedDateTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  batchedDateTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Total Batch Quantity',
      fieldName: 'batchQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  batchQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'Target Water-Cement Ratio',
      fieldName: 'wcRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wcRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Water-Cement Ratio',
      fieldName: 'wcBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wcBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Water Quantity in GallonsY',
      fieldName: 'waterRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Water Quantity in Gallons',
      fieldName: 'waterBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Water Variance',
      fieldName: 'waterPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Bentonite Slurry Quantity in lbs',
      fieldName: 'bentoniteSlurryRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentoniteSlurryRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Bentonite Slurry Quantity in lbs',
      fieldName: 'bentoniteSlurryBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentoniteSlurryBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Bentonite Slurry Variance',
      fieldName: 'bentoniteSlurryPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentoniteSlurryPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Sand Quantity in lbs',
      fieldName: 'sandRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Sand Quantity in lbs',
      fieldName: 'sandBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Sand Variance',
      fieldName: 'sandPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Sand Percent Mositure',
      fieldName: 'sandPercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandPercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of Sand in Gallons',
      fieldName: 'sandActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Bentonite Powder Quantity in lbs',
      fieldName: 'bentonitePowderRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentonitePowderRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Bentonite Powder Quantity in lbs',
      fieldName: 'bentonitePowderBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentonitePowderBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Bentonite Powder Variance',
      fieldName: 'bentonitePowderPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentonitePowderPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Water Quantity in Bentonite Slurry in gallons',
      fieldName: 'bentoniteSlurryWaterBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bentoniteSlurryWaterBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Cement Quantity in lbs',
      fieldName: 'cementRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Cement Quantity in lbs',
      fieldName: 'cementBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Cement Variance',
      fieldName: 'cementPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
