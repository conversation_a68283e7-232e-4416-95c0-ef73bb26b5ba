import dceModel from '@models/meta/dce.model';
import DCERelationModel from '@models/meta/dceRelation.model';
import RelationshipModel from '@models/meta/relationship.model';
import dceRelationshipModel from '@models/utils/dceRelationship.model';

const addNewRelation = async (entity: string, data: any) => {
  try {
    const dce = await dceModel.findByEntity(entity);
    if (dce) {
      const dceRelationMole = new DCERelationModel();
      const dceRelationDataWithRelatedDce = await dceRelationMole.getByDceIdAndWithRelatedDce(
        dce?.id
      );
      const relationshipTypeModel = new RelationshipModel();
      const dropdownRelationshipType = await relationshipTypeModel.findDropdownRelationById();
      const sampleRelationshipType = await relationshipTypeModel.findSampleTestRelationById();
      if (dropdownRelationshipType && dceRelationDataWithRelatedDce.length > 0) {
        const relationToAdd = dceRelationDataWithRelatedDce
          .filter((value) => {
            return value.columnName && data[value.columnName];
          })
          .map((value) => {
            if (value.columnName === 'sampleId') {
              return {
                projectId: data.projectId,
                sourceDCEId: value.relatedDceId,
                sourceDataId: data[value.columnName as string],
                targetDCEId: dce.id,
                targetDataId: data.id,
                relationshipTypeId: sampleRelationshipType?.id,
                createdBy: data.createdBy,
                createdUserId: data.createdUserId,
                updatedBy: data.updatedBy,
                additionalDetails: {
                  labId: data?.siteId,
                  testId: data?.testId,
                  testMethodId: data?.testMethodId,
                  testVariantId: data?.testVariantId,
                },
              };
            }
            return {
              projectId: data.projectId,
              sourceDCEId: dce.id,
              sourceDataId: data.id,
              targetDCEId: value.relatedDceId,
              targetDataId: data[value.columnName as string],
              relationshipTypeId: dropdownRelationshipType?.id,
              createdBy: data.createdBy,
              createdUserId: data.createdUserId,
              updatedBy: data.updatedBy,
            };
          });
        if (relationToAdd.length > 0) {
          await dceRelationshipModel.addNewRelation(relationToAdd);
        }
      }
    }
    return;
  } catch (error) {
    console.error(error);
  }
};

const editRelation = async (entity: string, newData: any, oldData: any) => {
  try {
    const dce = await dceModel.findByEntity(entity);
    if (dce) {
      const dceRelationMole = new DCERelationModel();
      const dceRelationDataWithRelatedDce = await dceRelationMole.getByDceIdAndWithRelatedDce(
        dce?.id
      );
      const relationshipTypeModel = new RelationshipModel();
      const sampleRelationshipType = await relationshipTypeModel.findSampleTestRelationById();
      const dropdownRelationshipType = await relationshipTypeModel.findDropdownRelationById();
      if (dropdownRelationshipType && dceRelationDataWithRelatedDce.length > 0) {
        await Promise.all(
          dceRelationDataWithRelatedDce.map(async (value) => {
            if (value.columnName) {
              const newValue = newData[value.columnName];
              const oldValue = oldData[value.columnName];
              const obj =
                value.columnName === 'sampleId'
                  ? {
                      projectId: oldData.projectId,
                      sourceDCEId: value.relatedDceId,
                      sourceDataId: oldData[value.columnName],
                      targetDCEId: dce.id,
                      targetDataId: oldData.id,
                      relationshipTypeId: sampleRelationshipType?.id,
                    }
                  : {
                      projectId: oldData.projectId,
                      sourceDCEId: dce.id,
                      sourceDataId: oldData.id,
                      targetDCEId: value.relatedDceId,
                      targetDataId: oldData[value.columnName],
                      relationshipTypeId: dropdownRelationshipType?.id,
                    };
              const relationshipData = await dceRelationshipModel.getRelationBySourceAndTarget(obj);
              let relationCheck = true;
              if (value.columnName === 'sampleId') {
                relationCheck = await dceRelationshipModel.relationCheck(
                  value?.relatedDceId || '',
                  oldData[value.columnName],
                  dce.id,
                  oldData.id
                );
              } else {
                relationCheck = await dceRelationshipModel.relationCheck(
                  dce.id,
                  oldData.id,
                  value.relatedDceId || '',
                  oldData[value.columnName]
                );
              }
              if (newValue !== oldValue) {
                if (!relationshipData && !relationCheck) {
                  if (newValue) {
                    if (value.columnName === 'sampleId') {
                      await dceRelationshipModel.addNewRelation([
                        {
                          projectId: newData.projectId,
                          targetDCEId: dce.id,
                          targetDataId: newData.id,
                          sourceDCEId: value.relatedDceId,
                          sourceDataId: newValue,
                          relationshipTypeId: sampleRelationshipType?.id,
                          createdBy: newData.createdBy,
                          createdUserId: newData.createdUserId,
                          updatedBy: newData.updatedBy,
                        },
                      ]);
                    } else {
                      await dceRelationshipModel.addNewRelation([
                        {
                          projectId: newData.projectId,
                          sourceDCEId: dce.id,
                          sourceDataId: newData.id,
                          targetDCEId: value.relatedDceId,
                          targetDataId: newValue,
                          relationshipTypeId:
                            value.columnName === 'sampleId'
                              ? sampleRelationshipType?.id
                              : dropdownRelationshipType?.id,
                          createdBy: newData.createdBy,
                          createdUserId: newData.createdUserId,
                          updatedBy: newData.updatedBy,
                        },
                      ]);
                    }
                  }
                } else if (newValue && oldValue && relationCheck && relationshipData) {
                  await dceRelationshipModel.editRelation(
                    { targetDataId: newValue, updatedBy: newData.updatedBy },
                    relationshipData?.id
                  );
                } else if (!newValue && oldValue && relationCheck && relationshipData) {
                  await dceRelationshipModel.editRelation(
                    { isDelete: true, updatedBy: newData.updatedBy },
                    relationshipData?.id
                  );
                }
              }
            }
          })
        );
      }
    }
    return;
  } catch (error) {
    console.error(error);
  }
};

export { addNewRelation, editRelation };
