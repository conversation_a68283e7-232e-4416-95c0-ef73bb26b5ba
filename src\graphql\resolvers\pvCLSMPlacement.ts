import { getManager, Between } from 'typeorm';
import { PvCLSMPlacement } from '@entities/p_cs/PvCLSMPlacement ';

export const pvCLSMPlacementResolvers = {
  Query: {
    pvCLSMPlacementById: async (_: any, { id }: { id: string }) => {
      const repository = getManager().getRepository(PvCLSMPlacement);
      return repository.findOne({
        where: { id },
        relations: ['project', 'purpose'],
      });
    },
    pvCLSMPlacementsByProject: async (
      _: any,
      {
        projectId,
        startDate,
        endDate,
        dateField = 'castDate',
      }: {
        projectId: string;
        startDate?: string;
        endDate?: string;
        dateField?: string;
      }
    ) => {
      const repository = getManager().getRepository(PvCLSMPlacement);
      const whereClause: any = {
        projectId,
        isDelete: false,
      };

      // Add date filtering if both dates are provided
      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        whereClause[dateField] = Between(start, end);
      }

      return repository.find({
        where: whereClause,
        relations: ['project', 'purpose'],
        order: { stationStart: 'ASC' },
      });
    },
  },
};
