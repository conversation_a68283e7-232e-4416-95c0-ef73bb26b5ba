import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import StationAndLatLongConverstion from '../../../../shared/utilities/station/stationAndLatLongConvertion';
import { Station } from '../../../../entities/p_map/Station';
import StationModel from '../../models/map/station.model';
import {
  convertEastingNorthingToLatLong,
  convertLatLongToEastingNorthing,
} from '../../../../shared/utilities/spatialCoordinates/eastingAndNothingConversion';
import getStationAndOffset from '@utils/photoVideo/getData';

class StationController {
  private model = new StationModel();
  getStationByCoordinates = async (req: Request, res: Response) => {
    try {
      const stationUtils = new StationAndLatLongConverstion();
      const {
        latitude,
        longitude,
        easting,
        northing,
      }: { latitude?: number; longitude?: number; easting?: number; northing?: number } = req.query;
      const { projectId } = req.params;
      let data: Station | null = null;
      if (latitude && longitude) {
        data = await stationUtils.getNearestStaionByLatAndLog(latitude, longitude, projectId);
      } else if (easting && northing) {
        data = await stationUtils.getNearestStaionByEastingAndNorthing(
          easting,
          northing,
          projectId
        );
      }
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getCoordinatesByStation = async (req: Request, res: Response) => {
    try {
      const data = await this.model.getCoordinatesByStation(
        req.params.station,
        req.params.projectId
      );
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getByProjectId = async (req: Request, res: Response) => {
    try {
      const data = await this.model.getStationByProjectId(req.params.projectId);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getStationAndOffset = async (req: Request, res: Response) => {
    try {
      const { latitude, longitude }: { latitude?: number; longitude?: number } = req.query;
      if (!latitude && !longitude) {
        return res.status(200).json({
          isSucceed: true,
          data: {},
          msg: req.__('DataNotFoundMessage'),
        });
      }
      const data = await getStationAndOffset(req.body.projectId, { latitude, longitude });
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  convertCoordinates = async (req: Request, res: Response) => {
    try {
      const {
        latitude,
        longitude,
        easting,
        northing,
      }: { latitude?: number; longitude?: number; easting?: number; northing?: number } = req.query;
      // const { projectId } = req.params;
      if (latitude && longitude) {
        const result: any = convertLatLongToEastingNorthing(latitude, longitude);
        if (result.easting && result.northing) {
          const message = req.__('DataFoundMessage');
          return res.status(200).json({
            isSucceed: true,
            data: { easting: result.easting, northing: result.northing },
            msg: message,
          });
        }
      } else if (easting && northing) {
        const result: any = convertEastingNorthingToLatLong(easting, northing);
        if (result.latitude && result.longitude) {
          const message = req.__('DataFoundMessage');
          return res.status(200).json({
            isSucceed: true,
            data: { latitude: result.latitude, longitude: result.longitude },
            msg: message,
          });
        }
      }
      const message = req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getStationsByAlignmentId = async (req:Request, res: Response) =>{
    try {
      const data = await this.model.getStationsByAlignmentId(req.params.alignmentId);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default StationController;
