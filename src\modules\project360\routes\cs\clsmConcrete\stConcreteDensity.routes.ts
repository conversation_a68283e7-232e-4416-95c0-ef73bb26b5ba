import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StConcreteDensity } from '../../../../../entities/p_cs/StConcreteDensity';
const router: Router = express.Router();

const GenricController = new CrudController<StConcreteDensity>(StConcreteDensity);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'concreteDensity')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'concreteDensity')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'concreteDensity')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'concreteDensity')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'concreteDensity')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'concreteDensity')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'concreteDensity')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'concreteDensity')
);

export default router;
