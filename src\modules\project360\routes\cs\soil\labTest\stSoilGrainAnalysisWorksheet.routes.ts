import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilGrainAnalysisWorksheet } from '../../../../../../entities/p_cs/StSoilGrainAnalysisWorksheet';
import soilGrainAnalysisWorksheetController from '../../../../controllers/cs/soilGrainAnalysisWorksheet.controller';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilGrainAnalysisWorksheet>(
  StSoilGrainAnalysisWorksheet
);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'grainAnalysisWorksheet')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'grainAnalysisWorksheet')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'grainAnalysisWorksheet')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'grainAnalysisWorksheet')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'grainAnalysisWorksheet')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'grainAnalysisWorksheet')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'grainAnalysisWorksheet')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'grainAnalysisWorksheet')
);
router.get(
  '/by/grainsize/:id',
  authenticateToken,
  soilGrainAnalysisWorksheetController.findByGrainAnalysisTestId
);

export default router;
