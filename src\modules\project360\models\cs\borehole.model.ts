import { get<PERSON>anager, <PERSON>Null, Not } from 'typeorm';
import { Borehole } from '../../../../entities/p_cs/Borehole';

class BoreholeModel {
  private BoreholeRepositry = getManager().getRepository(Borehole);

  private convertStationToNumber(station: string): number {
    const [prefix, suffix] = station.split('+');
    return parseInt(prefix) * 100 + parseInt(suffix);
  }

  getBoreholesByStationsRange = async (start: number, end: number, projectId: string) => {
    try {
      const startValue = start;
      const endValue = end;
      const boreholes = await this.BoreholeRepositry.find({
        where: [
          {
            station: Not(IsNull()),
            boreholePurpose: 'Verification',
            projectId,
          },
        ],
        relations: ['sample'],
      });
      const filteredBoreholes = boreholes.filter((borehole) => {
        const boreholeStationParsed = this.convertStationToNumber(borehole.station!);
        return boreholeStationParsed >= startValue && boreholeStationParsed <= endValue;
      });

      return filteredBoreholes;
    } catch (error) {
      throw error;
    }
  };
  getBoreholesWithVerificationPurpose = async () => {
    try {
      const boreholes = await this.BoreholeRepositry.find({
        where: [
          {
            boreholePurpose: 'Verification',
            holeTopElevation: Not(IsNull()),
            holeBottomElevation: Not(IsNull()),
            holeDiameter: Not(IsNull()),
          },
        ],
      });

      return boreholes;
    } catch (error) {
      throw error;
    }
  };
}

export default BoreholeModel;
