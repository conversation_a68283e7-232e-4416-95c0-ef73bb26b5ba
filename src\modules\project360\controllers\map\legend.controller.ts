import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import LegendModel from '../../models/map/legend.model';
import successResponse from '../../../../shared/middlewares/response/successResponse.middleware';

class LegendController {
  Model = new LegendModel();
  findByLayerId = async (req: Request, res: Response) => {
    try {
      const { layerId } = req.params;
      const data = await this.Model.findByLayerId(layerId);
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getCriteriaByLegendId = async (req: Request, res: Response) => {
    try {
      const { legendId } = req.params;
      const data = await this.Model.findByCriteriaByLegendIdAsString(legendId);
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  editLegend = async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const legendCriteria = req.body.legendCriteria;
      if (req.body.legendCriteria) {
        delete req.body.legendCriteria;
      }
      const legend = req.body;
      const data = await this.Model.edit(legend, legendCriteria, id);
      const message = data ? req.__('UpdatedSuccess') : req.__('UpdatedUnSuccess');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default LegendController;
