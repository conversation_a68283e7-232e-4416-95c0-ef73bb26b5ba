import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { RemedialTreatment } from '../../../../../entities/p_cs/RemedialTreatment';

const router: Router = express.Router();

const GenricController = new CrudController<RemedialTreatment>(RemedialTreatment);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'remedialTreatment')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'remedialTreatment')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'remedialTreatment')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'remedialTreatment')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'remedialTreatment')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'remedialTreatment')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'remedialTreatment')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'remedialTreatment')
);

export default router;
