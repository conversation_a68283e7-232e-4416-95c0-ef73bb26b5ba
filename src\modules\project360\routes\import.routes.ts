import express, { Router } from 'express';
import importController from '../controllers/import.controller';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import { createSubmittalBeforeFormImportMiddleware } from '../../../shared/middlewares/addSubmittalVersion.middleware';

const router: Router = express.Router();

router.post('/by/file', importController.importBasedOnSampleType);
router.get(
  '/create/template/:entity/:projectId',
  authenticateToken,
  importController.createTemplateByTable
);
router.post(
  '/by/table/:entity',
  authenticateToken,
  createSubmittalBeforeFormImportMiddleware,
  importController.importByTable
);

router.post(
  '/raw/data/by/table/:entity',
  authenticateToken,
  createSubmittalBeforeFormImportMiddleware,
  importController.importByRawDataTable
);
router.get('/get/by/project/:id/:entity', authenticateToken, importController.getData);
router.get('/download/by/project/:id/:entity', authenticateToken, importController.downloadData);
router.get(
  '/geojson/by/project/:id/:entity',
  authenticateToken,
  importController.downloadDataAsGeoJson
);
router.post(
  '/update/dce/by/excel/:projectId',
  authenticateToken,
  importController.updateDCEByExcelData
);
export default router;
