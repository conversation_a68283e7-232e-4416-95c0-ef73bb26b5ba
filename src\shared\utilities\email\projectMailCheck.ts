import EmailNotificaitonConfigurationModel from '../../../modules/project360/models/utils/emailNotificaitonConfiguration.model';
import { sendMail } from './email';
import pendingApprovalMail from './pendingApprovalEmail';

const checkAndSendMail = async () => {
  try {
    if (process.env.NODE_ENV == 'prod') {
      const date = new Date();
      const emailNotificationModel = new EmailNotificaitonConfigurationModel();
      const mailToSent = await emailNotificationModel.getByDate();
      emailNotificationModel.editNextDate(mailToSent, date);
      for (const mail of mailToSent) {
        const trigger: string = mail.trigger.name;
        if (trigger.replace(/\s+/g, '').toLowerCase() == 'pendingapproval') {
          pendingApprovalMail(mail.projectId || '', mail?.user?.[0]?.email);
          return;
        }
        const mailId = mail.user?.map((value) => value.email) || [];
        sendMail('test mail', mailId, 'This is a test mail please ignore');
      }
    }
  } catch (error) {
    console.error(error);
  }
};

export default checkAndSendMail;
