import { <PERSON><PERSON><PERSON>, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { StSoilGrainAnalysis } from '../p_cs/StSoilGrainAnalysis';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { HydrometerReadingTime } from '../p_domain/HydrometerReadingTime';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';

@Entity({ schema: 'p_cs' })
export class StSoilGrainAnalysisHydrometerReadings extends SubDCEColumns {
  @Column({ nullable: true })
  grainSizeAnalysisId?: string;

  @ManyToOne(() => StSoilGrainAnalysis, (grainAnalysis) => grainAnalysis.hydrometer)
  @JoinColumn({ name: 'grainSizeAnalysisId' })
  grainSizeAnalysis?: StSoilGrainAnalysis;

  @Column({ nullable: true })
  readingTimeId?: string;

  @ManyToOne(() => HydrometerReadingTime, { nullable: true })
  @JoinColumn({ name: 'readingTimeId' })
  hydrometerReadingTime?: HydrometerReadingTime;

  @Column({ nullable: true })
  hydrometerReading?: number;

  @Column({ nullable: true })
  soilRemainingPercent?: number;

  @Column({ nullable: true })
  sieveSize?: number;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
