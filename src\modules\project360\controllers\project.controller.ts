import projectModel from '../models/project.model';
import { Request, Response } from 'express';
// import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
// import { ProjectOrgStructure } from '../../../entities/p_auth/OrgStructure';
// import { createProjectSftpDirectory } from '../../../shared/utilities/sftpFunction';
// import userModel from '../../platform/models/user.model';
// import ticketModel from '../../platform/models/Ticket.model';
// import standardAgencyModel from '../models/standardAgency.model';
// import projectValidation from '../../../shared/utilities/validation/projectFieldValidation';
import { AzureBlobStorageService } from '../../../shared/utilities/sftpBlobAndFiles/azureBlobStorageService';
import approvalModel from '../models/approval.model';
import { Project } from '../../../entities/p_gen/Project';
import { generateTokenForSftp } from '../../../shared/utilities/jwt';
import { createProjectSftpDirectory } from '../../../shared/utilities/sftpBlobAndFiles/sftpFunction';
import standardAgencyModel from '../models/standardAgency.model';
import projectValidation from '../../../shared/utilities/validation/projectFieldValidation';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { ProjectOrgStructure } from '../../../entities/p_auth/OrgStructure';
import { getRelatedSelectBoxDataByProject } from '../../../shared/utilities/custom/getRelatedSelectBoxDataByProject';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import MapConfigModel from '@models/map/mapConfig.model';

class ProjectController {
  constructor() {}

  async addProejctByTransaction(req: Request, res: Response) {
    try {
      // getting value from request
      const { name } = req.body;
      if (!(req as any).user) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request bodys',
        });
      }
      req.body.createdBy = (req as any).user.name;
      req.body.updatedBy = (req as any).user.name;
      if (req.body.budget) {
        const formattedNumber = Number(req.body.budget).toLocaleString();
        req.body.budget = formattedNumber;
      }
      const createdUserId = (req as any).user.id;

      if (Array.isArray(req.body.standardAgency)) {
        const standardAgency = await standardAgencyModel.findByIds(req.body.standardAgency);
        req.body.standardAgency = standardAgency;
      }
      const stringWithoutSpaces = name.split(' ').join('');
      req.body.folderPath = `/${Date.now()}${stringWithoutSpaces}`;
      await createProjectSftpDirectory(req.body.folderPath);
      const newProject = req.body;
      await projectValidation.projectCreation(req, newProject);
      const { organizationRole } = req.body;
      // const createUserDetails = await stakeUserModel.findById((req as any).user.id);

      // if (!createUserDetails?.subscriber) {
      //   return res
      //     .status(401)
      //     .json({ isSucceed: false, data: [], msg: 'user is not a subscriber' });
      // }

      // if (createUserDetails?.subscriber && createUserDetails.subscriber.containerName) {
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );
      const container = Date.now();
      const containerExists = await azureBlobStorageService.containerExists(`${container}`);

      if (!containerExists) {
        await azureBlobStorageService.createContainer(`${container}`);
      }
      newProject.container = `${container}`;
      // }

      const adminStakeholder: Partial<Stakeholder> = {
        name: req.body.stakeholderName || 'admin',
        type: req.body.stakeholderName || 'admin',
        createdBy: (req as any).user.name,
        updatedBy: (req as any).user.name,
      };

      const stakeUser: any = {
        createdBy: (req as any).user.name,
        updatedBy: (req as any).user.name,
        roleId: 'c5aad20f-b292-4133-bc11-e8c50c4086b9',
        userId: createdUserId,
      };

      const orgStructure: Partial<ProjectOrgStructure> = {
        createdBy: (req as any).user.name,
        updatedBy: (req as any).user.name,
        roleType: organizationRole,
      };

      const projectData = await projectModel.addProjectByTransaction(
        newProject,
        adminStakeholder,
        stakeUser,
        orgStructure,
        (req as any).user.id
      );

      // sending success response
      const message = req.__('DataInputSuccess');
      return res.status(200).json({ isSucceed: true, data: projectData, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByUserId(req: Request, res: Response) {
    try {
      let projectData: Project[] = [];
      const { recent, fromDate, toDate }: { recent?: number; fromDate?: string; toDate?: string } =
        req.query;
      if (recent && !isNaN(recent) && !fromDate) {
        projectData = await projectModel.getProjectByUserIdByRecent(req.params.id, recent);
      } else if (fromDate && toDate) {
        projectData = await projectModel.getProjectByUserIdByRecentByWorkPackage(
          req.params.id,
          recent,
          new Date(fromDate),
          new Date(toDate)
        );
      } else {
        projectData = await projectModel.getProjectByUserId(req.params.id);
      }

      if (projectData.length > 0) {
        const mapCongiModel = new MapConfigModel();
        projectData = await Promise.all(
          projectData.map(async (value) => {
            const mapConfig = await mapCongiModel.getByProjectId(value.id);
            if (mapConfig) {
              (value as any).projectRadius = mapConfig.projectRadius || null;
            }
            return value;
          })
        );
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: projectData,
          msg: 'project found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByUserIdAndRole(req: Request, res: Response) {
    try {
      if (isNaN(Number(req.params.id))) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid route parameter',
        });
      }

      const screen = req.params.screen;

      const projectData = await projectModel.getProjectByUserId(req.params.id);
      const projectUserRole = await projectModel.getProjectRoleByUserId(req.params.id);
      let final: (Project | undefined)[] = [];
      if (screen == 'photoVideo') {
        const roleBasedFilter = projectData.map((project) => {
          const validRole = projectUserRole.find((value) => value.projectId === project.id);
          if (validRole) {
            const check = validRole.permission.includes('addMedia');
            if (check) {
              return project;
            }
          }
        });
        final = roleBasedFilter;
      }

      // getting the data from database with the given id

      // checking if data is found with the id
      if (final) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: final,
          msg: 'project found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getAll(req: Request, res: Response) {
    try {
      const projectData = await projectModel.getAll();
      // getting the data from database with the given id

      // checking if data is found with the id
      if (projectData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: projectData,
          msg: 'project found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findById(req: Request, res: Response) {
    try {
      if (!isNaN(Number(req.params.id))) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid route parameter',
        });
      }

      const projectData = await projectModel.getProjectId(req.params.id);
      // getting the data from database with the given id
      // checking if data is found with the id
      if (projectData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: projectData,
          msg: 'project found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async delete(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const projectMaterialData = await projectModel.deleteById(req.params.id);

      // checking if data is found with the id
      if (projectMaterialData.affected == 1) {
        const message = req.__('DeleteSuccess');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      } else {
        const message = req.__('NoRecordFoundForDelete');
        // if false send error as response
        return res.status(404).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async projectOverView(req: Request, res: Response) {
    try {
      const projectId = req.params.id;
      const overView: any = {};
      const projectData = await projectModel.getProjectId(projectId);
      if (!projectData) {
        const message = req.__('DataNotFoundMessage');
        return res.status(404).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
      overView.project = projectData;
      // const newTask = await ticketModel.findTaskByProjectIdAndStatus(projectId);
      const pendingApproval = await approvalModel.getPendingByProjectId(projectId);
      overView.newTask = [];
      overView.pendingApproval = pendingApproval;

      const message = req.__('DataFoundMessage');
      res.send({
        isSucceed: true,
        data: overView,
        msg: message,
      });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async createContainer(req: Request, res: Response) {
    const containerName = req.params.name;
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    const containerExists = await azureBlobStorageService.containerExists(containerName);

    if (!containerExists) {
      await azureBlobStorageService.createContainer(containerName);
    }

    res.send({ message: 'container created' });
  }

  async addSftpTokenInProject(req: Request, res: Response) {
    try {
      const sftpPassword = req.body.password;
      const reps = {
        password: sftpPassword,
      };
      const token = generateTokenForSftp(reps);
      projectModel.updateSFTPPassword(token, req.params.id);
      res.send({ message: 'password updated' });
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async getUserPermissionByProject(req: Request, res: Response) {
    try {
      const final = await projectModel.getUserPermissionByProjectId(
        req.params.userId,
        req.params.projectId
      );

      // getting the data from database with the given id

      // checking if data is found with the id
      if (final) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: final,
          msg: 'project found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getDropdownByEntityAndProjectId(req: Request, res: Response) {
    try {
      const { entity, id } = req.params;
      const dropDown = await getRelatedSelectBoxDataByProject(entity, id);

      return res.status(200).json({
        isSucceed: true,
        data: dropDown,
        msg: 'Data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  uploadLogo = async (req: Request, res: Response) => {
    try {
      const { filePath, projectId } = req.body;

      if (!(req as any).user) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request body',
        });
      }

      const logoData = await projectModel.updateLogoPath(filePath, projectId);

      return res.status(200).json({
        isSucceed: true,
        data: logoData,
        msg: 'logo added',
      });
    } catch (error) {
      // sending error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  uploadQuickRefGuide = async (req: Request, res: Response) => {
    try {
      const { filePath, projectId } = req.body;

      if (!(req as any).user) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request body',
        });
      }

      const updatedData = await projectModel.updateQuickRefGuidePath(filePath, projectId);

      return res.status(200).json({
        isSucceed: true,
        data: updatedData,
        msg: 'quick reference guide added',
      });
    } catch (error) {
      // sending error response
      return errorMiddleware(error, 500, res, req);
    }
  };
  scheduleFile = async (req: Request, res: Response) => {
    try {
      const { filePath, projectId } = req.body;

      if (!(req as any).user) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request body',
        });
      }

      const updatedData = await projectModel.updateScheduleFilePath(filePath, projectId);

      return res.status(200).json({
        isSucceed: true,
        data: updatedData,
        msg: 'quick reference guide added',
      });
    } catch (error) {
      // sending error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  async findByProjectByOrganization(req: Request, res: Response) {
    try {
      const { organizationId } = req.params;
      const stakeholderData = await projectModel.findProjectByOrganization(organizationId);

      // checking if data is found with the id
      if (stakeholderData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeholderData,
          msg: 'Projects found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async countByProjectByOrganization(req: Request, res: Response) {
    try {
      const { organizationId } = req.params;
      const count = await projectModel.countProjectByOrganization(organizationId);

      return res.status(200).json({
        isSucceed: true,
        data: { count },
        msg: 'Projects found',
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const projectController = new ProjectController();
export default projectController;
