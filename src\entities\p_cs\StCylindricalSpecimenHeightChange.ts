import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { Project } from '../p_gen/Project';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { Sample } from './Sample';
import { SampleType } from '../p_meta/SampleType';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StCylindricalSpecimenHeightChange extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  testNo?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Id',
      fieldName: 'batchId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: true,
      // batchNumber - ticketNumber
      query: `SELECT id FROM p_cs.concrete_batch_ticket WHERE "ticketNumber" = $1`,
      getListQuery: `SELECT id,"ticketNumber" as name FROM p_cs.concrete_batch_ticket WHERE "projectId" = $1 ORDER BY "updatedAt" DESC;`,
      listParams: 'id',
      listName: 'batchIdList',
    },
  })
  @Column()
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @ColumnInfo({
    customData: {
      name: 'Test Status (Pass/Fail)',
      fieldName: 'passFail',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Id', // this column will be entered by the
      fieldName: 'sampleId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.sample WHERE "QMSSampleYear" || '-' || "QMSLabSampleId" = $1`,
      getListQuery: `SELECT id,"QMSSampleYear" || '-' || "QMSLabSampleId" as name FROM p_cs.sample WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 75;`,
      listParams: 'id',
      listName: 'sampleList',
    },
  })
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @ColumnInfo({
    customData: {
      name: 'Test',
      fieldName: 'testId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_meta.test WHERE name = $1',
      getListQuery: 'Select id, name as name FROM p_meta.test',
      listName: 'testList',
    },
  })
  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @ColumnInfo({
    customData: {
      name: 'Test Method', // this column will be entered by the
      fieldName: 'testMethodId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.test_method WHERE "standardCode" = $1`,
      getListQuery:
        'SELECT id,"standardCode" as name FROM p_meta.test_method ORDER BY "updatedAt" DESC LIMIT 20;',
      listName: 'testMethodList',
    },
  })
  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @ColumnInfo({
    customData: {
      name: 'Sample Type', // this column will be entered by the
      fieldName: 'sampleTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.sample_type WHERE "name" = $1`,
      getListQuery:
        'SELECT id,"name" as name FROM p_meta.sample_type ORDER BY "updatedAt" DESC LIMIT 20;',
      listName: 'sampleTypeList',
    },
  })
  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @ColumnInfo({
    customData: {
      name: 'Total No. of Specimens',
      fieldName: 'totalNoOfSpecimens',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  totalNoOfSpecimens?: number;

  @ColumnInfo({
    customData: {
      name: 'Flow',
      fieldName: 'flow',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flow?: number;

  @ColumnInfo({
    customData: {
      name: 'Slump',
      fieldName: 'slump',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  slump?: number;

  @ColumnInfo({
    customData: {
      name: 'Consistency',
      fieldName: 'consistency',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  consistency?: string;

  @ColumnInfo({
    customData: {
      name: 'Magnification of Lens System',
      fieldName: 'magnification',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  magnification?: number;

  @ColumnInfo({
    customData: {
      name: 'Test Mixture Temperature',
      fieldName: 'testMixTemperature',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  testMixTemperature?: number;

  @ColumnInfo({
    customData: {
      name: 'Mold Temperature',
      fieldName: 'moldTemperature',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moldTemperature?: number;

  @ColumnInfo({
    customData: {
      name: 'Relative Humidity',
      fieldName: 'relativeHumidity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  relativeHumidity?: number;

  @ColumnInfo({
    customData: {
      name: 'Time Interval',
      fieldName: 'timeInterval',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  timeInterval?: string;

  @ColumnInfo({
    customData: {
      name: 'Height Change at Time of Hardening ',
      fieldName: 'heightChangeHardening',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightChangeHardening?: number;

  @ColumnInfo({
    customData: {
      name: 'Height Change at End',
      fieldName: 'heightChangeEnd',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightChangeEnd?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Height Decrease',
      fieldName: 'heightMaxDec',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightMaxDec?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Height Decrease Time',
      fieldName: 'heightMaxDecTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightMaxDecTime?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Height Increase',
      fieldName: 'heightMaxInc',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightMaxInc?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Height Increase Time',
      fieldName: 'heightMaxIncTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  heightMaxIncTime?: number;

  @ColumnInfo({
    customData: {
      name: 'Bleed (Yes/No)',
      fieldName: 'bleed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  bleed?: string;

  @ColumnInfo({
    customData: {
      name: 'Tester',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  tester?: string;

  @ColumnInfo({
    customData: {
      name: 'Test date',
      fieldName: 'dateTimeTest',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  dateTimeTest?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
