import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from './Project';
import { Material } from '../p_meta/Material';
import { MaterialType } from '../p_meta/MaterialType';
import { SampleType } from '../p_meta/SampleType';

@Entity({ schema: 'p_gen', name: 'material' })
export class ProjectMaterial {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  name?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => Material, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: Material;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  // TODO: Remove once data is changed from SampleType to MaterialType
  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @Column({ nullable: true })
  description?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
