import {
  DeepPartial,
  EntityTarget,
  ObjectLiteral,
  getConnection,
  getManager,
  getRepository,
} from 'typeorm';
import Submittal from '../../../entities/p_gen/Submittal';
import SubmittalDocument from '../../../entities/p_gen/SubmittalDocument';
import { ActionCode } from '../../../entities/p_domain/ActionCode';
import SubmittalVersion from '../../../entities/p_gen/SubmittalVersion';
import SubmittalRevision from '../../../entities/p_gen/SubmittalRevision';
import approvalSetupModel from './approvalSetup.model';
import { Approver } from '../../../entities/p_utils/Approver';
import { Approval } from '../../../entities/p_utils/Approval';
import { createAuditLog } from '../../../shared/utilities/auditLog/createAuditLog';
import dceModel from './meta/dce.model';
import { entityRelationList } from '../../../shared/utilities/entity/entityRelation';
import { SubmittalFormAttached } from '../../../entities/p_gen/SubmittalFormAttached';
import { IAuditLogFunctionInterface } from '../../../shared/interface/auditLogInterface';
import { renameFolderAndFileOnSFTP } from '../../../shared/utilities/Approval/transferFileBetweenSFTPs';

class SubmittalModel {
  constructor() {}
  async findByProjectId(projectId: string) {
    try {
      const data = await getManager()
        .getRepository(Submittal)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: [
            'stakeholder',
            'submittalClassification',
            'submittalReviewCode',
            'submittalType',
            'submittalList',
            'submittalForm',
            'submittalForm.submittalFormList.dce',
            'specification',
            'version',
            'ownerCode',
            'version.internalCode',
            'version.ownerCode',
            'version.revision',
            'version.revision.documents',
            'version.revision.ownerCode',
            'version.revision.internalCode',
          ],
          order: { updatedAt: 'DESC' },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }
  async findSubmittalVersionWithDocumnets(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .find({
          where: { id, isDelete: false },
          relations: [
            // 'stakeholder',
            // 'submittalClassification',
            // 'submittalReviewCode',
            // 'submittalType',
            // 'submittalList',
            // 'submittalForm',
            // 'submittalForm.dce',
            // 'specification',
            // 'version',
            // 'ownerCode',
            'internalCode',
            'ownerCode',
            'revision',
            'revision.documents',
            'revision.ownerCode',
            'revision.internalCode',
          ],
          order: { updatedAt: 'DESC' },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getById(submittalId: string) {
    try {
      const data = await getManager()
        .getRepository(Submittal)
        .find({
          where: { id: submittalId, isDelete: false },
          relations: [
            'stakeholder',
            'submittalClassification',
            'submittalReviewCode',
            'submittalType',
            'submittalList',
            'submittalForm',
            'submittalForm.submittalFormList.dce',
            'specification',
            'version',
            'version.internalCode',
            'version.ownerCode',
            'version.revision',
            'version.revision.documents',
          ],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findVersionbySubmittal(submittalId: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .find({
          where: { submittalId: submittalId, isDelete: false },
          relations: ['documents', 'internalCode', 'ownerCode', 'submittalType'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdDistinct(projectId: string) {
    try {
      const data = await getManager()
        .getRepository(Submittal)
        .find({
          where: { projectId: projectId, isDelete: false },
        });

      const uniqueData = Array.from(new Map(data.map((item) => [item.id, item])).values());

      return uniqueData;
    } catch (error) {
      throw error;
    }
  }

  async findByIdLastVersion(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .createQueryBuilder('submittal')
        .leftJoinAndSelect('submittal.revision', 'revision')
        .leftJoinAndSelect('submittal.internalCode', 'internalCode')
        .leftJoinAndSelect('submittal.ownerCode', 'ownerCode')
        .where('submittal.submittalId = :id', { id })
        .andWhere('submittal.isDelete = :isDelete', { isDelete: false })
        .orderBy('submittal.version', 'DESC')
        .limit(1)
        .getOne();

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdLastRevision(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalRevision)
        .createQueryBuilder('submittal')
        .where('submittal.submittalVersion = :id', { id })
        .andWhere('submittal.isDelete = :isDelete', { isDelete: false })
        .orderBy('submittal.version', 'DESC')
        .limit(1)
        .getOne();

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findAllRevisionByVersion(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalRevision)
        .createQueryBuilder('submittal')
        .where('submittal.submittalVersion = :id', { id })
        .andWhere('submittal.isDelete = :isDelete', { isDelete: false })
        .orderBy('submittal.version', 'DESC')
        .getMany();

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdLastRecord(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .createQueryBuilder('submittal')
        .where('submittal.submittalId = :id', { id })
        .andWhere('submittal.isDelete = :isDelete', { isDelete: false })
        .orderBy('submittal.version', 'DESC')
        .limit(1)
        .getOne();

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdRevision(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalRevision)
        .findOne({
          where: { id, isDelete: false },
          relations: ['documents', 'ownerCode'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdVersion(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .findOne({
          where: { id, isDelete: false },
          relations: ['internalCode', 'ownerCode', 'revision', 'revision.documents'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdVersionForAttchments(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .findOne({
          where: { id, isDelete: false },
          relations: [
            'submittal',
            'submittal.submittalForm',
            'submittal.submittalForm.submittalFormList.dce',
          ],
        });

      return data?.submittal?.submittalForm;
    } catch (error) {
      throw error;
    }
  }

  async findById(id: string) {
    try {
      const data = await getManager()
        .getRepository(Submittal)
        .findOne({
          where: { id, isDelete: false },
          relations: [
            'stakeholder',
            'submittalClassification',
            'submittalReviewCode',
            'submittalType',
            'submittalList',
            'submittalForm',
            'submittalForm.submittalFormList',
            'submittalForm.submittalFormList.dce',
            'specification',
            'version',
            'version.internalCode',
            'version.ownerCode',
            'version.revision',
            'version.revision.documents',
          ],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }
  async findSubmittalVersionById(id: string) {
    try {
      const data = await getManager()
        .getRepository(SubmittalVersion)
        .findOne({
          where: { id, isDelete: false },
          relations: [
            'submittal.stakeholder',
            'submittal.submittalClassification',
            'submittal.submittalReviewCode',
            'submittal.submittalType',
            'submittal.submittalList',
            // 'submittal.submittalForm',
            // 'submittal.submittalForm.submittalFormList',
            // 'submittalForm.submittalFormList.dce',
            'submittal.specification',
            'internalCode',
            'ownerCode',
            'revision',
            'revision.documents',
          ],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findActionCodeStatus(id: string) {
    try {
      const data = await getManager()
        .getRepository(ActionCode)
        .findOne({ where: { id, isDelete: false } });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async addSubmittalAndDocumentAndChangeStatus(
    submital: DeepPartial<Submittal>,
    documents: SubmittalDocument[],
    submittalVersion: SubmittalVersion,
    submittalRevision: SubmittalRevision,
    projectId: string,
    querys?: string[],
    auditLogs?: IAuditLogFunctionInterface[]
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const version = await transactionalEntityManager.save(SubmittalVersion, submittalVersion);
          submittalRevision.submittalVersionId = version.id;
          if (querys && querys.length > 0) {
            await Promise.all(
              querys.map(
                async (query) =>
                  await transactionalEntityManager.query(query, [version.id, version.createdAt])
              )
            );

            if (auditLogs && auditLogs.length > 0) {
              for (const element of auditLogs) {
                try {
                  await createAuditLog(
                    element.createdUserId,
                    element.projectId,
                    element.details,
                    element.createdBy,
                    element.date,
                    element.table,
                    element.entity,
                    element.tableId,
                    element.tableDetails
                  );
                } catch (error) {
                  console.log(error);
                }
              }
            }
          }
          const revision = await transactionalEntityManager.save(
            SubmittalRevision,
            submittalRevision
          );
          if (documents.length > 0) {
            documents.map((value) => {
              value.submittalRevisionId = revision.id;
              return value;
            });
            await transactionalEntityManager.save(SubmittalDocument, documents);
          }
          await transactionalEntityManager.update(Submittal, submital.id, submital);
          const submodule = await dceModel.findByEntity('submittal');
          if (submodule?.id) {
            const approvalData = await approvalSetupModel.checkSubmodule(submodule?.id, projectId);
            if (approvalData && revision.id) {
              await transactionalEntityManager
                .getRepository(Approver)
                .find({ where: { approvalSetupId: approvalData.id } });
              const entityMetadata = getRepository(SubmittalVersion).metadata;
              const approverList = await transactionalEntityManager
                .getRepository(Approver)
                .find({ where: { approvalSetupId: approvalData.id } });
              const approvalInstance: Approval[] = approverList.map((value) => {
                const approvalInstance: any = {
                  approverId: value.userId,
                  approvalSetupId: approvalData.id,
                  status: 'Pending',
                  projectId: projectId,
                  tableName: `${entityMetadata.schema}.${entityMetadata.tableName}`,
                  tableId: revision.id,
                  entity: 'submittal',
                  dceId: approvalData?.dceId,
                  // moduleId: approvalData?.moduleId,
                  submittedBy: revision.createdBy,
                };
                return approvalInstance;
              });
              await Promise.all(
                approvalInstance.map(async (value) => {
                  return transactionalEntityManager.save(Approval, value);
                })
              );
            }
          }
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  async addRevisionAndDocumentAndChangeStatus(
    submital: DeepPartial<Submittal>,
    documents: SubmittalDocument[],
    submittalVersion: DeepPartial<SubmittalVersion>,
    submittalRevision: SubmittalRevision,
    projectId: string
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          await transactionalEntityManager.update(
            SubmittalVersion,
            submittalVersion.id,
            submittalVersion
          );
          const revision = await transactionalEntityManager.save(
            SubmittalRevision,
            submittalRevision
          );
          await transactionalEntityManager.update(Submittal, submital.id, submital);
          if (documents.length > 0) {
            documents.map((value) => {
              value.submittalRevisionId = revision.id;
              return value;
            });
            await transactionalEntityManager.save(SubmittalDocument, documents);
          }
          const submodule = await dceModel.findByEntity('submittal');
          if (submodule?.id) {
            const approvalData = await approvalSetupModel.checkSubmodule(submodule?.id, projectId);
            if (approvalData && revision.id) {
              await transactionalEntityManager
                .getRepository(Approver)
                .find({ where: { approvalSetupId: approvalData.id } });
              const entityMetadata = getRepository(SubmittalVersion).metadata;
              const approverList = await transactionalEntityManager
                .getRepository(Approver)
                .find({ where: { approvalSetupId: approvalData.id } });
              const approvalInstance: Approval[] = approverList.map((value) => {
                const approvalInstance: any = {
                  approverId: value.userId,
                  approvalSetupId: approvalData.id,
                  status: 'Pending',
                  projectId: projectId,
                  tableName: `${entityMetadata.schema}.${entityMetadata.tableName}`,
                  tableId: revision.id,
                  entity: 'submittal',
                  dceId: approvalData?.dceId,
                  // moduleId: approvalData?.moduleId,
                  submittedBy: revision.createdBy,
                };
                return approvalInstance;
              });
              await Promise.all(
                approvalInstance.map(async (value) => {
                  return transactionalEntityManager.save(Approval, value);
                })
              );
            }
          }
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  async addSubmittalAtProject(submital: Submittal, createdUserId: string, formIds: string[]) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const submittal = await transactionalEntityManager.save(Submittal, submital);
          for (const element of formIds) {
            try {
              const items = {
                formId: element,
                submittalId: submittal.id || '',
              };
              await transactionalEntityManager.save(SubmittalFormAttached, items);
            } catch (error) {
              throw error;
            }
          }
          await createAuditLog(
            createdUserId,
            submital.projectId || '',
            `New submittal added  by ${submittal.createdBy} `,
            `${submittal.createdBy}`,
            new Date(),
            'p_gen.submittal',
            'submittal',
            submittal.id || ''
          );
          return submittal;
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  async updateAcceptStatus(
    updateDataForSubmittal: DeepPartial<Submittal>,
    data: DeepPartial<SubmittalVersion>,
    document: SubmittalDocument[],
    revision: DeepPartial<SubmittalRevision>
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          await transactionalEntityManager.update(
            Submittal,
            updateDataForSubmittal.id,
            updateDataForSubmittal
          );
          await transactionalEntityManager.update(SubmittalVersion, data.id, data);
          await transactionalEntityManager.update(SubmittalRevision, revision.id, revision);
          if (document.length > 0) {
            await transactionalEntityManager.save(SubmittalDocument, document);
          }
          // const approvalDetails = await transactionalEntityManager.findOne(Approval, {
          //   where: { tableName: 'p_gen.submittal_revision', tableId: revision.id },
          // });
          // if (approvalDetails) {
          //   let approvalStatus = 'Rejected';
          //   if (revision.status == 'Accepted') {
          //     approvalStatus = 'Approved';
          //   }
          //   await transactionalEntityManager.update(Approval, approvalDetails.id, {
          //     status: approvalStatus,
          //     ApprovedDate: new Date(),
          //     ApproverComment: revision.reviewerComments,
          //   });
          // }
        } catch (error) {
          throw error as Error;
        }
        return [];
      });
      // Try to find the updated entity
    } catch (error) {
      throw error;
    }
  }

  async update(id: string, data: DeepPartial<Submittal>): Promise<Submittal | undefined | null> {
    try {
      // Attempt to update the entity
      await getManager().getRepository(Submittal).update({ id }, data);

      // Try to find the updated entity
      const updatedEntity = await getManager().getRepository(Submittal).findOne({ where: { id } });

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async countByStatus(projectId: string, status: string) {
    try {
      const data = await getManager()
        .getRepository(Submittal)
        .count({
          where: { projectId, status, isDelete: false },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getFormAndSubmittalDetails(query: string) {
    try {
      const entityManager = getManager();
      const result = await entityManager.query(query);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async addSubmittalAndDocumentAndChangeStatusBeforForm(
    submital: DeepPartial<Submittal>,
    submittalVersion: SubmittalVersion
    // submittalRevision: SubmittalRevision
  ): Promise<string> {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const version = await transactionalEntityManager.save(SubmittalVersion, submittalVersion);
          // submittalRevision.submittalVersionId = version.id;
          // const revision = await transactionalEntityManager.save(
          //   SubmittalRevision,
          //   submittalRevision
          // );
          // if (documents.length > 0) {
          //   documents.map((value) => {
          //     value.submittalRevisionId = revision.id;
          //     return value;
          //   });
          //   await transactionalEntityManager.save(SubmittalDocument, documents);
          // }
          await transactionalEntityManager.update(Submittal, submital.id, submital);
          return version.id;
        } catch (error) {
          throw error as Error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async findAttachedTableData<T extends ObjectLiteral>(
    submittalVersionId: string,
    entity: EntityTarget<T>,
    entityName: string
  ) {
    try {
      const repository = getConnection().getRepository<T>(entity);
      if (entityName in entityRelationList) {
        const output = await repository.find({
          where: { submittalVersionId, isDelete: false } as any,
          relations: entityRelationList[entityName].relation,
        });

        if (output !== null) {
          return output;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  editVersion = async (
    submittalVersionId: string,
    keyword: string,
    projectId: string,
    createdAt: Date,
    transmittalNo: string,
    accosiatedTestQuerys?: string[],
    tableNames?: string[],
    documentToUpdate?: {
      updatedDocument: SubmittalDocument[];
      newPath: string;
      oldPath: string;
    } | null
  ) => {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const submittalVersionDetails = await transactionalEntityManager.findOne(
            SubmittalVersion,
            { where: { id: submittalVersionId }, relations: ['submittal'] }
          );
          await transactionalEntityManager.update(SubmittalVersion, submittalVersionId, {
            keyword,
            transmittalNo,
          });
          if (accosiatedTestQuerys && accosiatedTestQuerys.length > 0) {
            if (tableNames && tableNames.length > 0) {
              await Promise.all(
                tableNames.map(
                  async (tableName) =>
                    await transactionalEntityManager.query(`UPDATE ${tableName}
                      SET "submittalVersionId" = NULL
                      WHERE "submittalVersionId"='${submittalVersionId}';`)
                )
              );
            }
            await Promise.all(
              accosiatedTestQuerys.map(
                async (accosiatedTestQuerys) =>
                  await transactionalEntityManager.query(accosiatedTestQuerys, [
                    submittalVersionId,
                    createdAt,
                  ])
              )
            );
          }
          if (documentToUpdate?.oldPath && documentToUpdate?.newPath) {
            if (submittalVersionDetails?.submittal?.purposeId) {
              await renameFolderAndFileOnSFTP(
                documentToUpdate?.oldPath || '',
                documentToUpdate?.newPath || '',
                projectId,
                submittalVersionDetails?.submittal?.purposeId || ''
              );
            }
            if (documentToUpdate?.updatedDocument && documentToUpdate?.updatedDocument.length > 0) {
              await Promise.all(
                documentToUpdate?.updatedDocument.map(
                  async (value) =>
                    await transactionalEntityManager.update(SubmittalDocument, value.id, value)
                )
              );
            }
          }

          // if (documents.length > 0) {
          //   documents.map((value) => {
          //     value.submittalRevisionId = revision.id;
          //     return value;
          //   });
          //   await transactionalEntityManager.save(SubmittalDocument, documents);
          // }
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  };

  getAllDocumentsBySubmittalVersion = (submittalVersionId: string) => {
    try {
      return getManager()
        .getRepository(SubmittalDocument)
        .find({ where: { revision: { submittalVersionId }, isDelete: false } });
    } catch (error) {
      throw error;
    }
  };
  editRevision = async (
    revisionData: Partial<SubmittalRevision>,
    revisionId: string,
    documentToDelete: string[],
    newDocument: SubmittalDocument[]
  ) => {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          await transactionalEntityManager.update(SubmittalRevision, revisionId, revisionData);
          if (newDocument.length > 0) {
            newDocument.map((value) => {
              value.submittalRevisionId = revisionId;
              return value;
            });
            await transactionalEntityManager.save(SubmittalDocument, newDocument);
          }
          if (documentToDelete.length > 0) {
            if (documentToDelete.length > 0) {
              Promise.all(
                documentToDelete.map(
                  async (value) =>
                    await transactionalEntityManager.update(SubmittalDocument, value, {
                      isDelete: true,
                    })
                )
              );
            }
          }
        } catch (error) {
          throw error as Error;
        }
      });
    } catch (error) {
      throw error;
    }
  };
}

const submittalModel = new SubmittalModel();
export default submittalModel;
