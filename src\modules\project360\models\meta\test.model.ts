import { getManager, In } from 'typeorm';
import { Test } from '../../../../entities/p_meta/Test';

class TestModel {
  constructor() {}
  async getByName(name: string) {
    try {
      const approverSetupRepository = getManager().getRepository(Test);
      const nuclearGaugesData = await approverSetupRepository.findOne({
        where: { name: name, isDelete: false },
      });
      return nuclearGaugesData;
    } catch (error) {
      throw error;
    }
  }

  async getAllTestWithRelation() {
    try {
      const repository = getManager().getRepository(Test);
      const data = await repository.find({
        relations: ['testMethod', 'testVariant'],
        where: { isDelete: false },
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async findById(id: string) {
    try {
      const repository = getManager().getRepository(Test);
      const data = await repository.findOne({
        relations: ['dce'],
        where: { id, isDelete: false },
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async findByIds(ids: string[]) {
    try {
      const repository = getManager().getRepository(Test);
      const data = await repository.find({
        relations: ['dce'],
        where: { id: In(ids), isDelete: false },
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
}

const testModel = new TestModel();
export default testModel;
