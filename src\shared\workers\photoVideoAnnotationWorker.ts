import { photoVideoSftpWithDataAfterAnnotation } from '@utils/Approval/moveFileToSFTP';
import { parentPort, workerData } from 'worker_threads';

(async () => {
  try {
    const { dceId, tableId } = workerData;
    console.log('Worker started processing SFTP:', { dceId, tableId });

    await photoVideoSftpWithDataAfterAnnotation(dceId, tableId);

    console.log('Worker finished processing SFTP successfully');
    parentPort?.postMessage({ status: 'success' });
  } catch (error: any) {
    console.error('Worker Thread Error:', error);
    parentPort?.postMessage({ status: 'error', error: error.message });
  }
})();
