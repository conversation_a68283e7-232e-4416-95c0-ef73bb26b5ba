import { getManager } from 'typeorm';
import { ApprovalSetup } from '../../../entities/p_utils/ApprovalSetup';
import { Approver } from '../../../entities/p_utils/Approver';
import { ApprovalLevel } from '../../../entities/p_utils/ApprovalLevel';
import ApprovalStatusModel from './meta/approvalStatus.model';
import dceModel from './meta/dce.model';
import { entityList, EntityListInterface } from '@utils/entity/entityList';

class ApprovalSetupModel {
  constructor() {}
  async findByProjectId(projectId: string) {
    try {
      const approvalsetupData = await getManager()
        .createQueryBuilder(ApprovalSetup, 'approvalSetup')
        .where('approvalSetup.projectId = :projectId', { projectId })
        .andWhere('approvalSetup.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('approvalSetup.levels', 'levels', 'levels.isDelete = :isDelete', {
          isDelete: false,
        })
        .leftJoinAndSelect(
          'approvalSetup.approvalPurposeType',
          'approvalPurposeType',
          'approvalPurposeType.isDelete = :isDelete',
          { isDelete: false }
        )
        .leftJoinAndSelect('approvalSetup.activity', 'activity', 'activity.isDelete = :isDelete', {
          isDelete: false,
        })
        .leftJoinAndSelect('approvalSetup.dce', 'dce', 'dce.isDelete = :isDelete', {
          isDelete: false,
        })
        .leftJoinAndSelect('levels.approvers', 'approvers', 'approvers.isDelete = :isDelete', {
          isDelete: false,
        })
        .select(['approvalSetup', 'activity', 'dce', 'levels', 'approvers', 'approvalPurposeType'])
        .orderBy('levels.level', 'ASC')
        .getMany();
      return approvalsetupData;
    } catch (error) {
      throw error;
    }
  }

  async add(addApprovalSetup: ApprovalSetup) {
    try {
      const approvalSetupRepository = getManager().getRepository(ApprovalSetup);
      const addedApprovalSetup = approvalSetupRepository.create(addApprovalSetup);
      await approvalSetupRepository.save(addedApprovalSetup);
      return addApprovalSetup;
    } catch (error) {
      throw error;
    }
  }

  async checkSubmodule(dceId: string, projectId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalSetup)
        .findOne({
          where: { dceId, projectId, isDelete: false },
          relations: ['approvalPurposeType'],
        });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async checkSubmoduleWithPurposeType(dceId: string, projectId: string, purposeType: string) {
    try {
      const data = await getManager()
        .getRepository(ApprovalSetup)
        .findOne({
          where: {
            dceId,
            projectId,
            isDelete: false,
            purposeTypeId: purposeType,
          },
          relations: ['approvalPurposeType'],
        });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async checkSubmoduleWithFOrAllPurpose(dceId: string, projectId: string) {
    try {
      const data = await getManager()
        .getRepository(ApprovalSetup)
        .find({
          where: {
            dceId,
            projectId,
            isDelete: false,
          },
          relations: ['approvalPurposeType'],
        });
      return data
        .map((element) => element.purposeTypeId)
        .filter((id) => id !== undefined && id !== null);
    } catch (error) {
      throw error;
    }
  }

  async checkGetApprovers(approvalSetupId: string) {
    try {
      const data = await getManager()
        .getRepository(Approver)
        .findOne({ where: { approvalSetupId, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async approvalSetupCheck(
    moduleId: string,
    dceId: string,
    projectId: string,
    purposeTypeId: string
  ) {
    try {
      const data = await getManager()
        .getRepository(ApprovalSetup)
        .findOne({
          where: { activityId: moduleId, dceId, projectId, purposeTypeId, isDelete: false },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async addApprovalSetup(newApproval: ApprovalSetup, levels: levelInterface[]) {
    try {
      const approvalStatusModel = new ApprovalStatusModel();
      const StatusId = await approvalStatusModel.getByNameStatusId('ready for submission');
      const completedStatusId = await approvalStatusModel.getByNameStatusId('completed');
      const dceData = await dceModel.findById(newApproval.dceId || '');
      return await getManager().transaction(async (transactionalEntityManager) => {
        const finalOut: any = {};
        const approvalRepository = await transactionalEntityManager.save(
          ApprovalSetup,
          newApproval
        );

        await Promise.all(
          levels.map(async (value) => {
            const level: Partial<ApprovalLevel> = {
              createdBy: approvalRepository.createdBy,
              updatedBy: approvalRepository.updatedBy,
              approvalSetupId: approvalRepository.id,
              level: value.level,
              signer: value.signer,
            };

            const addedLevel = await transactionalEntityManager.save(ApprovalLevel, level);

            if (addedLevel && approvalRepository) {
              const finalApprovers = value.userId.map((value) => {
                const newApprover: Partial<Approver> = {};

                newApprover.approvalSetupId = approvalRepository.id;
                newApprover.createdBy = approvalRepository.createdBy;
                newApprover.updatedBy = approvalRepository.updatedBy;
                newApprover.levelId = addedLevel.id;
                newApprover.userId = value;
                return newApprover;
              });
              await transactionalEntityManager.save(Approver, finalApprovers);
            }
          })
        );
        finalOut.approvalSetup = approvalRepository;
        if (dceData && dceData?.entity) {
          if (dceData.entity in entityList) {
            const entityClass = entityList[dceData.entity as keyof EntityListInterface] as any;
            await transactionalEntityManager.update(
              entityClass,
              { approvalStatusId: completedStatusId, purposeId: newApproval.purposeTypeId },
              { approvalStatusId: StatusId }
            );
          }
        }

        console.log('Transaction committed successfully');
        return finalOut;
      });
    } catch (error) {
      console.error('Transaction failed. Rolling back.', error);
      throw error;
    }
  }

  async editApprovalSetup(
    approval: ApprovalSetup,
    newLevels: levelInterface[],
    editLevels: ApprovalLevel[],
    updatedBy: string
  ) {
    try {
      return await getManager().transaction(async (transactionalEntityManager) => {
        const finalOut: any = {};
        const approvalRepository = await transactionalEntityManager.update(
          ApprovalSetup,
          approval.id,
          approval
        );
        if (newLevels && newLevels.length > 0) {
          await Promise.all(
            newLevels.map(async (value) => {
              const level: Partial<ApprovalLevel> = {
                createdBy: updatedBy,
                updatedBy: updatedBy,
                approvalSetupId: approval.id,
                level: value.level,
                signer: value.signer,
              };

              const addedLevel = await transactionalEntityManager.save(ApprovalLevel, level);

              if (addedLevel && approvalRepository) {
                const finalApprovers = value.userId.map((value) => {
                  const newApprover: Partial<Approver> = {};

                  newApprover.approvalSetupId = approval.id;
                  newApprover.createdBy = updatedBy;
                  newApprover.updatedBy = updatedBy;
                  newApprover.levelId = addedLevel.id;
                  newApprover.userId = value;
                  return newApprover;
                });
                await transactionalEntityManager.save(Approver, finalApprovers);
              }
            })
          );
        }
        if (editLevels && editLevels.length > 0) {
          await Promise.all(
            editLevels.map(async (value) => {
              const level: Partial<ApprovalLevel> = {
                createdBy: updatedBy,
                updatedBy: updatedBy,
                approvalSetupId: approval.id,
                level: value.level,
                signer: value.signer,
                isDelete: value.isDelete && value.isDelete == true ? true : false,
              };

              const editLevel = await transactionalEntityManager.update(
                ApprovalLevel,
                value.id || '',
                level
              );

              if (editLevel) {
                const finalApprovers = (value as any)?.userId?.map((item: string) => {
                  const newApprover: Partial<Approver> = {};

                  newApprover.approvalSetupId = approval.id;
                  newApprover.createdBy = updatedBy;
                  newApprover.updatedBy = updatedBy;
                  newApprover.levelId = value.id;
                  newApprover.userId = item;
                  return newApprover;
                });
                if (finalApprovers && finalApprovers.length > 0) {
                  await transactionalEntityManager.update(
                    Approver,
                    { levelId: value.id || '' },
                    { isDelete: true }
                  );
                  await transactionalEntityManager.save(Approver, finalApprovers);
                }
              }
            })
          );
        }
        finalOut.approvalSetup = approvalRepository;

        console.log('Transaction committed successfully');
        return finalOut;
      });
    } catch (error) {
      console.error('Transaction failed. Rolling back.', error);
      throw error;
    }
  }

  async findApprovalLevelsBySetupId(approvalSetupId: string) {
    try {
      const data = await getManager()
        .getRepository(ApprovalLevel)
        .find({ where: { approvalSetupId, isDelete: false } });

      return data;
    } catch (error) {
      throw error;
    }
  }
}

interface levelInterface {
  level: number;
  userId: string[];
  signer: boolean;
}
const approvalSetupModel = new ApprovalSetupModel();
export default approvalSetupModel;
