import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { ConcreteMixDesign } from './ConcreteMixDesign';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ProjectMaterial } from '../p_gen/ProjectMaterial';
import { SampleType } from '../p_meta/SampleType';
import { Purpose } from '../p_meta/Purpose';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { EventLog } from '@entities/p_gen/EventLog';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class ConcreteBatchTicket extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Mix Id',
      fieldName: 'mixId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      // mixId
      query: `SELECT id FROM p_cs.concrete_mix_design WHERE "mixId" = $1`,
      getListQuery:
        'SELECT id,"mixId" as name FROM p_cs.concrete_mix_design WHERE "projectId" = $1 ORDER BY "updatedAt" DESC;',
      listParams: 'id',
      listName: 'mixIdList',
    },
  })
  @Column({ nullable: true })
  mixId?: string;

  @ManyToOne(() => ConcreteMixDesign, { nullable: true })
  @JoinColumn({ name: 'mixId' })
  concreteMixDesign?: ConcreteMixDesign;

  @ColumnInfo({
    customData: {
      name: 'Material',
      fieldName: 'materialId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_gen.material WHERE "name" = $1`,
      getListQuery:
        'SELECT id,"name" as name FROM p_gen.material WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 20;',
      listParams: 'id',
      listName: 'materialList',
    },
  })
  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @ColumnInfo({
    customData: {
      name: 'Batch Plant Id',
      fieldName: 'plantId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  plantId?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Mixer ID',
      fieldName: 'mixerId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  mixerId?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Number',
      fieldName: 'batchNumber',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  batchNumber?: number;

  @ColumnInfo({
    customData: {
      name: 'Ticket Number',
      fieldName: 'ticketNumber',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  ticketNumber?: number;

  @ColumnInfo({
    customData: {
      name: 'Truck Id',
      fieldName: 'truckId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  truckId?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Date Time',
      fieldName: 'batchedDateTime',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ type: 'timestamp', nullable: true })
  batchedDateTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Batch Used For',
      fieldName: 'batchUsed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  batchUsed?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Placement Location',
      fieldName: 'batchPlacementLocation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  batchPlacementLocation?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Batch Quantity in Cubic Yards',
      fieldName: 'batchVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  batchVolume?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Batch Weight in lbs',
      fieldName: 'batchWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  batchWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Target Water-Cement Ratio',
      fieldName: 'wcRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wcRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water-Cement Ratio',
      fieldName: 'wcActual',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wcActual?: number;

  @ColumnInfo({
    customData: {
      name: 'Water Quantity Trimmed for Slump Adjustment',
      fieldName: 'waterTrimmed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterTrimmed?: number;

  @ColumnInfo({
    customData: {
      name: 'Water Quantity Added for Slump Adjustment',
      fieldName: 'waterAdded',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterAdded?: number;

  @ColumnInfo({
    customData: {
      name: 'Addable Water',
      fieldName: 'addableWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  addableWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Water Quantity Added',
      fieldName: 'totalWaterAdded',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totaWaterAdded?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Water Allowed',
      fieldName: 'maximumWaterAllowed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumWaterAllowed?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Water Quantity in Gallons',
      fieldName: 'waterRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Water Quantity in Gallons',
      fieldName: 'waterBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Water Variance',
      fieldName: 'waterPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Ice Quantity in lbs',
      fieldName: 'iceRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  iceRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Ice Quantity in lbs',
      fieldName: 'iceBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  iceBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Ice Variance',
      fieldName: 'icePercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  icePercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Design Sand Quantity in lbs per CY',
      fieldName: 'sandDesigned',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandDesigned?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Sand Quantity in lbs',
      fieldName: 'sandRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Sand Quantity in lbs',
      fieldName: 'sandBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Sand Variance',
      fieldName: 'sandPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Sand Percent Mositure',
      fieldName: 'sandPercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandPercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of Sand in Gallons',
      fieldName: 'sandActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required #8 Aggregate Quantity in lbs',
      fieldName: 'sieveNo8AggregateRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo8AggregateRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched #8 Aggregate Quantity in lbs',
      fieldName: 'sieveNo8AggregateBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo8AggregateBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent #8 Aggregate Variance',
      fieldName: 'sieveNo8AggregatePercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo8AggregatePercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: '#8 Aggregate Percent Mositure',
      fieldName: 'sieveNo8AggregatePercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo8AggregatePercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of #8 Aggregate in Gallons',
      fieldName: 'sieveNo8AggregateActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo8AggregateActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required #67 Aggregate Quantity in lbs',
      fieldName: 'sieveNo67AggregateRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo67AggregateRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched #67 Aggregate Quantity in lbs',
      fieldName: 'sieveNo67AggregateBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo67AggregateBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent #67 Aggregate Variance',
      fieldName: 'sieveNo67AggregatePercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo67AggregatePercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: '#67 Aggregate Percent Mositure',
      fieldName: 'sieveNo67AggregatePercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo67AggregatePercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of #67 Aggregate in Gallons',
      fieldName: 'sieveNo67AggregateActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo67AggregateActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required #4 Aggregate Quantity in lbs',
      fieldName: 'sieveNo4AggregateRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo4AggregateRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched #4 Aggregate Quantity in lbs',
      fieldName: 'sieveNo4AggregateBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo4AggregateBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent #4 Aggregate Variance',
      fieldName: 'sieveNo4AggregatePercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo4AggregatePercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: '#4 Aggregate Percent Mositure',
      fieldName: 'sieveNo4AggregatePercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo4AggregatePercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of #4 Aggregate in Gallons',
      fieldName: 'sieveNo4AggregateActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieveNo4AggregateActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required 3In Aggregate Quantity in lbs',
      fieldName: 'sieve3InAggregateRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve3InAggregateRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched 3In Aggregate Quantity in lbs',
      fieldName: 'sieve3InAggregateBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve3InAggregateBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent 3In Aggregate Variance',
      fieldName: 'sieve3InAggregatePercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve3InAggregatePercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: '3In Aggregate Percent Mositure',
      fieldName: 'sieve3InAggregatePercentMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve3InAggregatePercentMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'Actual Water of 3In Aggregate in Gallons',
      fieldName: 'sieve3InAggregateActualWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sieve3InAggregateActualWater?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Cement Quantity in lbs',
      fieldName: 'cementRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Cement Quantity in lbs',
      fieldName: 'cementBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Cement Variance',
      fieldName: 'cementPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Required Fly Ash Quantity in lbs',
      fieldName: 'flyAshRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flyAshRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'Batched Fly Ash Quantity in lbs',
      fieldName: 'flyAshBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flyAshBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Percent Fly Ash Variance',
      fieldName: 'flyAshPercentVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flyAshPercentVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate Design',
      fieldName: 'fineAggregateDesign',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateDesign?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate Required',
      fieldName: 'fineAggregateRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate Batched',
      fieldName: 'fineAggregateBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate Variance',
      fieldName: 'fineAggregateVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate Moisture',
      fieldName: 'fineAggregateMoisture',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateMoisture?: number;

  @ColumnInfo({
    customData: {
      name: 'fine Aggregate ActWater',
      fieldName: 'fineAggregateActWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateActWater?: number;

  @ColumnInfo({
    customData: {
      name: 'admixtureRequired',
      fieldName: 'admixtureRequired',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixtureRequired?: number;

  @ColumnInfo({
    customData: {
      name: 'admixture Batched',
      fieldName: 'admixtureBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixtureBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'admixtureVariance',
      fieldName: 'admixtureVariance',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixtureVariance?: number;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ default: false })
  isDelete?: boolean;
}
