import { Request, Response } from 'express';
import taskModel from '../../models/task/task.model';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class TaskController {
  constructor() {}

  async addTaskAttachment(req: Request, res: Response) {
    try {
      req.body.createdBy = (req as any).user.name;
      req.body.updatedBy = (req as any).user.name;
      // getting value from request
      const newAttachment = req.body;

      // adding to database
      const attachmentData = await taskModel.addPorjectAttachment(newAttachment);
      // sending success response
      return res.status(200).json({
        isSucceed: true,
        data: attachmentData,
        msg: 'attachment added',
      });
    } catch (error) {
      // sending error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findTaskByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findTaskByProjectId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findTaskByUserId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findTaskByuserId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findTaskByUserIdWithStatus(req: Request, res: Response) {
    try {
      const status: string = req.params.status;
      // getting the data from database with the given id
      const taskData = await taskModel.findTaskByuserIdByStatus(req.params.id, status);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findTaskByTypeId(req: Request, res: Response) {
    try {
      let task: any[] = [];
      if (req.params.typeId === 'all') {
        const taskData = await taskModel.findTaskByProjectId(req.params.id);
        task = taskData;
      } else {
        const taskData = await taskModel.findTaskByTypeId(req.params.id, req.params.typeId);
        task = taskData;
      }
      // getting the data from database with the given id

      // checking if data is found with the id
      if (task.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: task,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findTaskByProjectIdAndBucketId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findTaskByProjectIdAndBucket(
        req.params.id,
        Number(req.params.bucketId)
      );
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findBucketByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findBucketByProjectId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'Buckets found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Bucket data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findBucketByProjectIdWithTask(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findBucketByProjectIdWithTask(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'Buckets found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Bucket data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findpriorityByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findPriorityByProjectId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'priority found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No priority data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findStatusByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findStatusByProjectId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'status found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No status data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findTypeByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findTypeByProjectId(req.params.id);
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'Type found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No type data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findTaskById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const taskData = await taskModel.findTaskById(Number(req.params.id));
      // checking if data is found with the id
      if (taskData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: taskData,
          msg: 'task found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: false,
          data: [],
          msg: 'No task data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

const taskController = new TaskController();
export default taskController;
