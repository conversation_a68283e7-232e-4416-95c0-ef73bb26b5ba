import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { sftpUploadProjectReportForPostBlastMiddleware } from '../../../../../shared/middlewares/uploadFileSftp.middleware';
import { PostBlastVideo } from '../../../../../entities/p_cs/PostBlastVideo';
import { createSubmittalBeforeFormMiddleware } from '../../../../../shared/middlewares/addSubmittalVersion.middleware';
// import { dateZoneConversionFroPostBlastVideo } from '../../../../../shared/middlewares/dateConversion/dateConversion.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const CrudFunctionController = new CrudController<PostBlastVideo>(PostBlastVideo);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post(
  '/',
  authenticateToken,
  sftpUploadProjectReportForPostBlastMiddleware,
  createSubmittalBeforeFormMiddleware,
  // dateZoneConversionFroPostBlastVideo,
  (req, res) => CrudFunctionController.create(req, res, 'postBlastVideo')
);
router.put('/:id', authenticateToken, sftpUploadProjectReportForPostBlastMiddleware, (req, res) =>
  CrudFunctionController.update(req, res, 'postBlastVideo')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.getDataCountByProjectId(req, res, 'postBlastVideo')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  CrudFunctionController.findByWorkActivityId(req, res, 'postBlastVideo')
);
router.get('/:id', authenticateToken, CrudFunctionController.findById);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.findByProjectId(req, res, 'postBlastVideo')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  CrudFunctionController.sendForApproval(req, res, 'postBlastVideo')
);
router.get('/', authenticateToken, CrudFunctionController.findAll);
router.delete('/:id', authenticateToken, (req, res) =>
  CrudFunctionController.softDelete(req, res, 'postBlastVideo')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  CrudFunctionController.multiSoftDelete(req, res, 'postBlastVideo')
);

export default router;
