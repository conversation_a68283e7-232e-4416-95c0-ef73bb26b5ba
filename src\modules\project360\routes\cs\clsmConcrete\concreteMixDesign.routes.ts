import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { ConcreteMixDesign } from '../../../../../entities/p_cs/ConcreteMixDesign';

const router: Router = express.Router();

const GenricController = new CrudController<ConcreteMixDesign>(ConcreteMixDesign);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'concreteMixDesign')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'concreteMixDesign')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'concreteMixDesign')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'concreteMixDesign')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'concreteMixDesign')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'concreteMixDesign')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'concreteMixDesign')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'concreteMixDesign')
);

export default router;
