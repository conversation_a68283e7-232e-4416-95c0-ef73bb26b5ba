import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  getRepository,
  BeforeUpdate,
  BeforeInsert,
} from 'typeorm';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from '../p_gen/Project';
import { Cardinality, RelationshipType } from '../p_meta/RelationshipType';

@Entity({ schema: 'p_utils' })
export class DCERelationship {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId!: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  sourceDCEId?: string;

  @Column({ nullable: true })
  sourceDataId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'sourceDCEId' })
  sourceDCE?: DataCaptureElements;

  @Column({ nullable: true })
  targetDCEId?: string;

  @Column({ nullable: true })
  targetDataId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'targetDCEId' })
  targetDCE?: DataCaptureElements;

  @Column()
  relationshipTypeId!: string;

  @ManyToOne(() => RelationshipType, { nullable: true })
  @JoinColumn({ name: 'relationshipTypeId' })
  relationshipType?: RelationshipType;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true, default: false })
  isMultiSelect?: boolean;

  @Column({ nullable: true })
  orderBy?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true, type: 'jsonb' })
  additionalDetails?: any;

  @BeforeInsert()
  @BeforeUpdate()
  async validateCardinality() {
    const relationshipType = await getRepository(RelationshipType).findOne({
      where: { id: this.relationshipTypeId },
    });

    if (relationshipType) {
      const cardinality = relationshipType.cardinality;

      // Check existing relationships between sourceDCEId and targetDCEId
      const query = await getRepository(DCERelationship)
        .createQueryBuilder('dcer')
        .where('dcer.sourceDCEId = :sourceDCEId', { sourceDCEId: this.sourceDCEId })
        .andWhere('dcer.sourceDataId = :sourceDataId', { sourceDataId: this.sourceDataId })
        .andWhere('dcer.isDelete = false');

      if (this.id) {
        query.andWhere('dcer.id != :id', { id: this.id });
      }

      const existingRelationships = await query.getCount();
      if (cardinality === Cardinality.ONE_TO_ONE && existingRelationships > 0) {
        throw new Error(
          'Cardinality violation: Only one relationship is allowed between these entities.'
        );
      }

      if (cardinality === Cardinality.ONE_TO_MANY && existingRelationships > 1) {
        throw new Error('Cardinality violation: Only one-to-many relationships are allowed.');
      }

      // No need to check for MANY_TO_MANY since it allows multiple relationships
    }
  }
}
