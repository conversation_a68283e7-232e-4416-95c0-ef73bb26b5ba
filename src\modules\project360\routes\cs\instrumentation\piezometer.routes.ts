import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { Piezometer } from '../../../../../entities/p_cs/Piezometer';

const router: Router = express.Router();

const GenricController = new CrudController<Piezometer>(Piezometer);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'piezometer')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'piezometer')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'piezometer')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'piezometer')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'piezometer'));
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'piezometer')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'piezometer')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'piezometer')
);

export default router;
