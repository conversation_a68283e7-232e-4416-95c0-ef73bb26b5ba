import { getManager } from 'typeorm';
import { ActivityLog } from '../../../entities/p_gen/ActivityLog';

class AuditLogModel {
  constructor() {}
  async addLog(newAuditLog: ActivityLog) {
    try {
      const auditLogRepository = getManager().getRepository(ActivityLog);
      const addedAuditLog = auditLogRepository.create(newAuditLog);
      await auditLogRepository.save(addedAuditLog);
      return addedAuditLog;
    } catch (error) {
      throw error;
    }
  }
  async findByProjectId(projectId: string) {
    try {
      // return await getManager()
      //   .getRepository(Feature)
      //   .find({ where: { proejctId: projectId } });

      const auditLog = await getManager()
        .getRepository(ActivityLog)
        .find({
          where: { projectId: projectId, isDelete: false },
        });

      return auditLog;
    } catch (error) {
      throw error;
    }
  }

  async findByTableIdEntity(projectId: string, tableId: string, entity: string) {
    try {
      // return await getManager()
      //   .getRepository(Feature)
      //   .find({ where: { proejctId: projectId } });

      const auditLog = await getManager()
        .getRepository(ActivityLog)
        .find({
          where: { projectId, tableId, entity, isDelete: false },
          order: { date: 'ASC' },
        });

      return auditLog;
    } catch (error) {
      throw error;
    }
  }
}

const auditLogModel = new AuditLogModel();
export default auditLogModel;
