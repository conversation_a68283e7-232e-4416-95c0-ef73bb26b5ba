import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  AfterLoad,
} from 'typeorm';
import { ApprovalSetup } from './ApprovalSetup';
import { ApprovalLevel } from './ApprovalLevel';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

@Entity({ schema: 'p_utils' })
export class Approver {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  approvalSetupId?: string;

  @ManyToOne(() => ApprovalSetup)
  @JoinColumn({ name: 'approvalSetupId' })
  approval?: ApprovalSetup;

  @Column({ nullable: true })
  levelId?: string;

  @ManyToOne(() => ApprovalLevel, { nullable: true })
  @JoinColumn({ name: 'levelId' })
  approvalLevel?: ApprovalLevel;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  user?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.userId) {
        const data = await getUserById(this.userId);
        this.user = data;
      } else {
        this.user = null;
      }
    } catch (error) {
      this.user = null;
    }
  }
}
