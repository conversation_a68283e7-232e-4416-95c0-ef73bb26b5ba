const ucsQuery = `SELECT 
    COALESCE(p_qms.st_soil_ucs."testedBy"::varchar, 'N/A') AS "testedBy",
    COALESCE(p_qms.st_soil_ucs."materialId"::varchar, 'N/A') AS "materialId",
    COALESCE(p_qms.st_soil_ucs."testNo"::varchar, 'N/A') AS "testNo",
    COALESCE(p_qms.st_soil_ucs."castDate"::varchar, 'N/A') AS "castDate",
    COALESCE(p_qms.st_soil_ucs."dateTested"::varchar, 'N/A') AS "dateTested",
    COALESCE(p_qms.st_soil_ucs."specimenAge"::varchar, 'N/A') AS "specimenAge",
    COALESCE(p_qms.st_soil_ucs."sampleId"::varchar, 'N/A') AS "sampleId",
    COALESCE(p_qms.st_soil_ucs."averageCylinderDiameter"::varchar, 'N/A') AS "averageCylinderDiameter",
    COALESCE(p_qms.st_soil_ucs."averageCylinderLength"::varchar, 'N/A') AS "averageCylinderLength",
    COALESCE(p_qms.st_soil_ucs."crossSectionalArea"::varchar, 'N/A') AS "crossSectionalArea",
    COALESCE(p_qms.st_soil_ucs."lengthDiameterRatio"::varchar, 'N/A') AS "lengthDiameterRatio",
    COALESCE(p_qms.st_soil_ucs."wetDensity"::varchar, 'N/A') AS "wetDensity",
    COALESCE(p_qms.st_soil_ucs."dryDensity"::varchar, 'N/A') AS "dryDensity",
    COALESCE(p_qms.st_soil_ucs."waterContent"::varchar, 'N/A') AS "waterContent",
    COALESCE(p_qms.st_soil_ucs."deformationLength"::varchar, 'N/A') AS "deformationLength",
    COALESCE(p_qms.st_soil_ucs."strainRate"::varchar, 'N/A') AS "strainRate",
    COALESCE(p_qms.st_soil_ucs."failureTime"::varchar, 'N/A') AS "failureTime",
    COALESCE(p_qms.st_soil_ucs."shearStrength"::varchar, 'N/A') AS "shearStrength",
    COALESCE(p_qms.st_soil_ucs."failureStrain"::varchar, 'N/A') AS "failureStrain",
    COALESCE(p_qms.st_soil_ucs."unconfinedCompressiveStrength"::varchar, 'N/A') AS "unconfinedCompressiveStrength",
        COALESCE(p_cs.sample."mixId"::text, 'N/A') AS "mixId",
    COALESCE(p_cs.concrete_mix_design."mixId", 'N/A') AS "mixCode",
    COALESCE(p_cs.pv_cut_master."uniqueLabel", 'N/A') AS "uniqueLabel"
FROM p_qms.st_soil_ucs
LEFT JOIN p_cs.sample ON p_qms.st_soil_ucs."sampleId" = p_cs.sample.id
LEFT JOIN p_cs.pv_cut_master ON p_cs.sample."panelNumberId" = p_cs.pv_cut_master.id
LEFT JOIN p_cs.concrete_mix_design ON p_cs.sample."mixId" = p_cs.concrete_mix_design.id
WHERE p_qms.st_soil_ucs."isDelete" = false
  AND p_qms.st_soil_ucs."sampleId" = {sampleId}`

  export { ucsQuery };