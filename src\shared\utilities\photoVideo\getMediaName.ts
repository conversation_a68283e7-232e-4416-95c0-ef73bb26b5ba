import { PhotoVideo } from '../../../entities/p_gen/PhotoVideo';
import stringTemplate from 'string-template';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import sftpConfidModel from '../../../modules/project360/models/sftpConfig.model';
// import mediaTypeModel from '../../../modules/project360/models/mediaType.model';
import GeneralProjectAreaModel from '../../../modules/project360/models/gen/area.model';
import featuretModel from '../../../modules/project360/models/feature.model';
import { getCurrentDateInDDMMYYYYFormatForPhotoVideo } from '../custom/dateFormating';

export const getMediaName = async (newPhotoVideoData: Partial<PhotoVideo>) => {
  try {
    const dceDetails = await dceModel.findByEntity('photoVideo');
    if (!dceDetails || !dceDetails.id || !newPhotoVideoData.dateTime) {
      throw new Error('Photo video DCE not found');
    }
    // const mediaName = await mediaTypeModel.getNameById(newPhotoVideoData?.typeId || '');
    if (newPhotoVideoData.featureId && newPhotoVideoData.generalProjectAreaId) {
      const featureName = await featuretModel.findBynameId(newPhotoVideoData.featureId);
      const generalProjectAreaModel = new GeneralProjectAreaModel();
      const GPAName = await generalProjectAreaModel.getNameById(
        newPhotoVideoData.generalProjectAreaId
      );
      const date = getCurrentDateInDDMMYYYYFormatForPhotoVideo(
        new Date(newPhotoVideoData.dateTime) || newPhotoVideoData?.createdAt || new Date()
      );
      const sftpData = await sftpConfidModel.findByDCEId(
        dceDetails?.id,
        newPhotoVideoData.projectId || ''
      );
      const photoNumber = `${newPhotoVideoData.photoNumber}`;
      if (sftpData && sftpData?.fileName) {
        const data = {
          date: date,
          description:
            newPhotoVideoData.description || 'new photo'.split('.').join('').replace(/ /g, '_'),
          // mediaType: mediaName?.split('.').join('').replace(/ /g, '_'),
          mediaType: featureName?.split('.').join('').replace(/ /g, '_'),
          feature: featureName?.split('.').join('').replace(/ /g, '_'),
          generalProjectArea: GPAName?.split('.').join('').replace(/ /g, '_'),
          photoNumber: photoNumber?.split('.').join('').replace(/ /g, '_'),
        };
        const newFileName = stringTemplate(sftpData?.fileName, data);
        return newFileName;
      }
    }
    return `${Date.now()}`;
  } catch (error) {
    throw error;
  }
};
