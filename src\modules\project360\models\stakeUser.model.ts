import { getManager, getRepository } from 'typeorm';
import { StakeUser } from '../../../entities/p_auth/StakeUser';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { getAllUsers } from 'src/shared/server/platformApi/user';
import { ProjectRoleRoute } from '@entities/p_auth/projectRoleRoute';
interface UserInfo {
  stakeuser_id: string;
  userId: string;
  name: string;
  email: string;
  role: string;
  stakeholderName: string;

}
class StakeUserModel {
  constructor() {}
  async addStakeUser(newStakeUser: StakeUser) {
    try {
      const stakeUserRepository = getManager().getRepository(StakeUser);
      const addedStakeUser = stakeUserRepository.create(newStakeUser);
      await stakeUserRepository.save(addedStakeUser);
      return addedStakeUser;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectAndUserId(projectId: string, userId: string): Promise<boolean> {
    const stakeholderRepository = getManager().getRepository(Stakeholder);
    const stakeholdersInProject = await stakeholderRepository.find({
      where: { projectId },
      relations: ['stakeUsers'],
    });

    let userFound = false;

    for (const stakeholder of stakeholdersInProject) {
      const userExist = stakeholder.stakeUsers?.find((item) => item.userId === userId);
      if (userExist) {
        userFound = true;
        break;
      }
    }

    return userFound;
  }

  async findByStakeholderId(stakeholderId: number) {
    try {
      return await getManager()
        .getRepository(StakeUser)
        .createQueryBuilder('stakeUser')
        .leftJoin('stakeUser.role', 'role')
        .where('stakeUser.stakeholderId = :stakeholderId', { stakeholderId })
        .andWhere('stakeUser.isDelete = :isDelete', { isDelete: false })
        .select([
          'stakeUser.createdAt',
          'stakeUser.createdBy',
          'stakeUser.id',
          'stakeUser.roleId',
          'stakeUser.stakeholderId',
          'stakeUser.updatedAt',
          'stakeUser.updatedBy',
          'stakeUser.userId',
          'role.name',
        ])
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async checkUserInProject(userId: string, projectId: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);

      const stakeuser = await stakeUserRepository
        .createQueryBuilder('stakeUser')
        .leftJoinAndSelect('stakeUser.stakeholder', 'stakeholder')
        .leftJoinAndSelect('stakeUser.role', 'role')
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeUser.isDelete = :isDelete', { isDelete: false })
        .andWhere('stakeUser.userId = :userId', { userId })
        .select(['stakeUser', 'stakeholder', 'role'])
        .getOne();
      return stakeuser ? true : false;
    } catch (error) {
      throw error;
    }
  }

  async findByUserListProjectId(projectId: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);

      const stakeusers = await stakeUserRepository
        .createQueryBuilder('stakeUser')
        .leftJoinAndSelect('stakeUser.stakeholder', 'stakeholder')
        .leftJoinAndSelect('stakeUser.role', 'role')
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeUser.isDelete = :isDelete', { isDelete: false })
        .select(['stakeUser', 'stakeholder', 'role'])
        .getMany();

      const allUsers = await getAllUsers();

      return stakeusers.filter((stakeUser) => {
        const user = allUsers.find((user) => user.id === stakeUser.userId);
        if (user && !user.isDelete) {
          stakeUser.user = user; // Attach the user details
          return true; // Keep this item
        }
        return false; // Remove from the array
      });
    } catch (error) {
      throw error;
    }
  }
  async findAllUserListProject() {
    const allUsers = await getAllUsers();
    const projectAttachedData = await Promise.all(
      allUsers.map(async (user) => {
        const stakeUserRepository = getRepository(StakeUser);
        const stakeusers = await stakeUserRepository
          .createQueryBuilder('stakeUser')
          .leftJoinAndSelect('stakeUser.stakeholder', 'stakeholder')
          .leftJoinAndSelect('stakeholder.project', 'project')
          .leftJoinAndSelect('stakeUser.role', 'role')
          .where('stakeUser.userId = :userId', { userId: user.id })
          .andWhere('stakeUser.isDelete = :isDelete', { isDelete: false })
          .select(['stakeUser', 'stakeholder', 'role'])
          .getMany();
        if (stakeusers.length > 0) {
          const allProjects = stakeusers.map((stakeUser) => {
            return {
              name: stakeUser?.stakeholder?.project?.name,
              role: stakeUser?.role?.name,
              projectId: stakeUser?.stakeholder?.project?.id,
              id: stakeUser?.id,
              designation: stakeUser?.designation,
              stakeholder: stakeUser?.stakeholder?.name,
            };
          });
          (user as any).projects = allProjects.length > 0 ? allProjects : [];
        } else {
          (user as any).projects = [];
        }
        return user;
      })
    );
    return projectAttachedData;
  }
  async findByUserListProjectIdForDropdown(projectId: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);

      const stakeUser = await stakeUserRepository
        .createQueryBuilder('stakeUser')
        .leftJoinAndSelect('stakeUser.stakeholder', 'stakeholder')
        .leftJoinAndSelect('stakeUser.role', 'role')
        .where('stakeholder.projectId = :projectId', { projectId })
        .select(['stakeUser', 'stakeholder', 'role'])
        .getMany();

      const sortedStakeUsers = stakeUser.sort((a, b) =>
        (a.user?.firstName || '').localeCompare(b.user?.firstName || '')
      );

      return sortedStakeUsers
        .filter((item) => item.user?.firstName && item.user.lastName)
        .map((item) => ({
          name: `${item.user?.firstName} ${item.user?.lastName}`,
          value: item.userId,
        }));
    } catch (error) {
      throw error;
    }
  }
  async delete(id: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);
      await stakeUserRepository.update({ id }, { isDelete: true });
      const stakeUser = await stakeUserRepository.findOne({
        where: { id },
        relations: ['stakeholder', 'stakeholder.project'],
      });

      return stakeUser;
    } catch (error) {
      throw error;
    }
  }
  async getUserInfoByProjectId(projectId: string): Promise<UserInfo[]> {
    try {
      const stakeHolderRepo = getRepository(Stakeholder);
      // gettnig all stakeholders for the project
      const rawStakeholders = await stakeHolderRepo
        .createQueryBuilder('stakeholder')
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeholder.isDelete = false')
        .select(['stakeholder.id', 'stakeholder.name'])
        .getMany();
     if (!rawStakeholders.length) return [];
   //  getting all users details along with role for the stakeholders 
     const stakeholderMap = new Map(rawStakeholders.map(s => [s.id, s.name]));
      const stakeholderIds = rawStakeholders.map(s => s.id);
      const stakeUserRepo = getRepository(StakeUser);
      const stakeUsers = await stakeUserRepo
        .createQueryBuilder('stakeUser')
        .leftJoinAndSelect('stakeUser.role', 'role') 
        .where('stakeUser.stakeholderId IN (:...stakeholderIds)', { stakeholderIds })
        .andWhere('stakeUser.isDelete = false')
        .select(['stakeUser.id', 'stakeUser.userId', 'stakeUser.stakeholderId', 'stakeUser.roleId', 'stakeUser.designation', 'role.name'])
        .getMany();
        if (!stakeUsers.length) return [];
      const result: UserInfo[] = stakeUsers.map(su => ({
        stakeuser_id: su.id,  
        userId: su.user?.id ?? '',  
        name: `${su.user?.firstName ?? ''} ${su.user?.lastName ?? ''}`.trim(),  
        email: su.user?.email ?? '', 
        role: su.role?.name ?? '',  
        stakeholderName: su.stakeholderId ? stakeholderMap.get(su.stakeholderId) ?? '' : '',
        stakeholderId: su.stakeholderId ?? '',  
      }));
      return result.filter(r => r.name && r.email && r.role && r.stakeholderName && r.stakeuser_id && r.userId);

    } catch (error) {
      console.error('Error fetching user info by project ID:', error);
      throw error;
    }
  }
  
  async getPageRoutesByUserIdAndProjectId(userId: string, projectId: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);
      const roleIdRaw = await stakeUserRepository
        .createQueryBuilder('stakeUser')
        .leftJoin('stakeUser.stakeholder', 'stakeholder')
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeUser.isDelete = false')
        .andWhere('stakeUser.userId = :userId', { userId })
        .select('stakeUser.roleId', 'roleId')
        .getRawOne();
  
      if (!roleIdRaw || !roleIdRaw.roleId) {
       return []; 
      }
      const roleId = roleIdRaw.roleId;
      const projectRoleRouteRepo = getRepository(ProjectRoleRoute);
      const projectRoleRoutes = await projectRoleRouteRepo
      .createQueryBuilder('prr')
      .innerJoinAndSelect('prr.route', 'route')
      .where('prr.projectRoleId = :roleId', { roleId })
        .andWhere('prr.isDelete = false')
        .andWhere('route.isDelete = false')
        .select([
          'route.id',
          'route.name',
          'route.route',
          'route.routeObject',
          'route.isActive',
          'route.type',
          'route.createdAt',
          'route.createdBy',
          'route.createdUserId',
          'route.updatedAt',
          'route.updatedBy',
          'prr.permission',      
          'prr.projectRoleId',
          'prr.routeId',
        ])
        .getMany();
          const routes = projectRoleRoutes.map((prr) => prr.route);
            return routes;
          } catch (error) {
            throw error; 
          }
  }
  
         
}

const stakeUserModel = new StakeUserModel();
export default stakeUserModel;
