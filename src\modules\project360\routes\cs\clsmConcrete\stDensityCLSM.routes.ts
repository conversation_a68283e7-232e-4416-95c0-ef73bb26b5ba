import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StDensityCLSM } from '../../../../../entities/p_cs/StDensityCLSM';

const router: Router = express.Router();

const GenricController = new CrudController<StDensityCLSM>(StDensityCLSM);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'stDensityCLSM')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'stDensityCLSM')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'stDensityCLSM')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'stDensityCLSM')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'stDensityCLSM')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'stDensityCLSM')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'stDensityCLSM')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'stDensityCLSM')
);

export default router;
