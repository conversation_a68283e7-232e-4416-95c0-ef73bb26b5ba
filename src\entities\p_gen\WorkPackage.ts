import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { Project } from '../p_gen/Project'; // Import the Projects entity
import { Site } from './Site';
import { WorkPackageActivity } from './WorkPackageActivity';
import { Area } from './Area';
import { Feature } from './Feature';
import Structure from './Structure';
import { EmailList } from './EmailList';
import { BaseEntityWithAudit } from '@entities/common/AuditColumns';

@Entity({ schema: 'p_gen' })
export class WorkPackage extends BaseEntityWithAudit {
  @Column({ nullable: true, unique: true })
  workPackageNo?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;

  @Column()
  startDate?: Date;

  @Column()
  endDate?: Date;

  @Column({ nullable: true })
  title?: string;

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  // Used for calendar invite. When a calendar invite is updated sequence no will be incremented
  @Column({ type: 'integer', default: 0 })
  sequence!: number;

  @Column({ type: 'uuid', nullable: true })
  emailListId?: string;

  @ManyToOne(() => EmailList, { nullable: true })
  @JoinColumn({ name: 'emailListId' })
  emailList?: EmailList;

  @Column({ nullable: true })
  description?: string;

  @Column({ default: 'Not Started' })
  status?: string;

  @OneToMany(() => WorkPackageActivity, (workPackageActivity) => workPackageActivity.workPackage)
  workPackageActivity?: WorkPackageActivity[];
}
