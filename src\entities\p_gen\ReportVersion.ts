import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import Report from './Report';
import ReportDocument from './ReportDocument';

@Entity({ schema: 'p_gen' })
class ReportVersion {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  version?: number;

  @Column({ nullable: true })
  reportId?: string;

  @ManyToOne(() => Report, (report) => report.version) 
  @JoinColumn({ name: 'reportId' })
  report?: Report;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  submissionDate?: Date;

  @Column({ nullable: true })
  responseDate?: Date;

  @Column({ nullable: true })
  reviewerComments?: string;

  @Column({ nullable: true })
  reportDate?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => ReportDocument, (document) => document.reportVersion)
  documents?: ReportDocument[];
}

export default ReportVersion;
