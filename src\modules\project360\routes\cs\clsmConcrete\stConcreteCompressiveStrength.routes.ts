import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StCompressiveStrength } from '@entities/p_cs/StCompressiveStrength';
import StCompressiveStrengthContorller from '@controllers//cs/stConcreteCompressiveStrength.controller';
const router: Router = express.Router();

const GenericController = new CrudController<StCompressiveStrength>(StCompressiveStrength);
const controller = new StCompressiveStrengthContorller();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenericController.findByProjectId(req, res, 'compressiveStrength')
);
router.get(
  '/by/depth/:ppDepth/:panelNumberId/:projectId',
  authenticateToken,
  controller.findByDepthAndPanelNumber
);
router.get('/by/sample/:sampleId', authenticateToken, controller.findBySample);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenericController.getDataCountByProjectId(req, res, 'compressiveStrength')
);
router.get('/by/borehole/:boreholeId/:projectId', authenticateToken, controller.findByBoreholeId);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenericController.findByWorkActivityId(req, res, 'compressiveStrength')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenericController.sendForApproval(req, res, 'compressiveStrength')
);
router.get('/:id', authenticateToken, GenericController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenericController.create(req, res, 'compressiveStrength')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenericController.update(req, res, 'compressiveStrength')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenericController.multiSoftDelete(req, res, 'compressiveStrength')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res, 'compressiveStrength')
);

export default router;
