import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilNuclearGaugeTest } from '../../../../../../entities/p_cs/StSoilNuclearGaugeTest';
import StSoilNuclearGaugeTestController from '@controllers//cs/stSoilNuclearGaugeTest.controller';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilNuclearGaugeTest>(StSoilNuclearGaugeTest);
const controller = new StSoilNuclearGaugeTestController();
// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'nuclearGaugeTest')
);
router.get('/by/station/:stationStart/:stationEnd/:projectId', authenticateToken, (req, res) =>
  controller.getDataByStation(req, res)
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'nuclearGaugeTest')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'nuclearGaugeTest')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'nuclearGaugeTest')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'nuclearGaugeTest')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'nuclearGaugeTest')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'nuclearGaugeTest')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'nuclearGaugeTest')
);

export default router;
