import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { DeckLoading } from '../../../../../entities/p_cs/DeckLoading';

const router: Router = express.Router();

const GenricController = new CrudController<DeckLoading>(DeckLoading);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'deckLoading')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'deckLoading')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'deckLoading')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'deckLoading'));
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'deckLoading')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'deckLoading')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'deckLoading')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'deckLoading')
);

export default router;
