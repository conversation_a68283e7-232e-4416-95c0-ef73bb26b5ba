import { StSoilNuclearGaugeTest } from '@entities/p_cs/StSoilNuclearGaugeTest';
import columnsToRemove from '@utils/custom/columnsToRemove';
import convertStationToNumber from '@utils/custom/convertStationToNumber';
import changeGeoJsonUUIDsToValue from '@utils/map/changeGeoJsonUUIDsToValues';
import { getManager, IsNull, Not } from 'typeorm';

class StSoilNuclearGaugeTestModel {
  Repo = getManager().getRepository(StSoilNuclearGaugeTest);
  async getDataByStationRange(stationStart: number, stationEnd: number, projectId: string) {
    try {
      const data = await this.Repo.find({
        where: { projectId, station: Not(IsNull()) },
      });

      const filteredData = data.filter((panel) => {
        const stationValue = convertStationToNumber(panel.station!);

        return stationValue >= stationStart && stationValue <= stationEnd;
      });

      const finalData = await changeGeoJsonUUIDsToValue(
        'nuclearGaugeTest',
        filteredData,
        projectId
      );

      return finalData.map((obj) =>
        Object.keys(obj).reduce((acc, key) => {
          if (!columnsToRemove.includes(key)) {
            acc[key] = obj[key as keyof StSoilNuclearGaugeTest];
          }
          return acc;
        }, {} as any)
      );
    } catch (error) {
      throw error;
    }
  }

  async getNuclearGaugeByTestNo(testNoId: string) {
    try {
      const data = await this.Repo.findOne({
        where: { testNoId: testNoId, isDelete: false },
        relations: ['test', 'testMethod', 'testResult', 'site'],
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
}
const stSoilNuclearGaugeTestModel = new StSoilNuclearGaugeTestModel();
export default stSoilNuclearGaugeTestModel;
