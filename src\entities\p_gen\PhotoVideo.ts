// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from './Project';
import { MediaType } from './MediaType';
import { Feature } from './Feature';
import { Area } from './Area';
import { Purpose } from '../p_meta/Purpose';
import { WorkPackageActivity } from './WorkPackageActivity';
import { EventLog } from './EventLog';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';

// Define the entity class
@Entity({ schema: 'p_gen' })
export class PhotoVideo {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true, type: 'uuid' })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  typeId?: string;

  @ManyToOne(() => MediaType, { nullable: true })
  @JoinColumn({ name: 'typeId' })
  mediaType?: MediaType;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  fileType?: string;

  @Column({ nullable: true })
  path?: string;

  @Column({ nullable: true })
  annotationImagePath?: string;

  @Column({ nullable: true })
  sftpPath?: string;

  @Column({ nullable: true })
  photographer?: string;

  @Column({ nullable: true })
  dateTime?: Date;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ nullable: true })
  station?: string;

  @Column({ nullable: true })
  offset?: string;

  @Column({ nullable: true })
  orientationView?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  photoNumber?: string;

  @Column({ nullable: true })
  source?: string;

  @Column({ nullable: true })
  imgDegrees?: string;

  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @Column()
  createdBy?: string;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
