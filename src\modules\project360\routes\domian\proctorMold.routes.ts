import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { ProctorMold } from '../../../../entities/p_domain/ProctorMold';

const router: Router = express.Router();

const CURDController = new CrudController<ProctorMold>(ProctorMold);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/', authenticateToken, (req, res) => CURDController.findAll(req, res));

export default router;
