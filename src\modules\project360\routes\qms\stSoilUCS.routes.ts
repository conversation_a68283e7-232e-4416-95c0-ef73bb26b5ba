import express, { Router } from 'express';

import { authenticateToken } from 'src/shared/middlewares/auth.middleware';
import CrudController from 'src/modules/generic/crudDriver.controller';
import { StSoilUCS } from '@entities/p_qms/StSoilUCS';
import StSoilUCSController from '@controllers//qms/stSoilUCS.controller';
const router: Router = express.Router();

const GenericController = new CrudController<StSoilUCS>(StSoilUCS);
const controller = new StSoilUCSController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenericController.findByProjectId(req, res, 'unconfinedCompressiveStrength')
);
router.get('/by/sample/:sampleId', authenticateToken, (req, res) =>
  controller.getBySampleId(req, res)
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenericController.findByWorkActivityId(req, res, 'unconfinedCompressiveStrength')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenericController.sendForApproval(req, res, 'unconfinedCompressiveStrength')
);
router.get('/:id', authenticateToken, GenericController.findById);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenericController.getDataCountByProjectId(req, res, 'unconfinedCompressiveStrength')
);

router.post('/', authenticateToken, (req, res) =>
  GenericController.create(req, res, 'unconfinedCompressiveStrength')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenericController.update(req, res, 'unconfinedCompressiveStrength')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenericController.multiSoftDelete(req, res, 'unconfinedCompressiveStrength')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res, 'unconfinedCompressiveStrength')
);

export default router;
