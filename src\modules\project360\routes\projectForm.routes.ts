import express, { Router } from 'express';
import { ProjectForm } from '../../../entities/p_gen/ProjectForm';
import ProjectFormController from '../controllers/projectForm.controller';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import CrudController from '../../generic/crudDriver.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUD = new CrudController<ProjectForm>(ProjectForm);
const controller = new ProjectFormController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/', authenticateToken, (req, res) => CRUD.create(req, res));
router.put('/edit/mode/:dceId/:projectId', authenticateToken, controller.editFormMode);
router.put('/:id', authenticateToken, (req, res) => CRUD.update(req, res));
router.get('/:id', authenticateToken, (req, res) => CRUD.findById(req, res));
router.delete('/:id', authenticateToken, (req, res) => CRUD.softDelete(req, res));
router.get('/by/dce/:dceId/:projectId', authenticateToken, controller.findByDceIdAndProjectId);
router.get('/json/by/dce/:dceId/:projectId', authenticateToken, controller.getJSONByDCEId);
router.get('/json/by/form/mobile/:formId', authenticateToken, controller.getJSONByFormIdMobile);
router.get('/json/by/entity/:entity/:projectId', authenticateToken, controller.getJSONByEntity);
router.get(
  '/json/by/dce/mode/:dceId/:projectId/:mode',
  authenticateToken,
  controller.getJSONByDCEIdWithMode
);
router.get(
  '/json/by/entity/mode/:entity/:projectId/:mode',
  authenticateToken,
  controller.getJSONByEntityWithMode
);
router.put('/set/default/form', authenticateToken, controller.setAsDefault);
router.post('/add/by/admin/form', authenticateToken, controller.addProjectFormByDefaultFrom);
router.get('/json/by/form/:formId', authenticateToken, controller.getJsonByFormId);
// Route to get only the form IDs create, view, edit
router.get('/mode-ids/by/dce/:dceId/:projectId', authenticateToken, controller.getModeIdsByDCE);
// Route to get the complete Form object for create, view, edit
router.get('/mode-forms/by/dce/:dceId/:projectId', authenticateToken, controller.getModeFormsByDCE);
router.get('/get/custom/forms/:projectId', authenticateToken, controller.getCustomFormByProjectId);
router.get(
  '/get/custom/form/:projectId/formKey/:formKey',
  authenticateToken,
  controller.getCustomFormByProjectIdAndFormKey
);
router.get(
  '/get/all/create/form/:projectId/:dceId',
  authenticateToken,
  controller.getAllCreateForm
);
router.get(
  '/get/all/multiple/dce/forms/:projectId',
  authenticateToken,
  controller.getAllMultipleDCEForms
);
router.get(
  '/get/custom/and/multiple/dce/forms/:projectId',
  authenticateToken,
  controller.getCustomAndMultipleDCEForms
);

export default router;
