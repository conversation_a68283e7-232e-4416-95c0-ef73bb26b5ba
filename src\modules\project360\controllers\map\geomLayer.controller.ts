import { Request, Response } from 'express';
import GeomLayerModel from '../../models/map/geomLayer.model';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import shapeLayerFileCheck from '../../../../shared/utilities/map/shapeLayerFileCheck';

class GeomLayerContorller {
  async shapeApi(req: Request, res: Response) {
    try {
      if (!req?.files) {
        throw new Error('Shapefile not Found');
      }
      const shapefileData = await shapeLayerFileCheck(req?.files);
      const model = new GeomLayerModel();
      const { projectId, srid, layerName, layerParentId } = req.body;

      await model.addGeomDataWithLayer(
        layerParentId,
        layerName,
        shapefileData,
        srid,
        projectId,
        `${(req as any).user.name}`
      );
      return res.status(200).json({
        isSucceed: true,
        data: shapefileData,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getLayerData(req: Request, res: Response) {
    try {
      const model = new GeomLayerModel();
      const { id } = req.params;

      const data = await model.getLayerDataAsGeoJson(id);
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async extractValueFromShapeFile(req: Request, res: Response) {
    try {
      if (req?.files?.file) {
        const shapefileData = await shapeLayerFileCheck(req?.files.file);
        return res.status(200).json({
          isSucceed: true,
          data: shapefileData,
          msg: 'data found',
        });
      } else {
        throw new Error('Shapefile not Found');
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  // async extractValueFromShapeFileWithZip(req: Request, res: Response) {
  //   try {
  //     const shapefile = req?.files?.file;
  //     const dBaseFile = req?.files?.dbf;
  //     let shapefileData: any = {};
  //     if (shapefile && dBaseFile) {
  //       shapefileData = await extractValueFromShapefileAnddBase(shapefile, dBaseFile);
  //     } else if (shapefile && path.extname((shapefile as any)?.name).toLowerCase() == '.shp') {
  //       shapefileData = await extractValueFromShapefile(shapefile);
  //     }
  //     else if (shapefile && path.extname((shapefile as any)?.name).toLowerCase() == '.zip') {
  //       const unzippedFile = await unzipFile(req?.files?.file)
  //       let dbf:any;
  //       let shp:any;
  //       unzippedFile.files.map((value)=>{
  //         if (value.path) {

  //         }
  //       })
  //       shapefileData = await extractValueFromShapefile(shapefile);
  //     }

  //     return res.status(200).json({
  //       isSucceed: true,
  //       data: shapefileData,
  //       msg: 'data found',
  //     });
  //   } catch (error) {
  //     return errorMiddleware(error, 500, res, req);
  //   }
  // }
}

export default GeomLayerContorller;
