import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project'; // Import the Projects entity
import { Stakeholder } from './Stakeholder'; // Import the Stakeholders entity

@Entity({ schema: 'p_auth' })
export class ProjectOrgStructure {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  parentId?: string;

  @Column()
  stakeholderId?: string;

  @ManyToOne(() => Stakeholder, { nullable: true }) 
  @JoinColumn({ name: 'stakeholderId' })
  stakeholder?: Stakeholder;

  @Column({ nullable: true })
  roleType?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
