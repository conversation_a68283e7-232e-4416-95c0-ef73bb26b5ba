import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { ProjectRole } from '../../../../entities/p_auth/Role';
import projectRolesController from '../../controllers/role and permission/projectRole.controller';

const router: Router = express.Router();

const CURD = new CrudController<ProjectRole>(ProjectRole);
router.get('/single/:id', authenticateToken, projectRolesController.findById);
router.get('/:roleId', authenticateToken, projectRolesController.getPermissionByRole);
router.get('/by/user/:userId', authenticateToken, projectRolesController.getRoleIdByUser);
router.get('/routes/by/user/:userId', authenticateToken, projectRolesController.getRoutesByUser);
router.get(
  '/routes/details/by/project/user/:userId/:projectId',
  authenticateToken,
  projectRolesController.getCompleteRoutesByUser
);
router.get(
  '/routes/by/project/user/:userId/:projectId',
  authenticateToken,
  projectRolesController.getRoutesByUserByProject
);

router.get('/by/project/:id', authenticateToken, projectRolesController.getByProjectId);
router.get(
  '/permission/by/project/user/:id/:userId',
  authenticateToken,
  projectRolesController.getByProjectId
);
router.get(
  '/permission/by/project/:id/user/:userId',
  authenticateToken,
  projectRolesController.getByProjectIdAndUser
);
router.put('/:id', authenticateToken, (req, res) => CURD.update(req, res));
router.post('/', authenticateToken, (req, res) => CURD.create(req, res));
router.post('/add/permission', authenticateToken, projectRolesController.addPermissionToRole);
router.delete('/:id', authenticateToken, (req, res) => CURD.softDelete(req, res));

export default router;
