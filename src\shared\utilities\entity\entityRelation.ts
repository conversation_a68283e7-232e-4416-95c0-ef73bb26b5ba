export interface EntityRelationListInterface {
  [key: string]: {
    relation: string[];
  };
}

export const entityRelationList: EntityRelationListInterface = {
  borehole: {
    relation: [],
  },
  dewatering: {
    relation: [],
  },
  inclinometer: {
    relation: [],
  },
  turbidityTesting: {
    relation: [],
  },
  blastReport: {
    relation: ['blastType'],
  },
  blastHole: {
    relation: ['blastReport'],
  },
  concreteMixDesign: {
    relation: [],
  },
  deckLoading: {
    relation: ['blastHole'],
  },
  explosiveTicket: {
    relation: [],
  },
  groutMixDesign: {
    relation: [],
  },
  penetrationRate: {
    relation: ['blastHole'],
  },
  remedialTreatment: {
    relation: ['blastReport', 'remedialTreatmentType', 'anchorType'],
  },
  vibration: {
    relation: ['blastReport', 'seismograph'],
  },
  seismograph: {
    relation: ['sensorMount', 'instrumentStatus'],
  },
  monitoringLocation: {
    relation: [
      'instrumentStatus',
      'featureConstruction',
      'monitoringAspect',
      'monitoringInstrument',
    ],
  },
  blastEvaluation: {
    relation: ['blastReport', 'overbreakType', 'movement', 'flyRockAssessment'],
  },
  piezometer: {
    relation: ['soilSymbol'],
  },
  fieldSurvey: {
    relation: ['feature', 'SurveyType'],
  },
  reboundSettleGauge: {
    relation: ['soilSymbol'],
  },
  concreteBatchTicket: {
    relation: ['concreteMixDesign'],
  },
  groutPlantTicket: {
    relation: ['groutMixDesign'],
  },
  concreteBleed: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'testResult'],
  },
  concreteSettingTime: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'testResult'],
  },
  waterPressureTest: {
    relation: ['borehole'],
  },
  surveyFeature: {
    relation: ['SurveyType', 'instrumentStatus'],
  },
  stBleedExpansion: {
    relation: ['groutPlantTicket'],
  },
  stDensitySlurry: {
    relation: ['groutPlantTicket'],
  },
  stViscosityMarshFunnel: {
    relation: ['groutPlantTicket'],
  },
  stWaterContentOfSoil: {
    relation: ['sample'],
  },
  stSoilCementContent: {
    relation: ['concreteBatchTicket'],
  },
  foundationPreparation: {
    relation: [],
  },
  materialHaulPlace: {
    relation: ['materialType'],
  },
  postBlastVideo: {
    relation: ['blastReport'],
  },
  seepageBarrier: {
    relation: ['concreteBatchTicket', 'sBType', 'criticalArea'],
  },
  seepageBarrierVerticality: {
    relation: ['seepageBarrier', 'sBVerticalityTest'],
  },
  blastDamageSurvey: {
    relation: ['blastReport'],
  },
  shrinkage: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'testResult'],
  },
  stHardenedSoilCementContent: {
    relation: ['batch'],
  },
  stSandContentBentSlurry: {
    relation: ['batch'],
  },
  concreteIssuesCutoffWall: {
    relation: ['seepageBarrier'],
  },
  freshPropertyIssuesCutoffWall: {
    relation: ['seepageBarrier', 'concreteBatchTicket'],
  },
  stDensityCLSM: {
    relation: ['concreteBatchTicket'],
  },
  stFLowConsistencyCLSM: {
    relation: ['concreteBatchTicket'],
  },
  photoVideo: {
    relation: ['feature', 'generalProjectArea', 'approvalStatus'],
  },
  sampleClassification: {
    relation: ['soilSymbol', 'test', 'testMethod', 'sample', 'site', 'testResult'],
  },
  nuclearGaugeTest: {
    relation: [
      'test',
      'testMethod',
      'sample',
      'testVariant',
      'depthInfoNGSC',
      'projectNuclearGauges',
      'site',
      'testResult',
    ],
  },
  sandConeTest: {
    relation: [
      'test',
      'testMethod',
      'sample',
      'depthInfoNGSC',
      'sandConeInfo',
      'site',
      'testResult',
    ],
  },
  grainSizeAnalysis: {
    relation: [
      'sample',
      'test',
      'testMethod',
      'breakerSieveGrainSize',
      'grainAnalysisWorksheet',
      'hydrometer',
      'site',
      'testResult',
    ],
  },
  sieveAnalysis: {
    relation: [
      'sample',
      'test',
      'testMethod',
      'breakerSieveGrainSize',
      'sieveAnalysisWorksheet',
      'site',
      'testResult',
    ],
  },
  grainAnalysisWorksheet: {
    relation: ['sieveSize'],
  },
  hydrometer: {
    relation: ['hydrometerReadingTime'],
  },
  moistureContent: {
    relation: ['sample', 'testVariant', 'test', 'testMethod', 'site', 'testResult'],
  },
  organicContent: {
    relation: [
      'sample',
      'testVariant',
      'test',
      'testMethod',
      'organicContentWorksheet',
      'site',
      'testResult',
    ],
  },
  passing200Sieve: {
    relation: ['sample', 'testVariant', 'test', 'testMethod', 'site', 'testResult'],
  },
  proctor: {
    relation: [
      'sample',
      'testVariant',
      'test',
      'testMethod',
      'site',
      'testResult',
      'compactionRammer',
      'proctorWorksheet',
      'testResult',
    ],
  },
  atterbergLimits: {
    relation: [
      'sample',
      'testVariant',
      'test',
      'testMethod',
      'atterbergLimitWorksheet',
      'site',
      'testResult',
    ],
  },
  sampleManagement: {
    relation: ['sampleSpecimen'],
  },
  absorptionAndSpecificGravity: {
    relation: ['test', 'testMethod', 'sample', 'site', 'testResult'],
  },
  stCLSMCompressiveStrength: {
    relation: ['concreteBatchTicket'],
  },
  stCLSMCompressiveStrengthSpecimen: {
    relation: ['stCLSMCompressiveStrength'],
  },
  stConcreteBleedingWorksheet: {
    relation: ['concreteBleeding'],
  },
  unconfinedCompressiveStrength: {
    relation: ['sample', 'test', 'testMethod', 'site', 'testResult', 'soilUCSSAC'],
  },
  compressiveStrength: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'site', 'testResult'],
  },
  stConcreteCompressiveStrengthSpecimen: {
    relation: ['stConcreteCompressiveStrength'],
  },
  concreteDensity: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'site', 'testResult'],
  },
  slumpTest: {
    relation: ['concreteBatchTicket', 'sample', 'test', 'testMethod', 'site', 'testResult'],
  },
  stSoilRelativeDensity: {
    relation: ['testMethod', 'testVariant', 'testResult'],
  },
  observation: {
    relation: ['equipment'],
  },
  hydraulicConductivity: {
    relation: [
      'sample',
      'test',
      'testMethod',
      'concreteBatchTicket',
      'hydraulicConductivityCellInfo',
      'hydraulicConductivityWorkSheet',
      'testResult',
    ],
  },
  concreteTemperature: {
    relation: ['sample', 'test', 'testMethod', 'concreteBatchTicket', 'testResult'],
  },
  ctLog: {
    relation: ['reportList'],
  },
  testReport: {
    relation: [],
  },
  cutMaster: {
    relation: ['cutInformation'],
  },
  cutInformation: {
    relation: [],
  },
  clsmPlacement: {
    relation: [],
  },
};
