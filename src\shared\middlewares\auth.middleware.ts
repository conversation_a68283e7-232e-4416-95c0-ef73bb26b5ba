import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utilities/jwt';

export function authenticateToken(req: Request, res: Response, next: NextFunction) {
  const authHeader = req.headers.authorization;

  if (!authHeader) {
    if (req.headers.user) {
      (req as any).user = JSON.parse(req.headers.user as string);
      next();
    } else if (
      req.headers['x-api-key'] &&
      req.method === 'GET' &&
      process.env.PROJECT360_API_KEY === req.headers['x-api-key']
    ) {
      next();
    } else {
      return res.status(401).json({
        isSucceed: false,
        data: [],
        msg: 'Unauthorized',
      });
    }
  } else {
    const [bearer, token] = authHeader.split(' ');

    if (bearer !== 'Bearer' || !token) {
      return res.status(401).json({
        isSucceed: false,
        data: [],
        msg: 'Invalid token format',
      });
    }

    try {
      const decodedToken = verifyToken(token);
      (req as any).user = decodedToken;

      next();
    } catch (err) {
      return res.status(401).json({
        isSucceed: false,
        data: [],
        msg: 'Invalid token',
      });
    }
  }
}
