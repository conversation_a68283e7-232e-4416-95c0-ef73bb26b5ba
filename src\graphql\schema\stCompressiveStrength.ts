import { gql } from 'apollo-server-express';

export const typeDefs = gql`
  # Custom scalar for Date handling
  scalar Date

  type Project {
    id: ID!
    name: String
  }

  type MaterialType {
    id: ID!
    name: String
  }

  type ProjectMaterial {
    id: ID!
    name: String
    materialTypeId: String
    materialType: MaterialType
  }

  type FractureType {
    id: ID!
    name: String
  }

  type CappingMethod {
    id: ID!
    name: String
  }

  type StCompressiveStrength {
    id: ID!
    projectId: String
    project: Project
    materialId: String
    material: ProjectMaterial
    materialTypeId: String
    materialType: MaterialType
    testNo: String
    fractureTypeId: String
    fractureType: FractureType
    lengthDiameterRatio: Float
    compressiveStrength: Float
    castDate: Date
    specimenAge: Float
    cylinderDiameter: Float
    cylinderDiameter1: Float
    cylinderDiameter2: Float
    cylinderLength: Float
    cylinderLength1: Float
    cylinderLength2: Float
    cappingMethodId: String
    cappingMethod: CappingMethod
    failureLoad: Float
    unitWeight: Float
    wetWeight: Float
    crossSectionalArea: Float
    dateTested: Date
    comments: String
    dateReceived: Date
    testedBy: String
    createdAt: Date
    updatedAt: Date
  }

  extend type Query {
    compressiveStrengthByMaterial(
      materialId: String!
      startDate: String!
      endDate: String!
      projectId: String!
    ): [StCompressiveStrength]

    compressiveStrengthByProject(
      projectId: ID!
      startDate: String
      endDate: String
      dateField: String
    ): [StCompressiveStrength]
  }
`;
