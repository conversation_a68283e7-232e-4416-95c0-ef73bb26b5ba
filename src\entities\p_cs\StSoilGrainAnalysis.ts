import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Sample } from './Sample'; // Import the TestResults entity
import { SieveSize } from '../p_domain/SieveSize';
import { TestMethod } from '../p_meta/TestMethod';
import { StSoilGrainAnalysisWorksheet } from './StSoilGrainAnalysisWorksheet';
import { StSoilGrainAnalysisHydrometerReadings } from './StSoilGrainAnalysisHydrometerReadings';
import { Project } from '../p_gen/Project';
import { Test } from '../p_meta/Test';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { TestVariant } from '../p_meta/Testvariant';
import { Site } from '@entities/p_gen/Site';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { EventLog } from '@entities/p_gen/EventLog';
import { SampleReductionMethod } from '@entities/p_domain/SampleReductionMethods';
import { TestAndDCEColumns } from '@entities/common/TestColumns';
import { GradationSampleSelection } from '@entities/p_meta/GradationSampleSelection';

@Entity({ schema: 'p_cs' })
export class StSoilGrainAnalysis extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  samplePreparationType?: string;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ type: 'decimal', nullable: true })
  minimumMassRequired?: number;

  @Column({ type: 'decimal', nullable: true })
  massReceived?: number;

  @Column({ type: 'decimal', nullable: true })
  sampleMass?: number;

  @Column({ type: 'decimal', nullable: true })
  maxParticleSizeOnVisualInspectionId?: number;

  @ManyToOne(() => SieveSize, { nullable: true })
  @JoinColumn({ name: 'maxParticleSizeOnVisualInspectionId' })
  maxParticleSizeOnVisualInspection?: SieveSize;

  @Column({ nullable: true })
  sampleReductionMethodId?: string;

  @ManyToOne(() => SampleReductionMethod, { nullable: true })
  @JoinColumn({ name: 'sampleReductionMethodId' })
  sampleReductionMethod?: SampleReductionMethod;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  isBulkSample?: boolean;

  @Column({ nullable: true })
  breakerSieveGrainSizeId?: string;

  @ManyToOne(() => SieveSize, { nullable: true })
  @JoinColumn({ name: 'breakerSieveGrainSizeId' })
  breakerSieveGrainSize?: SieveSize;

  @Column({ nullable: true })
  sampleSelectionId?: string;

  @ManyToOne(() => GradationSampleSelection, { nullable: true })
  @JoinColumn({ name: 'sampleSelectionId' })
  sampleSelection?: GradationSampleSelection;

  @Column({ nullable: true })
  sampleSpecimenId?: string;

  @ManyToOne(() => GradationSampleSelection, { nullable: true })
  @JoinColumn({ name: 'sampleSpecimenId' })
  sampleSpecimen?: GradationSampleSelection;

  @Column({ nullable: true })
  sampleMaterialStateId?: string;

  @ManyToOne(() => GradationSampleSelection, { nullable: true })
  @JoinColumn({ name: 'sampleMaterialStateId' })
  sampleMaterialState?: GradationSampleSelection;

  @Column({ nullable: true })
  sieveSpecimenId?: string;

  @ManyToOne(() => GradationSampleSelection, { nullable: true })
  @JoinColumn({ name: 'sieveSpecimenId' })
  sieveSpecimen?: GradationSampleSelection;

  @Column({ nullable: true })
  specimenBrokenProcedureId?: string;

  @ManyToOne(() => GradationSampleSelection, { nullable: true })
  @JoinColumn({ name: 'specimenBrokenProcedureId' })
  specimenBrokenProcedure?: GradationSampleSelection;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  isMaterialExcluded?: boolean;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  isCumulativeWeightRetained?: boolean;

  @Column({ nullable: true })
  reasonExcluded?: string;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  @Column({ nullable: true })
  isHydrometerSample?: boolean;

  @Column({ nullable: true })
  panNo?: string;

  @Column({ type: 'decimal', nullable: true })
  weightOfPan?: number;

  @Column({ type: 'decimal', nullable: true })
  wetSampleWithPan?: number;

  @Column({ type: 'decimal', nullable: true })
  drySampleWithPanBeforeWash?: number;

  @Column({ type: 'decimal', nullable: true })
  drySampleWithPanAfterWash?: number;

  @Column({ type: 'decimal', nullable: true })
  drySampleWeightBeforeWash?: number;

  @Column({ type: 'decimal', nullable: true })
  drySampleWeightAfterWash?: number;

  @Column({ type: 'decimal', nullable: true })
  weightLoss?: number;

  @Column({ type: 'decimal', nullable: true })
  percentagePassing200?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfWater?: number; //Calculated (wetSampleWithPan - drySampleWithPan)

  @Column({ type: 'decimal', nullable: true })
  weightOfDrySample?: number; //Calculated (drySampleWithPan - weightOfPan)

  @Column({ type: 'decimal', nullable: true })
  Cu?: number;

  @Column({ type: 'decimal', nullable: true })
  Cc?: number;

  @Column({ type: 'decimal', nullable: true })
  D10?: number;

  @Column({ type: 'decimal', nullable: true })
  D30?: number;

  @Column({ type: 'decimal', nullable: true })
  D60?: number;

  @Column({ type: 'decimal', nullable: true })
  D100?: number;

  @Column({ nullable: true })
  ASTMSoilClassification?: string;

  @Column({ nullable: true })
  AASHTOSoilClassification?: string;

  @Column({ nullable: true })
  ASTMSoilCode?: string;

  @Column({ nullable: true })
  AASHTOSoilCode?: string;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Particle Shape',
  //     fieldName: 'particleShapeId',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: true,
  //     query: 'SELECT id FROM p_domain.particle_shape WHERE name = $1',
  //     getListQuery:
  //       'Select id, alias as name FROM p_domain.particle_shape WHERE "specAgencyId" = $1',
  //     listParams: 'specAgencyId',
  //     listName: 'particleShapeList',
  //   },
  // })
  // @Column({ nullable: true })
  // particleShapeId?: string;

  // @ManyToOne(() => ParticleShape, { nullable: true })
  // @JoinColumn({ name: 'particleShapeId' })
  // particleShape?: ParticleShape;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Particle Hardness',
  //     fieldName: 'particleHardnessId',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: true,
  //     query: 'SELECT id FROM p_domain.particle_hardness WHERE name = $1',
  //     getListQuery:
  //       'Select id, alias as name FROM p_domain.particle_hardness WHERE "specAgencyId" = $1',
  //     listParams: 'specAgencyId',
  //     listName: 'particleHardnessList',
  //   },
  // })
  // @Column({ nullable: true })
  // particleHardnessId?: string;

  // @ManyToOne(() => ParticleHardness, { nullable: true })
  // @JoinColumn({ name: 'particleHardnessId' })
  // particleHardness?: ParticleHardness;

  //When Bulk Sample is selected

  @Column({ nullable: true })
  bulkPanNo?: string;

  @Column({ nullable: true })
  bulkSieveTime?: number;

  @Column({ nullable: true })
  fineSieveTime?: number;

  @Column({ type: 'decimal', nullable: true })
  drySampleWithBulkPan?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfBulkPan?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfDrySampleInBulkPan?: number; //Calculated (drySampleWithBulkPan - weightOfBulkPan)

  // When Hydrometer Sample is selected

  @Column({ type: 'decimal', nullable: true })
  soilSolidsSpecificGravity?: number;

  @Column({ type: 'decimal', nullable: true })
  liquidSpecificGravity?: number;

  @Column({ type: 'decimal', nullable: true })
  temperature?: number;

  //Hygroscopic Moisture
  @Column({ type: 'decimal', nullable: true })
  airDriedSample?: number;

  @Column({ type: 'decimal', nullable: true })
  ovenDriedSample?: number;

  @Column({ nullable: true })
  hygroscopicMoistureCorrection?: string;

  //Hydrometer Sample

  @Column({ type: 'decimal', nullable: true })
  airDryHydrometerSampleWeight?: number;

  @Column({ nullable: true })
  hydrometerCorrection?: string;

  @Column({ nullable: true })
  dispersionMethod?: string;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => StSoilGrainAnalysisWorksheet, (worksheet) => worksheet.grainSizeAnalysis, {
    cascade: true,
  })
  grainAnalysisWorksheet?: StSoilGrainAnalysisWorksheet[];

  @OneToMany(
    () => StSoilGrainAnalysisHydrometerReadings,
    (hydrometer) => hydrometer.grainSizeAnalysis,
    { cascade: true }
  )
  hydrometer?: StSoilGrainAnalysisHydrometerReadings[];

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
