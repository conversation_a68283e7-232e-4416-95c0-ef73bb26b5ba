import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from '../p_gen/Project';
import { Layer } from './Layer';

@Entity({ schema: 'p_map' })
export class GeoJsonConfiguration {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true }) 
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ type: 'varchar' })
  columnName?: string;

  @Column({ nullable: true })
  shapeFileColumnName?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  layerId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  layer?: Layer;

  @Column({ type: 'varchar', nullable: true })
  alias?: string;

  @Column({ default: false, nullable: true })
  hide?: boolean;

  @Column({ default: false, nullable: true })
  isURL?: boolean;

  @Column({ nullable: true })
  order?: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
