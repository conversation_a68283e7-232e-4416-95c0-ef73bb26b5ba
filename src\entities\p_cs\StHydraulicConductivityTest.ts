// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  JoinColumn,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';
import { Sample } from './Sample';
import { TestVariant } from '../p_meta/Testvariant';
import { HydraulicConductivityCellInfo } from '../p_domain/HydraulicConductivityCellInfo';
import { Test } from '../p_meta/Test';
import { StHydraulicConductivityWorkSheet } from './StHydraulicConductivityWorkSheet';
import { TestMethod } from '../p_meta/TestMethod';
import { Project } from '../p_gen/Project';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

// Define the entity for your table
@Entity({ schema: 'p_cs' })
export class StHydraulicConductivityTest extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  soilSampleId?: string;

  @Column({ nullable: true })
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ nullable: true })
  cellInfoId?: string;

  @ManyToOne(() => HydraulicConductivityCellInfo, { nullable: true })
  @JoinColumn({ name: 'cellInfoId' })
  hydraulicConductivityCellInfo?: HydraulicConductivityCellInfo;

  @Column({ nullable: true })
  passFail?: string;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  isWorksheet?: boolean;

  @OneToMany(
    () => StHydraulicConductivityWorkSheet,
    (worksheet) => worksheet.hydraulicConductivityTest,
    { cascade: true }
  )
  hydraulicConductivityWorkSheet?: StHydraulicConductivityWorkSheet[];

  @Column({ type: 'timestamp', nullable: true })
  startDateTime?: Date;

  @Column({ type: 'timestamp', nullable: true })
  endDateTime?: Date;

  @Column({ type: 'decimal', nullable: true })
  temperature?: number;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ nullable: true, type: 'decimal' })
  specificGravity?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialMass?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialLength?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialDiameter?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialArea?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialVolume?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialWaterContent?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialDryUnitWgt?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialWetUnitWgt?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialSaturation?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalMass?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalLength?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalDiameter?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalArea?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalVolume?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalWaterContent?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalDryUnitWgt?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalWetUnitWgt?: number;

  @Column({ nullable: true, type: 'decimal' })
  finalSaturation?: number;

  @Column({ nullable: true })
  permeantLiquid?: string;

  @Column({ nullable: true, type: 'decimal' })
  backPressure?: number;

  @Column({ nullable: true, type: 'decimal' })
  confiningStress?: number;

  @Column({ nullable: true, type: 'decimal' })
  areaHWTube?: number;

  @Column({ nullable: true, type: 'decimal' })
  areaTWTube?: number;

  @Column({ nullable: true, type: 'decimal' })
  specimenLength?: number;

  @Column({ nullable: true, type: 'decimal' })
  specimenArea?: number;

  @Column({ nullable: true, type: 'decimal' })
  initialHydraulicGradient?: number; // first value of hydra gradient

  @Column({ nullable: true, type: 'decimal' })
  finalHydraulicGradient?: number; // first value of hydra gradient

  @Column({ nullable: true, type: 'decimal' })
  avgHydraulicGradient?: number;

  @Column({ nullable: true, type: 'decimal' })
  avgHydraulicConductivity?: number;

  @Column({ nullable: true })
  tester?: string;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false, nullable: true })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
