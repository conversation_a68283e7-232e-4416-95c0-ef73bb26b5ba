import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { SpecAgency } from '../p_meta/SpecAgency'; // Import the SpecAgency entity

@Entity({ schema: 'p_domain' })
export class Specification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  specName?: string;

  @Column()
  specType?: string;

  @Column()
  description?: string;

  @Column()
  specVersion?: string;

  @Column({ type: 'timestamp' })
  releaseDate?: Date;

  @Column()
  linkToDocument?: string;

  @Column()
  specAgencyId?: string;

  @ManyToOne(() => SpecAgency, { nullable: true }) 
  @JoinColumn({ name: 'specAgencyId' })
  specAgency?: SpecAgency;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
