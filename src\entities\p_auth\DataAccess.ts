import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON>umn,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProjectRole } from './Role'; // Import the Roles entity

@Entity({ schema: 'p_auth' })
export class DataAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  roleId?: string;

  @ManyToOne(() => ProjectRole, { nullable: true }) 
  @JoinColumn({ name: 'roleId' })
  Role?: ProjectRole;

  @Column()
  dataType?: string;

  @Column()
  accessLevel?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
