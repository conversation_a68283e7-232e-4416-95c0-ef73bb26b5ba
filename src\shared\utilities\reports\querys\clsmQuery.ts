const clsmReportQuery = `
SELECT 
    COALESCE(s."sampleNo", 'N/A') AS "sampleNo",
    COALESCE(cmd."mixId"::TEXT, 'N/A') AS "mixCode",
    COALESCE(s."location", 'N/A') AS "location",
    COALESCE(s.northing::TEXT, 'N/A') AS "northing",
    COALESCE(s.easting::TEXT, 'N/A') AS "easting",
    COALESCE(s.elevation::TEXT, 'N/A') AS "elevation",
    COALESCE(d."dateTested"::TEXT, 'N/A') AS "dateTested",
    COALESCE(d."testedBy", 'N/A') AS "testedBy",
    COALESCE(d.density::TEXT, 'N/A') AS "density",
    COALESCE(d."theoreticalDensity"::TEXT, 'N/A') AS "theoreticalDensity",
    COALESCE(d."totalMassBatched"::TEXT, 'N/A') AS "totalMassBatched",
    COALESCE(d."totalAbsoluteVolume"::TEXT, 'N/A') AS "totalAbsoluteVolume",
    COALESCE(d."clsmVolume"::TEXT, 'N/A') AS "clsmVolume",
    COALESCE(d."cementMass"::TEXT, 'N/A') AS "cementMass",
    COALESCE(d."moldWeight"::TEXT, 'N/A') AS "moldWeight",
    COALESCE(d."moldWithWaterWeight"::TEXT, 'N/A') AS "moldWithWaterWeight",
    COALESCE(d."moldVolume"::TEXT, 'N/A') AS "moldVolume",
    COALESCE(d."unitWeightMoldWithSample"::TEXT, 'N/A') AS "unitWeightMoldWithSample",
    COALESCE(d.yield::TEXT, 'N/A') AS "yield",
    COALESCE(d."relativeYield"::TEXT, 'N/A') AS "relativeYield",
    COALESCE(d."cementContent"::TEXT, 'N/A') AS "cementContent",
    COALESCE(d."airContent"::TEXT, 'N/A') AS "airContent"
FROM 
    p_cs.st_concrete_density d
JOIN 
    p_cs.sample s ON d."sampleId" = s.id
LEFT JOIN 
    p_cs.concrete_mix_design cmd ON s."mixId" = cmd.id
WHERE 
    d."isDelete" = false
    AND s."isDelete" = false
    AND s.id = {sampleId}; `

 export { clsmReportQuery };