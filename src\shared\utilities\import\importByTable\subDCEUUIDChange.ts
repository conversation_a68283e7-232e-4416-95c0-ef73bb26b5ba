import DCERelationModel from '@models/meta/dceRelation.model';

const subDCEUUIDChange = async (data: any[], subDCEId: string, userId: string) => {
  try {
    const dceRelationModel = new DCERelationModel();
    const relation = await dceRelationModel.getBySubDceId(subDCEId);

    const updateData = [];

    for (const value of relation) {
      for (const item of data) {
        if (value.columnName && value.columnName in item) {
          const uuidValue = await dceRelationModel.getDataByDceRelationIdSingleDataForSub(
            subDCEId,
            value.columnName,
            item[value.columnName],
            userId
          );
          if (uuidValue.length > 0 && uuidValue[0].name) {
            item[value.columnName] = uuidValue[0].name;
          }
        }
        updateData.push(item);
      }
    }

    return updateData;
  } catch (error) {
    return data;
  }
};

export default subDCEUUIDChange;
