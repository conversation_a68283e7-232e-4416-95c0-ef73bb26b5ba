import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Project } from '../p_gen/Project';
import { StationAlignment } from './StationAlignment';

@Entity({ schema: 'p_map' })
export class Station {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  pointNumber?: number;

  @Column({ nullable: true })
  station?: string;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ nullable: true })
  stationPosition?: string;

  @Column({ nullable: true, default: 'primary' })
  alignment?: string;

  @Column({ nullable: true })
  stationAlignmentId?: string;

  @ManyToOne(() => StationAlignment, { nullable: true })
  @JoinColumn({ name: 'stationAlignmentId' })
  stationAlignment?: StationAlignment;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  easting?: number;

  northing?: number;
}
