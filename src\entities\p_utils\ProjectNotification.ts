import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  Column,
  AfterLoad,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { getTriggerById } from '../../shared/server/platformApi/trigger';

interface NotificationChannelType {
  email: boolean;
  notification: boolean;
  sms: boolean;
}

@Entity({ schema: 'p_utils' })
export class ProjectNotification {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  triggerId?: string;

  @Column({ type: 'jsonb', default: { email: true, notification: true, sms: true } })
  enable?: NotificationChannelType;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  trigger?: any | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.triggerId) {
        const data = await getTriggerById(this.triggerId);
        this.trigger = data;
      } else {
        this.trigger = null;
      }
    } catch (error) {
      this.trigger = null;
    }
  }
}
