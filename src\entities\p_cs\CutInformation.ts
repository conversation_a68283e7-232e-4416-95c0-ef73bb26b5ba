import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  AfterLoad,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { PanelInformation } from './PanelInformation';
import { Equipment } from '../p_meta/Equipment';
import { Station } from '../p_map/Station';
import StationModel from '../../modules/project360/models/map/station.model';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { Purpose } from '../p_meta/Purpose';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class CutInformation extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  projectId?: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  panelInformationId?: string;

  @ManyToOne(() => PanelInformation, { nullable: true })
  @JoinColumn({ name: 'panelInformationId' })
  panelInformation?: PanelInformation;

  @Column({ nullable: true, type: 'uuid' })
  equipmentId?: string;

  @ManyToOne(() => Equipment)
  @JoinColumn({ name: 'equipmentId' })
  equipment?: Equipment;

  @Column()
  equipmentDirection?: string;

  @Column({ nullable: true, type: 'uuid' })
  cutId?: string;

  @Column()
  cutType?: string;

  @Column()
  stationStart?: string;

  @Column()
  eastingStart?: number;

  @Column()
  northingStart?: number;

  @Column()
  latitudeStart?: number;

  @Column()
  longitudeStart?: number;

  @Column()
  stationEnd?: string;

  @Column()
  eastingEnd?: number;

  @Column()
  northingEnd?: number;

  @Column()
  latitudeEnd?: number;

  @Column()
  longitudeEnd?: number;

  @Column()
  offsetCenterline?: number;

  @Column()
  cutlength?: number;

  @Column()
  groundElevation?: number;

  @Column()
  bottomWallElevation?: number;

  @Column({ type: 'timestamp', nullable: true })
  PanelStartDate?: Date;

  @Column()
  trenchCutterRigId?: string;

  @Column({ type: 'timestamp', nullable: true })
  trenchCutterStartDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  trenchCutterEndDate?: Date;

  @Column()
  trenchCutterDuration?: number;

  @Column()
  trenchCutterDepth?: number;

  @Column()
  trenchCutterTopElevation?: number;

  @Column()
  trenchCutterBottomElevation?: number;

  @Column()
  excavatorId?: string;

  @Column({ type: 'timestamp', nullable: true })
  excavationStartDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  excavationEndDate?: Date;

  @Column()
  excavationDuration?: number;

  @Column()
  excavationDepth?: number;

  @Column()
  excavationTopElevation?: number;

  @Column()
  excavationBottomElevation?: number;

  @Column()
  createdBy?: string;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  startStationCoords?: Partial<Station> | null;
  endStationCoords?: Partial<Station> | null;

  @AfterLoad()
  async afterLoad() {
    try {
      const stationModel = new StationModel();

      if (this.stationStart && this.stationEnd && this.projectId) {
        const startData = await stationModel.getCoordinatesByStation(
          this.stationStart,
          this.projectId
        );

        this.startStationCoords = { latitude: startData.latitude, longitude: startData.longitude };
        const endData = await stationModel.getCoordinatesByStation(this.stationEnd, this.projectId);
        this.endStationCoords = { latitude: endData.latitude, longitude: endData.longitude };
      } else {
        this.startStationCoords = null;
        this.endStationCoords = null;
      }
    } catch (error) {
      this.startStationCoords = null;
      this.endStationCoords = null;
    }
  }
}
