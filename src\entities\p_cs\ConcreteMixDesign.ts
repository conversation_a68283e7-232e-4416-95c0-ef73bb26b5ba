import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { SampleType } from '../p_meta/SampleType';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class ConcreteMixDesign extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Trial/Final',
      fieldName: 'designType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  designType?: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Type', // this column will be entered by the
      fieldName: 'sampleTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.sample_type WHERE "name" = $1`,
      getListQuery:
        'SELECT id,"name" as name FROM p_meta.sample_type ORDER BY "updatedAt" DESC LIMIT 20;',
      listName: 'sampleTypeList',
    },
  })
  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @ColumnInfo({
    customData: {
      name: 'Mix ID',
      fieldName: 'mixId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  mixId?: string;

  @ColumnInfo({
    customData: {
      name: 'Mix Proportions Type',
      fieldName: 'mixProportionsType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  mixProportionsType?: string;

  @ColumnInfo({
    customData: {
      name: 'Mix Description',
      fieldName: 'mixDescription',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  mixDescription?: string;

  @ColumnInfo({
    customData: {
      name: 'Cement Type',
      fieldName: 'cementType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  cementType?: string;

  @ColumnInfo({
    customData: {
      name: 'Cement Supplier',
      fieldName: 'cementSupplier',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  cementSupplier?: string;

  @ColumnInfo({
    customData: {
      name: 'Admixture 1 Type',
      fieldName: 'admixture1Type',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  admixture1Type?: string;
  @ColumnInfo({
    customData: {
      name: 'Admixture 2 Type',
      fieldName: 'admixture2Type',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  admixture2Type?: string;
  @ColumnInfo({
    customData: {
      name: 'Admixture 3 Type',
      fieldName: 'admixture3Type',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  admixture3Type?: string;

  @ColumnInfo({
    customData: {
      name: 'admixture Supplier',
      fieldName: 'admixtureSupplier',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  admixtureSupplier?: string;

  @ColumnInfo({
    customData: {
      name: 'water Source',
      fieldName: 'waterSource',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  waterSource?: string;
  @ColumnInfo({
    customData: {
      name: 'Flyash Supplier',
      fieldName: 'flyashSupplier',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  flyashSupplier?: string;
  @ColumnInfo({
    customData: {
      name: 'Flyash Type',
      fieldName: 'flyashType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  flyashType?: string;

  @ColumnInfo({
    customData: {
      name: 'aggregate Source',
      fieldName: 'aggregateSource',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  aggregateSource?: string;

  @ColumnInfo({
    customData: {
      name: 'Water/Cement Ratio',
      fieldName: 'wcRatio',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wcRatio?: number;

  @ColumnInfo({
    customData: {
      name: 'Water Quantity per Cubic Yard',
      fieldName: 'waterQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  waterQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'cement Quantity',
      fieldName: 'cementQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'fineAggregate Quantity',
      fieldName: 'fineAggregateQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fineAggregateQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'moisture Content',
      fieldName: 'moistureContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moistureContent?: number;

  @ColumnInfo({
    customData: {
      name: 'air Content',
      fieldName: 'airContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  airContent?: number;

  @ColumnInfo({
    customData: {
      name: 'admixture 1 Weight',
      fieldName: 'admixture1Weight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixture1Weight?: number;
  @ColumnInfo({
    customData: {
      name: 'admixture 2 Weight',
      fieldName: 'admixture2Weight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixture2Weight?: number;
  @ColumnInfo({
    customData: {
      name: 'admixture 3 Weight',
      fieldName: 'admixture3Weight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixture3Weight?: number;

  @ColumnInfo({
    customData: {
      name: 'admixture Volume',
      fieldName: 'admixtureVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  admixtureVolume?: number;

  @ColumnInfo({
    customData: {
      name: 'target Flow',
      fieldName: 'targetFlow',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  targetFlow?: number;

  @ColumnInfo({
    customData: {
      name: 'flow Range',
      fieldName: 'flowRange',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  flowRange?: string;

  @ColumnInfo({
    customData: {
      name: 'theoretical Unit Weight',
      fieldName: 'theoreticalUnitWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  theoreticalUnitWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Sand Quantity per Cubic Yard',
      fieldName: 'sandQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sandQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'Sand Type',
      fieldName: 'sandType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  sandType?: string;

  @ColumnInfo({
    customData: {
      name: 'Aggregate Quantity 1',
      fieldName: 'aggregateQuantity1',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  aggregateQuantity1?: number;

  @ColumnInfo({
    customData: {
      name: 'Aggregate Quantity 2',
      fieldName: 'aggregateQuantity2',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  aggregateQuantity2?: number;

  @ColumnInfo({
    customData: {
      name: 'Aggregate Quantity 3',
      fieldName: 'aggregateQuantity3',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  aggregateQuantity3?: number;
  @ColumnInfo({
    customData: {
      name: 'Aggregate Type 1',
      fieldName: 'aggregateType1',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  aggregateType1?: string;

  @ColumnInfo({
    customData: {
      name: 'Aggregate Type 2',
      fieldName: 'aggregateType2',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  aggregateType2?: string;

  @ColumnInfo({
    customData: {
      name: 'Aggregate Type 3',
      fieldName: 'aggregateType3',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  aggregateType3?: string;

  @ColumnInfo({
    customData: {
      name: 'Fly Ash Quantity per Cubic Yard',
      fieldName: 'flyAshQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  flyAshQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
