import { EntityTarget, getManager, getRepository, ObjectLiteral } from 'typeorm';

/**
 * Sanitizes request data before updating the database.
 *
 * This function performs the following actions:
 * 1. **Ignore Fields with Spaces** - Some field names in SurveyJS forms contain spaces, which are not supported by our DB.
 * 2. **Ignore some fields** - Ignore fields that are not needed while updating records.
 * 3. **Convert Empty Strings to Null** - Ensures consistency by replacing empty string values (`""`) with `null`.
 *
 */
export const sanitizeRequestData = (
  data: Record<string, any>,
  ignoreFields: string[] = []
): Record<string, any> => {
  try {
    const defaultIgnoreFields: string[] = [];

    const fieldsToIgnore = new Set([...defaultIgnoreFields, ...ignoreFields]);

    const sanitizedData: Record<string, any> = {};

    for (const key in data) {
      if (fieldsToIgnore.has(key)) continue;
      if (key.includes(' ')) continue;
      sanitizedData[key] = data[key] === '' ? null : data[key];
    }

    return sanitizedData;
  } catch (error) {
    throw new Error(`Error sanitizing request data: ${error}`);
  }
};

// **
/* Move columns not present in the schema to the metadata field

// */

export const moveUnknownPrimitivesToMetadata = async <T extends ObjectLiteral>(
  data: Record<string, unknown>,
  entity: EntityTarget<T>
): Promise<Record<string, any>> => {
  const clonedData = { ...data };
  const metadata: Record<string, any> = {};

  // Get all column names defined in the entity
  const entityMetadata = getRepository(entity).metadata;
  const query = `SELECT column_name as name, udt_name as type
                          FROM information_schema.columns
                          WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;
  const entityColumnsResult: { name: string }[] = await getManager().query(query);
  const entityColumnNames = entityColumnsResult.map((col) => col.name);

  // Move unknown fields to metadata
  for (const key of Object.keys(clonedData)) {
    const value = clonedData[key];
    const isPrimitive =
      value === null ||
      typeof value === 'string' ||
      typeof value === 'number' ||
      typeof value === 'boolean';
    if (!entityColumnNames.includes(key) && key !== 'metadata' && isPrimitive) {
      metadata[key] = value;
      delete clonedData[key];
    }
  }

  // Add or merge into metadata field
  if (Object.keys(metadata).length > 0) {
    clonedData.metadata = {
      ...(clonedData.metadata || {}),
      ...metadata,
    };
  }

  return clonedData;
};
