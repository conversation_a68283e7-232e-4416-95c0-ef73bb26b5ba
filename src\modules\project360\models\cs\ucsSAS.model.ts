import { getManager } from 'typeorm';
import { StSoilUCSSAC } from '../../../../entities/p_cs/StSoilUCSSAC';

class UCSSASModel {
  constructor() {}


  async add(newUCSTest: StSoilUCSSAC) {
    try {
      const UCSTestRepository = getManager().getRepository(StSoilUCSSAC);
      const addedUCSest = UCSTestRepository.create(newUCSTest);
      await UCSTestRepository.save(addedUCSest);
      return addedUCSest;
    } catch (error) {
      throw error;
    }
  }
}

const ucsSASModel = new UCSSASModel();
export default ucsSASModel;
