import DCERelationModel from '../../../modules/project360/models/meta/dceRelation.model';

const convertTableToSurveyJSON = async (
  columnInfo: { name: string; type: string }[],
  entity: string,
  dceId: string,
  subdceDetails?: {
    name: string | undefined | null;
    columns: any;
    columnToRemove: string[];
  }[]
) => {
  try {
    const columnsToRemove = [
      'id',
      'approvalStatusId',
      'geom',
      'projectId',
      'submittalVersionId',
      'workPackageActivityId',
      'isDelete',
      'updatedAt',
      'createdBy',
      'createdAt',
      'updatedBy',
      'qcVerifier',
      'qcDate',
      'qaDate',
      'createdUserId',
      'qaVerifier',
      'sampleTestId',
      'eventLogId',
    ];
    const json: { pages: Page[] } = {
      pages: [
        {
          name: 'page1',
          elements: [],
          title: camelCaseToNormalCase(entity),
        },
      ],
    };

    interface Page {
      name: string;
      elements: any[];
      title: string;
    }

    for (const value of columnInfo) {
      if (!columnsToRemove.includes(value.name)) {
        const type = await typeConversion(value.type, value.name, dceId);
        json.pages[0].elements.push(type);
      }
    }
    if (subdceDetails && subdceDetails.length > 0) {
      for (const element of subdceDetails) {
        const matrixdynamicSchema: any = {
          type: 'matrixdynamic',
          name: element.name,
          title: 'Worksheet',
          columns: [],
          addRowText: 'Add Row',
        };
        for (const value of element.columns) {
          if (value && value.name) {
            if (
              !columnsToRemove.includes(value.name) &&
              !element.columnToRemove.includes(value.name)
            ) {
              const type = await typeConversionForSubDCE(value.type, value.name, dceId);
              matrixdynamicSchema.columns.push(type);
            }
          }
        }
        json.pages[0].elements.push(matrixdynamicSchema);
      }
    }
    return json;
  } catch (error) {
    throw error;
  }
};
export const convertTableToPanel = async (
  columnInfo: { name: string; type: string }[],
  entity: string,
  dceId: string,
  subdceDetails?: {
    name: string | undefined | null;
    columns: any;
    columnToRemove: string[];
  }[]
) => {
  try {
    const columnsToRemove = [
      'id',
      'approvalStatusId',
      'geom',
      'projectId',
      'submittalVersionId',
      'workPackageActivityId',
      'isDelete',
      'updatedAt',
      'createdBy',
      'createdAt',
      'updatedBy',
      'qcVerifier',
      'qcDate',
      'qaDate',
      'createdUserId',
      'qaVerifier',
      'sampleTestId',
      'eventLogId',
    ];
    const json: panel = {
      type: 'panel',
      state: 'expanded',
      name: entity,
      elements: [],
      title: camelCaseToNormalCase(entity),
    };

    interface panel {
      type: 'panel';
      name: string;
      elements: any[];
      title: string;
      state: 'expanded';
    }

    for (const value of columnInfo) {
      if (!columnsToRemove.includes(value.name)) {
        const type = await typeConversion(value.type, value.name, dceId, true, entity);
        json.elements.push(type);
      }
    }
    if (subdceDetails && subdceDetails.length > 0) {
      for (const element of subdceDetails) {
        const matrixdynamicSchema: any = {
          type: 'matrixdynamic',
          name: `${entity}.${element.name}`,
          title: 'Worksheet',
          columns: [],
          addRowText: 'Add Row',
        };
        for (const value of element.columns) {
          if (value && value.name) {
            if (
              !columnsToRemove.includes(value.name) &&
              !element.columnToRemove.includes(value.name)
            ) {
              const type = await typeConversionForSubDCE(value.type, value.name, dceId);
              matrixdynamicSchema.columns.push(type);
            }
          }
        }
        json.elements.push(matrixdynamicSchema);
      }
    }
    return json;
  } catch (error) {
    throw error;
  }
};

const camelCaseToNormalCase = (input: string): string => {
  return input
    .replace(/([a-z])([A-Z])/g, '$1 $2') // Insert a space between camelCase words
    .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize the first letter of each word
};

const typeConversion = async (
  columnType: string,
  columnName: string,
  dceId: string,
  multiple: boolean = false,
  entity?: string
): Promise<any> => {
  switch (columnType) {
    case 'varchar':
      return {
        type: 'text',
        inputType: 'text',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'numeric':
      return {
        type: 'text',
        inputType: 'number',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'int4':
      return {
        type: 'text',
        inputType: 'number',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'timestamp':
      return {
        type: 'text',
        inputType: 'datetime-local',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'bool':
      return {
        type: 'boolean',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'uuid':
      const dceRelaitonModel = new DCERelationModel();
      return {
        type: 'dropdown',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
        choicesByUrl: {
          url: await dceRelaitonModel.getDropdownLinkByDceRelationId(dceId, columnName),
          valueName: 'value',
          titleName: 'name',
          path: 'data',
        },
      };

    default:
      return {
        type: 'text',
        inputType: 'text',
        name: !multiple ? columnName : `${entity}.${columnName}`,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };
  }
};

export const typeConversionNormal = (columnType: string): string => {
  switch (columnType) {
    case 'varchar':
      return 'string';

    case 'numeric':
      return 'number';

    case 'int4':
      return 'number';

    case 'timestamp':
      return 'date';

    case 'bool':
      return 'boolean';

    case 'uuid':
      return 'uuid';

    default:
      return 'string';
  }
};

export const typeConversionForDceConfig = (columnType: string): string => {
  switch (columnType) {
    case 'varchar':
      return 'string';

    case 'numeric':
      return 'number';

    case 'int4':
      return 'number';

    case 'timestamp':
      return 'date';

    case 'bool':
      return 'boolean';

    case 'uuid':
      return 'singleSelect';

    default:
      return 'string';
  }
};

export const getTypeByValue = (value: any) => {
  if (value === null) {
    return 'null';
  }

  if (Array.isArray(value)) {
    return 'array';
  }

  const type = typeof value;

  if (type === 'object') {
    if (value instanceof Date) {
      return 'date';
    } else if (value instanceof RegExp) {
      return 'regexp';
    } else if (value instanceof Map) {
      return 'map';
    } else if (value instanceof Set) {
      return 'set';
    } else if (value instanceof WeakMap) {
      return 'weakmap';
    } else if (value instanceof WeakSet) {
      return 'weakset';
    } else if (value instanceof Promise) {
      return 'promise';
    }
  }

  if (type === 'string') {
    if (!isNaN(value) && !isNaN(parseFloat(value))) {
      return 'number';
    }
    if (!isNaN(Date.parse(value))) {
      return 'date';
    }
    return 'string';
  }

  if (type === 'number') {
    return 'number';
  }

  return type;
};

const typeConversionForSubDCE = async (
  columnType: string,
  columnName: string,
  subDceId: string
): Promise<any> => {
  switch (columnType) {
    case 'varchar':
      return {
        cellType: 'text',
        inputType: 'text',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'numeric':
      return {
        cellType: 'text',
        inputType: 'number',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'int4':
      return {
        cellType: 'text',
        inputType: 'number',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'int8':
      return {
        cellType: 'text',
        inputType: 'number',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'timestamp':
      return {
        cellType: 'text',
        inputType: 'datetime-local',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'bool':
      return {
        cellType: 'boolean',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };

    case 'uuid':
      const dceRelaitonModel = new DCERelationModel();
      return {
        cellType: 'dropdown',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
        choicesByUrl: {
          url: await dceRelaitonModel.getDropdownLinkByDceRelationIdForSubDCE(subDceId, columnName),
          valueName: 'value',
          titleName: 'name',
          path: 'data',
        },
      };

    default:
      return {
        type: 'text',
        inputType: 'text',
        name: columnName,
        title: camelCaseToNormalCase(columnName.replace('Id', '')),
      };
  }
};

export default convertTableToSurveyJSON;
