import { getManager, getRepository } from 'typeorm';

import { GeoJsonConfiguration } from '../../../../entities/p_map/GeoJSONConfig';
import dceModel from '../meta/dce.model';
import { entityList, EntityListInterface } from '../../../../shared/utilities/entity/entityList';
import { typeConversionNormal } from '../../../../shared/utilities/surveyJs/convertTableMetaDataToSurveyJsJson';
import { Layer } from '../../../../entities/p_map/Layer';
import GeomLayerModel from './geomLayer.model';
import UniversalFilterMappingModel from './universalFilterMapping.model';
import LayerModel from './layer.model';

class GeoJsonConfigurationModel {
  getData = async (columns: string[], projectId: string, layerId: string, createdBy: string) => {
    try {
      if (columns.length <= 0) {
        throw new Error('No columns were found.');
      }

      const geoJsonConfigurationRepository = getManager().getRepository(GeoJsonConfiguration);
      const exisitingConfig = await geoJsonConfigurationRepository.find({
        where: { layerId, isDelete: false },
        select: ['id', 'columnName', 'alias', 'hide', 'isURL', 'order', 'updatedAt'],
        order: { order: 'ASC' },
      });
      const layerModel = new LayerModel();
      const layerDetails = await layerModel.getById(layerId);
      if (!layerDetails) {
        throw new Error('Layer not found');
      }
      const columnTypes = await this.getColumnNameAndType(layerDetails);
      if (!exisitingConfig) {
        const newColumns = columns.map((value) => {
          const obj = {
            layerId,
            columnName: value,
            projectId,
            createdBy,
            updatedBy: createdBy,
            isURL: false,
          };
          const valueInLowerCase = value.toLowerCase();
          if (valueInLowerCase.includes('path') && !valueInLowerCase.includes('sftp')) {
            obj.isURL = true;
          }
          return obj;
        });
        const createdData = await geoJsonConfigurationRepository.create(newColumns);
        await geoJsonConfigurationRepository.save(createdData);
        const newConfig = await geoJsonConfigurationRepository.find({
          where: { layerId, isDelete: false },
          select: ['id', 'columnName', 'alias', 'hide', 'isURL', 'order', 'updatedAt'],
          order: { order: 'ASC' },
        });
        return newConfig.map((value) => {
          let type = '';
          if (value?.columnName && value?.columnName in columnTypes) {
            type = columnTypes[value?.columnName];
          }

          return {
            ...value,
            type: type, // Handle case where no match is found
          };
        });
      }
      const exisitingConfigColumnNameSet = new Set(exisitingConfig.map((obj) => obj.columnName));
      const differenceToAdd = columns.filter((item) => !exisitingConfigColumnNameSet.has(item));
      const columnNameSet = new Set(columns);
      const differenceToRemove = exisitingConfig.filter(
        (obj) => obj.columnName && !columnNameSet.has(obj.columnName)
      );

      if (differenceToAdd.length > 0) {
        const newColumns = differenceToAdd.map((value) => {
          const obj = {
            layerId,
            columnName: value,
            projectId,
            createdBy,
            updatedBy: createdBy,
          };
          return obj;
        });
        const createdData = await geoJsonConfigurationRepository.create(newColumns);
        await geoJsonConfigurationRepository.save(createdData);
      }
      if (differenceToRemove.length > 0) {
        for (const iterator of differenceToRemove) {
          await geoJsonConfigurationRepository.update(iterator.id, { isDelete: false });
        }
      }

      const finalConfig = await geoJsonConfigurationRepository.find({
        where: { layerId, isDelete: false },
        select: ['id', 'columnName', 'alias', 'hide', 'isURL', 'order', 'updatedAt'],
        order: { order: 'ASC' },
      });
      return finalConfig.map((value) => {
        let type = '';
        if (value?.columnName && value?.columnName in columnTypes) {
          type = columnTypes[value?.columnName];
        }

        return {
          ...value,
          type: type, // Handle case where no match is found
        };
      });
    } catch (error) {
      throw error;
    }
  };

  async findByProjectIdAndDceForGeoJsonGetData(projectId: string, dceId: string) {
    try {
      const geoJsonConfigurationRepository = getManager().getRepository(GeoJsonConfiguration);
      const configurationData = await geoJsonConfigurationRepository.find({
        where: { dceId, projectId },
        order: { order: 'ASC' },
      });
      return configurationData;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdAndDceIsURLForGeoJsonGetData(projectId: string, dceId: string) {
    try {
      const geoJsonConfigurationRepository = getManager().getRepository(GeoJsonConfiguration);
      const configurationData = await geoJsonConfigurationRepository.find({
        where: { dceId, projectId, isURL: true },
        order: { order: 'ASC' },
      });
      return configurationData;
    } catch (error) {
      throw error;
    }
  }

  async getDataByDCEIdAndProjectId(layerId: string, projectId: string) {
    try {
      const geoJsonConfigurationRepository = getManager().getRepository(GeoJsonConfiguration);
      return await geoJsonConfigurationRepository.find({
        where: { layerId, projectId },
        order: { order: 'ASC' },
      });
    } catch (error) {
      throw error;
    }
  }

  getColumnNameAndType = async (layer: Layer) => {
    try {
      if (layer.dceId) {
        const dceData = await dceModel.findById(layer?.dceId);
        if (dceData) {
          const entity = dceData.entity;

          if (entity && entity in entityList) {
            const entityClass: any = entityList[entity as keyof EntityListInterface];
            const entityMetadata = getRepository(entityClass).metadata;
            const query = `SELECT column_name as name, udt_name as type
                            FROM information_schema.columns
                            WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

            const out: { name: string; type: string }[] = await getManager().query(query);

            const finalOut: any = {};
            for (const iterator of out) {
              const type = typeConversionNormal(iterator.type);
              finalOut[iterator.name] = type;
            }

            return finalOut;
          }
        } else {
          throw new Error('No columns not found');
        }
      } else {
        const gemoLayerModel = new GeomLayerModel();
        const properties = await gemoLayerModel.getPropertiesAndTypeByLayerId(layer.id);
        if (properties) {
          return properties;
        } else {
          throw new Error('No columns not found');
        }
      }
    } catch (error) {
      throw error;
    }
  };
  async getColumnNameAndTypeUniversalFilter(layer: Layer) {
    try {
      if (layer.dceId) {
        const dceData = await dceModel.findById(layer?.dceId);
        if (dceData) {
          const entity = dceData.entity;

          if (entity && entity in entityList) {
            const entityClass: any = entityList[entity as keyof EntityListInterface];
            const entityMetadata = getRepository(entityClass).metadata;
            const query = `SELECT column_name as name, udt_name as type
                            FROM information_schema.columns
                            WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

            const out: { name: string; type: string }[] = await getManager().query(query);
            const universalFilterMappingModel = new UniversalFilterMappingModel();
            const universalMappinglist = await universalFilterMappingModel.findBylayerId(layer.id);
            const finalOut: any = {};
            for (const iterator of out) {
              const type = typeConversionNormal(iterator.type);
              if (!universalMappinglist.find((value) => value.columnName == iterator.name)) {
                finalOut[iterator.name] = type;
              }
            }

            return finalOut;
          }
        } else {
          throw new Error('No columns not found');
        }
      } else {
        const gemoLayerModel = new GeomLayerModel();
        const properties = await gemoLayerModel.getPropertiesAndTypeByLayerId(layer.id);
        if (properties) {
          return properties;
        } else {
          throw new Error('No columns not found');
        }
      }
    } catch (error) {
      throw error;
    }
  }

  getByIdLayerLableConversion = async (id: string) => {
    try {
      const geoJsonConfigurationRepository = getManager().getRepository(GeoJsonConfiguration);
      const data = await geoJsonConfigurationRepository.findOne({
        where: { id },
      });
      if (data) {
        return { name: data.columnName, status: true };
      }
      return { name: '', status: false };
    } catch (error) {
      return { name: '', status: false };
    }
  };

  async changeColumnOrder(newOrder: { id: string; order: number }[]) {
    try {
      await getManager().transaction(async (transactionalEntityManager) => {
        try {
          Promise.all(
            newOrder.map(async (value) => {
              try {
                await transactionalEntityManager.update(
                  GeoJsonConfiguration,
                  { id: value.id },
                  { order: value.order }
                );
              } catch (error) {
                throw error;
              }
            })
          );
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }
}

export default GeoJsonConfigurationModel;
