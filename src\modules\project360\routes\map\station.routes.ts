import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import StationController from '../../controllers/map/station.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const controller = new StationController();

// Mount the userRouter for CRUD operations at /auth/user/crud

router.get('/by/project/:projectId', authenticateToken, controller.getByProjectId);
router.get('/and/offset/:projectId', authenticateToken, controller.getStationAndOffset);
router.get('/by/coordinates/:projectId', authenticateToken, controller.getStationByCoordinates);
router.get(
  '/by/station/:station/:projectId',
  authenticateToken,
  controller.getCoordinatesByStation
);
router.get('/convert/coordinates/:projectId', authenticateToken, controller.convertCoordinates);
router.get('/by/alignment/:alignmentId', controller.getStationsByAlignmentId)

export default router;
