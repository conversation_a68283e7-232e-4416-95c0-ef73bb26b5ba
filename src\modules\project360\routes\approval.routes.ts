import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import approvalInstanceController from '../controllers/approval.controller';

const router: Router = express.Router();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/user/:id/:type', authenticateToken, approvalInstanceController.findByUserId);
router.get('/by/user/:id/:type/:projectId', authenticateToken, approvalInstanceController.findByUserIdAndProjectId);
router.patch(
  '/microservice/send/for/approval',
  authenticateToken,
  approvalInstanceController.microserviceSendForApproval
);
router.get(
  '/by/user/with/data/:id/:type',
  authenticateToken,
  approvalInstanceController.getApprovalWithData
);
router.put('/update', authenticateToken, approvalInstanceController.editMultipleApprovalStatus);
router.put(
  '/update/multiple',
  authenticateToken,
  approvalInstanceController.editMultipleApprovalStatus
);
router.get('/table/details/by/:id', authenticateToken, approvalInstanceController.findTableData);
// router.use('/', ApprovalSetupController.getRouter());

export default router;
