import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilAtterbergLimit } from '../../../../../../entities/p_cs/StSoilAtterbergLimit';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilAtterbergLimit>(StSoilAtterbergLimit);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'atterbergLimits')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'atterbergLimits')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'atterbergLimits')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'atterbergLimits')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'atterbergLimits')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'atterbergLimits')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'atterbergLimits')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'atterbergLimits')
);

export default router;
