import MobileAccessModel from '@models/auth/mobleAccess.model';
import projectRoleModel from '@models/role and permission/projectRole.model';

const getMobleAccessByUserAndProject = async (projectId: string, userId: string) => {
  try {
    const role = await projectRoleModel.findRoleByUserAndProject(projectId, userId);
    if (!role) {
      return {
        dceIds: [],
        formIds: [],
      };
    }
    const mobileAccessModel = new MobileAccessModel();
    return await mobileAccessModel.getByDCEAndFromIdsByRoleId(role.id);
  } catch (error) {
    throw error;
  }
};

export default getMobleAccessByUserAndProject;
