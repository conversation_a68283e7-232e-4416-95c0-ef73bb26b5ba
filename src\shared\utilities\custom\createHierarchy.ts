import { ProjectOrgStructure } from '../../../entities/p_auth/OrgStructure';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { FeatureLocation } from '../../../entities/p_domain/FeatureLocation';
import { FeatureSubLocation } from '../../../entities/p_domain/FeatureSubLocatoin';
import { FeatureType } from '../../../entities/p_domain/FeatureType';
import { Feature } from '../../../entities/p_gen/Feature';
import Structure from '../../../entities/p_gen/Structure';
import StructureType from '../../../entities/p_gen/StructureType';

interface OrgHierarchyNode {
  id: string | undefined;
  projectId: string | undefined;
  parentId: string | null | undefined;
  stakeholderId: string | null | undefined;
  roleType: string | undefined;
  stakeHolder: Stakeholder | undefined;
  level: number | undefined;
  children: OrgHierarchyNode[];
}

interface OrgHierarchyFeatureNode {
  id: string | undefined | null;
  projectId: string | undefined | null;
  parentId: string | null | undefined | null;
  name: string | null | undefined | null;
  featureTypeId: string | undefined | null;
  featureType: FeatureType | undefined | null;
  featureLocationId: string | undefined | null;
  featureLocation: FeatureLocation | undefined | null;
  featureSubLocationId: string | undefined | null;
  featureSubLocation: FeatureSubLocation | undefined | null;
  createdBy: string | undefined | null;
  updatedBy: string | undefined | null;
  createdAt: Date | undefined | null;
  updatedAt: Date | undefined | null;
  description: string | undefined | null;
  level: number | undefined | null;
  children: OrgHierarchyFeatureNode[];
}

interface OrgHierarchyFeatureNodeForDropdown {
  value: string | undefined | null;
  parentId: string | null | undefined | null;
  name: string | null | undefined | null;
  children: OrgHierarchyFeatureNodeForDropdown[];
}

export function buildHierarchyOrgStructure(data: ProjectOrgStructure[]): OrgHierarchyNode[] {
  const map = new Map<string, OrgHierarchyNode>();
  const hierarchy: OrgHierarchyNode[] = [];

  data.forEach((orgStructure) => {
    const id = orgStructure.id;
    const parentId = orgStructure.parentId;

    if (id !== undefined) {
      const newNode: OrgHierarchyNode = {
        id,
        projectId: orgStructure.projectId,
        parentId: parentId || null,
        roleType: orgStructure.roleType,
        stakeholderId: orgStructure.stakeholderId,
        stakeHolder: orgStructure.stakeholder,
        level: 0,
        children: [],
      };

      map.set(id, newNode);

      if (parentId === null || parentId === undefined) {
        hierarchy.push(newNode);
      } else {
        const parent = map.get(String(parentId));
        if (parent) {
          parent.children.push(newNode);
        }
      }
    }
  });

  return hierarchy;
}

export function buildHierarchyFeature(data: Feature[]): OrgHierarchyFeatureNode[] {
  const map = new Map<string, OrgHierarchyFeatureNode>();
  const hierarchy: OrgHierarchyFeatureNode[] = [];

  data.forEach((orgStructure) => {
    const id = orgStructure.id;
    const parentId = orgStructure.parentId;

    if (id !== undefined) {
      const newNode: OrgHierarchyFeatureNode = {
        id: orgStructure.id,
        projectId: orgStructure.projectId,
        parentId: parentId || null,
        featureLocationId: orgStructure.featureLocationId || null,
        featureLocation: orgStructure.featureLocation || null,
        featureSubLocationId: orgStructure.featureSubLocationId || null,
        featureSubLocation: orgStructure.featureSubLocation || null,
        featureTypeId: orgStructure.featureTypeId || null,
        featureType: orgStructure.featureType || null,
        name: orgStructure.name || null,
        createdAt: orgStructure.createdAt || null,
        createdBy: orgStructure.createdBy || null,
        updatedAt: orgStructure.updatedAt || null,
        updatedBy: orgStructure.updatedBy || null,
        description: orgStructure.description || null,
        level: 0,
        children: [],
      };

      map.set(id, newNode);

      if (parentId === null || parentId === undefined) {
        hierarchy.push(newNode);
      } else {
        const parent = map.get(parentId as string);
        if (parent) {
          parent.children.push(newNode);
        } else {
          const placeholderParent: OrgHierarchyFeatureNode = {
            id: parentId,
            projectId: null,
            parentId: null,
            name: null,
            createdAt: null,
            createdBy: null,
            updatedAt: null,
            updatedBy: null,
            description: null,
            level: 0,
            children: [newNode],
            featureTypeId: null,
            featureType: null,
            featureLocationId: null,
            featureLocation: null,
            featureSubLocationId: null,
            featureSubLocation: null,
          };

          map.set(parentId, placeholderParent);
        }
      }
    }
  });

  return hierarchy;
}
export function buildHierarchyFeatureForDropdown(
  data: Feature[]
): OrgHierarchyFeatureNodeForDropdown[] {
  const map = new Map<string, OrgHierarchyFeatureNodeForDropdown>();
  const hierarchy: OrgHierarchyFeatureNodeForDropdown[] = [];

  data.forEach((orgStructure) => {
    const value = orgStructure.id;
    const parentId = orgStructure.parentId;

    if (value !== undefined) {
      const newNode: OrgHierarchyFeatureNodeForDropdown = {
        value: orgStructure.id,
        parentId: parentId || null,
        name: orgStructure.name,
        children: [],
      };

      map.set(value, newNode);

      if (parentId === null || parentId === undefined) {
        hierarchy.push(newNode);
      } else {
        const parent = map.get(parentId as string);
        if (parent) {
          parent.children.push(newNode);
        } else {
          const placeholderParent: OrgHierarchyFeatureNodeForDropdown = {
            value: parentId,
            parentId: null,
            name: null,
            children: [newNode],
          };

          map.set(parentId, placeholderParent);
        }
      }
    }
  });

  return hierarchy;
}

interface OrgHierarchyStructureNode {
  id: string | undefined | null;
  projectId: string | undefined | null;
  parentId: string | null | undefined;
  name: string | null | undefined;
  latitude: number | undefined | null;
  longitude: number | undefined | null;
  createdBy: string | undefined | null;
  updatedBy: string | undefined | null;
  createdAt: Date | undefined | null;
  updatedAt: Date | undefined | null;
  description: string | undefined | null;
  type: StructureType | undefined | null;
  level: number | undefined | null;
  children: OrgHierarchyStructureNode[];
}

export function buildHierarchyStructure(data: Structure[]): OrgHierarchyStructureNode[] {
  const map = new Map<string, OrgHierarchyStructureNode>();
  const hierarchy: OrgHierarchyStructureNode[] = [];

  // First pass: Create all nodes
  data.forEach((orgStructure) => {
    const id = orgStructure.id;
    if (id !== undefined) {
      map.set(id, {
        id,
        projectId: orgStructure.projectId,
        parentId: orgStructure.parentId || null,
        latitude: orgStructure.latitude,
        longitude: orgStructure.longitude,
        name: orgStructure.name,
        type: orgStructure.type,
        createdAt: orgStructure.createdAt,
        createdBy: orgStructure.createdBy,
        updatedAt: orgStructure.updatedAt,
        updatedBy: orgStructure.updatedBy,
        description: orgStructure.description,
        level: 0, // Will be updated later
        children: [],
      });
    }
  });

  // Second pass: Assign children and calculate levels
  data.forEach((orgStructure) => {
    const node = map.get(orgStructure.id);
    if (node) {
      if (node.parentId === null) {
        hierarchy.push(node);
      } else {
        const parent = map.get(node.parentId || '');
        if (parent) {
          parent.children.push(node);
          node.level = parent.level || 0 + 1;
        } else {
          console.warn(`Parent not found for ID: ${node.parentId}`);
        }
      }
    }
  });

  return hierarchy;
}
