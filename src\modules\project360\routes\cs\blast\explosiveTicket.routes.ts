import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { ExplosiveTicket } from '../../../../../entities/p_cs/ExplosiveTicket';

const router: Router = express.Router();

const GenricController = new CrudController<ExplosiveTicket>(ExplosiveTicket);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'explosiveTicket')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'explosiveTicket')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'explosiveTicket')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'explosiveTicket')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'explosiveTicket')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'explosiveTicket')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'explosiveTicket')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'explosiveTicket')
);

export default router;
