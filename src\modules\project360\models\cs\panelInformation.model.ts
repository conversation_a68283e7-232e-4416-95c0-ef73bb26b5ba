import { get<PERSON>anager, <PERSON>Null, Not } from 'typeorm';
import { PanelInformation } from '../../../../entities/p_cs/PanelInformation';

class PanelInformationModel {
  private PanelInformationrepository = getManager().getRepository(PanelInformation);

  private convertStationToNumber(station: string): number {
    const [prefix, suffix] = station.split('+');
    return parseInt(prefix) * 100 + parseInt(suffix);
  }

  getPanelsByStations = async (start: number, end: number) => {
    try {
      const startValue = start;
      const endValue = end;
      const panels = await this.PanelInformationrepository.find({
        where: [
          {
            stationStart: Not(IsNull()),
            stationEnd: Not(IsNull()),
          },
        ],
        relations: ['cutInformation'],
      });

      return panels.filter((panel) => {
        const panelStartValue = this.convertStationToNumber(panel.stationStart!);
        const panelEndValue = this.convertStationToNumber(panel.stationEnd!);

        return (
          (panelStartValue <= endValue && panelEndValue >= startValue) || // Case 1: Panel overlaps the given range
          (panelStartValue >= startValue && panelStartValue <= endValue) || // Case 2: Panel's start is within the range
          (panelEndValue >= startValue && panelEndValue <= endValue) // Case 3: Panel's end is within the range
        );
      });
    } catch (error) {
      throw error;
    }
  };
}

export default PanelInformationModel;
