import { MigrationInterface, QueryRunner } from 'typeorm';

export class Mig2105251747831643031 implements MigrationInterface {
  name = 'Mig2105251747831643031';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" ADD "wetWeight" numeric`);
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" ADD "dryWeight" numeric`);
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" ADD "panWeight" numeric`);
    await queryRunner.query(
      `ALTER TABLE "p_cs"."st_soil_sand_cone_test" ADD "volumeOfSandCone" numeric`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "p_cs"."st_soil_sand_cone_test" DROP COLUMN "volumeOfSandCone"`
    );
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" DROP COLUMN "panWeight"`);
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" DROP COLUMN "dryWeight"`);
    await queryRunner.query(`ALTER TABLE "p_cs"."st_soil_sand_cone_test" DROP COLUMN "wetWeight"`);
  }
}
