import { getManager } from 'typeorm';
import { LayerParent } from '../../../../entities/p_map/LayerParent';
import filterByUpdateAt from '../../../../shared/utilities/custom/filterDataByUpdateAt';

class LayerParentModel {
  async getByProjectId(projectId: string) {
    try {
      const layerParentRepo = getManager().getRepository(LayerParent);

      const data = await layerParentRepo.find({
        where: {
          projectId,
          isDelete: false,
        },
        relations: [
          'layer',
          'layer.legend',
          'layer.geomLayer',
          'layer.dce',
          'layer.legend.legendCriteria',
        ],
        order: { order: 'ASC', layer: { order: 'ASC' } },
      });

      const final = data.map((value) => {
        value.layer = value.layer.filter((item) => item.isDelete == false);
        value.layer = value.layer.map((item) => {
          item.legend = item.legend?.filter((fil) => fil.isDelete == false);
          return item;
        });
        return value;
      });

      return final;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectIdForMobile(projectId: string, updatedAfter?: Date) {
    try {
      const layerParentRepo = getManager().getRepository(LayerParent);

      const data = await layerParentRepo
        .createQueryBuilder('lp')
        .leftJoinAndSelect('lp.layer', 'l')
        .leftJoinAndSelect('l.legend', 'lg')
        .where('lp.projectId = :projectId', { projectId })
        .andWhere('lp.isDelete = :isDelete', { isDelete: false })
        .andWhere('lp.enableMobile = :enableMobile', { enableMobile: true })
        .andWhere('l.isDelete = :isDelete', { isDelete: false })
        .andWhere('l.enableMobile = :enableMobile', { enableMobile: true })
        .orderBy('lp.order', 'ASC')
        .addOrderBy('l.order', 'ASC')
        .select([
          'lp.name',
          'lp.order',
          'lp.projectId',
          'lp.updatedAt',
          'l.id',
          'l.name',
          'l.updatedAt',
          'l.dceId',
          'l.label',
          'l.mapLabelColor',
          'l.layerType',
          'l.order',
          'l.mapLabelZoom',
          'l.enablePopupForMap',
          'l.enableLabelForMap',
          'lg.color',
          'lg.path',
          'lg.updatedAt',
          // 'lg.fill',
          // 'lg.size',
        ])
        .getMany();

      if (updatedAfter) {
        const final = filterByUpdateAt(updatedAfter, data);
        return final;
      }
      const final = data.map((value) => {
        value.layer = value.layer?.map((item) => {
          if (item.legend && item.legend.length > 0) {
            const legend = item.legend?.filter(
              (fil) => fil.isDelete == false && fil.isDefault == true
            );
            if (legend && legend.length < 0) {
              item.legend = [item.legend[0]];
            }
          }
          return {
            labelColor: item?.mapLabelColor,
            zoomLevel: item?.mapLayerZoom,
            enablePopUp: item?.enablePopupForMap,
            enableLabel: item?.enableLabelForMap,
            ...item,
          };
        });
        return value;
      });
      return final;
    } catch (error) {
      throw error;
    }
  }

  async changeOrder(newOrder: { id: string; order: number }[]) {
    try {
      await getManager().transaction(async (transactionalEntityManager) => {
        try {
          Promise.all(
            newOrder.map(async (value) => {
              try {
                await transactionalEntityManager.update(
                  LayerParent,
                  { id: value.id },
                  { order: value.order }
                );
              } catch (error) {
                throw error;
              }
            })
          );
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }
}

export default LayerParentModel;
