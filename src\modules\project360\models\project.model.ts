import { getConnection, getManager } from 'typeorm';
import { Project } from '../../../entities/p_gen/Project';
import { StakeUser } from '../../../entities/p_auth/StakeUser';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { ProjectOrgStructure } from '../../../entities/p_auth/OrgStructure';
import StructureType from '../../../entities/p_gen/StructureType';
import { createAuditLog } from '../../../shared/utilities/auditLog/createAuditLog';
import { ProjectRoleRoute } from '../../../entities/p_auth/projectRoleRoute';
import { TaskStatus } from '../../../entities/p_utils/TaskStatus';
import { getOrganzationById } from '../../../shared/server/platformApi/organization';
import { TaskType } from '../../../entities/p_utils/TaskType';
import { TaskPriority } from '../../../entities/p_utils/TaskPriority';
import { Bucket } from '../../../entities/p_utils/Bucket';
import { ActivityLog } from '../../../entities/p_gen/ActivityLog';
import projectRoleModel from './role and permission/projectRole.model';
import { checkIsSuperAdmin } from '../../../shared/server/platformApi/role';
import workPackageModel from './workPackage/workPackage.model';

class ProjectModel {
  constructor() {}
  async addProject(newproject: Project) {
    try {
      const projectRepository = getManager().getRepository(Project);
      const addedProject = projectRepository.create(newproject);
      await projectRepository.save(addedProject);
      return addedProject;
    } catch (error) {
      throw error;
    }
  }

  async updateSFTPPassword(token: string, id: string) {
    try {
      const projectRepository = getManager().getRepository(Project);
      const addedProject = projectRepository.update(id, { sftpToken: token });
      return addedProject;
    } catch (error) {
      throw error;
    }
  }

  async addProjectByTransaction(
    newProject: Project,
    newStakeholder: Partial<Stakeholder>,
    newStakeUser: StakeUser,
    NewOrgStructure: Partial<ProjectOrgStructure>,
    createdUserId: string
  ) {
    try {
      const projectData = await getManager().transaction(async (transactionalEntityManager) => {
        const organizationName = await getOrganzationById(newProject.organizationId || '');
        newStakeholder.name = organizationName?.name;
        const projectRepository = await transactionalEntityManager.save(Project, newProject);

        if (projectRepository && projectRepository.id) {
          newStakeholder.projectId = projectRepository.id;
          newStakeholder.projectNo = projectRepository.projectNumber;
          newStakeholder.projectId = projectRepository.id;
          newStakeholder.organizationId = projectRepository.organizationId;
          const stakeholderRepository = await transactionalEntityManager.save(
            Stakeholder,
            newStakeholder
          );
          if (stakeholderRepository && stakeholderRepository.id) {
            newStakeUser.stakeholderId = stakeholderRepository.id;
            await transactionalEntityManager.save(StakeUser, newStakeUser);
            NewOrgStructure.projectId = projectRepository.id;
            NewOrgStructure.stakeholderId = stakeholderRepository.id;
            await transactionalEntityManager.save(ProjectOrgStructure, NewOrgStructure);
            const taskStatus: any[] = [
              {
                name: 'Todo',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'In progress',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },

              {
                name: 'Ready to test',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Done',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
            ];
            await Promise.all(
              taskStatus.map(async (value) => {
                return transactionalEntityManager.save(TaskStatus, value);
              })
            );

            const defaultValue: any[] = [
              {
                name: 'Structure',
                projectId: projectRepository.id,
                level: 1,
                description: 'Structure',
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Sub-structure',
                projectId: projectRepository.id,
                level: 2,
                description: 'Sub-structure',
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Element',
                projectId: projectRepository.id,
                level: 3,
                description: 'Element',
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
            ];

            await Promise.all(
              defaultValue.map(async (value) => {
                return transactionalEntityManager.save(StructureType, value);
              })
            );

            const defaultTaskType: any[] = [
              {
                name: 'RFI',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Submittal',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
            ];
            await Promise.all(
              defaultTaskType.map(async (value) => {
                return transactionalEntityManager.save(TaskType, value);
              })
            );

            const defaultTaskPriority: any[] = [
              {
                name: 'High',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Medium ',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
              {
                name: 'Low ',
                projectId: projectRepository.id,
                createdBy: projectRepository.createdBy,
                updatedBy: projectRepository.createdBy,
              },
            ];
            await Promise.all(
              defaultTaskPriority.map(async (value) => {
                return transactionalEntityManager.save(TaskPriority, value);
              })
            );

            const defaultBucket = {
              name: 'Common',
              projectId: projectRepository.id,
              createdBy: projectRepository.createdBy,
              updatedBy: projectRepository.createdBy,
            };
            await transactionalEntityManager.save(Bucket, defaultBucket);
          }
        }
        console.log('Transaction committed successfully');
        return projectRepository;
      });
      await createAuditLog(
        createdUserId,
        projectData.id,
        `Project created by ${projectData?.createdBy} `,
        `${projectData?.createdBy}`,
        projectData.createdAt as Date,
        'p_gen.project',
        'project',
        projectData.id
      );
      return projectData;
    } catch (error) {
      console.error('Transaction failed. Rolling back.', error);
      throw error;
    }
  }

  async getProjectByUserId(userId: string) {
    try {
      const isAdmin = await checkIsSuperAdmin(userId);
      const connection = await getConnection();
      if (isAdmin) {
        return connection.getRepository(Project).find({ where: { isDelete: false } });
      }
      const stakeUsers = await connection
        .getRepository(StakeUser)
        .find({ where: { userId: userId } });

      // Extract the stakeholderIds from the StakeUser entries
      const stakeholderIds = stakeUsers.map((value) => value.stakeholderId);
      if (stakeholderIds.length <= 0) {
        return [];
      }
      const objectsWithIds = await connection
        .getRepository(Stakeholder)
        .createQueryBuilder('entity')
        .where('entity.id IN (:...ids)', { ids: stakeholderIds })
        .andWhere('entity.isDelete = :isDelete', { isDelete: false })
        .getMany();

      const projectIds = objectsWithIds.map((value) => value.projectId);
      if (projectIds.length > 0) {
        const projects = await connection
          .getRepository(Project)
          .createQueryBuilder('project')
          .leftJoinAndSelect('project.phase', 'phase')
          .where('project.id IN (:...ids)', {
            ids: projectIds,
          })
          .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
          .select(['project', 'phase.id', 'phase.name'])
          .getMany();

        const filterProject = projects.filter((value) => value.isDelete === false);
        const finalProjectList: Project[] = [];
        for (const value of filterProject) {
          const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
          if (item) {
            (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
          }
          finalProjectList.push(value);
        }

        return filterProject.sort((a: any, b: any) => a.name.localeCompare(b.name));
      }

      // Find the projects associated with the stakeholderIds
      return [];
    } catch (error) {
      throw error;
    }
  }

  async getRecentProjectByUserId(userId: string) {
    try {
      const connection = await getConnection();
      const stakeUsers = await connection
        .getRepository(StakeUser)
        .find({ where: { userId: userId } });

      // Extract the stakeholderIds from the StakeUser entries
      const stakeholderIds = stakeUsers.map((value) => value.stakeholderId);
      if (stakeholderIds.length <= 0) {
        return [];
      }
      const objectsWithIds = await connection
        .getRepository(Stakeholder)
        .createQueryBuilder('entity')
        .where('entity.id IN (:...ids)', { ids: stakeholderIds })
        .andWhere('entity.isDelete = :isDelete', { isDelete: false })
        .getMany();

      const projectIds = objectsWithIds.map((value) => value.projectId);
      if (projectIds.length > 0) {
        const projects = await connection
          .getRepository(Project)
          .createQueryBuilder('project')
          .leftJoinAndSelect('project.phase', 'phase')
          .where('project.id IN (:...ids)', {
            ids: projectIds,
          })
          .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
          .select(['project', 'phase.id', 'phase.name'])
          .getMany();

        const filterProject = projects.filter((value) => value.isDelete === false);
        return filterProject;
      }

      // Find the projects associated with the stakeholderIds
      return [];
    } catch (error) {
      throw error;
    }
  }

  async getProjectByUserIdByRecent(userId: string, recent?: number) {
    try {
      const connection = await getManager();
      const isAdmin = await checkIsSuperAdmin(userId);
      const stakeUsers = await connection
        .getRepository(StakeUser)
        .find({ where: { userId: userId } });

      // Extract the stakeholderIds from the StakeUser entries
      const stakeholderIds = stakeUsers.map((value) => value.stakeholderId);
      if (stakeholderIds.length <= 0 && !isAdmin) {
        return [];
      }

      let activeProjectList: any = await connection
        .getRepository(ActivityLog)
        .createQueryBuilder('activityLog')
        .leftJoinAndSelect('activityLog.project', 'project')
        .where('activityLog.createdUserId = :createdUserId', { createdUserId: userId })
        .orderBy('activityLog.projectId') // First order by the distinct on field
        .addOrderBy('activityLog.date', 'DESC')
        .distinctOn(['activityLog.projectId'])
        .select(['activityLog.projectId'])
        .limit(recent)
        .getMany();

      if (isAdmin && activeProjectList.length != recent) {
        const allProjects = await connection
          .getRepository(Project)
          .find({ select: ['id'], take: recent });
        activeProjectList = allProjects.map((value) => ({ projectId: value.id }));
      }
      let projectIds: any[] = [];
      const activeProjectIds = activeProjectList.map((value: any) => value.projectId);
      if (!isAdmin) {
        const objectsWithIds = await connection
          .getRepository(Stakeholder)
          .createQueryBuilder('entity')
          .where('entity.id IN (:...ids)', { ids: stakeholderIds })
          .andWhere('entity.isDelete = :isDelete', { isDelete: false })
          .getMany();

        projectIds = objectsWithIds
          .map((value) => value.projectId)
          .filter((value) => activeProjectIds.includes(value) || isAdmin);

        if (projectIds.length <= 0 && activeProjectIds.length <= 0) {
          projectIds = activeProjectIds;
          if (activeProjectIds.length <= 0) {
            const objectsWithIds = await connection
              .getRepository(Stakeholder)
              .createQueryBuilder('entity')
              .where('entity.id IN (:...ids)', { ids: stakeholderIds })
              .andWhere('entity.isDelete = :isDelete', { isDelete: false })
              .limit(recent)
              .getMany();
            projectIds = objectsWithIds.map((value) => value.projectId);
          }
        }
      }

      if (isAdmin) {
        projectIds = activeProjectList.map((value: any) => value.projectId);
      }

      if (projectIds.length > 0) {
        const projects = await connection
          .getRepository(Project)
          .createQueryBuilder('project')
          .leftJoinAndSelect('project.phase', 'phase')
          .where('project.id IN (:...ids)', {
            ids: projectIds,
          })
          .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
          .select(['project', 'phase.id', 'phase.name'])
          .getMany();

        const filterProject = projects.filter((value) => value.isDelete === false);
        const finalProjectList: Project[] = [];
        for (const value of filterProject) {
          const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
          if (item) {
            (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
          } else {
            (value as any).role = { name: 'Admin', qaqc: [] };
          }
          finalProjectList.push(value);
        }
        return finalProjectList;
      }

      // Find the projects associated with the stakeholderIds
      return [];
    } catch (error) {
      throw error;
    }
  }

  async getProjectByUserIdByRecentByWorkPackage(
    userId: string,
    recent?: number,
    fromDate?: Date,
    toDate?: Date
  ) {
    try {
      const connection = await getManager();
      const isAdmin = await checkIsSuperAdmin(userId);
      const projectIds = await workPackageModel.getProjectIdsByWorkpackage(
        userId,
        recent,
        fromDate,
        toDate
      );
      if (projectIds && projectIds?.length > 0) {
        const stakeUsers = await connection
          .getRepository(StakeUser)
          .find({ where: { userId: userId } });

        // Extract the stakeholderIds from the StakeUser entries
        const stakeholderIds = stakeUsers.map((value) => value.stakeholderId);
        if (stakeholderIds.length <= 0 && !isAdmin) {
          return [];
        }
        let userProjectIds: any = [];
        if (!isAdmin) {
          const objectsWithIds = await connection
            .getRepository(Stakeholder)
            .createQueryBuilder('entity')
            .where('entity.id IN (:...ids)', { ids: stakeholderIds })
            .andWhere('entity.isDelete = :isDelete', { isDelete: false })
            .getMany();
          userProjectIds = objectsWithIds.map((value) => value.projectId);
        }

        if (isAdmin) {
          userProjectIds = (await projectModel.getAll()).map((value) => value.id);
        }

        const filteredProject = projectIds.filter((value) => userProjectIds.includes(value));

        const projects = await connection
          .getRepository(Project)
          .createQueryBuilder('project')
          .leftJoinAndSelect('project.phase', 'phase')
          .where('project.id IN (:...ids)', {
            ids: filteredProject,
          })
          .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
          .select(['project', 'phase.id', 'phase.name'])
          .getMany();

        if (recent && projects.length > recent) {
          const finalProject = [];
          for (let index = 0; index < recent; index++) {
            const element = projects[index];
            finalProject.push(element);
          }
          const finalProjectList: Project[] = [];
          for (const value of finalProject) {
            const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
            if (item) {
              (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
            } else {
              (value as any).role = { name: 'Admin', qaqc: [] };
            }
            finalProjectList.push(value);
          }
          return finalProjectList;
        } else if (recent && projects.length < recent && userProjectIds.length > projects.length) {
          const finalProject = projects || [];
          const AllProjectUnderUer = await connection
            .getRepository(Project)
            .createQueryBuilder('project')
            .leftJoinAndSelect('project.phase', 'phase')
            .where('project.id IN (:...ids)', {
              ids: userProjectIds,
            })
            .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
            .select(['project', 'phase.id', 'phase.name'])
            .getMany();

          const filteredProjectUnderUser = AllProjectUnderUer.filter(
            (item) => !finalProject.find((value) => value.id == item.id)
          );
          const projectDifferent = recent - projects.length;
          const userProjectDifferent =
            projectDifferent > filteredProjectUnderUser.length
              ? userProjectIds.length
              : projectDifferent;
          for (let index = 0; index < userProjectDifferent; index++) {
            const element = filteredProjectUnderUser[index];
            if (element && !finalProject.find((value) => value.id == element.id)) {
              finalProject.push(element);
            }
          }
          const finalProjectList: Project[] = [];
          for (const value of finalProject) {
            const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
            if (item) {
              (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
            } else {
              (value as any).role = { name: 'Admin', qaqc: [] };
            }
            finalProjectList.push(value);
          }
          return finalProjectList;
        }

        const finalProjectList: Project[] = [];
        for (const value of projects) {
          const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
          if (item) {
            (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
          }
          finalProjectList.push(value);
        }

        return finalProjectList;
      } else if (projectIds && projectIds?.length <= 0 && recent) {
        const projects = await this.getProjectByUserIdByRecent(userId, recent);
        const finalProjectList: Project[] = [];
        for (const value of projects) {
          const item = await projectRoleModel.getByProjectIdAndUser(value.id, userId);
          if (item) {
            (value as any).role = { name: item.name, qaqc: (item as any)?.qaqc };
          }
          finalProjectList.push(value);
        }
        return projects;
      }

      // Find the projects associated with the stakeholderIds
      return [];
    } catch (error) {
      throw error;
    }
  }

  async getProjectId(projectId: string) {
    try {
      const connection = await getConnection();

      const project = await connection
        .getRepository(Project)
        .createQueryBuilder('project')
        .where('project.id = :id', { id: projectId })
        .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
        .leftJoinAndSelect('project.phase', 'phase')
        .leftJoinAndSelect('project.standardAgency', 'standardAgency')
        .select(['project', 'phase.id', 'phase.name', 'standardAgency'])
        .getOne();

      return project;
    } catch (error) {
      throw error;
    }
  }
  async getTimezoneByProjectId(projectId: string) {
    try {
      const connection = await getConnection();

      const project = await connection.getRepository(Project).findOne({
        where: { id: projectId, isDelete: false },
      });

      return project?.timezone || 'America/New_York';
    } catch (error) {
      throw error;
    }
  }

  async getAll() {
    try {
      const connection = await getConnection();

      const project = await connection
        .getRepository(Project)
        .createQueryBuilder('project')
        .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
        .leftJoinAndSelect('project.phase', 'phase')
        .select(['project', 'phase.id', 'phase.name'])
        .getMany();

      return project;
    } catch (error) {
      throw error;
    }
  }

  async getSpecAgencyIdByProjectId(projectId: string) {
    try {
      const connection = await getConnection();

      const project = await connection
        .getRepository(Project)
        .createQueryBuilder('project')
        .where('project.id = :id', { id: projectId })
        .andWhere('project.isDelete = :isDeleted', { isDeleted: false })
        .leftJoinAndSelect('project.phase', 'phase')
        .leftJoinAndSelect('project.standardAgency', 'standardAgency')
        .leftJoinAndSelect('project.specAgency', 'specAgency')
        .select(['project', 'phase.id', 'phase.name', 'standardAgency'])
        .getOne();

      const StandardAgency = project?.specAgency?.id;

      return StandardAgency;
    } catch (error) {
      throw error;
    }
  }

  async deleteById(id: string) {
    try {
      return await getManager().getRepository(Project).update({ id }, { isDelete: true });
    } catch (error) {
      throw error;
    }
  }

  async getProjectRoleByUserId(userId: string) {
    try {
      const stakeUserRepository = getManager().getRepository(StakeUser);
      const stakeUserDetials = await stakeUserRepository.find({
        where: { userId },
        relations: ['stakeholder', 'role', 'role.permission'],
      });
      const final: projectRoleObj[] = [];
      if (stakeUserDetials.length > 0) {
        stakeUserDetials.map((value) => {
          const obj: any = {};

          obj.projectId = value.stakeholder?.projectId;

          const permissionName: string[] = [];

          obj.permission = permissionName;
          final.push(obj);
        });
      }

      return final;
    } catch (error) {
      throw error;
    }
  }

  async getUserPermissionByProjectId(userId: string, projectId: string) {
    try {
      const stakeuserRepository = getManager().getRepository(StakeUser);
      const routes: string[] = [];
      const routesAttribute: string[] = [];
      const projectLevelPermission: any[] = [];

      const stakeuserDetails = await stakeuserRepository.findOne({
        where: {
          userId,
          stakeholder: {
            projectId: projectId,
          },
        },
        relations: ['stakeholder', 'role'],
      });

      const roleRoutes = await getManager()
        .getRepository(ProjectRoleRoute)
        .find({ where: { projectRoleId: stakeuserDetails?.roleId }, relations: ['route'] });

      if (roleRoutes) {
        roleRoutes.map((value) => {
          if (value.route && value.route.routeObject) {
            routesAttribute.push(value.route.routeObject);
          }
        });
      }

      return {
        routes,
        routesAttribute,
        projectPermission: projectLevelPermission,
      };
    } catch (error) {
      throw error;
    }
  }

  updateLogoPath = async (logoPath: string, id: string) => {
    try {
      const projectRepository = getManager().getRepository(Project);
      const updatedProject = await projectRepository.update(id, { logoPath });

      return {
        ...updatedProject,
        path: logoPath,
      };
    } catch (error) {
      throw error;
    }
  };

  updateQuickRefGuidePath = async (quickRefGuidPath: string, id: string) => {
    try {
      const projectRepository = getManager().getRepository(Project);
      const updatedProject = await projectRepository.update(id, { quickRefGuidPath });

      return {
        ...updatedProject,
        path: quickRefGuidPath,
      };
    } catch (error) {
      throw error;
    }
  };
  updateScheduleFilePath = async (path: string, id: string) => {
    try {
      const projectRepository = getManager().getRepository(Project);
      const updatedProject = await projectRepository.update(id, { schedulePDFPath: path });

      return {
        ...updatedProject,
        path,
      };
    } catch (error) {
      throw error;
    }
  };

  async findProjectByOrganization(organizationId: string) {
    try {
      const stakeUserRepository = getManager().getRepository(Stakeholder);
      const data = await stakeUserRepository.find({
        where: { organizationId: organizationId, isDelete: false, project: { isDelete: false } },
        relations: ['project', 'orgSturcture'],
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async countProjectByOrganization(organizationId: string) {
    try {
      const stakeUserRepository = getManager().getRepository(Stakeholder);
      const count = await stakeUserRepository.count({
        where: { organizationId: organizationId, isDelete: false, project: { isDelete: false } },
      });
      return count;
    } catch (error) {
      throw error;
    }
  }
}

interface projectRoleObj {
  projectId: string;
  permission: string[];
}

const projectModel = new ProjectModel();
export default projectModel;
