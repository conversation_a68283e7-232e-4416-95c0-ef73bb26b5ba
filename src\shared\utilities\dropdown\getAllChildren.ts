import { DataCaptureElemetRelation } from '@entities/p_meta/DataCaptureElemetRelation';

const getAllChildren = (
  columnName: string,
  relationData: DataCaptureElemetRelation[]
): string[] => {
  try {
    const children = relationData.filter(
      (relation) => relation.routeParams?.split(',').includes(columnName)
    );
    return children.map((child) => child.columnName || '');
  } catch (error) {
    throw error;
  }
};

export default getAllChildren;
