import express, { Router } from 'express';
import MetaDCEConfigurationController from '../../controllers/meta/adminDceConfiguration.controller';
import CrudController from '../../../generic/crudDriver.controller';
import { DCEConfiguration } from '../../../../entities/p_meta/AdminDCEConfiguration';
import { dceConfigEditMiddleware } from 'src/shared/middlewares/dceConfigEdit.middleware';
import { authenticateToken } from 'src/shared/middlewares/auth.middleware';

const router: Router = express.Router();
const controller = new MetaDCEConfigurationController();
const crud = new CrudController<DCEConfiguration>(DCEConfiguration);

router.get('/by/dce/:id', controller.getByDceId);
router.post('/:id', controller.addTableColumnsByDceId);
router.put('/:id', authenticateToken, dceConfigEditMiddleware, (req, res) => crud.update(req, res));
router.patch('/change/order', authenticateToken, controller.changeOrder);

export default router;
