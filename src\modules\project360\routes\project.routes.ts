import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import { Project } from '../../../entities/p_gen/Project';
import CrudController from '../../generic/crudDriver.controller';
import projectController from '../controllers/project.controller';
import {
  sftpUploadImageMiddleware,
  sftpUploadProjectFileMiddleware,
} from '../../../shared/middlewares/uploadFileSftp.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const ProjectDetailController = new CrudController<Project>(Project);

router.put('/:id', authenticateToken, (req, res) => ProjectDetailController.update(req, res));
router.get('/', authenticateToken, projectController.getAll);
router.post('/create', authenticateToken, projectController.addProejctByTransaction);
router.delete('/:id', authenticateToken, projectController.delete);
router.patch('/sftp/:id', authenticateToken, projectController.addSftpTokenInProject);
router.get(
  '/by/organization/:organizationId',
  authenticateToken,
  projectController.findByProjectByOrganization
);
router.get(
  '/count/by/organization/:organizationId',
  authenticateToken,
  projectController.countByProjectByOrganization
);
router.get('/by/user/:id', authenticateToken, projectController.findByUserId);
router.get('/by/:id', authenticateToken, projectController.findById);
router.get('/overview/:id', authenticateToken, projectController.projectOverView);
router.get('/:id', authenticateToken, projectController.findById);
router.get('/container/:name', authenticateToken, projectController.createContainer);
router.get(
  '/dropdown/by/entity/:entity/:id',
  authenticateToken,
  projectController.getDropdownByEntityAndProjectId
);
router.get(
  '/permission/by/user/:userId/:projectId',
  authenticateToken,
  projectController.getUserPermissionByProject
);
router.patch(
  '/upload/logo',
  authenticateToken,
  sftpUploadImageMiddleware,
  projectController.uploadLogo
);

router.patch(
  '/upload/quickrefguide',
  authenticateToken,
  sftpUploadProjectFileMiddleware,
  projectController.uploadQuickRefGuide
);
router.patch(
  '/upload/schedule',
  authenticateToken,
  sftpUploadProjectFileMiddleware,
  projectController.scheduleFile
);

export default router;
