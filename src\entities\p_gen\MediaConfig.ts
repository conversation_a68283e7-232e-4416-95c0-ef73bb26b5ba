import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON>olumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';

@Entity({ schema: 'p_gen' })
export class MediaConfig {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ default : false})
  enableLogo!: boolean;

  @Column({nullable: true})
  logoPosition?: string;

  @Column({ default : false})
  enableCompass!: boolean;

  @Column({nullable: true})
  compassPosition?: string;

  @Column({ default : false})
  enableAnnotation!: boolean;

  @Column({nullable: true})
  annotationPosition?: string;

  @Column({nullable: true})
  fontSize?: number

  @Column({nullable: true})
  charWidthScale?: number

  @Column({nullable: true})
  previewImage?: string

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
