import { TestResultConfiguration } from "@entities/p_meta/TestResultConfiguration";
import { getManager, Repository } from "typeorm";


class TestResultConfigModel {
  private repository: Repository<TestResultConfiguration>;

  constructor() {
    this.repository = getManager().getRepository(TestResultConfiguration);
  }

  findColumnsByDCEId = async (dceId: string): Promise<string[] | null> => {
    try {
      const result = await this.repository.findOne({ where: { dceId, isDelete: false } });
      if (result) {
        return result.resultColumns || null;
      }
      return null;
    } catch (error) {
      throw error;
    }
  }
}


const testResultConfigModel = new TestResultConfigModel();
export default testResultConfigModel;