import { Request, Response } from 'express';
import LayerModel from '../../models/map/layer.model';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import { Layer } from '../../../../entities/p_map/Layer';
import { Legend } from '../../../../entities/p_map/Legend';
import changeOrderSorting from '../../../../shared/utilities/custom/changeOrder';
import shapeLayerFileCheck, {
  getGeojsonType,
} from '../../../../shared/utilities/map/shapeLayerFileCheck';
import GeoJsonConfigurationModel from '../../models/map/geoJSONConfig.model';
import isUUID from '../../../../shared/utilities/custom/isUUID';
import dceModel from '../../models/meta/dce.model';
import axios from 'axios';

class LayerController {
  async getLayerGeoJson(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const { type } = req.query;

      // Handle GET and POST requests differently
      if (req.method === 'GET') {
        // For GET requests, retrieve all data without applying any filters
        const data = await model.getLayerGeoJson(id, (req as any).user, type as string);
        return res.status(200).json({
          isSucceed: true,
          data: data || {},
          msg: 'data found',
        });
      } else {
        // For POST requests, use the existing behavior
        const { filters } = req.body;
        if (filters) {
          const data = await model.getLayerGeoJson(
            id,
            (req as any).user,
            type as string,
            req.body.filters
          );
          return res.status(200).json({
            isSucceed: true,
            data: data || {},
            msg: 'data found',
          });
        } else {
          // if there is no config, get the data of last 2 weeks.
          const data = await model.getLayerGeoJson(id, (req as any).user, type as string, {
            date: {
              startDate: new Date(new Date().getDate() - 30),
              endDate: new Date(),
            },
          });
          return res.status(200).json({
            isSucceed: true,
            data: data || {},
            msg: 'data found',
          });
        }
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async downloadByLayerGeoJson(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const layerData = await model.getById(id);
      const data = await model.getLayerGeoJson(id, (req as any).user);
      if (data && (data as any)?.features) {
        const response = await axios.post(
          `https://func-shapefile-smartinfrahub-com.azurewebsites.net/api/httppythontrigger?code=xcsRTmqO5etiBuxig2YppiA0T_RnfhqAy1vaANgDnT3eAzFuennOzw%3D%3D&layer=${layerData?.name}`,
          { type: 'FeatureCollection', features: (data as any)?.features },
          {
            responseType: 'arraybuffer',
          }
        );
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', `attachment; filename=${layerData?.name}.zip`);

        return res.send(response.data);
      }
      return res.status(404).json({
        isSucceed: true,
        data: [],
        msg: 'No data found in layer',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getById(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const data = await model.getById(id);
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getLayerGeoJsonByEntity(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { entity, id } = req.params;
      const data = await model.getLayerGeoJsonByEnity(entity, id, (req as any).user);
      return res.status(200).json({
        isSucceed: true,
        data: data || {},
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addLayer(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const {
        layerName,
        dceId,
        layerParentId,
        projectId,
        order,
        legendName,
        color,
        size,
        path,
        opacity,
        fill,
        label,
        mapLabelColor,
        enablePopupForMap,
        labelColumn,
        layerType,
        enableIcon,
        enableMobile,
        profileLayer,
        colorFor2d,
        colorFor3d,
        mapLayerZoom,
        enableFilters,
        excludeOnFilter,
      } = req.body;
      const layer: Partial<Layer> = {
        name: layerName,
        layerParentId,
        projectId,
        order,
        profileLayer,
        colorFor2d,
        colorFor3d,
        enableFilters: true ? enableFilters == 'true' : false,
        label: label || '',
        mapLabelColor: mapLabelColor || '',
        excludeOnFilter: true ? excludeOnFilter == 'true' : false,
        enablePopupForMap: true ? enablePopupForMap == 'true' : false,
        labelColumn,
        layerType,
        enableMobile: true ? enableMobile == 'true' : false,
        enableIcon: true ? enableIcon == 'true' : false,
        mapLayerZoom,
        createdBy: `${(req as any).user.name}`,
        updatedBy: `${(req as any).user.name}`,
      };
      const legend: Partial<Legend> = {
        color,
        name: legendName,
        size,
        projectId,
        path: path || '',
        opacity: opacity || '',
        fill: fill || '',
        createdBy: `${(req as any).user.name}`,
        updatedBy: `${(req as any).user.name}`,
        isDefault: true,
      };
      let result;
      if (dceId) {
        layer.dceId = dceId;
        const dceData = await dceModel.findById(dceId);
        // TODO: change Layer type by the dce geo json type
        layer.layerType = dceData?.geoJsonType || 'Point';
        result = await model.addLayerDataWithDce(layer, legend);
      } else if (!dceId && req?.files?.shapefile) {
        const shapefileData = await shapeLayerFileCheck(req?.files?.shapefile);
        const geoJosn = getGeojsonType(shapefileData);
        layer.layerType = geoJosn || '';
        result = await model.addGeomDataWithLayer(shapefileData, projectId, layer, legend);
      } else {
        result = await model.addLayerWithoutDCEorShape(layer, legend);
      }
      return res.status(200).json({
        isSucceed: true,
        data: result,
        msg: 'data added',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async edit(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const {
        layerName,
        dceId,
        layerParentId,
        projectId,
        order,
        legendName,
        color,
        size,
        path,
        opacity,
        fill,
        label,
        profileLabelColorFor2d,
        profileLabelColorFor3d,
        popupTypeFor2d,
        popupTypeFor3d,
        profileLabelZoom,
        layerOpacity,
        mapOutlineColor,
        applyDateFilter,
        mapOutlineWidth,
        enablePopupForMap,
        enableLabelForMap,
        labelColumn,
        layerType,
        geomLayerId,
        legendId,
        enableIcon,
        enableMobile,
        mapLabelZoom,
        enableFilters,
        excludeOnFilter,
        mapLabelColor,
        enableForMap,
        enableFor2d,
        enableFor3d,
        mapLayerZoom,
        enableForProgress,
        displayOnMap,
        displayOn2d,
        displayOn3d,
        displayOnProgress,
        colorFor2d,
        colorFor3d,
        colorForProgress,
        opacityFor2d,
        opacityFor3d,
        opacityForProgress,
        enablePopupFor2d,
        enablePopupFor3d,
        popupType,
        profileLayer,
        filterSortColumn,
      } = req.body;
      const layerId = req.params.id;
      const layer: Partial<Layer> = {
        name: layerName,
        layerParentId,
        projectId,
        order,
        applyDateFilter,
        mapLabelZoom,
        mapLayerZoom: mapLayerZoom,
        excludeOnFilter,
        enableFilters,
        label: label || '',
        mapLabelColor: mapLabelColor || '',
        enablePopupForMap,
        enableLabelForMap,
        labelColumn,
        layerType,
        updatedBy: (req as any)?.user?.name,
        enableMobile: true
          ? enableMobile && enableMobile == 'true'
          : false
          ? enableMobile && enableMobile == 'false'
          : null,
        enableIcon: true
          ? enableIcon && enableIcon == 'true'
          : false
          ? enableIcon && enableIcon == 'false'
          : null,
        layerOpacity: layerOpacity || 0.7,
        mapOutlineColor,
        mapOutlineWidth,
        enableForMap,
        enableFor2d,
        enableFor3d,
        enableForProgress,
        displayOnMap,
        displayOn2d,
        displayOn3d,
        displayOnProgress,
        colorFor2d,
        colorFor3d,
        colorForProgress,
        opacityFor2d,
        opacityFor3d,
        opacityForProgress,
        enablePopupFor2d,
        enablePopupFor3d,
        profileLabelColorFor2d,
        profileLabelColorFor3d,
        popupTypeFor2d,
        popupTypeFor3d,
        profileLabelZoom,
        popupType: popupType || '',
        profileLayer,
        filterSortColumn,
      };
      const legend: Partial<Legend> = {
        color,
        name: legendName,
        size,
        projectId,
        path: path || '',
        opacity: opacity || '',
        fill: fill || '',
        updatedBy: (req as any)?.user?.name,
      };
      let result;
      if (dceId) {
        layer.dceId = dceId;
        result = await model.editLayerDataWithDce(
          layerId,
          legendId,
          removeNullValues(layer),
          removeNullValues(legend)
        );
      } else if (!dceId && req?.files?.shapefile) {
        const shapefileData = await shapeLayerFileCheck(req?.files?.shapefile);
        result = await model.editGeomDataWithLayer(
          shapefileData,
          removeNullValues(layer),
          removeNullValues(legend),
          layerId,
          geomLayerId,
          legendId
        );
      } else {
        result = await model.editLayerDataWithDce(
          layerId,
          legendId,
          removeNullValues(layer),
          removeNullValues(legend)
        );
      }
      return res.status(200).json({
        isSucceed: true,
        data: {...result.updatedLayer, legend: {...result.updatedLegend}},
        msg: 'data added',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async changeOrder(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { oldIndex, targetIndex, layerParentId } = req.body;
      const data = await model.getByParentLayerId(layerParentId);
      const newOrderArray = changeOrderSorting(data, oldIndex, targetIndex);
      const orderChange = newOrderArray.map((value, index) => {
        return { id: value.id, order: index + 1 };
      });
      await model.changeOrder(orderChange);
      return res.json({ isSucceed: true, data: [], msg: 'Order Changed' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getByProjectId(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const { type } = req.query;
      let data: Layer[] = [];
      const geoJsonConfigurationModel = new GeoJsonConfigurationModel();
      const layerModel = new LayerModel();

      if (type && type === 'mobile') {
        const dataFromDB = await model.getByProjectIdForMobile(id);
        for (const element of dataFromDB) {
          try {
            if (element.id) {
              try {
                const columns = await layerModel.getColumnName(element.id);
                const data = await geoJsonConfigurationModel.getData(
                  columns || [],
                  id,
                  element.id,
                  (req as any).user.name
                );
                element.popupConfig = data;
              } catch (error) {}
            }
            if (element.label) {
              const isUU = isUUID(element.label);
              if (isUU) {
                const lableColumnName = await geoJsonConfigurationModel.getByIdLayerLableConversion(
                  element.label
                );
                if (lableColumnName.name) {
                  element.label = lableColumnName.name;
                }
              }
            }
            data.push(element);
          } catch (error) {
            throw error;
          }
        }
      } else {
        data = await model.getByProjectId(id);
      } 
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getByProjectIdAndLayerKeys(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const { layerKeys } = req.query;
      const keysArray = layerKeys ? (layerKeys as string).split(',') : [];
      const data: Layer[] = await model.getByProjectIdAndLayerKeys(id, keysArray);
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getByColumnNamesById(req: Request, res: Response) {
    try {
      const model = new LayerModel();
      const { id } = req.params;
      const data = await model.getColumnName(id);
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

function removeNullValues(obj: any) {
  for (const key in obj) {
    if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
      delete obj[key];
    }
  }
  return obj;
}

export default LayerController;
