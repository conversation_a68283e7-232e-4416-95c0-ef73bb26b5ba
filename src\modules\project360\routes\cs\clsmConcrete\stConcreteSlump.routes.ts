import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StConcreteSlump } from '../../../../../entities/p_cs/StConcreteSlump';

const router: Router = express.Router();

const GenricController = new CrudController<StConcreteSlump>(StConcreteSlump);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'slumpTest')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'slumpTest')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'slumpTest')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'slumpTest')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'slumpTest'));
router.get('/:id', authenticateToken, GenricController.findById);

router.put('/:id', authenticateToken, (req, res) => GenricController.update(req, res, 'slumpTest'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'slumpTest')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'slumpTest')
);

export default router;
