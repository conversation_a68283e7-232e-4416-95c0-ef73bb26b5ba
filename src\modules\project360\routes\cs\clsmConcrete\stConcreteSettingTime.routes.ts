import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StConcreteSettingTime } from '../../../../../entities/p_cs/StConcreteSettingTime';

const router: Router = express.Router();

const GenricController = new CrudController<StConcreteSettingTime>(StConcreteSettingTime);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'concreteSettingTime')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'concreteSettingTime')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'concreteSettingTime')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'concreteSettingTime')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'concreteSettingTime')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'concreteSettingTime')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'concreteSettingTime')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'concreteSettingTime')
);

export default router;
