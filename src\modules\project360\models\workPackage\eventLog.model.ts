import { getManager } from 'typeorm';
import { EventLog } from '../../../../entities/p_gen/EventLog';
import { WorkPackageActivity } from '../../../../entities/p_gen/WorkPackageActivity';
import { WorkPackage } from '../../../../entities/p_gen/WorkPackage';

class EventLogModel {
  async getByWorkPackageActivityIKd(workPackageActivityId: string) {
    try {
      const repository = getManager().getRepository(EventLog);
      return await repository.find({
        where: { workPackageActivityId, isDelete: false },
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      throw error;
    }
  }
  async getByUser(userId: string, projectId?: string) {
    try {
      const repository = getManager().getRepository(EventLog);
      if (projectId) {
        return await repository.find({
          where: { projectId, userId, isDelete: false },
          order: { createdAt: 'DESC' },
        });
      }

      return await repository.find({
        where: { userId, isDelete: false },
        order: { createdAt: 'DESC' },
      });
    } catch (error) {
      throw error;
    }
  }
  async addEventLog(log: EventLog, newLog: boolean) {
    try {
      const entityManager = getManager();
      const out = await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const addData = await transactionalEntityManager.save(EventLog, log);
          if (newLog && log.workPackageActivityId) {
            const workPackageActivity = await transactionalEntityManager.findOne(
              WorkPackageActivity,
              { where: { id: log.workPackageActivityId } }
            );
            const workPackageId = workPackageActivity?.workPackageId;
            if (workPackageId) {
              await transactionalEntityManager.update(
                WorkPackage,
                { id: workPackageId },
                { status: 'Pending' }
              );
            }
          }
          return addData;
        } catch (error) {
          throw error;
        }
      });
      return out;
    } catch (error) {
      throw error;
    }
  }
}

export default EventLogModel;
