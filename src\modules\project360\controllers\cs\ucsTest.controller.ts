// import { Request, Response } from 'express';
// import { ucsTestCalculation } from '../../../../shared/utilities/cs/testCalculationFunctions';
// import ucsTestModel from '../../models/cs/ucsTest.model';
// import { extractDataForUcs } from '../../../../shared/utilities/cs/ucsSASFunction';
// import ucsSASModel from '../../models/cs/ucsSAS.model';

// class UCSTestController {
//   constructor() {}

//   // find by id
//   async findById(req: Request, res: Response) {
//     try {
//       // getting the data from database with the given id
//       const ucsTestData = await ucsTestModel.findById(req.params.id);
//       // checking if data is found with the id
//       if (ucsTestData) {
//         // if true data will send as response
//         const message = req.__('DataFoundMessage');
//         return res.status(200).json({
//           isSucceed: true,
//           data: ucsTestData,
//           msg: message,
//         });
//       } else {
//         const message = req.__('DataNotFoundMessage');
//         // if false send error as response
//         return res.status(200).json({
//           isSucceed: true,
//           data: [],
//           msg: message,
//         });
//       }
//     } catch (error) {
//       // error response
//       return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
//     }
//   }

//   async create(req: Request, res: Response) {
//     try {
//       const {
//         specimenDiameter,
//         dryWeight,
//         specimenHeight,
//         waterContent,
//         wetWeight,
//         deformationLength,
//         failureLoad,
//       } = req.body;
//       if ((req as any).user) {
//         req.body.createdBy = (req as any).user.name;
//         req.body.updatedBy = (req as any).user.name;
//       }

//       if (
//         !specimenDiameter ||
//         !dryWeight ||
//         !specimenHeight ||
//         !waterContent ||
//         !wetWeight ||
//         !deformationLength ||
//         !failureLoad
//       ) {
//         const message = req.__('InvalidInputDataError');
//         return res.status(500).json({ isSucceed: false, data: [], msg: message });
//       }

//       const area = ucsTestCalculation.CalculateAreaByDiameter(specimenDiameter);
//       req.body.area = area;

//       const initialDryDensity = ucsTestCalculation.CalculateInitialDryDensity(
//         dryWeight,
//         specimenDiameter,
//         specimenHeight
//       );
//       req.body.initialDryDensity = initialDryDensity;

//       if (!waterContent) {
//         const waterContent = ucsTestCalculation.CalculateWaterContent(wetWeight, dryWeight);
//         req.body.waterContent = waterContent;
//       }

//       // const failureStrain = ucsTestCalculation.CalculateFailureStrain(
//       //   specimenHeight,
//       //   deformationLength
//       // );
//       // req.body.failureStrain = failureStrain;

//       // const unconfinedCompressiveStrength =
//       //   ucsTestCalculation.CalculateUnconfinedCompressiveStrength(failureLoad, area);
//       // req.body.unconfinedCompressiveStrength = unconfinedCompressiveStrength;

//       // const shearStrength = ucsTestCalculation.CalculateShearStrength(
//       //   unconfinedCompressiveStrength
//       // );
//       // req.body.shearStrength = shearStrength;

//       // getting the data from database with the given id
//       const hydraulicConductivityData = await ucsTestModel.add(req.body);
//       // checking if data is found with the id
//       // if true data will send as response
//       const message = req.__('DataInputSuccess');
//       return res.status(200).json({
//         isSucceed: true,
//         data: hydraulicConductivityData,
//         msg: message,
//       });
//     } catch (error) {

//       const message = req.__('DataInputFail');
//       // error response
//       return res
//         .status(500)
//         .json({ isSucceed: false, data: [], msg: (error as any).message || message });
//     }
//   }

//   async addFromImport(req: Request, res: Response) {
//     try {
//       const file = req.files?.file;
//       const ucsTestId = req.body.ucsTestId;
//       const ucsData = await ucsTestModel.findById(ucsTestId);
//       if (file) {
//         const fileContent = (file as any).data.toString('utf-8');
//         const lines: any[] = fileContent.split('\n');
//         const dataStartIndex = lines.findIndex((line) => line.startsWith('Time (secs)'));

//         // Extract data for each line starting from dataStartIndex
//         const testData = lines.slice(dataStartIndex + 1).map(extractDataForUcs);
//         // Assuming this code is inside an asynchronous function
//         const promises = testData.map(async (value) => {
//           if (!isNaN(value.time) && !isNaN(value.displacement) && !isNaN(value.load)) {
//             if ((req as any).user) {
//               (value as any).createdBy = (req as any).user.name;
//               (value as any).updatedBy = (req as any).user.name;
//             }
//             value.ucsTestId = ucsTestId;
//             await ucsSASModel.add(value as any);
//           }
//         });

//         const maxLoadObject = testData.reduce((maxLoad, currentObject) => {
//           return currentObject.load > maxLoad.load ? currentObject : maxLoad;
//         }, testData[0]);
//         const newUCSTest: any = {};
//         newUCSTest.failureTime = maxLoadObject.time;
//         newUCSTest.deformationLength = maxLoadObject.displacement;
//         newUCSTest.failureLoad = maxLoadObject.load;
//         if (ucsData && ucsData.specimenHeight) {
//           const failureStrain = ucsTestCalculation.CalculateFailureStrain(
//             ucsData.specimenHeight,
//             maxLoadObject.displacement
//           );
//           newUCSTest.failureStrain = failureStrain;
//         }
//         if (ucsData && ucsData.specimenDiameter && ucsData.specimenHeight) {
//           const unconfinedCompressiveStrength =
//             ucsTestCalculation.CalculateUnconfinedCompressiveStrength(
//               maxLoadObject.load,
//               ucsData.specimenDiameter,
//               ucsData.specimenHeight,
//               maxLoadObject.displacement
//             );
//           newUCSTest.unconfinedCompressiveStrength = unconfinedCompressiveStrength;
//         }

//         if (ucsData && ucsData.specimenDiameter && ucsData.specimenHeight) {
//           const unconfinedCompressiveStrength =
//             ucsTestCalculation.CalculateUnconfinedCompressiveStrength(
//               maxLoadObject.load,
//               ucsData.specimenDiameter,
//               ucsData.specimenHeight,
//               maxLoadObject.displacement
//             );
//           newUCSTest.unconfinedCompressiveStrength = unconfinedCompressiveStrength;
//         }

//         if (newUCSTest.unconfinedCompressiveStrength) {
//           const shearStrength = ucsTestCalculation.CalculateShearStrength(
//             newUCSTest.unconfinedCompressiveStrength
//           );
//           newUCSTest.shearStrength = shearStrength;
//         }

//         await ucsTestModel.updateFailLoad(ucsTestId, newUCSTest);

//         // Wait for all asynchronous operations to complete
//         await Promise.all(promises);
//         const message = req.__('DataInputSuccess');
//         return res.status(200).json({
//           isSucceed: true,
//           data: [],
//           msg: message,
//         });
//       } else {
//         const message = req.__('InvalidInputDataError');
//         return res.status(400).json({ isSucceed: false, data: [], msg: message });
//       }
//     } catch (error) {
//       const message = req.__('DataInputFail');
//       // error response
//       return res
//         .status(500)
//         .json({ isSucceed: false, data: [], msg: (error as any).message || message });
//     }
//   }

//   async enterManually(req: Request, res: Response) {
//     try {
//       const { failureLoad, deformationLength, failureTime } = req.body;
//       const ucsTestId = req.params.id;
//       const ucsData = await ucsTestModel.findById(ucsTestId);
//       const newUCSTest: any = {};
//       newUCSTest.failureTime = failureTime;
//       newUCSTest.deformationLength = deformationLength;
//       newUCSTest.failureLoad = failureLoad;
//       if (ucsData && ucsData.specimenHeight) {
//         const failureStrain = ucsTestCalculation.CalculateFailureStrain(
//           ucsData.specimenHeight,
//           deformationLength
//         );
//         newUCSTest.failureStrain = failureStrain;
//       }
//       if (ucsData && ucsData.specimenDiameter && ucsData.specimenHeight) {
//         const unconfinedCompressiveStrength =
//           ucsTestCalculation.CalculateUnconfinedCompressiveStrength(
//             failureLoad,
//             ucsData.specimenDiameter,
//             ucsData.specimenHeight,
//             deformationLength
//           );
//         newUCSTest.unconfinedCompressiveStrength = unconfinedCompressiveStrength;
//       }

//       if (ucsData && ucsData.specimenDiameter && ucsData.specimenHeight) {
//         const unconfinedCompressiveStrength =
//           ucsTestCalculation.CalculateUnconfinedCompressiveStrength(
//             failureLoad,
//             ucsData.specimenDiameter,
//             ucsData.specimenHeight,
//             deformationLength
//           );
//         newUCSTest.unconfinedCompressiveStrength = unconfinedCompressiveStrength;
//       }

//       if (newUCSTest.unconfinedCompressiveStrength) {
//         const shearStrength = ucsTestCalculation.CalculateShearStrength(
//           newUCSTest.unconfinedCompressiveStrength
//         );
//         newUCSTest.shearStrength = shearStrength;
//       }

//       await ucsTestModel.updateFailLoad(ucsTestId, newUCSTest);
//       const message = req.__('DataInputSuccess');
//       return res.status(200).json({
//         isSucceed: true,
//         data: [],
//         msg: message,
//       });
//     } catch (error) {
//       const message = req.__('DataInputFail');
//       // error response
//       return res
//         .status(500)
//         .json({ isSucceed: false, data: [], msg: (error as any).message || message });
//     }
//   }
// }

// const ucsTestController = new UCSTestController();
// export default ucsTestController;
