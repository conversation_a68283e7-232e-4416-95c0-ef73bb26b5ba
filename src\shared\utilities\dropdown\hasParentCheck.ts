const hasParentCheck = (routeParameter: string[]): { hasParent: boolean; routes: string } => {
  const routeParameterWithOutProject = routeParameter.filter((route) => route !== 'projectId');
  if (
    routeParameterWithOutProject.length > 0 &&
    routeParameterWithOutProject?.[0] &&
    routeParameterWithOutProject?.[0] !== ''
  ) {
    return {
      hasParent: true,
      routes: routeParameterWithOutProject[0],
    };
  }
  return {
    hasParent: false,
    routes: '',
  };
};

export default hasParentCheck;
