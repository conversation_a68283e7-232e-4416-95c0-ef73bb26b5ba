import { getManager, Not } from 'typeorm';
import { ProjectRole } from '../../../../entities/p_auth/Role';
import { ProjectRoleRoute } from '../../../../entities/p_auth/projectRoleRoute';
import { Route } from '../../../../entities/p_auth/Route';
import { Stakeholder } from '../../../../entities/p_auth/Stakeholder';
import RoleDataAccessModel from '../auth/roleDataAccess.model';
import { checkIsSuperAdmin } from '../../../../shared/server/platformApi/role';
import purposeModel from '../meta/purpose.model';
import { StakeUser } from '../../../../entities/p_auth/StakeUser';

class ProjectRoleModel {
  constructor() {}

  async addPermissionToRole(roleId: string, permission: any[]) {
    try {
      const finalOut: any[] = [];
      const roleRepository = getManager().getRepository(ProjectRole);
      const role = await roleRepository.findOne({
        where: { id: roleId, isDelete: false },
        // relations: [''],
      });
      if (role) {
        const entityManager = getManager();
        await entityManager.transaction(async (transactionalEntityManager) => {
          await Promise.all(
            permission.map(async (value) => {
              const permissionStringArray: string[] = ['0', '0', '0', '0', '0'];
              if (value.list) {
                if (value.list.add) {
                  permissionStringArray[0] = '1';
                }
                if (value.list.view) {
                  permissionStringArray[1] = '1';
                }
                if (value.list.delete) {
                  permissionStringArray[2] = '1';
                }
                if (value.list.edit) {
                  permissionStringArray[3] = '1';
                }
              }
              const finalPermission = permissionStringArray.join('');
              const entityToUpdate = await transactionalEntityManager.findOne(ProjectRoleRoute, {
                where: { projectRoleId: roleId, routeId: value.routeId },
              });
              if (entityToUpdate) {
                finalOut.push(entityToUpdate);
                // Update the properties of the entity
                entityToUpdate.permission = finalPermission;

                // Save the updated entity
                await transactionalEntityManager.save(entityToUpdate);
              } else {
                await transactionalEntityManager.save(ProjectRoleRoute, {
                  projectRoleId: roleId,
                  routeId: value.routeId,
                  permission: finalPermission,
                });
              }
            })
          );
        });
      }
      return finalOut;
    } catch (error) {
      throw error;
    }
  }

  async getPermissionByRole(roleId: string) {
    try {
      // const stakeholderRoleRepository = getManager().getRepository(StakeholderRole);
      const permissionRepository = getManager().getRepository(ProjectRoleRoute);
      const routeRepository = getManager().getRepository(Route);

      const rolePermission = await permissionRepository.find({
        where: { projectRoleId: roleId },
        relations: ['route'],
      });

      let final: PermissionComplete[] = [];
      const routes = await routeRepository.find({
        where: { isDelete: false, isActive: true, type: 'Project' },
      });
      routes.map((route) => {
        const parents = route.routeObject?.split('.') || [];

        if (parents[parents.length - 1] != 'root') {
          parents.map((value, index) => {
            value = convertToTitleCase(value);
            if (index !== parents.length - 1) {
              const parentAlreadyThere = final.find((ob) => ob.parent == value);

              if (!parentAlreadyThere) {
                if (index == 0) {
                  const newParent: PermissionComplete = {
                    parent: value,
                    children: [],
                  };
                  final.push(newParent);
                } else {
                  if (!isParentExists(final, value)) {
                    const child: PermissionChild = {
                      parent: value,
                      children: [],
                    };

                    final =
                      addChildToParent(final, convertToTitleCase(parents[index - 1]), child) || [];
                  }
                }
              }
            } else {
              const permissionIsThere = rolePermission?.find(
                (item) => item?.route?.id == route?.id
              );
              const obj = { add: false, view: false, delete: false, edit: false };
              if (permissionIsThere) {
                const permission = permissionIsThere?.permission;
                if (permission) {
                  const regex = /^[01]+$/;
                  if (regex.test(permission)) {
                    if (permission[0] == '1') {
                      obj.add = true;
                    }
                    if (permission[1] == '1') {
                      obj.view = true;
                    }
                    if (permission[2] == '1') {
                      obj.delete = true;
                    }
                    if (permission[3] == '1') {
                      obj.edit = true;
                    }
                  }
                }
              }
              const child: PermissionChild = {
                name: value,
                list: obj,
                routeId: route.id,
              };

              final = addChildToParent(final, convertToTitleCase(parents[index - 1]), child);
            }
          });
        }
      });

      return final;
    } catch (error) {
      throw error;
    }
  }

  async getPermissionByUser(userId: string) {
    try {
      const stakeholderRepo = getManager().getRepository(Stakeholder);

      const stakeholderWithUser = await stakeholderRepo.find({
        where: { stakeUsers: { userId, isDelete: false } },
        relations: ['project', 'stakeUsers', 'stakeUsers.role'],
      });

      const userProjectRoleIds = stakeholderWithUser.map((value) => {
        if (value.projectId) {
          const object: { projectId: string; roleId: string[] } = {
            projectId: value.projectId,
            roleId: [],
          };
          value.stakeUsers?.map((item) => {
            if (item.roleId) {
              object.roleId.push(item.roleId);
            }
          });

          return object;
        }
      });
      return userProjectRoleIds;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectId(projectId: string) {
    try {
      const data = await getManager()
        .getRepository(ProjectRole)
        .find({ where: { projectId, isDelete: false }, order: { updatedAt: 'DESC' } });
      return data;
    } catch (error) {
      throw error;
    }
  }
  async getById(roleId: string) {
    try {
      const data = await getManager()
        .getRepository(ProjectRole)
        .find({
          where: { id: roleId, isDelete: false },
          relations: ['dataAccess', 'dataAccess.purpose'],
        });
      return data;
    } catch (error) {
      throw error;
    }
  }

  getByProjectIdAndUser = async (projectId: string, userId: string) => {
    try {
      const stakeholderData = await getManager()
        .getRepository(Stakeholder)
        .findOne({
          where: { projectId, isDelete: false, stakeUsers: { userId, isDelete: false } },
          relations: [
            'stakeUsers',
            'stakeUsers.role',
            'stakeUsers.role.dataAccess',
            'stakeUsers.role.dataAccess.purpose',
          ],
        });

      if (stakeholderData && stakeholderData.stakeUsers && stakeholderData.stakeUsers.length > 0) {
        const qaqc = stakeholderData?.stakeUsers[0].role?.dataAccess?.map(
          (value) => value.access !== 'No Access'
        );
        if (stakeholderData?.stakeUsers[0].role) {
          (stakeholderData?.stakeUsers[0].role as any).qaqc = qaqc;
        }
        return stakeholderData?.stakeUsers[0].role;
      }
    } catch (error) {
      throw error;
    }
  };

  getByAccessPurposeIds = async (
    projectId: string,
    userId: string,
    access: string,
    view?: string
  ) => {
    try {
      const RoleData = await this.getByProjectIdAndUser(projectId, userId);
      const isAdmin = await checkIsSuperAdmin(userId);
      if (isAdmin) {
        const allPurpose = await purposeModel.getAll();
        if (allPurpose && allPurpose.length > 0) {
          return allPurpose.map((value) => value.id);
        }
      }
      if (RoleData) {
        const dataAccessModel = new RoleDataAccessModel();
        const accessDetails = await dataAccessModel.getByRoleId(RoleData.id);
        if (view && accessDetails && accessDetails.length > 0) {
          return accessDetails
            .filter((value) => value.access != 'No Access')
            .map((value) => value.purposeId);
        }
        if (accessDetails && accessDetails.length > 0) {
          return accessDetails
            .filter((value) => value.access === access)
            .map((value) => value.purposeId);
        }
      }
      return [];
    } catch (error) {
      throw error;
    }
  };

  async filterObjectsByName(objects: any[], keyword: string) {
    return objects.filter((obj) => obj.name.toLowerCase().includes(keyword.toLowerCase()));
  }

  async findById(roleId: string) {
    try {
      const data = await getManager()
        .getRepository(ProjectRole)
        .findOne({ where: { id: roleId, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  findRoleByUserAndProject = async (projectId: string, userId: string) => {
    try {
      const stakeholder = await getManager()
        .getRepository(Stakeholder)
        .findOne({
          where: { projectId, isDelete: false, stakeUsers: { userId, isDelete: false } },
          relations: ['stakeUsers'],
        });
      const stakeuser = stakeholder?.stakeUsers?.find((value) => value.userId === userId);
      const role = await getManager()
        .getRepository(ProjectRole)
        .findOne({
          where: { projectId, id: stakeuser?.roleId },
          relations: ['dataAccess', 'dataAccess.purpose'],
        });

      return role;
    } catch (error) {
      throw error;
    }
  };

  async findByProjectIdAndUserId(projectId: string, userId: string) {
    try {
      const stakeholderRepo = getManager().getRepository(Stakeholder);
      const roleRepo = getManager().getRepository(ProjectRole);
      await roleRepo.findOne({});
      const projectRoleRouteRepo = getManager().getRepository(ProjectRoleRoute);

      const stakeholderInProjectWithUser = await stakeholderRepo.find({
        where: { projectId, stakeUsers: { userId } },
        relations: ['stakeUser'],
      });
      const roleIds = stakeholderInProjectWithUser.flatMap(
        (value) =>
          value.stakeUsers
            ?.map((item) => item.roleId?.toString())
            .filter((roleId) => roleId !== undefined) ?? []
      );
      const routes: string[] = [];
      await Promise.all(
        roleIds.map(async (value) => {
          const routeAndPermission = await projectRoleRouteRepo.find({
            where: { projectRoleId: value, isDelete: false },
            relations: ['route'],
          });
          if (routeAndPermission) {
            routeAndPermission.map((item) => {
              if (item.route?.routeObject) {
                if (!routes.includes(item.route?.routeObject)) {
                  routes.push(item.route.routeObject);
                }
              }
            });
          }
        })
      );
    } catch (error) {
      throw error;
    }
  }

  async findByRotuesUserId(userId: string) {
    try {
      const stakeholderRepo = getManager().getRepository(Stakeholder);
      const projectRoleRouteRepo = getManager().getRepository(ProjectRoleRoute);
      const routesAttributeWithCrud: { route: string; hiddenIds: string[] }[] = [];
      const stakeholderWithUser = await stakeholderRepo.find({
        where: { isDelete: false, stakeUsers: { userId, isDelete: false } },
        relations: ['stakeUsers'],
      });

      const roleIds = stakeholderWithUser.flatMap(
        (value) =>
          value.stakeUsers
            ?.map((item) => item.roleId?.toString())
            .filter((roleId) => roleId !== undefined) ?? []
      );

      const routes: string[] = [];
      await Promise.all(
        roleIds.map(async (value) => {
          const routeAndPermission = await projectRoleRouteRepo.find({
            where: { projectRoleId: value },
            relations: ['route'],
          });
          if (routeAndPermission) {
            routeAndPermission.map((item) => {
              if (item.route?.routeObject) {
                if (!routes.includes(item.route?.routeObject)) {
                  routes.push(item.route?.routeObject);
                  const permissions: string[] = [];
                  if (item.permission && !item.route?.routeObject?.includes('root')) {
                    const permission = item?.permission;
                    if (permission) {
                      const regex = /^[01]+$/;
                      if (regex.test(permission)) {
                        if (permission[0] == '0') {
                          // obj.add = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-add`);
                        }
                        if (permission[1] == '0') {
                          // obj.view = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-view`);
                        }
                        if (permission[2] == '0') {
                          // obj.delete = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-delete`);
                        }
                        if (permission[3] == '0') {
                          // obj.edit = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-edit`);
                        }
                      }
                    }
                    const crudObj = {
                      route: item.route?.route || '',
                      hiddenIds: permissions,
                    };
                    routesAttributeWithCrud.push(crudObj);
                  }
                }
              }
            });
          }
        })
      );
      return {
        routesAttribute: routes,
        routeWithCrud: routesAttributeWithCrud,
      };
    } catch (error) {
      throw error;
    }
  }

  async findByOnlyRotuesUserId(userId: string, projectId: string) {
    try {
      const stakeholderRepo = getManager().getRepository(Stakeholder);
      const stakeUserRepo = getManager().getRepository(StakeUser);
      const projectRoleRouteRepo = getManager().getRepository(ProjectRoleRoute);
      const stakeholderWithUser = await stakeholderRepo.findOne({
        where: { isDelete: false, projectId, stakeUsers: { userId, isDelete: false } },
      });

      if (!stakeholderWithUser) {
        throw new Error('User not part of the given project');
      }

      const stakeuser = await stakeUserRepo.findOne({
        where: { stakeholderId: stakeholderWithUser?.id, userId, isDelete: false },
      });

      if (!stakeuser || !stakeuser?.roleId) {
        throw new Error('User role not found');
      }

      const roleId = stakeuser?.roleId;

      const routes: Route[] = [];
      const routeAndPermission = await projectRoleRouteRepo.find({
        where: { projectRoleId: roleId },
        relations: ['route'],
      });
      if (routeAndPermission) {
        routeAndPermission.map((item) => {
          if (item.route) {
            routes.push(item.route);
          }
        });
      }
      return routes;
    } catch (error) {
      throw error;
    }
  }
  async findByRotuesUserIdWithProjectId(userId: string, projectId: string) {
    try {
      const stakeholderRepo = getManager().getRepository(Stakeholder);
      const projectRoleRouteRepo = getManager().getRepository(ProjectRoleRoute);
      const routesAttributeWithCrud: { route: string; hiddenIds: string[] }[] = [];
      const stakeholderWithUser = await stakeholderRepo.find({
        where: { projectId, isDelete: false, stakeUsers: { userId, isDelete: false } },
        relations: ['stakeUsers'],
      });

      const roleIds = stakeholderWithUser.flatMap(
        (value) =>
          value.stakeUsers
            ?.map((item) => item.roleId?.toString())
            .filter((roleId) => roleId !== undefined) ?? []
      );

      const routes: string[] = [];
      await Promise.all(
        roleIds.map(async (value) => {
          const routeAndPermission = await projectRoleRouteRepo.find({
            where: { projectRoleId: value, permission: Not('00000') },
            relations: ['route'],
          });
          if (routeAndPermission) {
            routeAndPermission.map((item) => {
              if (item.route?.routeObject) {
                if (!routes.includes(item.route?.routeObject)) {
                  routes.push(item.route?.routeObject);
                  const permissions: string[] = [];
                  if (item.permission && !item.route?.routeObject?.includes('root')) {
                    const permission = item?.permission;
                    if (permission) {
                      const regex = /^[01]+$/;
                      if (regex.test(permission)) {
                        if (permission[0] == '0') {
                          // obj.add = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-add`);
                        }
                        if (permission[1] == '0') {
                          // obj.view = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-view`);
                        }
                        if (permission[2] == '0') {
                          // obj.delete = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-delete`);
                        }
                        if (permission[3] == '0') {
                          // obj.edit = true;
                          permissions.push(`${item.route?.route?.split('/').pop()}-edit`);
                        }
                      }
                    }
                    const crudObj = {
                      route: item.route?.route || '',
                      hiddenIds: permissions,
                    };
                    routesAttributeWithCrud.push(crudObj);
                  }
                }
              }
            });
          }
        })
      );
      return {
        routesAttribute: routes,
        routeWithCrud: routesAttributeWithCrud,
      };
    } catch (error) {
      throw error;
    }
  }
}

interface PermissionList {
  add: boolean;
  view: boolean;
  delete: boolean;
  edit: boolean;
}

interface PermissionChild {
  routeId?: string;
  parent?: string;
  name?: string;
  list?: PermissionList;
  children?: PermissionChild[];
}

interface PermissionComplete {
  parent: string;
  children: PermissionChild[];
}

function addChildToParent(
  permissionArray: PermissionComplete[],
  parentName: string,
  childObject: PermissionChild
) {
  for (let i = 0; i < permissionArray.length; i++) {
    if (permissionArray[i].parent === parentName) {
      if (!permissionArray[i].children) {
        permissionArray[i].children = [];
      }
      permissionArray[i].children.push(childObject);
    } else if (permissionArray[i].children) {
      addChildToParent(permissionArray[i].children as any, parentName, childObject);
    }
  }
  return permissionArray;
}

function isParentExists(
  permissionArray: (PermissionChild | PermissionComplete)[],
  parentName: string
): boolean {
  for (let i = 0; i < permissionArray.length; i++) {
    if ('parent' in permissionArray[i] && permissionArray[i].parent === parentName) {
      return true;
    } else if (
      'children' in permissionArray[i] &&
      permissionArray[i].children &&
      isParentExists(permissionArray[i].children || [], parentName)
    ) {
      return true;
    }
  }
  return false;
}

function convertToTitleCase(inputString: string) {
  const words = inputString.split('_');
  const titleCaseWords = words.map(function (word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
  });
  return titleCaseWords.join(' ');
}

const projectRoleModel = new ProjectRoleModel();
export default projectRoleModel;
