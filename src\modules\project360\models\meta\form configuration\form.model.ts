import { getManager, getRepository, <PERSON><PERSON><PERSON>, Like } from 'typeorm';
import { DefaultForm } from '../../../../../entities/p_meta/Form';
import { EntityListInterface, entityList } from '../../../../../shared/utilities/entity/entityList';
import dceModel from '../dce.model';
import convertTableToSurveyJSON, {
  convertTableToPanel,
} from '../../../../../shared/utilities/surveyJs/convertTableMetaDataToSurveyJsJson';
import { checkChoiceURl } from '../../../../../shared/utilities/surveyJs/checkChoiceUrl';
import DCERelationModel from '../dceRelation.model';
import SubDCEModel from '../SubDce.model';

class FormModel {
  private repo = getManager().getRepository(DefaultForm);

  findByDceId = async (dceId: string) => {
    try {
      const dceRelaitonModel = new DCERelationModel();

      const formData = await this.repo.find({
        where: { dceId, isDelete: false },
        order: { createdAt: 'DESC' },
      });
      const finalForm = [];
      if (formData && formData.length > 0) {
        for (const element of formData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(element?.form, relationData, dceId);
          element.form = newJson;
          if (element.mode) {
            (element.mode as any) = element.mode?.split(',');
          }
          finalForm.push(element);
        }
      }
      return finalForm;
    } catch (error) {
      throw error;
    }
  };
  findById = async (id: string) => {
    try {
      const dceRelaitonModel = new DCERelationModel();

      const formData = await this.repo.findOne({
        where: { id, isDelete: false },
      });
      if (formData) {
        const relationData = await dceRelaitonModel.getByDceId(formData.dceId || '');
        const newJson = await checkChoiceURl(formData?.form, relationData, formData.dceId || '');
        formData.form = newJson;
      }
      return formData;
    } catch (error) {
      throw error;
    }
  };
  editModeByDce = async (dceId: string, mode: { formId: string; mode: string[] }[]) => {
    try {
      const entityManager = getManager();
      await entityManager.transaction(async (transactionalEntityManager) => {
        await transactionalEntityManager.update(DefaultForm, { dceId }, { mode: null });
        for (const element of mode) {
          const stringMode = element.mode.join(',');
          await transactionalEntityManager.update(DefaultForm, element.formId, {
            mode: stringMode,
          });
        }
      });
    } catch (error) {
      throw error;
    }
  };

  setAsDefault = async (mode: string, dceId: string, formId: string) => {
    try {
      await this.repo.update(
        { dceId, mode: Like(`%${mode}%`), isDelete: false },
        { default: false }
      );
      await this.repo.update(
        { id: formId, mode: Like(`%${mode}%`), isDelete: false },
        { default: true }
      );
      return true;
    } catch (error) {
      throw error;
    }
  };
  getJSONByDCE = async (dceId: string, mode?: string) => {
    try {
      let data = await this.repo.findOne({
        where: { dceId, isDelete: false },
      });
      if (mode) {
        data = await this.repo.findOne({
          where: { dceId, isDelete: false, defaultMode: Like(`%${mode}%`) },
        });
      }
      const dceRelaitonModel = new DCERelationModel();
      if (!data || !data.form) {
        const jsonFromDB = await this.createByDce(dceId);
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(jsonFromDB, relationData, dceId);
        return newJson;
      }
      const relationData = await dceRelaitonModel.getByDceId(dceId);
      const newJson = await checkChoiceURl(data?.form, relationData, dceId);

      return newJson;
    } catch (error) {
      throw error;
    }
  };

  getCreateAdminForm = async (dceId: string) => {
    try {
      const data = await this.repo.findOne({
        where: { dceId, isDelete: false, defaultMode: Like(`%create%`) },
      });

      const dceRelaitonModel = new DCERelationModel();
      if (!data || !data.form) {
        const jsonFromDB = await this.createByDce(dceId);
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(jsonFromDB, relationData, dceId);
        const dceData = await dceModel.findById(dceId);
        return { name: dceData?.name, form: newJson, id: dceId };
      }
      const relationData = await dceRelaitonModel.getByDceId(dceId);
      const newJson = await checkChoiceURl(data?.form, relationData, dceId);
      data.form = newJson;
      return data;
    } catch (error) {
      throw error;
    }
  };
  getDefaultJSONByDCE = async (dceId: string, mode?: string) => {
    try {
      let data = await this.repo.findOne({
        where: { dceId, isDelete: false, default: true },
      });
      if (mode) {
        data = await this.repo.findOne({
          where: { dceId, isDelete: false, defaultMode: Like(`%${mode}%`), default: true },
        });
      }
      const dceRelaitonModel = new DCERelationModel();
      if (!data || !data.form) {
        const jsonFromDB = await this.createByDce(dceId);
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(jsonFromDB, relationData, dceId);
        return newJson;
      }
      const relationData = await dceRelaitonModel.getByDceId(dceId);
      const newJson = await checkChoiceURl(data?.form, relationData, dceId);

      return newJson;
    } catch (error) {
      throw error;
    }
  };
  getJSONByFormId = async (formId: string, dceId: string) => {
    try {
      const data = await this.repo.findOne({
        where: { id: formId, isDelete: false },
      });
      const dceRelaitonModel = new DCERelationModel();

      if (!data || !data.form) {
        const jsonFromDB = await this.createByDce(dceId || '');
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(jsonFromDB, relationData, dceId);
        return newJson;
      }
      const relationData = await dceRelaitonModel.getByDceId(dceId);
      const newJson = await checkChoiceURl(data?.form, relationData, dceId);

      return newJson;
    } catch (error) {
      throw error;
    }
  };

  getCustomFormByFormKey = async (formKey: string) => {
    try {
      const data = await this.repo.findOne({
        where: { dceId: IsNull(), formKey, isDelete: false, isCustom: true },
      });
      return data;
    } catch (error) {
      throw error;
    }
  };
  getCustomForm = async (formId: string, dceId: string) => {
    try {
      const data = await this.repo.findOne({
        where: { isCustom: true, isDelete: false, dceId: IsNull() },
      });
      const dceRelaitonModel = new DCERelationModel();

      if (!data || !data.form) {
        const jsonFromDB = await this.createByDce(dceId || '');
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(jsonFromDB, relationData, dceId);
        return newJson;
      }
      const relationData = await dceRelaitonModel.getByDceId(dceId);
      const newJson = await checkChoiceURl(data?.form, relationData, dceId);

      return newJson;
    } catch (error) {
      throw error;
    }
  };
  getModeByForm = async (formId: string) => {
    try {
      const data = await this.repo.findOne({
        where: { id: formId, isDelete: false },
      });
      if (data?.mode) {
        (data.mode as any) = data.mode.split(',');
      }
      return data?.mode;
    } catch (error) {
      throw error;
    }
  };
  getFormByModeAndDce = async (dceId: string) => {
    try {
      const data = await this.repo.find({
        where: { dceId, isDelete: false },
      });
      const finalData = { create: '', edit: '', view: '' };
      data.forEach((value) => {
        if (value.mode) {
          const modeAsArray = value.mode.split(',');
          if (modeAsArray.length > 0) {
            if (finalData.create == '' && modeAsArray.includes('create')) {
              finalData.create = value.id;
            }
            if (finalData.edit == '' && modeAsArray.includes('edit')) {
              finalData.edit = value.id;
            }
            if (finalData.view == '' && modeAsArray.includes('view')) {
              finalData.view = value.id;
            }
          }
        }
      });

      return finalData;
    } catch (error) {
      throw error;
    }
  };

  update = async (id: string, data: DefaultForm): Promise<DefaultForm | undefined | null> => {
    try {
      // Attempt to update the entity
      await this.repo.update(id, data as any);

      // Try to find the updated entity
      const updatedEntity = await this.repo.findOne({ where: { id } });

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  };
  createByDce = async (dceId: string) => {
    try {
      const dceData = await dceModel.findById(dceId);
      if (dceData) {
        const entity = dceData.entity;

        if (entity && entity in entityList) {
          const entityClass: any = entityList[entity as keyof EntityListInterface];
          const entityMetadata = getRepository(entityClass).metadata;
          const query = `SELECT column_name as name, udt_name as type
                          FROM information_schema.columns
                          WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

          const out = await getManager().query(query);

          const subDCEModel = new SubDCEModel();
          const subDceData = await subDCEModel.findByDCEId(dceData.id);
          const finalSubDCEList: {
            name: string | undefined | null;
            columns: any;
            columnToRemove: string[];
          }[] = [];
          if (subDceData && subDceData.length > 0) {
            for (const element of subDceData) {
              const subDCEEntityClass: any =
                entityList[element.entity as keyof EntityListInterface];
              const subDCEEntityMetadata = getRepository(subDCEEntityClass).metadata;
              const subDCEquery = `SELECT column_name as name, udt_name as type
                  FROM information_schema.columns
                  WHERE table_name = '${subDCEEntityMetadata.tableName}' and table_schema = '${subDCEEntityMetadata.schema}'`;

              const subDCEout = await getManager().query(subDCEquery);

              finalSubDCEList.push({
                name: element.entity,
                columns: subDCEout,
                columnToRemove: element.columnsToRemove?.split(',') || [],
              });
            }
          }
          const json = await convertTableToSurveyJSON(out, entity, dceId, finalSubDCEList);

          return json;
        }
      } else {
        throw new Error('DCE not found');
      }
    } catch (error) {
      throw error;
    }
  };
  createByMultipleDce = async (dceIds: string[]) => {
    try {
      const page: {
        name: string;
        elements: any[];
        title: string;
      } = {
        name: 'page1',
        elements: [],
        title: 'page1',
      };
      for (const dceId of dceIds) {
        const dceData = await dceModel.findById(dceId);
        if (dceData) {
          const entity = dceData.entity;

          if (entity && entity in entityList) {
            const entityClass: any = entityList[entity as keyof EntityListInterface];
            const entityMetadata = getRepository(entityClass).metadata;
            const query = `SELECT column_name as name, udt_name as type
                            FROM information_schema.columns
                            WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

            const out = await getManager().query(query);

            const subDCEModel = new SubDCEModel();
            const subDceData = await subDCEModel.findByDCEId(dceData.id);
            const finalSubDCEList: {
              name: string | undefined | null;
              columns: any;
              columnToRemove: string[];
            }[] = [];
            if (subDceData && subDceData.length > 0) {
              for (const element of subDceData) {
                const subDCEEntityClass: any =
                  entityList[element.entity as keyof EntityListInterface];
                const subDCEEntityMetadata = getRepository(subDCEEntityClass).metadata;
                const subDCEquery = `SELECT column_name as name, udt_name as type
                    FROM information_schema.columns
                    WHERE table_name = '${subDCEEntityMetadata.tableName}' and table_schema = '${subDCEEntityMetadata.schema}'`;

                const subDCEout = await getManager().query(subDCEquery);

                finalSubDCEList.push({
                  name: element.entity,
                  columns: subDCEout,
                  columnToRemove: element.columnsToRemove?.split(',') || [],
                });
              }
            }
            const json = await convertTableToPanel(out, entity, dceId, finalSubDCEList);

            page.elements.push(json);
          }
        } else {
          throw new Error('DCE not found');
        }
      }
      return { pages: [page] };
    } catch (error) {
      throw error;
    }
  };
  async createBySubDce(dceId: string) {
    try {
      const subDCEModel = new SubDCEModel();
      const dceData = await subDCEModel.findById(dceId);
      if (dceData) {
        const entity = dceData.entity;

        if (entity && entity in entityList) {
          const entityClass: any = entityList[entity as keyof EntityListInterface];
          const entityMetadata = getRepository(entityClass).metadata;
          const query = `SELECT column_name as name, udt_name as type
                          FROM information_schema.columns
                          WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

          const out = await getManager().query(query);
          const json = await convertTableToSurveyJSON(out, entity, dceId);

          return json;
        }
      } else {
        throw new Error('DCE not found');
      }
    } catch (error) {
      throw error;
    }
  }

  toCamelCase = (str: string) => {
    return str
      .replace(/#/g, '')
      .replace(/_./g, (match) => match.charAt(1).toUpperCase()) // replace underscores and capitalize following character
      .replace(/^./, (match) => match.toLowerCase());
  };
}

export default FormModel;
