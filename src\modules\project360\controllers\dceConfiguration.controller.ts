import { Request, Response } from 'express';
import dceModel from '../models/meta/dce.model';
import GeoJsonConfigurationModel from '../models/dceConfiguration.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import DCECoordinatesConfigModel from '../models/dceCoordinatesConfig.model';
import { moveElement } from '@utils/dce/changeOrder';

class GeoJsonConfigurationController {
  async getData(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const dceId = req.params.id;
      const projectId = req.params.projectId;
      const dceData = await dceModel.findById(dceId);
      if (dceData) {
        const data = await model.getData(dceData, projectId);

        res.json({ isSucceed: true, data: data, msg: 'Data found' });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getById(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const id = req.params.id;

      const data = await model.getById(id);
      res.json({ isSucceed: true, data: data, msg: 'Data found' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getDataByEntity(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const coordinatesModel = new DCECoordinatesConfigModel();
      const entity = req.params.entity;
      const projectId = req.params.projectId;
      const dceDetails = await dceModel.findByEntity(entity);

      if (dceDetails) {
        const data = await model.getData(dceDetails, projectId);

        const coordinatesData = await coordinatesModel.getByDceAndProject(dceDetails.id, projectId);
        const isLatAndLong = coordinatesData
          ? coordinatesData?.isLatLong === true
            ? 'latLong'
            : 'eastnorth'
          : 'none';
        res.json({ isSucceed: true, data: data || [], isLatAndLong, msg: 'Data found' });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async changeOrder(req: Request, res: Response) {
    try {
      const { oldIndex, targetIndex, dceId, projectId } = req.body;
      const dceData = await dceModel.findById(dceId);
      if (dceData) {
        const model = new GeoJsonConfigurationModel();
        const dceConfigs = await model.getDataByDCEIdAndProjectId(dceId, projectId);
        if (dceConfigs.length > 0) {
          const newOrderArray = moveElement(dceConfigs, oldIndex, targetIndex);
          const dceConfigForOrderChange = newOrderArray.map((value, index) => {
            return { id: value.id, order: index + 1 };
          });
          await model.changeColumnOrder(dceConfigForOrderChange);
        }
      }
      res.json({ isSucceed: true, data: [], msg: 'Order Changed' });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }
}

export default GeoJsonConfigurationController;
