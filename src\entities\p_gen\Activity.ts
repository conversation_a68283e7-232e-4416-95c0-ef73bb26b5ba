import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToMany,
  ManyToOne,
  JoinColumn,
  JoinTable,
} from 'typeorm';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from './Project';
import { ProjectForm } from './ProjectForm';

@Entity({ schema: 'p_gen', name: 'activity' })
export class ProjectActivity {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @ManyToMany(() => DataCaptureElements, (dataCaptureElement) => dataCaptureElement.activities)
  @JoinTable({
    name: 'activity_data_capture_elements',
    joinColumn: { name: 'activityId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'dataCaptureElementId', referencedColumnName: 'id' },
  })
  dataCaptureElements?: DataCaptureElements[];

  @ManyToMany(() => ProjectForm, (form) => form.activities)
  @JoinTable({
    name: 'activity_project_form',
    joinColumn: { name: 'activityId', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'formId', referencedColumnName: 'id' },
  })
  forms?: ProjectForm[];
}
