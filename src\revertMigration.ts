import dataSource from './migrationConnection';
import 'reflect-metadata';

// To revert the last migration, run the following command:
// npx ts-node -r tsconfig-paths/register src/revertMigration.ts

async function revertMigration() {
  try {
    await dataSource.initialize();
    console.log('DataSource has been initialized');
    await dataSource.undoLastMigration();
    console.log('Last migration has been reverted');
    await dataSource.destroy();
    console.log('Connection closed');
  } catch (error) {
    console.error('Error during migration revert:', error);
  }
}

revertMigration();
