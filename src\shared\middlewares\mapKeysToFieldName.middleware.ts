import { EntityTarget, ObjectLiteral } from 'typeorm';
import {
  DataFromEntityInterface,
  getAllCustomColumnInfo,
} from '../utilities/entity/customDecorator';
import { NextFunction, Request, Response } from 'express';
import errorMiddleware from './error/error.middleware';

export class MapKeysToFieldName<T extends ObjectLiteral> {
  private Entity: EntityTarget<T>;

  constructor(entity: EntityTarget<T>) {
    this.Entity = entity;
  }

  async mapFields(req: Request, res: Response, next: NextFunction) {
    try {
      let data = [];
      if (Array.isArray(req.body)) {
        data = req.body;
      } else {
        data.push(req.body);
      }
      const columnInfo = await getAllCustomColumnInfo(this.Entity);
      const result = this.mapKeysToFieldNames(data, columnInfo);
      req.body = result;
      next();
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  private mapKeysToFieldNames(data: any[], columnInfo: DataFromEntityInterface[]): any[] {
    return data.map((item) => {
      const newItem: any = {};
      for (const key in item) {
        const column = columnInfo.find((c) => c.name === key);
        if (column && column.inTemplate) {
          newItem[column.fieldName] = item[key];
        }
        newItem.rowNumber = item.rowNumber;
      }
      return newItem;
    });
  }
}
