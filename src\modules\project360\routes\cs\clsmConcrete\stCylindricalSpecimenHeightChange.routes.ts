import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StCylindricalSpecimenHeightChange } from '../../../../../entities/p_cs/StCylindricalSpecimenHeightChange';

const router: Router = express.Router();

const GenricController = new CrudController<StCylindricalSpecimenHeightChange>(
  StCylindricalSpecimenHeightChange
);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'shrinkage')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'shrinkage')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'shrinkage')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'shrinkage')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'shrinkage'));
router.put('/:id', authenticateToken, (req, res) => GenricController.update(req, res, 'shrinkage'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'shrinkage')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'shrinkage')
);

export default router;
