import { getOffsetSign, nearestStationFromLatLong } from './coordinatesFunction';

// Total Horizontal Distance of Project = 25,663 feet
// Total Vertical Distance of Project = 20,325 feet

const getStationAndOffset = async (projectId: string, data: any) => {
  try {
    const latitude = parseFloat(data.latitude).toString();

    const longitude = parseFloat(data.longitude).toString();

    const { nearestStation, minDistance } = await nearestStationFromLatLong(
      Number(latitude),
      Number(longitude),
      projectId
    );
    let stationInFt = null;
    let nearestAlignment = null;
    let neareststationCoordinates = null;
    let neareststationlongitude = null;
    let neareststationlatitude = null;

    if (nearestStation) {
      stationInFt = nearestStation.station;
      nearestAlignment = nearestStation.stationAlignmentId;
      neareststationCoordinates = nearestStation.stationPosition;
      neareststationlongitude = nearestStation.longitude;
      neareststationlatitude = nearestStation.latitude;
    } else {
      throw new Error('No nearest station found.');
    }

    const offsetSign = getOffsetSign(
      latitude,
      longitude,
      neareststationlatitude as number,
      neareststationlongitude as number,
      neareststationCoordinates || ''
    );
    let station = null;
    if (stationInFt) {
      const stationParts = stationInFt.split('+');
      station = stationParts[0] + '+' + stationParts[1].slice(0, 2);
    }
    return {
      station,
      offset: `${offsetSign}` + Math.round(minDistance).toString(),
      stationAlignmentId: nearestAlignment,
    };
  } catch (err) {
    console.error('Error processing image:', err);
    throw err;
  }
};

export default getStationAndOffset;
