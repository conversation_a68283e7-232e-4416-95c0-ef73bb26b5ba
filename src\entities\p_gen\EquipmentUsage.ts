import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON><PERSON>n,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Equipment } from '../p_meta/Equipment'; // Import the Equipment entity

@Entity({ schema: 'p_gen' })
export class EquipmentUsage {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  equipmentId?: string;

  @ManyToOne(() => Equipment, { nullable: true }) 
  @JoinColumn({ name: 'equipmentId' })
  equipment?: Equipment;

  @Column({ type: 'timestamp' })
  usageDate?: Date;

  @Column()
  note?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
