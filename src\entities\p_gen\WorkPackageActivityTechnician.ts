import { <PERSON><PERSON>oa<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { WorkPackageActivity } from './WorkPackageActivity';
import { getUserById } from '../../shared/server/platformApi/user';
import { IUser } from '../../shared/server/platformApi/interface/IUser';

@Entity({ schema: 'p_gen' })
export class WorkPackageActivityTechnician {
  @PrimaryColumn('uuid')
  workPackageActivityId!: string;

  @PrimaryColumn('uuid')
  userId!: string;

  @ManyToOne(() => WorkPackageActivity, (work) => work.technician)
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  user?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.userId) {
        const data = await getUserById(this.userId);
        this.user = data;
      } else {
        this.user = null;
      }
    } catch (error) {
      this.user = null;
    }
  }
}
