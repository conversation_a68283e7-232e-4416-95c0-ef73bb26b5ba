import { getManager, getRepository } from 'typeorm';
import { StHydraulicConductivityWorkSheet } from '../../../../entities/p_cs/StHydraulicConductivityWorkSheet';
// import { HydraulicConductivityCellInfo } from '../../../../entities/p_meta/HydraulicConductivityCellInfo';

class HydraulicConductivityWorksheetModel {
  constructor() {}

  async findById(SampleId: string) {
    try {
      return await getManager()
        .getRepository(StHydraulicConductivityWorkSheet)
        .findOne({
          where: { id: SampleId, isDelete: false },
          relations: ['hydraulicConductivityCellInfo'],
        });
    } catch (error) {
      throw error;
    }
  }

  async findByIdWithCalculatedValue(wsId: string) {
    try {
      const worksheetRepository = getRepository(StHydraulicConductivityWorkSheet);
      const resultQuery = await getManager().query(`
      SELECT 
        AVG(
          (
            (
              (
                (
                  (
                    (
                      (
                        (
                          (
                            (
                              (
                                (
                                  (
                                    (
                                      ("hydraulicConductivityTest"."initialArea" * "hydraulicConductivityTest"."finalArea") * 2.54 * "hydraulicConductivityTest"."specimenLength"
                                    ) * LOG(
                                      (
                                        (
                                          (
                                            (("ws"."startHeadwaterPressure" - "ws"."startTailwaterPressure") * 70.3)
                                            + (("ws"."endHeadwaterPressure" - "ws"."endTailwaterPressure") * 70.3)
                                          ) / 2 + (
                                            (
                                              (
                                                (1 - "ws"."startReading1") * "hydraulicConductivityCellInfo"."headPipeLength"
                                              )
                                              - (
                                                (1 - "ws"."startReading2") * "hydraulicConductivityCellInfo"."tailPipeLength"
                                              )
                                            )
                                          )
                                        ) / (
                                          (
                                            (("ws"."startHeadwaterPressure" - "ws"."startTailwaterPressure") * 70.3)
                                            + (("ws"."endHeadwaterPressure" - "ws"."endTailwaterPressure") * 70.3)
                                          ) / 2 + (
                                            (
                                              (
                                                (1 - "ws"."endReading1") * "hydraulicConductivityCellInfo"."headPipeLength"
                                              )
                                              - (
                                                (1 - "ws"."endReading2") * "hydraulicConductivityCellInfo"."tailPipeLength"
                                              )
                                            )
                                          )
                                        )
                                      )
                                    )
                                  ) / (
                                    (
                                      "hydraulicConductivityTest"."initialArea" + "hydraulicConductivityTest"."finalArea"
                                    ) * (
                                      .25 * PI() * POWER("hydraulicConductivityTest"."initialDiameter", 2)
                                    ) * POWER(2.54, 2) * EXTRACT(epoch FROM AGE("ws"."startDate", "ws"."endDate")) / (24 * 60 * 60)
                                  )
                                )
                              )
                            )
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
          )
        ) AS meanavgk
      FROM "p_cs"."st_hydraulic_conductivity_work_sheet" "ws" 
      INNER JOIN "p_cs"."st_hydraulic_conductivity_test" "hydraulicConductivityTest" 
        ON "hydraulicConductivityTest"."id" = "ws"."HCTestId"
      INNER JOIN "p_meta"."hydraulic_conductivity_cell_info" "hydraulicConductivityCellInfo" 
        ON "hydraulicConductivityCellInfo"."id" = "ws"."cellInfoId"
        WHERE "ws"."id" = ${wsId}
  `);
      const meanavgk = resultQuery[0].meanavgk;

      const result = await worksheetRepository
        .createQueryBuilder('worksheet')
        .where('worksheet.id = :id', { id: wsId })
        .innerJoin('worksheet.hydraulicConductivityCellInfo', 'hydraulicConductivityCellInfo')
        .leftJoin('worksheet.hydraulicConductivityTest', 'hydraulicConductivityTest')
        .distinct(true)
        .select([
          'worksheet',
          'hydraulicConductivityCellInfo',
          'hydraulicConductivityTest',
          'AGE(worksheet.startDate, worksheet.endDate) AS elapsedTime',
          `${meanavgk} as meanavgk`,
          '(worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3 AS startPressureHeadDifference',
          '(worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3 AS endStartPressureHeadDifference',
          '(((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength)) AS startElevationDiff',
          '(((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) *  hydraulicConductivityCellInfo.tailPipeLength)) AS endElevationDiff',
          '((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / 2.54) / hydraulicConductivityTest.specimenLength AS startHydraulic',
          '((((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3) + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / 2.54) / hydraulicConductivityTest.specimenLength AS endHydraulic',
          '((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection) AS inLetPermVolume',
          '((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) AS outletPermVolume',
          '(((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))))) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) AS averagek',
          `(CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END) AS astm`,
          `( ${meanavgk} * (1 + CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END)) AS meanplus`,
          `( ${meanavgk} * ( 1 - CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END)) AS meanminus`,
          `(
            CASE 
                WHEN (
                        ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * Log(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                ))
                        ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (0.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) >= (
                          ${meanavgk} * (
                            1 - CASE 
                                WHEN ${meanavgk} > 0.00000001
                                    THEN .25
                                ELSE .5
                                END
                            )
                        )
                    AND (
                        ((hydraulicConductivityTest.initialArea  * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                ))
                        ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.specimenLength, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) <= (
                          ${meanavgk} * (
                            1 + CASE 
                                WHEN ${meanavgk} > 0.00000001
                                    THEN .25
                                ELSE .5
                                END
                            )
                        )
                    THEN 'True'
                ELSE 'False'
                END
            ) AS meanplusminus`,
          '(((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) AS outflowinflow',
          `CASE 
          WHEN (((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) >= 0.75
              AND (((worksheet.startReading2  - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) <= 1.25
              AND (
                  CASE 
                      WHEN (
                              ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                      ))
                              ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) >= (
                                ${meanavgk} * (
                                  1 - CASE 
                                      WHEN ${meanavgk} > 0.00000001
                                          THEN .25
                                      ELSE .5
                                      END
                                  )
                              )
                          AND (
                              ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                      ))
                              ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) <= (
                                ${meanavgk} * (
                                  1 + CASE 
                                      WHEN ${meanavgk} > 0.00000001
                                          THEN .25
                                      ELSE .5
                                      END
                                  )
                              )
                          THEN 'True'
                      ELSE 'False'
                      END
                  ) = 'True'
              THEN 'OK'
          ELSE 'NG'
          END AS okng`,
        ])
        .getRawOne();

      return result;
    } catch (error) {
      throw error;
    }
  }

  async findWorksheetByHCTestId(HCTestId: string) {
    try {
      const wsData = await getRepository(StHydraulicConductivityWorkSheet).find({
        where: { HCTestId: HCTestId },
        relations: ['hydraulicConductivityCellInfo'],
      });
      return wsData;
    } catch (error) {
      throw error;
    }
  }
  async findByHCTestId(WsIds: StHydraulicConductivityWorkSheet[]) {
    try {
      const final: any[] = await Promise.all(
        WsIds.map(async (value) => {
          try {
            const worksheetRepository = getRepository(StHydraulicConductivityWorkSheet);
            const resultQuery = await getManager().query(`
        SELECT 
          AVG(
            (
              (
                (
                  (
                    (
                      (
                        (
                          (
                            (
                              (
                                (
                                  (
                                    (
                                      (
                                        ("hydraulicConductivityTest"."initialArea" * "hydraulicConductivityTest"."finalArea") * 2.54 * "hydraulicConductivityTest"."specimenLength"
                                      ) * LOG(
                                        (
                                          (
                                            (
                                              (("ws"."startHeadwaterPressure" - "ws"."startTailwaterPressure") * 70.3)
                                              + (("ws"."endHeadwaterPressure" - "ws"."endTailwaterPressure") * 70.3)
                                            ) / 2 + (
                                              (
                                                (
                                                  (1 - "ws"."startReading1") * "hydraulicConductivityCellInfo"."headPipeLength"
                                                )
                                                - (
                                                  (1 - "ws"."startReading2") * "hydraulicConductivityCellInfo"."tailPipeLength"
                                                )
                                              )
                                            )
                                          ) / (
                                            (
                                              (("ws"."startHeadwaterPressure" - "ws"."startTailwaterPressure") * 70.3)
                                              + (("ws"."endHeadwaterPressure" - "ws"."endTailwaterPressure") * 70.3)
                                            ) / 2 + (
                                              (
                                                (
                                                  (1 - "ws"."endReading1") * "hydraulicConductivityCellInfo"."headPipeLength"
                                                )
                                                - (
                                                  (1 - "ws"."endReading2") * "hydraulicConductivityCellInfo"."tailPipeLength"
                                                )
                                              )
                                            )
                                          )
                                        )
                                      )
                                    ) / (
                                      (
                                        "hydraulicConductivityTest"."initialArea" + "hydraulicConductivityTest"."finalArea"
                                      ) * (
                                        .25 * PI() * POWER("hydraulicConductivityTest"."initialDiameter", 2)
                                      ) * POWER(2.54, 2) * EXTRACT(epoch FROM AGE("ws"."startDate", "ws"."endDate")) / (24 * 60 * 60)
                                    )
                                  )
                                )
                              )
                            )
                          )
                        )
                      )
                    )
                  )
                )
              )
            )
          ) AS meanavgk
        FROM "p_cs"."st_hydraulic_conductivity_work_sheet" "ws" 
        INNER JOIN "p_cs"."st_hydraulic_conductivity_test" "hydraulicConductivityTest" 
          ON "hydraulicConductivityTest"."id" = "ws"."HCTestId"
        INNER JOIN "p_meta"."hydraulic_conductivity_cell_info" "hydraulicConductivityCellInfo" 
          ON "hydraulicConductivityCellInfo"."id" = "ws"."cellInfoId"
          WHERE "ws"."id" = ${value.id}
    `);
            const meanavgk = resultQuery[0].meanavgk;

            const result = await worksheetRepository
              .createQueryBuilder('worksheet')
              .where('worksheet.id = :id', { id: value.id })
              .innerJoin('worksheet.hydraulicConductivityCellInfo', 'hydraulicConductivityCellInfo')
              .leftJoin('worksheet.hydraulicConductivityTest', 'hydraulicConductivityTest')
              .distinct(true)
              .select([
                'worksheet.endReading2',
                'worksheet.startReading2',
                'worksheet.id',
                'worksheet.startDate',
                'worksheet.endDate',
                'AGE(worksheet.startDate, worksheet.endDate) AS elapsedTime',
                'worksheet.startReading1 AS startheadWaterReading',
                'worksheet.endReading1 AS endHeadWaterReading',
                'worksheet.startHeadwaterPressure',
                'worksheet.endHeadwaterPressure',
                'hydraulicConductivityTest.finalArea',
                'worksheet.startTailwaterPressure',
                'hydraulicConductivityTest.initialArea',
                'hydraulicConductivityTest.specimenLength',
                `${meanavgk} as meanavgk`,
                '(worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3 AS startPressureHeadDifference',
                '(worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3 AS endStartPressureHeadDifference',
                '(((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength)) AS startElevationDiff',
                '(((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) *  hydraulicConductivityCellInfo.tailPipeLength)) AS endElevationDiff',
                '((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / 2.54) / hydraulicConductivityTest.specimenLength AS startHydraulic',
                '((((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3) + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / 2.54) / hydraulicConductivityTest.specimenLength AS endHydraulic',
                '((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection) AS inLetPermVolume',
                '((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) AS outletPermVolume',
                '(((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))))) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) AS averagek',
                `(CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END) AS astm`,
                `( ${meanavgk} * (1 + CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END)) AS meanplus`,
                `( ${meanavgk} * ( 1 - CASE WHEN ${meanavgk} > 0.00000001 THEN .25 ELSE .5 END)) AS meanminus`,
                `(
              CASE 
                  WHEN (
                          ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * Log(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                  ))
                          ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (0.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) >= (
                            ${meanavgk} * (
                              1 - CASE 
                                  WHEN ${meanavgk} > 0.00000001
                                      THEN .25
                                  ELSE .5
                                  END
                              )
                          )
                      AND (
                          ((hydraulicConductivityTest.initialArea  * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                  ))
                          ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.specimenLength, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) <= (
                            ${meanavgk} * (
                              1 + CASE 
                                  WHEN ${meanavgk} > 0.00000001
                                      THEN .25
                                  ELSE .5
                                  END
                              )
                          )
                      THEN 'True'
                  ELSE 'False'
                  END
              ) AS meanplusminus`,
                '(((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) AS outflowinflow',
                `CASE 
            WHEN (((worksheet.startReading2 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) >= 0.75
                AND (((worksheet.startReading2  - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeCorrection) / ((worksheet.endReading1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeCorrection)) <= 1.25
                AND (
                    CASE 
                        WHEN (
                                ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                        ))
                                ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) >= (
                                  ${meanavgk} * (
                                    1 - CASE 
                                        WHEN ${meanavgk} > 0.00000001
                                            THEN .25
                                        ELSE .5
                                        END
                                    )
                                )
                            AND (
                                ((hydraulicConductivityTest.initialArea * hydraulicConductivityTest.finalArea) * 2.54 * hydraulicConductivityTest.specimenLength) * LOG(((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.startReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.startReading2) * hydraulicConductivityCellInfo.tailPipeLength))) / ((((worksheet.startHeadwaterPressure - worksheet.startTailwaterPressure) * 70.3) + ((worksheet.endHeadwaterPressure - worksheet.endTailwaterPressure) * 70.3)) / 2 + (((1 - worksheet.endReading1) * hydraulicConductivityCellInfo.headPipeLength) - ((1 - worksheet.endReading2) * hydraulicConductivityCellInfo.tailPipeLength))
                                        ))
                                ) / ((hydraulicConductivityTest.initialArea + hydraulicConductivityTest.finalArea) * (.25 * PI() * power(hydraulicConductivityTest.initialDiameter, 2)) * power(2.54, 2) * EXTRACT(epoch FROM AGE(worksheet.startDate, worksheet.endDate)) / (24 * 60 * 60)) <= (
                                  ${meanavgk} * (
                                    1 + CASE 
                                        WHEN ${meanavgk} > 0.00000001
                                            THEN .25
                                        ELSE .5
                                        END
                                    )
                                )
                            THEN 'True'
                        ELSE 'False'
                        END
                    ) = 'True'
                THEN 'OK'
            ELSE 'NG'
            END AS okng`,
              ])
              .getRawOne();
            return result;
          } catch (error) {
            console.error(`Error processing worksheet ID ${value.id}:`, error);

            throw error;
          }
        })
      );

      return final;
    } catch (error) {
      throw error;
    }
  }
}

const hydraulicConductivityWorksheetModel = new HydraulicConductivityWorksheetModel();
export default hydraulicConductivityWorksheetModel;
