import { Request, Response } from 'express';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import ProjectFormModel from '../models/projectForm.model';
import dceModel from '../models/meta/dce.model';

class ProjectFormController {
  private model = new ProjectFormModel();

  findByDceIdAndProjectId = async (req: Request, res: Response) => {
    try {
      const data = await this.model.findByDceId(req.params.dceId, req.params.projectId);

      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  editFormMode = async (req: Request, res: Response) => {
    try {
      const { mode }: { mode: { formId: string; mode: string[] }[] } = req.body;
      const { dceId, projectId } = req.params;
      await this.model.editModeByDceAndProject(dceId, mode, projectId);
      const message = req.__('UpdatedSuccess');
      return res.json({ isSucceed: true, data: [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getJsonByFormId = async (req: Request, res: Response) => {
    try {
      const { formId } = req.params;
      await this.model.findByJsonFormId(formId);
      const message = req.__('DataFoundMessage');
      return res.json({ isSucceed: true, data: [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  public update = async (req: Request, res: Response) => {
    try {
      const id: number | string = req.params.id;
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }

      const data = req.body;
      const result = await this.model.update(id, data);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data updated' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  setAsDefault = async (req: Request, res: Response) => {
    try {
      const { projectId, mode, dceId, formId } = req.body;

      await this.model.setAsDefault(projectId, mode, dceId, formId);
      const message = req.__('DataFoundMessage');
      return res.json({ isSucceed: true, data: [], msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByDCEId = async (req: Request, res: Response) => {
    try {
      const type = req.query.type;
      if (type == 'mobile') {
        const data = await this.model.getJSONByDCEAndProjectForMobile(
          req.params.dceId,
          req.params.projectId,
          req.query.updatedAfter as any
        );
        const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
        return res.json({ isSucceed: true, data: data || {}, msg: message });
      }
      const data = await this.model.getJSONByDCEAndProject(req.params.dceId, req.params.projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByFormIdMobile = async (req: Request, res: Response) => {
    try {
      const { formId } = req.params;
      const data = await this.model.getJSONByFormForMobile(formId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getCustomFormByProjectId = async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const data = await this.model.getCustomFormByProjectId(projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getCustomFormByProjectIdAndFormKey = async (req: Request, res: Response) => {
    try {
      const { projectId, formKey } = req.params;
      const data = await this.model.getCustomFormByProjectIdAndFormKey(projectId, formKey);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByEntity = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findByEntity(req.params.entity);
      if (!dceData) {
        throw new Error('DCE Not Found');
      }
      const data = await this.model.getJSONByDCEAndProject(dceData.id, req.params.projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getJSONByDCEIdWithMode = async (req: Request, res: Response) => {
    try {
      const type = req.query.type;
      if (type == 'mobile') {
        const data = await this.model.getJSONByDCEAndProjectForMobileWithMode(
          req.params.dceId,
          req.params.projectId,
          'create',
          req.query.updatedAfter as any
        );
        const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
        return res.json({ isSucceed: true, data: data || {}, msg: message });
      }
      const data = await this.model.getJSONByDCEAndProjectWithMode(
        req.params.dceId,
        req.params.projectId,
        req.params.mode
      );
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getDefaultJSONByDCEIdWithMode = async (req: Request, res: Response) => {
    try {
      const type = req.query.type;
      if (type == 'mobile') {
        const data = await this.model.getJSONByDCEAndProjectForMobileWithMode(
          req.params.dceId,
          req.params.projectId,
          'create',
          req.query.updatedAfter as any
        );
        const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
        return res.json({ isSucceed: true, data: data || {}, msg: message });
      }
      const data = await this.model.getJSONByDCEAndProjectWithMode(
        req.params.dceId,
        req.params.projectId,
        req.params.mode
      );
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getJSONByEntityWithMode = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findByEntity(req.params.entity);
      if (!dceData) {
        throw new Error('DCE Not Found');
      }
      const data = await this.model.getDefaultJSONByDCEAndProjectWithMode(
        dceData.id,
        req.params.projectId,
        req.params.mode
      );
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getAllCreateForm = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findById(req.params.dceId);
      if (!dceData) {
        throw new Error('DCE Not Found');
      }
      const data = await this.model.getAllJSONByDCEAndProjectWithMode(
        req.params.dceId,
        req.params.projectId
      );
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  addProjectFormByDefaultFrom = async (req: Request, res: Response) => {
    try {
      const { projectId, defaultFormId } = req.body;
      const data = await this.model.addProjectFormByDefaultFrom(
        defaultFormId,
        projectId,
        (req as any).user.name,
        (req as any).user.id
      );
      const message = data ? req.__('DataInputSuccess') : req.__('DataInputFail');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getModeIdsByDCE = async (req: Request, res: Response) => {
    try {
      const { dceId, projectId } = req.params;
      const data = await this.model.getFormIdsByModeAndDce(dceId, projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getModeFormsByDCE = async (req: Request, res: Response) => {
    try {
      const { dceId, projectId } = req.params;
      const data = await this.model.getFormByModeAndDce(dceId, projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getAllMultipleDCEForms = async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const data = await this.model.getAllMultipleDCEForms(projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  getCustomAndMultipleDCEForms = async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const data = await this.model.getCustomAndMultipleDCEForms(projectId);
      const message = data ? req.__('DataNotFoundMessage') : req.__('DataNotFoundMessage');
      return res.json({ isSucceed: true, data: data || {}, msg: message });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default ProjectFormController;
