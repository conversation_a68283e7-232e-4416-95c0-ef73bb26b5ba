import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CustomFormDataContorller from '@controllers//gen/customFormData.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const contorller = new CustomFormDataContorller();

router.post('/', authenticateToken, (req, res) => contorller.create(req, res));

export default router;
