import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  ManyToMany,
  JoinTable,
  AfterLoad,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ProjectEquipment } from '../p_gen/ProjectEquipment';
import { Feature } from '../p_gen/Feature';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Area } from '../p_gen/Area';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class Observation extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  locationId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'locationId' })
  location?: Area;

  @Column({ nullable: true })
  startStation?: string;

  @Column({ nullable: true })
  endStation?: string;

  @Column({ nullable: true })
  startOffset?: string;

  @Column({ nullable: true })
  endOffset?: string;

  @Column({ nullable: true })
  startLatitude?: string;

  @Column({ nullable: true })
  startLongitude?: string;

  @Column({ nullable: true })
  endLatitude?: string;

  @Column({ nullable: true })
  endLongitude?: string;

  @Column({ nullable: true })
  checkIn?: Date;

  @Column({ nullable: true })
  checkOut?: Date;

  @ManyToMany(() => ProjectEquipment, (projectEquipment) => projectEquipment.observation)
  @JoinTable() // This decorator indicates the owner side of the relationship
  equipment?: ProjectEquipment[];

  @Column({ nullable: true })
  personnelResponsible?: string;

  @Column({ nullable: true })
  workPerformed?: string;

  @Column({ nullable: true, type: 'timestamp' })
  date?: Date;

  @Column({ nullable: true })
  weatherImpact?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @AfterLoad()
  loadEquipmentIds() {
    (this.equipment as any) = this.equipment?.map((eq) => eq?.id) || [];
  }
}
