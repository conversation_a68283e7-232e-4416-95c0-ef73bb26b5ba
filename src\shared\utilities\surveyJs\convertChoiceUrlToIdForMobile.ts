import hasParentCheck from '@utils/dropdown/hasParentCheck';
import { DataCaptureElemetRelation } from '../../../entities/p_meta/DataCaptureElemetRelation';
import dceModel from '@models/meta/dce.model';
import getAllChildren from '@utils/dropdown/getAllChildren';

export const convertDropdownToURl = async (
  formJson: any,
  relationData: DataCaptureElemetRelation[]
) => {
  let newJson = formJson;
  try {
    for (const iterator of relationData) {
      const parent = await hasParentCheck(iterator?.routeParams?.split(',') || []);
      const id = !parent.hasParent
        ? `${iterator.columnName}.${iterator.relatedTableName}`
        : `${iterator.columnName}.${iterator.relatedTableName}.${parent.routes}`;
      const children = getAllChildren(iterator.columnName || '', relationData);
      const parentId = parent.hasParent
        ? await dceModel.getForeignKeyRelatedTable(iterator.relatedTableName || '', parent.routes)
        : null;
      const columnName = iterator.columnName || '';
      newJson = addChoiceId(
        newJson,
        id,
        columnName,
        parentId,
        children.length > 0 ? children : null
      );
    }
    return newJson;
  } catch (error) {
    throw error;
  }
};

const addChoiceId = (
  jsonObj: any,
  choiceIdValue: string,
  columnName: string,
  choiceParentValue?: string | null,
  children?: string[] | null
) => {
  for (const key in jsonObj) {
    if (jsonObj.hasOwnProperty(key)) {
      if (key === 'choicesByUrl') {
        if (columnName == 'equipmentId') {
          if (jsonObj.name === 'equipmentId') {
            jsonObj.dropdownId = choiceIdValue;
          }
        } else {
          if (jsonObj.name === columnName) {
            jsonObj.parentDropdownId = choiceParentValue ? choiceParentValue?.split('.')[0] : null;
            jsonObj.children = children;
            jsonObj.dropdownId = choiceIdValue;
          }
        }
      } else if (typeof jsonObj[key] === 'object' && jsonObj[key] !== null) {
        addChoiceId(jsonObj[key], choiceIdValue, columnName, choiceParentValue, children);
      }
    }
  }
  return jsonObj;
};
export const convertDropdownToURlForMutiple = async (
  formJson: any,
  relationData: DataCaptureElemetRelation[],
  subDceRelationData: DataCaptureElemetRelation[]
) => {
  const newJson = { ...formJson };

  try {
    // Preprocess all promises
    const processedData = await Promise.all(
      relationData.map(async (iterator) => {
        const columnName = iterator.columnName || '';

        const routeParams = iterator?.routeParams?.split(',') || [];
        const parent = await hasParentCheck(routeParams);
        const children = getAllChildren(columnName, relationData);

        const id = !parent.hasParent
          ? `${columnName}.${iterator.relatedTableName}`
          : `${columnName}.${iterator.relatedTableName}.${parent.routes}`;

        // Check if the parent has a foreign key relationship
        const parentId = parent.hasParent
          ? await dceModel.getForeignKeyRelatedTable(iterator.relatedTableName || '', parent.routes)
          : null;

        return {
          id,
          columnName: `${iterator.dce?.entity}.${columnName}`,
          parentId: parentId ? `${iterator.dce?.entity}.${parentId}` : null,
          children,
        };
      })
    );

    // Apply addChoiceIdForMutiple
    let updatedJson = newJson;
    for (const { id, columnName, parentId, children } of processedData) {
      updatedJson = addChoiceIdForMutiple(
        updatedJson,
        id,
        columnName,
        parentId,
        children.length > 0 ? children : null
      );
    }

    if (subDceRelationData.length > 0) {
      for (const { columnName, relatedTableName } of subDceRelationData) {
        const id = `${columnName}.${relatedTableName}`;
        if (columnName) {
          traverseAndPatchMatrixDynamic(updatedJson, columnName, id);
        }
      }
    }

    return updatedJson;
  } catch (error) {
    throw error;
  }
};

const addChoiceIdForMutiple = (
  jsonObj: any,
  choiceIdValue: string,
  columnName: string,
  choiceParentValue?: string | null,
  children?: string[] | null
) => {
  for (const key in jsonObj) {
    if (Object.prototype.hasOwnProperty.call(jsonObj, key)) {
      if (key === 'choicesByUrl') {
        const fieldName = jsonObj.name;

        // Ensure we're matching the full field path
        if (typeof fieldName === 'string' && fieldName === columnName) {
          const parentParts = choiceParentValue?.split('.') || [];
          jsonObj.parentDropdownId =
            parentParts.length >= 2 ? `${parentParts[0]}.${parentParts[1]}` : null;
          jsonObj.children = children;
          jsonObj.dropdownId = choiceIdValue;
        }
      } else if (typeof jsonObj[key] === 'object' && jsonObj[key] !== null) {
        addChoiceIdForMutiple(jsonObj[key], choiceIdValue, columnName, choiceParentValue, children);
      }
    }
  }
  return jsonObj;
};

const traverseAndPatchMatrixDynamic = (obj: any, columnName: string, dropdownId: string) => {
  if (typeof obj !== 'object' || obj === null) return;

  for (const key in obj) {
    if (obj[key] && typeof obj[key] === 'object') {
      const item = obj[key];

      // Check if it's a matrixdynamic type
      if (item.type === 'matrixdynamic' && Array.isArray(item.columns)) {
        item.columns = item.columns.map((col: any) => {
          if (col.name === columnName) {
            return {
              ...col,
              dropdownId, // Add dropdownId to the column
            };
          }
          return col;
        });
      }

      // Recurse into nested objects
      traverseAndPatchMatrixDynamic(item, columnName, dropdownId);
    }
  }
};
