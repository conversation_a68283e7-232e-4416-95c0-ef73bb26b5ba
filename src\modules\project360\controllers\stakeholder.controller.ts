import { Request, Response } from 'express';
import stakeholderModel from '../models/stakeholder.model';
import projectOrgStructureModel from '../models/organizationStructure.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';

class StakeholderController {
  constructor() {}
  async addStakeholder(req: Request, res: Response) {
    try {
      if (!(req as any).user) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invalid request bodys',
        });
      }
      req.body.createdBy = (req as any).user.name;
      req.body.updatedBy = (req as any).user.name;
      // getting value from request
      const { organizationRole, projectId } = req.body;

      if (!organizationRole) {
        return res.status(400).json({
          isSucceed: false,
          data: [],
          msg: 'Invaild request body',
        });
      }
      if (organizationRole === 'Subcontractor') {
        if (!req.body.parentId) {
          return res.status(400).json({
            isSucceed: false,
            data: [],
            msg: 'Invaild request body',
          });
        }
      }

      const adminStakeholder: any = {
        name: req.body.name,
        type: req.body.type,
        projectNo: req.body.projectNo,
        projectId: req.body.projectId,
        organizationId: req.body.organizationId,
        createdBy: req.body.createdBy,
        updatedBy: req.body.updatedBy,
      };

      const stakeholderData = await stakeholderModel.addStakeholder(
        adminStakeholder,
        (req as any).user.id || ''
      );

      if (organizationRole === 'Owner') {
        const ownerCheckParentId = await projectOrgStructureModel.findByRole('Owner', projectId);

        if (ownerCheckParentId.length > 0) {
          return res.status(400).json({
            isSucceed: false,
            data: [],
            msg: 'Role owner already exist',
          });
        }
        const orgStructure: any = {
          projectId: stakeholderData.projectId,
          createdBy: stakeholderData.createdBy,
          updatedBy: stakeholderData.updatedBy,
          roleType: organizationRole,
          stakeholderId: stakeholderData.id,
        };
        const projectOrgStructureData =
          await projectOrgStructureModel.addOrgStructure(orgStructure);

        const needChangeParentId = await projectOrgStructureModel.findByRole(
          'Contractor',
          projectId
        );
        if (needChangeParentId.length > 0) {
          needChangeParentId.map(async (value) => {
            if (value.id && projectOrgStructureData.id) {
              await projectOrgStructureModel.EditParentId(value.id, projectOrgStructureData.id);
            }
          });
        }
      }

      if (organizationRole === 'Contractor') {
        let parentId = null;
        const QAParentId = await projectOrgStructureModel.findByRole('Owner', projectId);
        const SubcontractorExist = await projectOrgStructureModel.findByRoleWithoutParent(
          'Subcontractor',
          projectId
        );

        if (QAParentId.length > 0) {
          if (QAParentId[0].id) {
            parentId = QAParentId[0].id;
          }
        }
        const orgStructure: any = {
          projectId: stakeholderData.projectId,
          createdBy: stakeholderData.createdBy,
          updatedBy: stakeholderData.updatedBy,
          roleType: organizationRole,
          stakeholderId: stakeholderData.id,
          parentId: parentId || undefined,
        };
        const result = await projectOrgStructureModel.addOrgStructure(orgStructure);

        if (SubcontractorExist.length > 0) {
          SubcontractorExist.map(async (value) => {
            if (value.id && result.id) {
              await projectOrgStructureModel.EditParentId(value.id, result.id);
            }
          });
        }
      }

      if (organizationRole === 'QA') {
        let parentId = null;
        const QAParentId = await projectOrgStructureModel.findByRole('Owner', projectId);
        if (QAParentId.length > 0) {
          if (QAParentId[0].id) {
            parentId = QAParentId[0].id;
          }
        }
        const orgStructure: any = {
          projectId: stakeholderData.projectId,
          createdBy: stakeholderData.createdBy,
          updatedBy: stakeholderData.updatedBy,
          roleType: organizationRole,
          stakeholderId: stakeholderData.id,
          parentId: parentId || undefined,
        };
        await projectOrgStructureModel.addOrgStructure(orgStructure);
      }

      if (organizationRole === 'Subcontractor') {
        const { parentId } = req.body;

        if (!parentId) {
          return res.status(400).json({
            isSucceed: false,
            data: [],
            msg: 'Invaild request body',
          });
        }
        const orgStructure: any = {
          projectId: stakeholderData.projectId,
          createdBy: stakeholderData.createdBy,
          updatedBy: stakeholderData.updatedBy,
          roleType: organizationRole,
          stakeholderId: stakeholderData.id,
          parentId: parentId,
        };
        await projectOrgStructureModel.addOrgStructure(orgStructure);
      }

      // sending success response
      return res.status(200).json({ isSucceed: true, data: stakeholderData, msg: 'project' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  // find config by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      return stakeholderModel.findById(req.params.id).then((stakeholderData) => {
        // checking if data is found with the id
        if (stakeholderData) {
          // if true data will send as response
          return res.status(200).json({
            isSucceed: true,
            data: stakeholderData,
            msg: 'stakeholder found',
          });
        } else {
          // if false send error as response
          return res.status(200).json({
            isSucceed: true,
            data: [],
            msg: 'No config data found',
          });
        }
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByProjectId(req: Request, res: Response) {
    try {
      const stakeholderData = await stakeholderModel.findByProjectId(req.params.id);
      // checking if data is found with the id
      if (stakeholderData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeholderData,
          msg: 'stakeholder found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No stakeholder data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByUserProjectId(req: Request, res: Response) {
    try {
      const stakeholderData = await stakeholderModel.findByUserProjectId(req.params.id);

      // checking if data is found with the id
      if (stakeholderData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: stakeholderData,
          msg: 'stakeholder found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No stakeholder data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

const stakeholderController = new StakeholderController();
export default stakeholderController;
