import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { Sample } from './Sample';
import { SampleType } from '../p_meta/SampleType';
import { Purpose } from '../p_meta/Purpose';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { Site } from '@entities/p_gen/Site';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StConcreteTemperature extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Batch Id',
      fieldName: 'batchId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      // batchNumber - ticketNumber
      query: `SELECT id FROM p_cs.concrete_batch_ticket WHERE "ticketNumber" = $1`,
      getListQuery: `SELECT id,"ticketNumber" as name FROM p_cs.concrete_batch_ticket WHERE "projectId" = $1 ORDER BY "updatedAt" DESC;`,
      listParams: 'id',
      listName: 'batchIdList',
    },
  })
  @Column({ nullable: true })
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @ColumnInfo({
    customData: {
      name: 'Sample Id', // this column will be entered by the
      fieldName: 'sampleId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.sample WHERE "QMSSampleYear" || '-' || "QMSLabSampleId" = $1`,
      getListQuery: `SELECT id,"QMSSampleYear" || '-' || "QMSLabSampleId" as name FROM p_cs.sample WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 75;`,
      listParams: 'id',
      listName: 'sampleList',
    },
  })
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @ColumnInfo({
    customData: {
      name: 'Test Method', // this column will be entered by the
      fieldName: 'testMethodId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.test_method WHERE "standardCode" = $1`,
      getListQuery:
        'SELECT id,"standardCode" as name FROM p_meta.test_method ORDER BY "updatedAt" DESC LIMIT 20;',
      listName: 'testMethodList',
    },
  })
  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'Temperature',
      fieldName: 'temperature',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  temperature?: number;

  @ColumnInfo({
    customData: {
      name: 'Temperature RT',
      fieldName: 'temperatureRT',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  temperatureRT?: number;

  @ColumnInfo({
    customData: {
      name: 'Tester',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ length: 2000, nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ type: 'timestamp', nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
