export interface IProctorTestReport {
  sampleNo: string;
  testNo: string;
  sampledDate: string; // or Date if you prefer Date objects
  sampledBy: string;
  soilDescription: string;
  sampleLocation: string;
  sampleSource: string;
  materialName: string;
  materialId: number;
  northing: number;
  easting: number;
  depth: number;
  soilClassification: string | null;
  testMethod: string | null;
  mcMoistureContent: number | null;
  mcTestMethod: string | null;
  alLiquidLimit: number | null;
  alPlasticLimit: number | null;
  alPlasticityIndex: number | null;
  alTestMethod: string | null;
  alTestVariant: string | null;
  orOrganicContent: number | null;
  orTestMethod: string | null;
  asSpecificGravity: number | null;
  asAbsorption: number | null;
  asTestMethod: string | null;
  pMaximumDryDensity: number | null;
  pOptimumMoisture: number | null;
  pCorrectedMaximumDryDensity: number | null;
  pCorrectedOptimumMoisture: number | null;
  pCorrectedTestMethod: string;
  pTestMethod: string | null;
  pp200: number | null;
  ppTestMethod: string | null;
  dateTested: string; // or Date if you handle date parsing
  testedBy: string;
}
