import express, { Router } from 'express';
import { TurbidityTesting } from '../../../../../entities/p_cs/TurbidityTesting';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';

const router: Router = express.Router();

const GenricController = new CrudController<TurbidityTesting>(TurbidityTesting);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'turbidityTesting')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'turbidityTesting')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'turbidityTesting')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'turbidityTesting')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'turbidityTesting')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'turbidityTesting')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'turbidityTesting')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'turbidityTesting')
);

export default router;
