import { getManager, getRepository } from 'typeorm';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { StakeUser } from '../../../entities/p_auth/StakeUser';
import { createAuditLog } from '../../../shared/utilities/auditLog/createAuditLog';

class StakeholderModel {
  constructor() {}
  async addStakeholder(newStakeholder: Stakeholder, createdUserId: string) {
    try {
      const stakeholderRepository = getManager().getRepository(Stakeholder);
      const addedStakeholder = stakeholderRepository.create(newStakeholder);
      await stakeholderRepository.save(addedStakeholder);
      createAuditLog(
        createdUserId,
        addedStakeholder.projectId || '',
        `New stakeholder added by ${addedStakeholder?.createdBy}`,
        `${addedStakeholder?.createdBy} `,
        addedStakeholder.createdAt as Date,
        'p_auth.stakeholder',
        'stakeholder',
        addedStakeholder.id
      );
      return addedStakeholder;
    } catch (error) {
      throw error;
    }
  }

  async findById(stakeholderId: string) {
    try {
      return await getManager()
        .getRepository(Stakeholder)
        .findOne({
          where: { id: stakeholderId, isDelete: false },
          relations: ['stakeUsers'],
        });
    } catch (error) {
      throw error;
    }
  }
  async findPrimeByProjectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(Stakeholder)
        .findOne({
          where: { projectId, isDelete: false, prime: true },
        });
    } catch (error) {
      throw error;
    }
  }

  async findByProjectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(Stakeholder)
        .createQueryBuilder('stakeholder')
        .leftJoinAndSelect('stakeholder.stakeUsers', 'stakeUser')
        .leftJoinAndSelect('stakeholder.orgSturcture', 'orgSturcture')
        .leftJoin('stakeUser.role', 'role')
        .select(['stakeholder', 'stakeUser', 'role.name', 'orgSturcture'])
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeholder.isDelete = :isDelete', { isDelete: false })
        .getMany();
    } catch (error) {
      throw error;
    }
  }

  async findByUserProjectId(projectId: string) {
    try {
      const stakeUserRepository = getRepository(StakeUser);
      const data = await stakeUserRepository
        .createQueryBuilder('stakeUser')
        .innerJoinAndSelect('stakeUser.stakeholder', 'stakeholder')
        .where('stakeholder.projectId = :projectId', { projectId })
        .andWhere('stakeUser.isDelete = :isDelete', { isDelete: false })
        .getMany();

      const final = data
        .filter((value) => value.user) // Filter items that have a user
        .map((value) => {
          const firstName = value?.user?.firstName || '';
          const lastName = value?.user?.lastName || '';
          const fullName = `${firstName} ${lastName}`.trim(); 
          return {
            user_id: value?.user?.id,
            user_firstName: firstName,
            user_lastName: lastName,
            user_fullName: fullName,
            organization_name: value?.user?.organization?.name, // Use optional chaining to handle missing organization
          }
        })
        .sort((a, b) => a.user_firstName.localeCompare(b.user_firstName)); ;
      return final;
    } catch (error) {
      throw error;
    }
  }
}

const stakeholderModel = new StakeholderModel();
export default stakeholderModel;
