import { DCERelationship } from '../../../entities/p_utils/DCERelationship';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import { ISampleTestRelationEditData } from '../../interface/relationshipInterface';
import { entityList, EntityListInterface } from '../entity/entityList';

const editDataInTestTableForSampleTestRelation = async (
  sampleId: string,
  dceRelationData: DCERelationship,
  createdUserId: string,
  editedBy: string
): Promise<{ dceId: string; dataId: string; additionalDetails: any }[]> => {
  try {
    const finalOut: { dceId: string; dataId: string; additionalDetails: any }[] = [];
    if (
      dceRelationData.targetDCE &&
      dceRelationData.targetDCE?.entity &&
      dceRelationData?.targetDataId
    ) {
      const entityString = dceRelationData.targetDCE.entity;
      const dceId = dceRelationData.targetDCE.id;
      const data: ISampleTestRelationEditData = { sampleId, updatedBy: editedBy };
      const columnsToCheck = ['testId', 'testMethodId', 'testVariantId'];
      if (entityString in entityList) {
        const results = await Promise.all(
          columnsToCheck.map((column) => dceModel.checkColumnExistByDce(entityString, column))
        );
        const [columnTestIdexist, columnTestMethodIdexist, columntestVariantIdexist] = results;
        if (columnTestIdexist) {
          data.testId = dceRelationData.additionalDetails.testId;
        }
        if (columnTestMethodIdexist) {
          data.testMethodId = dceRelationData.additionalDetails.testMethodId;
        }
        if (columntestVariantIdexist) {
          data.testVariantId = dceRelationData.additionalDetails.testVariantId;
        }

        const response = await dceModel.editDataByEntity(
          dceRelationData?.targetDataId,
          data,
          entityList[entityString as keyof EntityListInterface] as any,
          createdUserId,
          editedBy,
          entityString
        );
        if (response) {
          finalOut.push({
            dataId: response.id as string,
            dceId,
            additionalDetails: dceRelationData.additionalDetails,
          });
        }
      }
    }
    return finalOut;
  } catch (error) {
    throw error;
  }
};

export default editDataInTestTableForSampleTestRelation;
