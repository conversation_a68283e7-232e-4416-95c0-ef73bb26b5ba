import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Project } from './Project';
import { Feature } from './Feature';
import { TestMethod } from '../p_meta/TestMethod';
import { ProjectMaterial } from './ProjectMaterial';
import { Test } from '../p_meta/Test';
import Structure from './Structure';
import { TestConfigurationRule } from './TestConfigRule';
import { Site } from './Site';

@Entity({ schema: 'p_gen' })
export class TestConfiguration {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', nullable: false })
  projectId?: string;

  @Column({ nullable: true })
  testMethodId?: string;

  @Column({ nullable: true })
  ruleExpression?: string;

  @Column({ nullable: true })
  effectedColumn?: string;

  @Column({ nullable: true })
  valueIfTrue?: string;

  @Column({ nullable: true })
  valueIfFalse?: string;

  @Column({ nullable: true })
  startDate?: Date;

  @Column({ nullable: true })
  endDate?: Date;

  @ManyToOne(() => TestMethod, { nullable: true }) 
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: false })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true }) 
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true }) 
  @JoinColumn({ name: 'siteId' })
  site?: Site;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: false })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true }) 
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true }) 
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ nullable: false })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true }) 
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => TestConfigurationRule, (event) => event.testConfig)
  rule?: TestConfigurationRule[];
}
