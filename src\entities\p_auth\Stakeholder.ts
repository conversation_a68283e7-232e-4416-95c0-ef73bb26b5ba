import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  AfterLoad,
} from 'typeorm';
// import { Organization } from '../g_auth/Organization';
import { StakeUser } from './StakeUser';
import { Project } from '../p_gen/Project';
import { ProjectOrgStructure } from './OrgStructure';
import { getOrganzationById } from '../../shared/server/platformApi/organization';
import { IOrganization } from '../../shared/server/platformApi/interface/IOrganization';

@Entity({ schema: 'p_auth' })
export class Stakeholder {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  type?: string;

  @Column({ nullable: true })
  projectNo?: string;

  @Column({ default: true })
  isActive?: boolean;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  organizationId?: string;

  // @ManyToOne(() => Organization, { nullable: true })
  // @JoinColumn({ name: 'organizationId' })
  // organization?: Organization;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  prime?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => StakeUser, (stakeUser) => stakeUser.stakeholder)
  stakeUsers?: StakeUser[];

  @OneToMany(() => ProjectOrgStructure, (org) => org.stakeholder)
  orgSturcture?: ProjectOrgStructure[];

  organization?: IOrganization | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.organizationId) {
        const data = await getOrganzationById(this.organizationId);
        this.organization = data;
      } else {
        this.organization = null;
      }
    } catch (error) {
      this.organization = null;
    }
  }
}
