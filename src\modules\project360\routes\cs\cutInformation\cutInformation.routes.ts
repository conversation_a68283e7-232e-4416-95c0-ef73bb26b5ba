import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { CutInformation } from '../../../../../entities/p_cs/CutInformation';

const router: Router = express.Router();

const GenricController = new CrudController<CutInformation>(CutInformation);

router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, '')
);
router.get('/all', authenticateToken, (req, res) => GenricController.findAll(req, res));
router.get('/:id', authenticateToken, GenricController.findById);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'cutInformation')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'cutInformation')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'cutInformation')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'cutInformation')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'cutInformation')
);

export default router;
