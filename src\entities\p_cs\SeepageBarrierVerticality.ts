import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { SeepageBarrier } from './SeepageBarrier';
import { SBVerticalityTest } from '../p_domain/SBVerticalityTest';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class SeepageBarrierVerticality extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Seepage Barrier ID',
      fieldName: 'seepageBarrierId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column()
  seepageBarrierId?: string;

  @ManyToOne(() => SeepageBarrier, { nullable: true })
  @JoinColumn({ name: 'seepageBarrierId' })
  seepageBarrier?: SeepageBarrier;

  @ColumnInfo({
    customData: {
      name: 'Verticality Test Type',
      fieldName: 'verticalityTestTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.sb_verticality_type WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  verticalityTestTypeId?: string;

  @ManyToOne(() => SBVerticalityTest, { nullable: true })
  @JoinColumn({ name: 'verticalityTestTypeId' })
  sBVerticalityTest?: SBVerticalityTest;

  @ColumnInfo({
    customData: {
      name: 'Panel Id',
      fieldName: 'panelId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  panelId?: number;

  @ColumnInfo({
    customData: {
      name: 'Measured Panel Depth',
      fieldName: 'measuredPanelDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  measuredPanelDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Calculated Panel Depth',
      fieldName: 'calculatedPanelDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  calculatedPanelDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Measured Panel Size Identification',
      fieldName: 'measuredPanelSizeIdentification',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  measuredPanelSizeIdentification?: string;

  @ColumnInfo({
    customData: {
      name: 'Deviation from X',
      fieldName: 'deviationX',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  deviationX?: number;

  @ColumnInfo({
    customData: {
      name: 'Deviation from Y',
      fieldName: 'deviationY',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  deviationY?: number;

  @ColumnInfo({
    customData: {
      name: 'Deviation from Z',
      fieldName: 'deviationZ',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  deviationZ?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Deviation Elevation',
      fieldName: 'maximumDeviationElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumDeviationElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation Interval Range',
      fieldName: 'elevationInterval',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevationInterval?: number;

  @ColumnInfo({
    customData: {
      name: 'Distance from Closing Panel Center',
      fieldName: 'distanceClosingPanelCenter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  distanceClosingPanelCenter?: number;

  @ColumnInfo({
    customData: {
      name: 'Downward Distance from Panel Axis Center',
      fieldName: 'downwardDistancePanelAxisCenter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  downwardDistancePanelAxisCenter?: number;

  @ColumnInfo({
    customData: {
      name: 'Upward Distance from Panel Axis Center',
      fieldName: 'upwardDistancePanelAxisCenter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  upwardDistancePanelAxisCenter?: number;

  @ColumnInfo({
    customData: {
      name: 'Mode Indicator',
      fieldName: 'modeIndicator',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  modeIndicator?: string;

  @ColumnInfo({
    customData: {
      name: 'Latitude',
      fieldName: 'latitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Longitude',
      fieldName: 'longitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting',
      fieldName: 'easting',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing',
      fieldName: 'northing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Azimuth of Deviation',
      fieldName: 'azimuth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  azimuth?: number;

  @ColumnInfo({
    customData: {
      name: 'Inclination of Deviation',
      fieldName: 'inclination',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  inclination?: number;

  @ColumnInfo({
    customData: {
      name: 'Joint Id',
      fieldName: 'jointId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  JointId?: string;

  @ColumnInfo({
    customData: {
      name: 'Verticality Level',
      fieldName: 'verticalityLevel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  verticalityLevel?: string;

  @ColumnInfo({
    customData: {
      name: 'Verticality Date',
      fieldName: 'verticalityDate',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  verticalityDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
