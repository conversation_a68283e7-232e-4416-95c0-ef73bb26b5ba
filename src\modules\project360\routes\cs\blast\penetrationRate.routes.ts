import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { PenetrationRate } from '../../../../../entities/p_cs/PenetrationRate';

const router: Router = express.Router();

const GenricController = new CrudController<PenetrationRate>(PenetrationRate);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'penetrationRate')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'penetrationRate')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'penetrationRate')
);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'penetrationRate')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'penetrationRate')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'penetrationRate')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'penetrationRate')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'penetrationRate')
);

export default router;
