import { GeoJsonFeature } from '../import/importByTable/createDownloadTemplate';
import { convertEastingNorthingToLatLong } from '../spatialCoordinates/eastingAndNothingConversion';

export const point = (data: any) => {
  try {
    const geoJsonFeatures: any[] = [];
    for (let i = 0; i < data.length; i++) {
      const jsonData = data[i];
      if (!('latitude' in jsonData) || !('longitude' in jsonData)) {
        throw new Error(
          'Invalid JSON object. Please provide objects with latitude and longitude properties.'
        );
      }
      let lat = jsonData.latitude;
      let long = jsonData.longitude;
      if (!lat || !long) {
        if (jsonData.easting && jsonData.northing) {
          const out: any = convertEastingNorthingToLatLong(
            Number(jsonData.easting),
            Number(jsonData.northing)
          );
          long = out.longitude || 0;
          lat = out.latitude || 0;
        } else {
          lat = 0;
          long = 0;
        }
      }
      if (lat == 0 && long == 0) {
        continue;
      }

      delete jsonData.longitude;
      delete jsonData.latitude;
      const geoJsonFeature: GeoJsonFeature = {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [Number(long), Number(lat)],
        },
        properties: {
          ...jsonData,
        },
      };

      // Add additional properties to the GeoJSON feature
      for (const key in jsonData) {
        if (key !== 'latitude' && key !== 'longitude') {
          geoJsonFeature.properties[key] = jsonData[key];
        }
      }

      geoJsonFeatures.push(geoJsonFeature);
    }
    return geoJsonFeatures;
  } catch (error) {
    throw error;
  }
};

export const polygon = (data: any[]) => {
  try {
    const geoJsonFeatures: any[] = [];
    for (let i = 0; i < data.length; i++) {
      const jsonData = data[i];
      if (!('geom' in jsonData)) {
        throw new Error('Invalid JSON object. Please provide objects with geom properties.');
      }

      const geoJsonFeature = {
        type: 'Feature',
        geometry: jsonData.geom,
        properties: jsonData,
      };

      geoJsonFeatures.push(geoJsonFeature);
    }
    return geoJsonFeatures;
  } catch (error) {
    throw error;
  }
};
