import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilMoistureContent } from '../../../../../../entities/p_cs/StSoilMoistureContent';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilMoistureContent>(StSoilMoistureContent);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'moistureContent')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'moistureContent')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'moistureContent')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'moistureContent')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'moistureContent')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'moistureContent')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'moistureContent')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'moistureContent')
);

export default router;
