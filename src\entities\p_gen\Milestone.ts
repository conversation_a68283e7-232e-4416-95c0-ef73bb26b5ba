import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  // OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
// import { Ticket } from '../g_supp/Ticket';

@Entity({ schema: 'p_gen' })
export class Milestone {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column('varchar')
  name?: string;

  @Column('timestamp')
  targetDate?: Date;

  @Column('varchar')
  description?: string;

  @Column('varchar')
  status?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  // @OneToMany(() => Ticket, (ticket) => ticket.milestone)
  // ticket?: Ticket[];
}
