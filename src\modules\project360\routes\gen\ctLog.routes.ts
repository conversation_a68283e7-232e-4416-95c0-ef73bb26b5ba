import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { CTLog } from '../../../../entities/p_gen/CTLog';
import CTLogController from '../../controllers/gen/ctLog.controller';

const router: Router = express.Router();

const GenericController = new CrudController<CTLog>(CTLog);
const controller = new CTLogController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenericController.findByProjectId(req, res, 'ctLog')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenericController.getDataCountByProjectId(req, res, 'ctLog')
);
router.get('/by/project/:projectId/purpose/:purposeId', authenticateToken, (req, res) =>
  controller.getNextCTNoByProjectIdAndPurposedId(req, res)
);
router.get('/test/summary/:id', authenticateToken, (req, res) =>
  controller.getTestSummary(req, res)
);
router.get('/:id', authenticateToken, GenericController.findById);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenericController.sendForApproval(req, res, 'ctLog')
);
router.post('/', authenticateToken, (req, res) => GenericController.create(req, res, 'ctLog'));
router.post('/multiple/add', authenticateToken, (req, res) =>
  controller.addMultipleTests(req, res)
);
router.put('/:id', authenticateToken, (req, res) => GenericController.update(req, res, 'ctLog'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenericController.multiSoftDelete(req, res, 'ctLog')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res, 'ctLog')
);

export default router;
