import { Request, Response } from 'express';
import importSpeceficFunction from '../../../shared/utilities/import/importSpeceficFunction';
import CreateTemplate from '../../../shared/utilities/import/importByTable/createDownloadTemplate';
import {
  checkWorkSheet,
  extractDataFromExcel,
  extractDataFromExcelByTable,
} from '../../../shared/utilities/import/getValuesFromExcel';
import ImportData from '../../../shared/utilities/import/importByTable/importData';

import GetData from '../../../shared/utilities/import/importByTable/getData';

import subModuleModel from '../models/meta/dce.model';

import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import { EntityListInterface, entityList } from '../../../shared/utilities/entity/entityList';
import { entityRelationList } from '../../../shared/utilities/entity/entityRelation';
import projectModel from '../models/project.model';
import { camelCaseToTitleCase } from '../../../shared/utilities/custom/caseConversion';
import CtLog from '../models/gen/ctLog.model';
import { parse } from 'csv-parse/sync';
interface UploadedFile {
  name: string;
  data: Buffer;
  mimetype: string;
  size: number;
}
class ImportController {
  constructor() {}

  async importBasedOnSampleType(req: Request, res: Response) {
    try {
      const file = req.files?.excel;
      const name = (file as any).name.split('.')[0];

      if ((file as any).name.split('.')[1] !== 'xlsx') {
        const message = req.__('InvalidImportFile');
        return res.status(400).json({ isSucceed: false, data: [], msg: message });
      }
      if (req.files?.excel) {
        const data = [];
        if (name == 'Earthwork Field Tests') {
          const densityTestData = await importSpeceficFunction.earthWorkForNCAndSC(
            (req.files.excel as any).data,
            req.body.projectId
          );
          data.push(densityTestData);
        } else if (name == 'Earthwork Lab Tests') {
          const densityTestData = await importSpeceficFunction.earthWork(
            (req.files.excel as any).data,
            req.body.projectId
          );
          data.push(densityTestData);
        } else if (name == 'Water Tests') {
          const densityTestData = await importSpeceficFunction.water(
            (req.files.excel as any).data,
            req.body.projectId
          );
          data.push(densityTestData);
        } else {
          const message = req.__('InvalidImportFile');
          return res.status(400).json({ isSucceed: false, data: [], msg: message });
        }
        return res.status(200).json({ isSucceed: true, data: data, msg: 'import success' });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  createTemplateByTable = async (req: Request, res: Response) => {
    try {
      const entityFromReq = req.params.entity;
      const { projectId } = req.params;
      const keyVariable: string = entityFromReq;

      const fileName = camelCaseToTitleCase(req.params.entity);
      if (keyVariable in entityList) {
        const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
        // Now 'entityValue' holds the entity type corresponding to the keyVariable
        const createTemplateObj = new CreateTemplate(entityValue);
        const excel = await createTemplateObj.createExcel(
          fileName,
          entityFromReq,
          projectId,
          (req as any).user.id
        );
        res.setHeader(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );
        res.setHeader('Content-Disposition', `attachment; filename=${fileName} template.xlsx`);

        res.send(excel);
      } else {
        throw new Error(`Key '${keyVariable}' not found in the object.`);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  async downloadData(req: Request, res: Response) {
    try {
      const entityFromReq = req.params.entity;
      const keyVariable: string = entityFromReq;
      const projectId = req.params.id;
      const projectDetails = await projectModel.getProjectId(projectId);
      const fileName = camelCaseToTitleCase(req.params.entity);
      if (keyVariable in entityList) {
        const entityValue: any = entityList[keyVariable as keyof EntityListInterface];

        // Now 'entityValue' holds the entity type corresponding to the keyVariable
        const createTemplateObj = new CreateTemplate(entityValue);
        const excel = await createTemplateObj.createDownloadExcel(
          projectId,
          keyVariable,
          entityValue,
          (req as any).user,
          req.query
        );

        res.setHeader(
          'Content-Type',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        );
        const currentDate = new Date();
        const year = currentDate.getFullYear() % 100;
        const month = currentDate.getMonth() + 1;
        const day = currentDate.getDate();
        const hours = currentDate.getHours();
        const minutes = currentDate.getMinutes();
        const seconds = currentDate.getSeconds();
        res.setHeader(
          'Content-Disposition',
          `attachment; filename=${
            projectDetails?.name || 'project'
          } ${fileName} export data ${day}-${month}-20${year} ${hours}:${minutes}:${seconds}.xlsx`
        );
        res.send(excel);
      } else {
        throw new Error(`Key '${keyVariable}' not found in the object.`);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async downloadDataAsGeoJson(req: Request, res: Response) {
    try {
      const entityFromReq = req.params.entity;
      const keyVariable: string = entityFromReq;
      const projectId = req.params.id;

      if (keyVariable in entityList) {
        const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
        // Now 'entityValue' holds the entity type corresponding to the keyVariable
        const createTemplateObj = new CreateTemplate(entityValue);

        const data = await createTemplateObj.getData(projectId, keyVariable, (req as any).user);

        return res.status(200).json(data);
      } else {
        throw new Error(`Key '${keyVariable}' not found in the object.`);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  importByTable = async (req: Request, res: Response) => {
    try {
      if (req.files?.excel) {
        const entityFromReq = req.params.entity;
        const keyVariable: string = entityFromReq;
        const fileName = camelCaseToTitleCase(req.params.entity);

        const worksheetCheck = await checkWorkSheet((req.files.excel as any).data, fileName);
        if (!worksheetCheck) {
          const message = req.__('InvalidImportFile');
          throw new Error(message);
        }
        if (keyVariable in entityList) {
          const entityData = await subModuleModel.findByEntity(keyVariable);
          const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
          const dataFromExcel = await extractDataFromExcelByTable((req.files.excel as any).data);

          const importObj = new ImportData(entityValue);
          if (entityData?.id) {
            let excel: any = [];
            if (req.body.submittal) {
              excel = await importObj.addData(
                dataFromExcel,
                req.body.projectId,
                (req as any).user.name,
                (req as any).user.id,
                keyVariable,
                (req.files.excel as any).name,
                req.query?.sampleId as string,
                req.body.submittal.submittal,
                req.body.submittal.submitalVersion
              );
            } else {
              excel = await importObj.addData(
                dataFromExcel,
                req.body.projectId,
                (req as any).user.name,
                (req as any).user.id,
                keyVariable,
                (req.files.excel as any).name,
                req.query?.sampleId as string
              );
            }

            const message = req.__('DataInputSuccess');
            res.send({ isSucceed: true, data: excel, msg: message });
          } else {
            throw new Error(`Key '${keyVariable}' not found in the object.`);
          }
        } else {
          throw new Error(`Key '${keyVariable}' not found in the object.`);
        }
      } else {
        const message = req.__('InvalidImportFile');
        throw new Error(message);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  async importByRawDataTable(req: Request, res: Response) {
    try {
      if (!req.body.projectId) {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'Invaild request body' });
      }
      if (!Array.isArray(req.body.data)) {
        return res.status(400).json({ isSucceed: false, data: [], msg: 'Invaild request body' });
      }
      const entityFromReq = req.params.entity;
      const keyVariable: string = entityFromReq;
      if (keyVariable in entityList) {
        const entityData = await subModuleModel.findByEntity(keyVariable);
        const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
        const data = req.body.data;
        const importObj = new ImportData(entityValue);
        if (entityData?.id) {
          let excel: any = [];
          if (req.body.submittal) {
            excel = await importObj.addData(
              data,
              req.body.projectId,
              (req as any).user.name,
              keyVariable,
              req.body.submittal.submittal,
              req.body.submittal.submitalVersion
            );
          } else {
            excel = await importObj.addData(
              data,
              req.body.projectId,
              (req as any).user.name,
              (req as any).user.id,
              keyVariable,
              (req.files?.excel as any).name
            );
          }

          const message = req.__('DataInputSuccess');
          res.send({ isSucceed: true, data: excel, msg: message });
        } else {
          throw new Error(`Key '${keyVariable}' not found in the object.`);
        }
      } else {
        throw new Error(`Key '${keyVariable}' not found in the object.`);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getData(req: Request, res: Response) {
    try {
      if (req.params.entity && req.params.id) {
        const entityFromReq = req.params.entity;
        const keyVariable: string = entityFromReq;
        if (keyVariable in entityList) {
          const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
          const getDataClassObj = new GetData(entityValue);
          if (keyVariable in entityList) {
            const result = await getDataClassObj.getFromDb(
              'projectId',
              req.params.id,
              entityRelationList[keyVariable].relation
            );

            const message = req.__('DataFoundMessage');
            res.send({ isSucceed: true, data: result, msg: message });
          }
        } else {
          throw new Error(`Key '${keyVariable}' not found in the object.`);
        }
      } else {
        throw new Error(`Key  not found in the object.`);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  /**
   * Processes an uploaded Excel file to update CT-related DCE records by project ID.
   * Parses, filters, and updates multiple tables based on matching CT Numbers.
   */
  async updateDCEByExcelData(req: Request, res: Response): Promise<any> {
    if (!req.files?.excel) {
      return res.status(400).json({ isSucceed: false, msg: 'Excel file is required.' });
    }

    const projectId = req.params.projectId;
    const file = req.files.excel as UploadedFile;
    const buffer = file.data;
    const fileName = file.name;
    try {
      // Parse Excel data into an array of objects using headers from the first row
      let rawRows: Record<string, string>[] = [];
      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        rawRows = await extractDataFromExcel(buffer);
      } else if (fileName.endsWith('.csv')) {
        rawRows = parse(buffer, {
          columns: true,
          skip_empty_lines: true,
          trim: true,
        });
      } else {
        return res.status(400).json({
          isSucceed: false,
          msg: 'Unsupported file format. Please upload a CSV or Excel file.',
        });
      }
      const data = await CtLog.findCtnumberbyExcelDataAndUpdateDceTables(rawRows, projectId);

      if (data) {
        console.log('CT updated successfully in all three tables for your FYI:');
        return res.status(200).json({
          isSucceed: true,
          msg: 'Processed successfully.',
          data: data,
        });
      }

      return res.status(400).json({
        isSucceed: false,
        msg: 'Failed to process file.',
        data: data,
      });
    } catch (err) {
      console.error('CSV parse error:', err);
      return res.status(500).json({
        isSucceed: false,
        msg: 'Failed to process file.',
      });
    }
  }
}

const importController = new ImportController();
export default importController;
