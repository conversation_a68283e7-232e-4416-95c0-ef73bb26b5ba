import { Request, Response } from 'express';
import ProjectActivityModel from '../models/projectActivity.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import filterByUpdateAt from '../../../shared/utilities/custom/filterDataByUpdateAt';
import getMobleAccessByUserAndProject from '@utils/role/getMobleAccessByUserAndProject';
import { checkIsSuperAdmin } from 'src/shared/server/platformApi/role';

class ProjectActivityController {
  async getByProjectId(req: Request, res: Response) {
    try {
      const { type, updatedAfter } = req.query;
      const projectActivityModel = new ProjectActivityModel();

      // Step 1: Fetch raw activity data by project ID
      const rawData = await projectActivityModel.getByProjectId(req.params.id);
      const message = req.__('DataFoundMessage');

      // Step 2: If mobile type, apply access-based filtering
      if (type === 'mobile') {
        // Fetch access permissions (contains dceIds and formIds)
        const access = await getMobleAccessByUserAndProject(req.params.id, (req as any).user.id);

        const dceIdSet = new Set(access.dceIds || []);
        const formIdSet = new Set(access.formIds || []);
        const isAdmin = await checkIsSuperAdmin((req as any).user.id);

        // Step 3: Filter mobile-compatible activities and access-based DCEs
        const mobileData = rawData
          .map((activity) => {
            const filteredDCEs = isAdmin
              ? activity.dataCaptureElements?.filter((el) => el.isMobile) || []
              : activity.dataCaptureElements?.filter((el) => el.isMobile && dceIdSet.has(el.id)) ||
                [];

            const filteredForms = isAdmin
              ? activity.forms || []
              : activity.forms?.filter((form) => formIdSet.has(form.id)) || [];

            return {
              ...activity,
              dataCaptureElements: filteredDCEs,
              forms: filteredForms,
            };
          })
          .filter(
            (activity) => activity.dataCaptureElements.length > 0 || activity.forms.length > 0
          );

        // Step 4: Filter forms and separate them into customForm & multipleDCE
        const finalData = mobileData.map(({ forms = [], ...rest }) => {
          const customForm = [];
          const multipleDCE = [];

          for (const form of forms) {
            const { id, isCustom, isMultiple, name } = form;
            const formData = { id, isCustom, isMultiple, name };
            if (isCustom) customForm.push(formData);
            if (isMultiple) multipleDCE.push(formData);
          }

          return { ...rest, customForm, multipleDCE };
        });

        // Step 5: Optionally filter by updatedAfter timestamp
        const resultData = updatedAfter
          ? filterByUpdateAt(new Date(updatedAfter as string), finalData)
          : finalData;

        return res.status(200).json({
          isSucceed: true,
          data: resultData,
          msg: message,
        });
      }

      // Default case: return full data
      return res.status(200).json({
        isSucceed: true,
        data: rawData,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addActivityWithDCE(req: Request, res: Response) {
    try {
      const { activity, dceIds, formIds } = req.body;
      const projectActivityModel = new ProjectActivityModel();
      if ((req as any).user) {
        activity.createdBy = (req as any).user.name;
        activity.updatedBy = (req as any).user.name;
        activity.createdUserId = (req as any).user.id;
      }
      const result = await projectActivityModel.addActivityWithDCE(activity, dceIds, formIds);
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: result,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async update(req: Request, res: Response) {
    try {
      const { activity, dceIds, formIds } = req.body;
      const { id } = req.params;
      if ((req as any).user) {
        activity.updatedBy = (req as any).user.name;
      }
      const projectActivityModel = new ProjectActivityModel();
      const result = await projectActivityModel.update(id, activity, dceIds, formIds);
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: result,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default ProjectActivityController;
