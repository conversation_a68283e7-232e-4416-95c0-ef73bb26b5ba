import { getManager, Between } from 'typeorm';
import { PvCutMaster } from '@entities/p_cs/PvCutMaster';

export const pvCutMasterResolvers = {
  Query: {
    pvCutMasterById: async (_: any, { id }: { id: string }) => {
      const repository = getManager().getRepository(PvCutMaster);
      return repository.findOne({
        where: { id },
        relations: ['project', 'purpose', 'area', 'approvalStatus', 'cutInformation'],
      });
    },
    pvCutMastersByProject: async (
      _: any,
      {
        projectId,
        startDate,
        endDate,
        dateField = 'panelStartDate',
      }: {
        projectId: string;
        startDate?: string;
        endDate?: string;
        dateField?: string;
      }
    ) => {
      const repository = getManager().getRepository(PvCutMaster);
      const whereClause: any = {
        projectId,
        isDelete: false,
      };

      // Add date filtering if both dates are provided
      if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        whereClause[dateField] = Between(start, end);
      }

      return repository.find({
        where: whereClause,
        relations: ['project', 'purpose', 'area', 'approvalStatus', 'cutInformation'],
        order: { stationStart: 'ASC' },
      });
    },
  },
};
