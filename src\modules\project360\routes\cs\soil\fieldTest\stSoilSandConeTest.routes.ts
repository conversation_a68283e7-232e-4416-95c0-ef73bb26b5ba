import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilSandConeTest } from '../../../../../../entities/p_cs/StSoilSandConeTest';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilSandConeTest>(StSoilSandConeTest);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'sandConeTest')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'sandConeTest')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'sandConeTest')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'sandConeTest')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'sandConeTest')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'sandConeTest')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'sandConeTest')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'sandConeTest')
);

export default router;
