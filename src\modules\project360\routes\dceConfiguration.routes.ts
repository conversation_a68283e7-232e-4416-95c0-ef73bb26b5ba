import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import GeoJsonConfigurationController from '../controllers/dceConfiguration.controller';
import { dceConfiguration } from '../../../entities/p_gen/dceConfiguration';
import CrudController from '../../generic/crudDriver.controller';
import { dceConfigEditMiddleware } from '../../../shared/middlewares/dceConfigEdit.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const crud = new CrudController<dceConfiguration>(dceConfiguration);
const dceConfigController = new GeoJsonConfigurationController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/dce/project/:id/:projectId', authenticateToken, dceConfigController.getData);
router.get(
  '/by/entity/project/:entity/:projectId',
  authenticateToken,
  dceConfigController.getDataByEntity
);
router.put('/:id', authenticateToken, dceConfigEditMiddleware, (req, res) => crud.update(req, res));
router.get('/:id', authenticateToken, dceConfigController.getById);
router.patch('/change/order', authenticateToken, dceConfigController.changeOrder);

export default router;
