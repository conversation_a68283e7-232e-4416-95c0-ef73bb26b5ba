import keyValueStoreModel from '@models/meta/keyValueStore.model';
import { Request, Response } from 'express';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class KeyValueStoreController {
  constructor() {}

  async findBy<PERSON>ey(req: Request, res: Response) {
    try {
      const key = req.params.key;
      const keyValueStore = await keyValueStoreModel.getByKey(key);
      if (keyValueStore?.value) {
        return res.status(200).json({
          isSucceed: true,
          data: keyValueStore.value,
          msg: 'Data found',
        });
      } else {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const keyValueStoreController = new KeyValueStoreController();
export default keyValueStoreController;
