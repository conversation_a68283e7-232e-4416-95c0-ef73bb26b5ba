import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  ValueTransformer,
} from 'typeorm';

import { Project } from '../p_gen/Project';
import { Purpose } from '../p_meta/Purpose';
import { ProjectRole } from './Role';

const ArrayToStringTransformer: ValueTransformer = {
  to: (value: string[] | null) => (Array.isArray(value) ? value.join(',') : value),
  from: (value: string | null) => (value ? value.split(',') : []),
};

@Entity({ schema: 'p_auth' })
export class RoleDataAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  roleId?: string;

  @ManyToOne(() => ProjectRole, { nullable: true }) 
  @JoinColumn({ name: 'roleId' })
  projectRole?: ProjectRole;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true }) 
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  access?: string;

  @Column({ nullable: true, type: 'varchar', transformer: ArrayToStringTransformer })
  status?: string[];

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
