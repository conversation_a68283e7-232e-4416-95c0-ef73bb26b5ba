import { Request, Response } from 'express';
import testVariantModel from '../../models/meta/testVariant.model';

class TestVariantController {
  constructor() {}

  async all(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const { testVariantId } = req.query;
      let testVariantData;
      if (testVariantId) {
        testVariantData = await testVariantModel.getByTestId(testVariantId as string);
      } else {
        testVariantData = await testVariantModel.getAll();
      }
      // checking if data is found with the id
      if (testVariantData && testVariantData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testVariantData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async getByTestId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const testVariantData = await testVariantModel.getByTestId(req.params.id);
      // checking if data is found with the id
      if (testVariantData && testVariantData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testVariantData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async getByTestMethodId(req: Request, res: Response) {
    try {
      const testVariantData = await testVariantModel.getByTestMethodId(req.params.id);
      if (testVariantData && testVariantData.length > 0) {
        return res.status(200).json({
          isSucceed: true,
          data: testVariantData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }
}

const testVariantController = new TestVariantController();
export default testVariantController;
