import { Request, Response } from 'express';
import { ErrorLog } from '../../../entities/p_utils/ErrorLog';
import ErrorLogModel from '../../../modules/project360/models/log/errorLog.model';

const errorMiddleware = (error: any, status: number = 500, res: Response, req: Request) => {
  try {
    const date = new Date();
    // sendMail(
    //   `Error ${process.env.NODE_ENV}`,
    //   '<EMAIL>',
    //   `url: ${req.originalUrl},</br> body: ${JSON.stringify(
    //     req.body
    //   )},</br> qurey: ${JSON.stringify(req.query)},</br> params: ${JSON.stringify(
    //     req.params
    //   )},</br> file: ${JSON.stringify(req.file)}, </br> date: ${date},</br> error: ${
    //     error.message
    //   }</br> user: ${(req as any)?.user?.name}`
    // );
    const errorLogModel = new ErrorLogModel();
    const newErrorlog: Partial<ErrorLog> = {
      body: req.body,
      user: (req as any)?.user?.name,
      error: error.message,
      file: `${JSON.stringify(req.file)}`,
      query: JSON.stringify(req.query),
      params: JSON.stringify(req.params),
      time: date,
      url: req.originalUrl,
      method: req.method,
    };
    errorLogModel.addErrorLog(newErrorlog);
    console.error(error);
    return res.status(status).json({
      isSucceed: false,
      data: [],
      msg: error.message || 'Internal Server Error',
    });
  } catch (error) {
    console.log('Error middleware');
    console.error(error);
  }
};

export default errorMiddleware;
