import express, { Router } from 'express';

import CrudController from '../../../../generic/crudDriver.controller';
import { Vibration } from '../../../../../entities/p_cs/Vibration';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';

const router: Router = express.Router();

const GenricController = new CrudController<Vibration>(Vibration);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'vibration')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'vibration')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'vibration')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'vibration')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'vibration')
);
router.put('/:id', authenticateToken, (req, res) => GenricController.update(req, res, 'vibration'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'vibration')
);

export default router;
