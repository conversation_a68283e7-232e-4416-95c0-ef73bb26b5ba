import { getManager } from 'typeorm';
import { RelationshipType } from '../../../../entities/p_meta/RelationshipType';

class RelationshipModel {
  Repo = getManager().getRepository(RelationshipType);
  findById = async (id: string) => {
    try {
      return await this.Repo.findOne({
        where: { id, isDelete: false },
      });
    } catch (error) {
      throw error;
    }
  };
  findSampleTestRelationById = async () => {
    try {
      return await this.Repo.findOne({
        where: { isSampleTestRelation: true, isDelete: false },
      });
    } catch (error) {
      throw error;
    }
  };
  findDropdownRelationById = async () => {
    try {
      return await this.Repo.findOne({
        where: { isDropdownRelation: true, isDelete: false },
      });
    } catch (error) {
      throw error;
    }
  };
}

export default RelationshipModel;
