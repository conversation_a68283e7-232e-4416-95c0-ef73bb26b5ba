import { gql } from 'apollo-server-express';

export const typeDefs = gql`
  # Custom scalar for Date handling
  scalar Date

  type PvCLSMPlacement {
    id: ID!
    projectId: String
    project: Project
    uniqueLabel: String
    area: String
    technology: String
    mixDesign: String
    stationStart: String
    stationEnd: String
    northingStart: Float
    eastingStart: Float
    northingEnd: Float
    eastingEnd: Float
    averageCLSMTopElevation: Float
    numSamples: Int
    castAmount: Float
    castArea: Float
    sampleSet1: Boolean
    sampleSet1Station: String
    sampleSet2: Boolean
    sampleSet2Station: String
    castDate: Date
    testId: String
    comments: String
    purposeId: String
    purpose: Purpose
    qcVerifier: String
    approvalStatusId: String
    qcDate: Date
    createdAt: Date
    updatedAt: Date
    longitudeStart: String
    latitudeStart: String
    longitudeEnd: String
    latitudeEnd: String
  }

  extend type Query {
    pvCLSMPlacementById(id: ID!): PvCLSMPlacement
    pvCLSMPlacementsByProject(
      projectId: ID!
      startDate: String
      endDate: String
      dateField: String
    ): [PvCLSMPlacement]
  }
`;
