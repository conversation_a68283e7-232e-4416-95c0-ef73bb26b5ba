import { get<PERSON>ana<PERSON>, In, IsNull, Not } from 'typeorm';
import { DataCaptureElemetRelation } from '../../../../entities/p_meta/DataCaptureElemetRelation';
import projectModel from '../project.model';
import {
  getDropdownByTable,
  getDropdownByTableWithParent,
} from '../../../../shared/utilities/dropdown/getDropdownByTable';
import getCustomDropdownLinks from '../../../../shared/utilities/dropdown/getCustomDropdowLinks';
import getCustomDropdownValues from '../../../../shared/utilities/dropdown/getCustomDropdownValues';
import projectRoleModel from '../role and permission/projectRole.model';
import hasParentCheck from '@utils/dropdown/hasParentCheck';
import dceModel from './dce.model';

class DCERelationModel {
  private Repository = getManager().getRepository(DataCaptureElemetRelation);
  async getDataByDceRelationId(
    dceId: string,
    columnName: string,
    params: any,
    userId: string,
    view?: string
  ) {
    try {
      let relationData = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
      });
      if (!relationData) {
        relationData = await this.Repository.findOne({
          where: { subDceId: dceId, columnName, isDelete: false },
        });
      }
      if (relationData?.custom) {
        return (await getCustomDropdownValues(relationData.custom, params, '', userId)) || [];
      }

      let routeParams = relationData?.routeParams ? relationData.routeParams.split(',') : [];
      if (columnName === 'testId') {
        routeParams.push('dceId');
        params['dceId'] = dceId;
      }
      // If the query params contain placeholder value ignore that routeParam
      routeParams = routeParams.filter((param) => {
        return !(params[param] && /^\{.*\}$/.test(params[param])); // Remove if value is wrapped in {}
      });

      const additionalParams = relationData?.additionalParams?.split(',');
      const defaultParams = this.parseStringToArray(relationData?.defaultValue || '');
      Object.keys(params).filter((value) => routeParams?.includes(value));
      if (relationData) {
        const rd = relationData;
        let query = '';
        let joinClause = '';

        let selectClause = '';
        if (relationData.foreignKeyMapping) {
          const relation = relationData?.foreignKeyMapping?.table;
          const column = relationData?.relatedColumnToDisplay;
          const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
          joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${rd.relatedTableName}."${joinColumn}"`;
          selectClause = `${relation}.${column} AS name`;
        } else {
          selectClause = `${relationData.relatedColumnToDisplay} AS name`;
        }
        query = `SELECT ${rd.relatedTableName}.id AS value, ${selectClause}
         FROM ${relationData.relatedTableName} ${joinClause}`;
        let whereCondition = 'WHERE';
        if (routeParams && routeParams?.length > 0) {
          routeParams.map((value, index) => {
            if (!relationData) return;
            let condition = `${rd.relatedTableName}."${value}" = '${params[value]}'`;
            if (index != routeParams.length - 1) {
              condition = `${condition} AND`;
            }
            whereCondition = `${whereCondition} ${condition}`;
          });
        }

        if (params?.projectId) {
          const project: any = await projectModel.getProjectId(params.projectId);
          if (additionalParams && additionalParams.length > 0) {
            additionalParams.map((value, index) => {
              if (project && value in project) {
                let condition = `${rd.relatedTableName}."${value}" = '${project[value]}'`;
                if (index != additionalParams.length - 1) {
                  condition = `${condition} AND`;
                }
                if (whereCondition !== 'WHERE') {
                  whereCondition = `${whereCondition} AND ${condition}`;
                } else {
                  whereCondition = `${whereCondition} ${condition}`;
                }
              }
            });
          }
        }

        if (defaultParams.length > 0) {
          defaultParams.forEach((value) => {
            const fullColumnName = `${rd.relatedTableName}."${value.item}"`;
            if (whereCondition !== 'WHERE') {
              whereCondition = `${whereCondition} AND ${fullColumnName} = '${value.value}'`;
            } else {
              whereCondition = `${whereCondition} ${fullColumnName} = '${value.value}'`;
            }
          });
        }
        if (params?.projectId) {
          if (relationData.relatedDceId) {
            const purpsoe = await projectRoleModel.getByAccessPurposeIds(
              params?.projectId,
              userId,
              'Full Access',
              view
            );
            if (purpsoe.length <= 0) {
              return [];
            }
            if (whereCondition !== 'WHERE') {
              whereCondition = `${whereCondition} AND ${
                rd.relatedTableName
              }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
            } else {
              whereCondition = `${whereCondition} AND  ${
                rd.relatedTableName
              }."purposeId" IN (${purpsoe.map((value) => `'${value}'`)})`;
            }
          }
        }

        if (whereCondition !== 'WHERE') {
          query = `${query} ${whereCondition} AND ${rd.relatedTableName}."isDelete" = false`;
        } else {
          query = `${query} WHERE ${rd.relatedTableName}."isDelete" = false`;
        }

        // Sort in Ascending order
        query = `${query} ORDER BY name ASC`;

        const out = await getManager().query(query);
        if (out && out?.length > 0) {
          return out.map((value: any) => {
            if (value?.name) {
              value.name = String(value.name);
            }
            return value;
          });
        }
        return out;
      }
    } catch (error) {
      throw error;
    }
  }

  async getDataByDceRelationIdSingleData(
    dceId: string,
    columnName: string,
    id: any,
    userId: string
  ) {
    try {
      const relationData = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
      });
      if (relationData?.custom && relationData?.custom !== 'purposeByUser') {
        return (await getCustomDropdownValues(relationData.custom, {}, id, userId)) || [];
      }
      if (relationData) {
        let joinClause = '';
        let selectClause = '';

        if (relationData.foreignKeyMapping) {
          const relation = relationData?.foreignKeyMapping?.table;
          const column = relationData?.relatedColumnToDisplay;
          const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
          joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
          selectClause = `${relation}.${column} AS name`;
        } else {
          selectClause = `${relationData.relatedColumnToDisplay} AS name`;
        }

        const query = `SELECT ${relationData.relatedTableName}.id as value, ${selectClause} FROM ${relationData.relatedTableName} ${joinClause} WHERE ${relationData.relatedTableName}."isDelete" = false AND ${relationData.relatedTableName}.id = '${id}'`;

        const out = getManager().query(query);

        return out;
      }
    } catch (error) {
      throw error;
    }
  }
  async getDataByDceRelationIdSingleDataForSub(
    dceId: string,
    columnName: string,
    id: any,
    userId: string
  ) {
    try {
      const relationData = await this.Repository.findOne({
        where: { subDceId: dceId, columnName, isDelete: false },
      });
      if (relationData?.custom && relationData?.custom !== 'purposeByUser') {
        return (await getCustomDropdownValues(relationData.custom, {}, id, userId)) || [];
      }
      if (relationData) {
        let joinClause = '';
        let selectClause = '';

        if (relationData.foreignKeyMapping) {
          const relation = relationData?.foreignKeyMapping?.table;
          const column = relationData?.relatedColumnToDisplay;
          const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
          joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
          selectClause = `${relation}.${column} AS name`;
        } else {
          selectClause = `${relationData.relatedColumnToDisplay} AS name`;
        }

        const query = `SELECT ${relationData.relatedTableName}.id as value, ${selectClause} FROM ${relationData.relatedTableName} ${joinClause} WHERE ${relationData.relatedTableName}."isDelete" = false AND ${relationData.relatedTableName}.id = '${id}'`;

        const out = getManager().query(query);

        return out;
      }
    } catch (error) {
      throw error;
    }
  }
  async getDataForImport(
    dceId: string,
    columnName: string,
    name: string,
    purposeId: string,
    projectId: string
  ) {
    try {
      const relationData = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
      });
      if (relationData?.custom && relationData?.custom !== 'purposeByUser') {
        const customData =
          (await getCustomDropdownValues(relationData?.custom, {
            projectId: projectId,
          })) || [];
        if (!Array.isArray(customData)) {
          return '';
        }
        return customData.find((value) => value.name === name)?.value || '';
      }
      const checkPurposeExisit: [{ columnExist: boolean }] = await getManager().query(
        `SELECT
          EXISTS (
          SELECT 1
            FROM
              information_schema.columns
            WHERE
              table_schema = '${relationData?.relatedTableName?.split('.')?.[0]}'
            AND 
              table_name = '${relationData?.relatedTableName?.split('.')?.[1]}'
            AND
              column_name = 'purposeId'
        ) AS columnExist`
      );
      const projectIdExisit: [{ columnExist: boolean }] = await getManager().query(
        `SELECT
          EXISTS (
          SELECT 1
            FROM
              information_schema.columns
            WHERE
              table_schema = '${relationData?.relatedTableName?.split('.')?.[0]}'
            AND 
              table_name = '${relationData?.relatedTableName?.split('.')?.[1]}'
            AND
              column_name = 'projectId'
        ) AS columnExist`
      );
      if (relationData) {
        let joinClause = '';
        let selectClause = '';
        let whereClause = '';

        if (relationData.foreignKeyMapping) {
          const relation = relationData?.foreignKeyMapping?.table;
          const column = relationData?.relatedColumnToDisplay;
          const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
          joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
          selectClause = `${relationData.relatedTableName}.id as value`;
          whereClause =
            checkPurposeExisit.length > 0 && checkPurposeExisit[0].columnExist
              ? `${relationData.relatedTableName}."isDelete" = false AND LOWER(CAST(${relation}.${column} AS TEXT)) = LOWER(CAST('${name}' AS TEXT)) AND ${relationData.relatedTableName}."purposeId" = '${purposeId}'`
              : `${relationData.relatedTableName}."isDelete" = false AND LOWER(CAST(${relation}.${column} AS TEXT)) = LOWER(CAST('${name}' AS TEXT))`;
        } else {
          selectClause = `id as value`;
          whereClause =
            checkPurposeExisit.length > 0 && checkPurposeExisit[0].columnExist
              ? `"isDelete" = false AND LOWER(CAST(${relationData.relatedColumnToDisplay} AS TEXT)) = LOWER(CAST('${name}' AS TEXT)) AND "purposeId" = '${purposeId}'`
              : `"isDelete" = false AND LOWER(CAST(${relationData.relatedColumnToDisplay} AS TEXT)) = LOWER(CAST('${name}' AS TEXT))`;
        }

        if (projectIdExisit.length > 0 && projectIdExisit[0].columnExist) {
          whereClause = `${whereClause} AND ${relationData.relatedTableName}."projectId" = '${projectId}'`;
        }

        const query = `SELECT ${selectClause} FROM ${relationData.relatedTableName} ${joinClause} WHERE ${whereClause} LIMIT 1`;

        const out = await getManager().query(query);
        if (out.length <= 0) {
          return '';
        }
        return out[0].value;
      }
    } catch (error) {
      throw error;
    }
  }
  async getDataByDceRelationIdMulit(dceId: string, columnName: string, id: string[]) {
    try {
      const relationData = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
      });
      // if (relationData?.custom) {
      //   return (await getCustomDropdownValues(relationData.custom, {}, id)) || [];
      // }
      if (relationData) {
        let joinClause = '';
        let selectClause = '';

        if (relationData.foreignKeyMapping) {
          const relation = relationData?.foreignKeyMapping?.table;
          const column = relationData?.relatedColumnToDisplay;
          const joinColumn = relationData?.foreignKeyMapping?.joinColumn;
          joinClause = `LEFT JOIN ${relation} ON ${relation}.id = ${relationData.relatedTableName}."${joinColumn}"`;
          selectClause = `${relation}.${column} AS name`;
        } else {
          selectClause = `${relationData.relatedColumnToDisplay} AS name`;
        }

        const query = `SELECT ${relationData.relatedTableName}.id as value, ${selectClause} FROM ${
          relationData.relatedTableName
        } ${joinClause} WHERE ${relationData.relatedTableName}."isDelete" = false AND ${
          relationData.relatedTableName
        }.id IN (${id.map((item) => `'${item}'`).join(', ')})`;

        const out = getManager().query(query);

        return out;
      }
    } catch (error) {
      throw error;
    }
  }

  parseStringToArray = (input: string) => {
    if (!input || input == '') {
      return [];
    }
    return input.split(',').map((pair) => {
      const [item, value] = pair.split('=');
      return { item, value };
    });
  };

  getDropdownByProject = async (projectId: string, userId: string, updatedAt?: Date) => {
    try {
      const allRelations = await this.Repository.find({ where: { isDelete: false } });
      if (allRelations.length <= 0) {
        throw new Error('Dce relation not found');
      }
      const allDropdowns: {
        id: string;
        parentId?: string | null;
        dropdown: { value: string; name: string }[];
      }[] = [];
      for (const item of allRelations) {
        const hasParent = hasParentCheck(item?.routeParams?.split(',') || []);
        if (!hasParent.hasParent) {
          const id = `${item.columnName}.${item.relatedTableName}`;
          if (!allDropdowns.find((value) => value.id === id)) {
            const dropdown = item.custom
              ? await getCustomDropdownValues(item.custom, { projectId }, '', userId)
              : await getDropdownByTable(item, projectId, userId, updatedAt);
            const dropdownAsString: { value: string; name: string }[] = dropdown
              .map(
                (item: {
                  value: string;
                  name: string | null;
                  children?: any[];
                  parentId: string | null;
                }) => {
                  if (item.children && item.children.length > 0) {
                    return {
                      value: String(item.value),
                      name: item.name !== null ? String(item.name) : null,
                      children: item.children,
                      parentId: null,
                    };
                  }
                  return {
                    value: String(item.value),
                    name: item.name !== null ? String(item.name) : null,
                    parentId: null,
                  };
                }
              )
              .filter(
                (item: {
                  value: string;
                  name: string | null;
                  children?: any[];
                  parentId: string | null;
                }): item is {
                  value: string;
                  name: string | null;
                  children?: any[];
                  parentId: string | null;
                } => item.name !== null
              );
            const obj: {
              id: string;
              parentId?: string | null;
              dropdown: { value: string; name: string }[];
            } = {
              id,
              parentId: null,
              dropdown: dropdownAsString,
            };
            allDropdowns.push(obj);
          }
        } else {
          if (hasParent.routes === '') {
            continue;
          }
          const id = `${item.columnName}.${item.relatedTableName}.${hasParent.routes}`;
          const check = allDropdowns.find((value) => value.id === id);
          if (!check) {
            const dropdown = item.custom
              ? await getCustomDropdownValues(item.custom, { projectId }, '', userId)
              : await getDropdownByTableWithParent(
                  item,
                  projectId,
                  userId,
                  hasParent.routes,
                  updatedAt
                );
            const dropdownAsString: { value: string; name: string; parentValue: string }[] =
              dropdown
                .map((item: { value: string; name: string | null; parentValue: string }) => ({
                  value: String(item.value),
                  name: item.name !== null ? String(item.name) : null,
                  parentValue: item.parentValue,
                }))
                .filter(
                  (item: {
                    value: string;
                    name: string;
                    parentValue: string;
                  }): item is { value: string; name: string; parentValue: string } =>
                    item.name !== null
                );
            const parentId = await dceModel.getForeignKeyRelatedTable(
              item.relatedTableName || '',
              hasParent.routes
            );
            if (parentId) {
              const obj: {
                id: string;
                parentId: string;
                dropdown: { value: string; name: string }[];
              } = {
                id,
                parentId,
                dropdown: dropdownAsString,
              };
              allDropdowns.push(obj);
            }
          }
        }
      }
      return allDropdowns.filter((value) => value.dropdown.length > 0);
    } catch (error) {
      throw error;
    }
  };
  getDropdownByDce = async (dceId: string, projectId: string, userId: string) => {
    try {
      // Getting dcerelation with dceid
      const allRelations = await this.Repository.find({
        where: { isDelete: false, dceId },
      });
      if (allRelations.length <= 0) {
        return {};
      }
      const allDropdowns: { [key: string]: string[] } = {};
      // Getting dropdown values for each column in dcerelation
      for (const item of allRelations) {
        const dropdown: { name: string }[] = item.custom
          ? await getCustomDropdownValues(item.custom, { projectId }, '', userId)
          : await getDropdownByTable(item, projectId, userId);
        if (!dropdown) {
          return {};
        }
        if (item.columnName) {
          allDropdowns[item.columnName] = dropdown.map((option) => option.name);
        }
      }
      return allDropdowns;
    } catch (error) {
      throw error;
    }
  };

  async getDropdownLinkByDceRelationId(dceId: string, columnName: string) {
    try {
      const relationData = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
      });
      if (relationData?.custom) {
        return getCustomDropdownLinks(relationData.custom) || '';
      }
      const paramsNeeded = relationData?.routeParams?.split(',');

      let query = '';

      paramsNeeded?.map((value, index) => {
        if (index == 0) {
          query = `${value}={${value}}`;
        } else {
          query = `${query}&${value}={${value}}`;
        }
      });
      if (query != '') {
        const link = `${process.env.BACKEND_BASE_URL}/project360/meta/relation/dce/get/dropdown/${dceId}/${columnName}?${query}`;
        return link;
      } else {
        const link = `${process.env.BACKEND_BASE_URL}/project360/meta/relation/dce/get/dropdown/${dceId}/${columnName}`;
        return link;
      }
    } catch (error) {
      throw error;
    }
  }
  async getDropdownLinkByDceRelationIdForSubDCE(subDceId: string, columnName: string) {
    try {
      const relationData = await this.Repository.findOne({
        where: { subDceId, columnName, isDelete: false },
      });
      const paramsNeeded = relationData?.routeParams?.split(',');

      let query = '';

      paramsNeeded?.map((value, index) => {
        if (index == 0) {
          query = `${value}={${value}}`;
        } else {
          query = `${query}&${value}={${value}}`;
        }
      });
      if (query != '') {
        const link = `${process.env.BACKEND_BASE_URL}/project360/meta/relation/dce/get/dropdown/${subDceId}/${columnName}?${query}`;
        return link;
      } else {
        const link = `${process.env.BACKEND_BASE_URL}/project360/meta/relation/dce/get/dropdown/${subDceId}/${columnName}`;
        return link;
      }
    } catch (error) {
      throw error;
    }
  }

  getByDceId = async (dceId: string) => {
    try {
      return await this.Repository.find({
        where: { dceId, isDelete: false },
        relations: ['relatedDce'],
      });
    } catch (error) {
      throw error;
    }
  };

  getByAll = async () => {
    try {
      return await this.Repository.find({
        where: { isDelete: false },
        relations: ['relatedDce'],
      });
    } catch (error) {
      throw error;
    }
  };
  getByAllWithDCEByEntity = async (entities: string[]) => {
    try {
      return await this.Repository.find({
        where: { isDelete: false, dce: { entity: In(entities) } },
        relations: ['relatedDce', 'dce'],
      });
    } catch (error) {
      throw error;
    }
  };
  subDCERelationWithDCEEntity = async (entities: string[]) => {
    try {
      return await this.Repository.find({
        where: { isDelete: false, SubDce: { dce: { entity: In(entities) }, isDelete: false } },
        relations: ['relatedDce', 'dce'],
      });
    } catch (error) {
      throw error;
    }
  };

  getByDceIdAndWithRelatedDce = async (dceId: string) => {
    try {
      return await this.Repository.find({
        where: { dceId, isDelete: false, relatedDceId: Not(IsNull()) },
        relations: ['relatedDce'],
      });
    } catch (error) {
      throw error;
    }
  };
  getBySubDceId = async (subDceId: string) => {
    try {
      return await this.Repository.find({
        where: { subDceId, isDelete: false },
        relations: ['relatedDce'],
      });
    } catch (error) {
      throw error;
    }
  };

  getRelatedDceRotueByDceIdAndColumn = async (dceId: string, columnName: string) => {
    try {
      const data = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
        relations: ['relatedDce', 'relatedDce.route'],
      });
      if (!data?.relatedDceId) {
        return '';
      }
      return data?.relatedDce?.routeId;
    } catch (error) {
      throw error;
    }
  };

  getRelatedDceEntityByDceIdAndColumn = async (dceId: string, columnName: string) => {
    try {
      const data = await this.Repository.findOne({
        where: { dceId, columnName, isDelete: false },
        relations: ['relatedDce', 'relatedDce.route'],
      });
      if (!data?.relatedDceId) {
        return '';
      }
      return data?.relatedDce?.entity;
    } catch (error) {
      throw error;
    }
  };

  getColumnsByDCEId = async (dceId: string) => {
    try {
      return await this.Repository.find({
        where: { dceId, isDelete: false },
        select: ['columnName'],
      });
    } catch (error) {
      throw error;
    }
  };
}

export default DCERelationModel;
