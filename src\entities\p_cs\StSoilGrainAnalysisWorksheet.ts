import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON>One, JoinColumn } from 'typeorm';
import { StSoilGrainAnalysis } from './StSoilGrainAnalysis';
import { SieveSize } from '../p_domain/SieveSize';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';

@Entity({ schema: 'p_cs' })
export class StSoilGrainAnalysisWorksheet extends SubDCEColumns {
  @Column({ nullable: true })
  grainSizeAnalysisId?: string;

  @ManyToOne(() => StSoilGrainAnalysis, (grainAnalysis) => grainAnalysis.grainAnalysisWorksheet)
  @JoinColumn({ name: 'grainSizeAnalysisId' })
  grainSizeAnalysis?: StSoilGrainAnalysis;

  @Column({ nullable: true })
  sieveSizeId?: string;

  @ManyToOne(() => SieveSize, { nullable: true })
  @JoinColumn({ name: 'sieveSizeId' })
  sieveSize?: SieveSize;

  //TODO: Need to refactor

  @Column({ nullable: true })
  sieveNumberId?: string;

  @ManyToOne(() => SieveSize, { nullable: true })
  @JoinColumn({ name: 'sieveNumberId' })
  sieveNumber?: SieveSize;

  @Column({ type: 'decimal', nullable: true })
  sieveSizeValue?: number;

  @Column({ type: 'decimal', nullable: true })
  cumulativeWeightRetained?: number;

  @Column({ type: 'decimal', nullable: true })
  percentageRetained?: number;

  @Column({ type: 'decimal', nullable: true })
  percentagePassing?: number;

  @Column({ type: 'decimal', nullable: true })
  individualWeightRetained?: number;

  @Column({ nullable: true })
  passFail?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
