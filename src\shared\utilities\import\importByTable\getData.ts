import { DeepPartial, EntityTarget, ObjectLiteral, Repository, getConnection } from 'typeorm';

class GetData<T extends ObjectLiteral> {
  private Entity: EntityTarget<T>;
  private repository: Repository<T>;
  constructor(entity: EntityTarget<T>) {
    this.Entity = entity;
    this.repository = getConnection().getRepository(entity);
  }

  async getFromDb<K extends keyof T>(
    propertyName: K,
    propertyValue: T[K],
    relation: string[]
  ): Promise<DeepPartial<T>[]> {
    try {
      const whereCondition = { [propertyName]: propertyValue } as any;

      const output = await this.repository.find({
        where: whereCondition,
        relations: relation,
      });

      return output;
    } catch (error) {
      throw error;
    }
  }

  async getFromDbForSubmittal<K extends keyof T>(
    propertyName: string,
    propertyValue: T[K],
    relation: string[],
    tableName: string
  ) {
    try {
      if (GetData.doesColumnExist(tableName, propertyName)) {
        const whereCondition = { [propertyName]: propertyValue } as any;

        const output = await this.repository.find({
          where: whereCondition,
          relations: relation,
        });

        return output;
      }
    } catch (error) {
      throw error;
    }
  }

  static doesColumnExist(tableName: string, columnName: string): boolean {
    const connection = getConnection();
    const metadata = connection.getMetadata(tableName);
    const column = metadata.findColumnWithPropertyPath(columnName);

    return !!column;
  }
}

export default GetData;
