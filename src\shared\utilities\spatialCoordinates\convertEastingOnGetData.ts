import { convertEastingNorthingToLatLong } from './eastingAndNothingConversion';

const conversionCache: { [key: string]: { latitude: string; longitude: string } } = {};

// Function to clear the cache
const clearCache = () => {
  for (const key in conversionCache) {
    if (conversionCache.hasOwnProperty(key)) {
      delete conversionCache[key];
    }
  }
};

// Clear the cache every 24 hours (24 hours = 86400000 milliseconds)
setInterval(clearCache, 86400000);

export const convertEastingNorthingToLatLongOnGetData = (
  easting: number,
  northing: number
): { latitude: string; longitude: string } | null => {
  // Check if the result is already in the cache
  if (conversionCache[`${easting},${northing}`]) {
    return conversionCache[`${easting},${northing}`];
  }

  try {
    const res = convertEastingNorthingToLatLong(easting, northing);

    if (res && (res as any)?.latitude && (res as any)?.longitude) {
      // Store the result in the cache
      conversionCache[`${easting},${northing}`] = res as { latitude: string; longitude: string };

      return res as { latitude: string; longitude: string };
    }
    return null;
  } catch (error) {
    throw error;
  }
};
