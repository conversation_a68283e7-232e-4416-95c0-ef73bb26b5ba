import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import soilGrainAnalysisHydrometerController from '../../controllers/cs/soilGrainAnalysisHydrometerReading.controller';
import { StSoilGrainAnalysisHydrometerReadings } from '../../../../entities/p_cs/StSoilGrainAnalysisHydrometerReadings';

const router: Router = express.Router();

// Create a generic router for the User entity
const CrudFunctionController = new CrudController<StSoilGrainAnalysisHydrometerReadings>(
  StSoilGrainAnalysisHydrometerReadings
);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/', authenticateToken, soilGrainAnalysisHydrometerController.create);
router.put('/:id', authenticateToken, (req, res) => CrudFunctionController.update(req, res));
router.get('/:id', authenticateToken, soilGrainAnalysisHydrometerController.findById);
router.get(
  '/by/grainanalysis/:id',
  authenticateToken,
  soilGrainAnalysisHydrometerController.findByGrainAnalysisTestId
);
router.delete('/:id', authenticateToken, (req, res) => CrudFunctionController.softDelete(req, res));

export default router;
