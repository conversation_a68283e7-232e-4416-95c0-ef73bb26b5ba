import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { BlastType } from '../p_domain/BlastType';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { Purpose } from '../p_meta/Purpose';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class BlastReport extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Planned Blast Id',
      fieldName: 'designBlastId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  designBlastId?: string;

  @ColumnInfo({
    customData: {
      name: 'As-Built Blast Id',
      fieldName: 'finalBlastId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  finalBlastId?: string;

  @ColumnInfo({
    customData: {
      name: 'Planned/Final',
      fieldName: 'recordType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  recordType?: string;

  @ColumnInfo({
    customData: {
      name: 'Blast Date Time',
      fieldName: 'blastDateTime',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastDateTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Blast Type',
      fieldName: 'blastTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.blast_type WHERE name = $1',
      getListQuery: 'Select id, alias as name FROM p_domain.blast_type WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'blastTypeList',
    },
  })
  @Column({ nullable: true })
  blastTypeId?: string;

  @ManyToOne(() => BlastType, { nullable: true })
  @JoinColumn({ name: 'blastTypeId' })
  blastType?: BlastType;

  @ColumnInfo({
    customData: {
      name: 'No. of Holes',
      fieldName: 'noOfHoles',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  noOfHoles?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Drilled Length',
      fieldName: 'totalDrilledLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalDrilledLength?: number;

  @ColumnInfo({
    customData: {
      name: 'Burden Per Shot',
      fieldName: 'burdenPerShot',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  burdenPerShot?: number;

  @ColumnInfo({
    customData: {
      name: 'Stiffness Ratio of bench per burden',
      fieldName: 'stiffnessRatio',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  stiffnessRatio?: number;

  @ColumnInfo({
    customData: {
      name: 'Bedding Orientation Correction Factor',
      fieldName: 'beddingOrientationCorrectionFactor',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  beddingOrientationCorrectionFactor?: number;

  @ColumnInfo({
    customData: {
      name: 'Geological Condition Correction Factor',
      fieldName: 'geologicalConditionCorrectionFactor',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  geologicalConditionCorrectionFactor?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Hole Diameter',
      fieldName: 'minimumHoleDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumHoleDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Hole Diameter',
      fieldName: 'maximumHoleDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumHoleDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Hole Depth',
      fieldName: 'minimumHoleDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumHoleDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Hole Depth',
      fieldName: 'maximumHoleDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumHoleDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Weight of Explosive per Hole',
      fieldName: 'minimumWeightPerHole',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumWeightPerHole?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Weight of Explosive per Hole',
      fieldName: 'maximumWeightPerHole',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumWeightPerHole?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Spacing',
      fieldName: 'minimumSpacing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumSpacing?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Spacing',
      fieldName: 'maximumSpacing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumSpacing?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum Stem Spacing',
      fieldName: 'minimumStemSpacing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumStemSpacing?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Stem Spacing',
      fieldName: 'maximumStemSpacing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumStemSpacing?: number;

  @ColumnInfo({
    customData: {
      name: 'Stem Material',
      fieldName: 'stemMaterial',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  stemMaterial?: string;

  @ColumnInfo({
    customData: {
      name: 'Maximum Subdrilling Depth',
      fieldName: 'maximumSubdrillingDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumSubdrillingDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Primer Weight',
      fieldName: 'totalPrimerWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalPrimerWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Primer Weight Per Hole',
      fieldName: 'primerWeightPerHole',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  primerWeightPerHole?: number;

  @ColumnInfo({
    customData: {
      name: 'Minimum hole-to-hole Delay',
      fieldName: 'minimumHoleDelay',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  minimumHoleDelay?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum hole-to-hole Delay',
      fieldName: 'maximumHoleDelay',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumHoleDelay?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum Weight Of Explosives Per 8ms Delay',
      fieldName: 'maximumWeightOfExplosivesPer8msDelay',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumWeightOfExplosivesPer8msDelay?: number;

  @ColumnInfo({
    customData: {
      name: 'Maximum No. Of Holes/Decks Per 8ms Delay',
      fieldName: 'maximumNoOfHolesPer8msDelay',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumNoOfHolesPer8msDelay?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Charge/Power Factor Per Blast (lbs/BCY)',
      fieldName: 'totalChargePerBlast',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalChargePerBlast?: number;

  @ColumnInfo({
    customData: {
      name: 'Volume Excavated',
      fieldName: 'volumeExcavated',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  volumeExcavated?: number;

  @ColumnInfo({
    customData: {
      name: 'Explosives Volume Per Length of Drill Hole (SCY/LF)',
      fieldName: 'explosivesVolumePerDrillHoleLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  explosivesVolumePerDrillHoleLength?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Explosives Weight',
      fieldName: 'totalExplosivesWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalExplosivesWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Lithology Of Rock Blasted',
      fieldName: 'blastLithology',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  blastLithology?: string;

  @ColumnInfo({
    customData: {
      name: 'Specific Gravity Of Rock Material Blasted',
      fieldName: 'specificGravityOfRockMaterial',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  specificGravityOfRockMaterial?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Weight Of Explosive1 Per Foot (lbs/ft)',
      fieldName: 'totalWeightOfExplosive1PerFoot',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalWeightOfExplosive1PerFoot?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Weight Of Explosive2 Per Foot (lbs/ft)',
      fieldName: 'totalWeightOfExplosive2PerFoot',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalWeightOfExplosive2PerFoot?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Rock Volume Produced',
      fieldName: 'totalRockVolumeProduced',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalRockVolumeProduced?: number;

  @ColumnInfo({
    customData: {
      name: 'Blasting Agent 1 Diameter (In)',
      fieldName: 'blastingAgent1Diameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  blastingAgent1Diameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Blasting Agent 2 Diameter (In)',
      fieldName: 'blastingAgent2Diameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  blastingAgent2Diameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Detonator Type',
      fieldName: 'detonatorType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  detonatorType?: string;

  @ColumnInfo({
    customData: {
      name: 'Average Pre-Split Hole Angle',
      fieldName: 'averagePreSplitHoleAngle',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  averagePreSplitHoleAngle?: number;

  @ColumnInfo({
    customData: {
      name: 'Pre-Split Hole Wall Area',
      fieldName: 'PreSplitHoleWallArea',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  preSplitHoleWallArea?: number;

  @ColumnInfo({
    customData: {
      name: 'Loading Density Of Det Cord (Grains/Foot)',
      fieldName: 'loadingDensityOfDetCord',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  loadingDensityOfDetCord?: number;

  @ColumnInfo({
    customData: {
      name: 'Fragmentation Index Of Uniformity Predicted',
      fieldName: 'fragmentationIndexPredicted',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fragmentationIndexPredicted?: number;

  @ColumnInfo({
    customData: {
      name: 'Fragmentation Index Of Uniformity Model',
      fieldName: 'fragmentationIndexModel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  fragmentationIndexModel?: number;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column('geometry', { nullable: true })
  geom?: any;
}
