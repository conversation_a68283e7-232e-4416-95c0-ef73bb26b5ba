import { get<PERSON>ana<PERSON>, <PERSON><PERSON><PERSON>, Like } from 'typeorm';
import { ProjectForm } from '../../../entities/p_gen/ProjectForm';
import FormModel from './meta/form configuration/form.model';
import {
  convertDropdownToURl,
  convertDropdownToURlForMutiple,
} from '../../../shared/utilities/surveyJs/convertChoiceUrlToIdForMobile';
import DCERelationModel from './meta/dceRelation.model';
import { checkChoiceURl } from '../../../shared/utilities/surveyJs/checkChoiceUrl';
import getEntitiesFromMultipleDCEForms from '@utils/surveyJs/getEntitiesFromMultipleDCEForms';

interface FinalData {
  create: ProjectForm[];
  edit: ProjectForm[];
  view: ProjectForm[];
}

class ProjectFormModel {
  private repo = getManager().getRepository(ProjectForm);

  findByDceId = async (dceId: string, projectId: string) => {
    try {
      const dceRelaitonModel = new DCERelationModel();

      const formData = await this.repo.find({
        where: { dceId, projectId, isDelete: false },
        order: { createdAt: 'DESC' },
        relations: ['parentForm'],
      });
      const finalData: ProjectForm[] = [];
      if (formData.length > 0) {
        for (const element of formData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(element?.form, relationData, dceId);
          if (element.mode) {
            (element.mode as any) = element.mode.split(',');
          }
          if (newJson) {
            element.form = newJson;
          }
          if (element?.parentForm?.mode) {
            (element.parentForm.mode as any) = element?.parentForm?.mode.split(',');
          }
          finalData.push(element);
        }
      }
      return formData;
    } catch (error) {
      throw error;
    }
  };

  findByJsonFormId = async (id: string) => {
    try {
      const dceRelaitonModel = new DCERelationModel();

      const formData = await this.repo.findOne({
        where: { id, isDelete: false },
      });
      if (formData) {
        const relationData = await dceRelaitonModel.getByDceId(formData.dceId || '');
        const newJson = await checkChoiceURl(formData?.form, relationData, formData.dceId || '');
        if (formData.mode) {
          (formData.mode as any) = formData.mode.split(',');
        }
        if (newJson) {
          formData.form = newJson;
        }
      }
      return formData?.form;
    } catch (error) {
      throw error;
    }
  };
  setAsDefault = async (projectId: string, mode: string, dceId: string, formId: string) => {
    try {
      await this.repo.update(
        { dceId, projectId, mode: Like(`%${mode}%`), isDelete: false },
        { default: false }
      );
      await this.repo.update(
        { id: formId, projectId, mode: Like(`%${mode}%`), isDelete: false },
        { default: true }
      );
      return true;
    } catch (error) {
      throw error;
    }
  };

  checkFirstForm = async (projectId: string, dceId: string, mode: string) => {
    const formData = await this.repo.findOne({
      where: { dceId, projectId, isDelete: false, mode: Like(`%${mode}%`) },
    });

    if (formData) return false;

    return true;
  };

  findAllByDceId = async (dceId: string, projectId: string) => {
    try {
      const formData = await this.repo.find({
        where: { dceId, projectId, isDelete: false },
      });

      return formData;
    } catch (error) {
      throw error;
    }
  };

  async update(id: string, data: ProjectForm): Promise<ProjectForm | undefined | null> {
    try {
      // Attempt to update the entity
      await this.repo.update(id, data as any);

      // Try to find the updated entity
      const updatedEntity = await this.repo.findOne({ where: { id } });

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async getJSONByDCEAndProject(dceId: string, projectId: string) {
    try {
      const data = await this.repo.findOne({
        where: { dceId, projectId, isDelete: false },
      });
      const dceRelaitonModel = new DCERelationModel();
      if (!data) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getJSONByDCE(dceId);

        if (adminFormData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(adminFormData, relationData, dceId);
          const final = newJson;
          return final;
        }
        return adminFormData;
      }
      if (data.form) {
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(data?.form, relationData, dceId);
        const final = newJson;
        return final;
      }
      return data.form;
    } catch (error) {
      throw error;
    }
  }
  async getJSONByDCEAndProjectWithMode(dceId: string, projectId: string, mode: string) {
    try {
      const data = await this.repo.findOne({
        where: { dceId, projectId, isDelete: false, mode: Like(`%${mode}%`) },
      });
      const dceRelaitonModel = new DCERelationModel();
      if (!data) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getJSONByDCE(dceId, mode);

        if (adminFormData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(adminFormData, relationData, dceId);
          const final = newJson;
          return final;
        }
        return adminFormData;
      }
      if (data.form) {
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(data?.form, relationData, dceId);
        const final = newJson;
        return final;
      }
      return data.form;
    } catch (error) {
      throw error;
    }
  }
  async getDefaultJSONByDCEAndProjectWithMode(dceId: string, projectId: string, mode: string) {
    try {
      const data = await this.repo.findOne({
        where: { dceId, projectId, isDelete: false, mode: Like(`%${mode}%`), default: true },
      });
      const dceRelaitonModel = new DCERelationModel();
      if (!data) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getJSONByDCE(dceId, mode);

        if (adminFormData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(adminFormData, relationData, dceId);
          const final = newJson;
          return final;
        }
        return adminFormData;
      }
      if (data.form) {
        const relationData = await dceRelaitonModel.getByDceId(dceId);
        const newJson = await checkChoiceURl(data?.form, relationData, dceId);
        const final = newJson;
        return final;
      }
      return data.form;
    } catch (error) {
      throw error;
    }
  }
  async getAllJSONByDCEAndProjectWithMode(dceId: string, projectId: string) {
    try {
      const data = await this.repo.find({
        where: { dceId, projectId, isDelete: false, mode: Like(`%create%`), enableMobile: true },
      });
      const final: any[] = [];
      const dceRelaitonModel = new DCERelationModel();
      if (data.length < 0) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getCreateAdminForm(dceId);

        if (adminFormData) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(adminFormData, relationData, dceId);

          return [
            { name: adminFormData.name, isDefault: true, form: newJson, id: adminFormData.id },
          ];
        }
        return adminFormData;
      }
      if (data.length > 0) {
        for (const element of data) {
          const relationData = await dceRelaitonModel.getByDceId(dceId);
          const newJson = await checkChoiceURl(element?.form, relationData, dceId);
          const form = {
            name: element.name,
            isDefault: element.default,
            form: newJson,
            id: element.id,
          };
          final.push(form);
        }
        return final;
      }
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getJSONByDCEAndProjectForMobile(dceId: string, projectId: string, updatedAfter?: Date) {
    try {
      const dceRelationModel = new DCERelationModel();
      const data = await this.repo.findOne({
        where: { dceId, projectId, isDelete: false, default: true, mode: Like(`%create%`) },
      });
      const relationData = await dceRelationModel.getByDceId(dceId);
      if (!data) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getJSONByDCE(dceId);

        const finalForm = convertDropdownToURl(adminFormData, relationData);
        return finalForm;
      }
      if (updatedAfter && data?.updatedAt && data?.updatedAt < updatedAfter) {
        return {};
      }
      const finalForm = convertDropdownToURl(data.form, relationData);
      return finalForm;
    } catch (error) {
      throw error;
    }
  }

  async getJSONByFormForMobile(fromId: string) {
    try {
      const data = await this.repo.findOne({
        where: { id: fromId, isDelete: false },
      });

      const entities = getEntitiesFromMultipleDCEForms(data?.form);
      const dceRelationModel = new DCERelationModel();
      const relationData = await dceRelationModel.getByAllWithDCEByEntity(entities);
      const subDceRelationData = await dceRelationModel.subDCERelationWithDCEEntity(entities);
      if (!data) {
        throw new Error('Form not found');
      }

      const finalForm = await convertDropdownToURlForMutiple(
        data.form,
        relationData,
        subDceRelationData
      );
      return finalForm;
    } catch (error) {
      throw error;
    }
  }

  async getJSONByDCEAndProjectForMobileWithMode(
    dceId: string,
    projectId: string,
    mode: string,
    updatedAfter?: Date
  ) {
    try {
      const dceRelationModel = new DCERelationModel();
      const data = await this.repo.findOne({
        where: { dceId, projectId, isDelete: false, mode: Like(`%${mode}%`) },
      });
      const relationData = await dceRelationModel.getByDceId(dceId);
      if (!data) {
        const defaultFormModel = new FormModel();
        const adminFormData = await defaultFormModel.getJSONByDCE(dceId, mode);

        const finalForm = convertDropdownToURl(adminFormData, relationData);
        return finalForm;
      }
      if (updatedAfter && data?.updatedAt && data?.updatedAt < updatedAfter) {
        return {};
      }
      const finalForm = convertDropdownToURl(data.form, relationData);
      return finalForm;
    } catch (error) {
      throw error;
    }
  }

  addProjectFormByDefaultFrom = async (
    defaultFormId: string,
    projectId: string,
    createdBy: string,
    createdUserId: string
  ) => {
    try {
      const defaultFormModel = new FormModel();
      const defaultFormDetails = await defaultFormModel.findById(defaultFormId);
      if (!defaultFormDetails || !defaultFormDetails.dceId) {
        throw new Error('Form not found');
      }

      const newProjectForm: Partial<ProjectForm> = {
        parentFormId: defaultFormDetails.id,
        createdBy,
        updatedBy: createdBy,
        createdUserId,
        name: defaultFormDetails.name,
        projectId,
        form: defaultFormDetails.form,
        dceId: defaultFormDetails.dceId,
        description: defaultFormDetails.description,
      };
      const data = await this.repo.save(newProjectForm);
      return data;
    } catch (error) {
      throw error;
    }
  };

  editModeByDceAndProject = async (
    dceId: string,
    mode: { formId: string; mode: string[] }[],
    projectId: string
  ) => {
    try {
      const entityManager = getManager();
      await entityManager.transaction(async (transactionalEntityManager) => {
        await transactionalEntityManager.update(ProjectForm, { dceId, projectId }, { mode: null });
        for (const element of mode) {
          const stringMode = element.mode.join(',');
          await transactionalEntityManager.update(ProjectForm, element.formId, {
            mode: stringMode,
          });
        }
      });
    } catch (error) {
      throw error;
    }
  };

  getFormIdsByModeAndDce = async (dceId: string, projectId: string) => {
    try {
      const data = await this.repo.find({
        where: { dceId, projectId, isDelete: false },
      });
      const finalData = { create: '', edit: '', view: '' };
      data.forEach((value) => {
        if (value.mode) {
          const modeAsArray = value.mode.split(',');
          if (modeAsArray.length > 0) {
            if (finalData.create == '' && modeAsArray.includes('create')) {
              finalData.create = value.id;
            }
            if (finalData.edit == '' && modeAsArray.includes('edit')) {
              finalData.edit = value.id;
            }
            if (finalData.view == '' && modeAsArray.includes('view')) {
              finalData.view = value.id;
            }
          }
        }
      });

      return finalData;
    } catch (error) {
      throw error;
    }
  };

  getFormByModeAndDce = async (dceId: string, projectId: string) => {
    try {
      const data = await this.repo.find({
        where: { dceId, projectId, isDelete: false },
      });
      const finalData: FinalData = { create: [], edit: [], view: [] };
      data.forEach((value) => {
        if (value.mode) {
          const modeAsArray = value.mode.split(',');
          if (modeAsArray.length > 0) {
            if (modeAsArray.includes('create')) {
              finalData.create.push(value);
            }
            if (modeAsArray.includes('edit')) {
              finalData.edit.push(value);
            }
            if (modeAsArray.includes('view')) {
              finalData.view.push(value);
            }
          }
        }
      });

      return finalData;
    } catch (error) {
      throw error;
    }
  };
  getAllMultipleDCEForms = async (projectId: string) => {
    try {
      const data = await this.repo.find({
        where: { projectId, isDelete: false, isMultiple: true },
      });

      return data;
    } catch (error) {
      throw error;
    }
  };
  getCustomAndMultipleDCEForms = async (projectId: string) => {
    try {
      const data = await this.repo.find({
        where: [
          { projectId, isDelete: false, isMultiple: true },
          { projectId, isDelete: false, isCustom: true },
        ],
      });

      return data;
    } catch (error) {
      throw error;
    }
  };
  getCustomFormByProjectId = async (projectId: string) => {
    try {
      const data = await this.repo.find({
        where: { dceId: IsNull(), projectId, isDelete: false, isCustom: true },
      });
      return data;
    } catch (error) {
      throw error;
    }
  };

  getCustomFormByProjectIdAndFormKey = async (projectId: string, formKey: string) => {
    try {
      const data = await this.repo.findOne({
        where: { dceId: IsNull(), projectId, formKey, isDelete: false, isCustom: true },
      });
      return data;
    } catch (error) {
      throw error;
    }
  };
}

export default ProjectFormModel;
