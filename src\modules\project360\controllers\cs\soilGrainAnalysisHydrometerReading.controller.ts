import { Request, Response } from 'express';
import soilGrainAnalysisHydrometerModel from '../../models/cs/soilGrainAnalysisHydrometerReading.model';

class SoilGrainAnalysisHydrometerController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const soilGrainAnalysishydrometerData = await soilGrainAnalysisHydrometerModel.findById(
        req.params.id
      );
      // checking if data is found with the id
      if (soilGrainAnalysishydrometerData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: soilGrainAnalysishydrometerData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async findByGrainAnalysisTestId(req: Request, res: Response) {
    try {
      const soilGrainAnalysishydrometerData =
        await soilGrainAnalysisHydrometerModel.findBySoilGrainAnalysisId(req.params.id);
      // getting the data from database with the given id

      if (soilGrainAnalysishydrometerData && soilGrainAnalysishydrometerData.length > 0) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: soilGrainAnalysishydrometerData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async create(req: Request, res: Response) {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }

      // getting the data from database with the given id
      const soilGrainAnalysishydrometerData = await soilGrainAnalysisHydrometerModel.add(req.body);
      // checking if data is found with the id
      // if true data will send as response
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: soilGrainAnalysishydrometerData,
        msg: message,
      });
    } catch (error) {
      const message = req.__('DataInputFail');
      // error response
      return res
        .status(500)
        .json({ isSucceed: false, data: [], msg: (error as any).message || message });
    }
  }
}

const soilGrainAnalysisHydrometerController = new SoilGrainAnalysisHydrometerController();
export default soilGrainAnalysisHydrometerController;
