import * as ExcelJS from 'exceljs';
import {
  DeepPartial,
  EntityTarget,
  In,
  ObjectLiteral,
  Repository,
  getConnection,
  getManager,
} from 'typeorm';
import Submittal from '../../../../entities/p_gen/Submittal';
import SubmittalVersion from '../../../../entities/p_gen/SubmittalVersion';
import { getAuditDetails } from '../../auditLog/getAuditDetailsByEntity';
import { stationFormatting } from '../../custom/stationFormatting';
import moment from 'moment-timezone';
import projectModel from '../../../../modules/project360/models/project.model';
import { convertEastingNorthingToLatLong } from '../../spatialCoordinates/eastingAndNothingConversion';
import dceModel from '../../../../modules/project360/models/meta/dce.model';
import purposeModel from '../../../../modules/project360/models/meta/purpose.model';
import DCEConfigurationModel from '../../../../modules/project360/models/dceConfiguration.model';
import SubDCEModel from '../../../../modules/project360/models/meta/SubDce.model';
import DCERelationModel from '../../../../modules/project360/models/meta/dceRelation.model';
import { typeConversionForDceConfig } from '../../surveyJs/convertTableMetaDataToSurveyJsJson';
import CreateTemplate from './createDownloadTemplate';
import dceConfigToExcelConversion, {
  convertSubdceToExcel,
} from '../../dce/dceConfigToDatagridConversion';
import { DataCaptureElemetRelation } from '../../../../entities/p_meta/DataCaptureElemetRelation';
import { AzureBlobStorageService } from '../../sftpBlobAndFiles/azureBlobStorageService';
import { stringToUniqueNumber } from './importIdGenerateAndDecode';
import { ImportMapping } from '../../../../entities/p_utils/ImportMapping';
import { entityRelationList } from '../../entity/entityRelation';
import ImportMappingModel from '../../../../modules/project360/models/utils/importMapping.model';
import { camelCaseToTitleCase } from '../../custom/caseConversion';
import getStationAndOffset from '../../photoVideo/getData';
import { stationConversionOnImportData } from '../../spatialCoordinates/stationConversionOnAddData';
import { entityList } from '../../entity/entityList';
import { addNewRelation } from '@utils/relationship/relationshipChangesOnDropdown';
import MetaDCEConfigurationModel from '@models/meta/adminDceConfiguration.model';
import sampleModel from '@models/sample.model';
import { DCECoordinatesConfiguration } from '@entities/p_gen/DCECoordinatesConfig';
import { dceValidationBeforeApproval } from '@utils/Approval/dceValidation';
import ctLogModel from '@models/gen/ctLog.model';
import testLogPrefixModel from '@models/gen/testLogPrefix.model';

export interface IDataInput {
  entity: string;
  data: any[];
  index: number;
}
interface IColumnInfo {
  name: string;
  alias: string;
  isRelation: boolean;
  type: string;
  dceRelation?: DataCaptureElemetRelation;
}
class ImportData<T extends ObjectLiteral> {
  private Entity: EntityTarget<T>;
  private repository: Repository<T>;
  constructor(entity: EntityTarget<T>) {
    this.Entity = entity;
    this.repository = getConnection().getRepository(entity);
  }
  async addData(
    data: IDataInput[],
    projectId: string,
    createdBy: string,
    createdUserId: string,
    entityName: string,
    originalFileName: string,
    sampleId?: string,
    submittal?: DeepPartial<Submittal>,
    submittalVersion?: SubmittalVersion
  ) {
    try {
      let final: any[] = [];
      // const importIds = []
      // Create a new workbook
      const { dataWithOutNull, importIds } = this.removeObjectsWithAllNullValues(data);
      const relatedIdToRemove: string[] = [];
      const removedImpotId = dataWithOutNull.map((item: IDataInput) => {
        // Filter the dataItems and collect Related Ids for items that are going to be removed
        item.data = item.data.filter((dataItem) => {
          if (dataItem.hasOwnProperty('Import Id') && dataItem['Import Id'] != null) {
            // Store the Related Id of the item going to be removed
            if (dataItem.hasOwnProperty('Related Id')) {
              relatedIdToRemove.push(dataItem['Related Id']);
            }
            return false; // Remove the item
          }
          return true; // Keep the item
        });
        return item;
      });
      final = removedImpotId;
      if (data.length > 1) {
        final = this.removeRelatedId(final, relatedIdToRemove);
      }
      if (final.length <= 0) {
        throw new Error('No data to import');
      }
      const columnInfo = await this.getColumnInfo(data, projectId);
      // const columnInfo = await getAllCustomColumnInfo(this.Entity);
      final = await this.mapKeysToFieldNames(final, columnInfo);
      const projectDetails = await projectModel.getProjectId(projectId);

      final = this.addCreatedAndUpdatedBy(final, createdBy, createdUserId, sampleId);
      final = this.parseCustomDateFormat(
        final,
        [...columnInfo],
        projectDetails?.timezone || 'America/New_York'
      );

      final = this.stationFormattingMethod(final);
      final = this.convertEastingAndNorthing(final);
      if (entityName in entityList) {
        final = await stationConversionOnImportData(
          (data as any)?.projectId || '',
          data,
          entityName
        );
      }
      final = await this.replaceForeignkey(final, columnInfo, projectId, createdUserId);
      final = this.isProjectId(final, projectId);
      const result = await this.addToDatabase(
        createdUserId,
        this.processData(final),
        entityName,
        importIds,
        submittal,
        submittalVersion
      );
      const filePath = await this.returnExcel(
        result,
        projectId,
        entityName,
        createdUserId,
        columnInfo,
        originalFileName
      );
      return { result, filePath };
    } catch (error) {
      throw error;
    }
  }

  private processData = (input: { entity: string; data: any[]; index: number }[]): any[] => {
    // Step 1: Flatten all data objects while keeping track of relatedId and index
    const flattenedData: any[] = [];

    input.forEach((item) => {
      item.data.forEach((dataItem) => {
        flattenedData.push({
          ...dataItem,
          entity: item.entity,
          index: item.index,
        });
      });
    });
    const sortedFlattenedData = flattenedData.sort((a, b) => a.index - b.index);
    let groupedItems: any[] = [];
    // Step 2: Group by relatedId
    sortedFlattenedData.forEach((value) => {
      if (!value.importId) {
        if (value.index == 0) {
          groupedItems.push(value);
        } else {
          const findParent = groupedItems.find((item) => item.relatedId == value.relatedId);
          delete value.relatedId;
          delete value.index;
          if (!findParent[value.entity] || findParent[value.entity].length <= 0) {
            findParent[value.entity] = [value];
          } else {
            findParent[value.entity].push(value);
          }
          delete value.entity;
          groupedItems = groupedItems.map((item) =>
            item.relatedId === findParent.relatedId ? findParent : item
          );
        }
      }
    });

    // Step 3: Convert grouped object into the desired array structure
    const result = groupedItems.map((value) => {
      if (value.relatedId) {
        delete value.relatedId;
        delete value.index;
        delete value.entity;
      }
      return value;
    });

    return result;
  };

  private removeObjectsWithAllNullValues = (
    arr: IDataInput[]
  ): {
    dataWithOutNull: {
      entity: string;
      data: any[];
      index: number;
    }[];
    importIds: string[];
  } => {
    try {
      const importIds: string[] = arr
        .filter(
          (item: IDataInput) =>
            item.index === 0 && // Check if item.index is 0
            item.data && // Ensure item.data exists
            item.data.some((dataItem) => dataItem['Import Id'] != null) // Check if any item in data has 'Import Id'
        )
        .flatMap(
          (item: IDataInput) =>
            item.data
              .filter((value) => value['Import Id'] != null) // Filter out items without 'Import Id'
              .map((value) => value['Import Id']) // Map to 'Import Id' values
        );

      const data = arr.filter((items) => {
        // Check if all values in the object are null
        items.data = items.data.filter((obj) => {
          return Object.values(obj).some((value) => value !== null);
        });
        return items;
      });
      const result = data.every(
        (item) =>
          Array.isArray(item.data) &&
          item.data.every(
            (innerObj) => Object.keys(innerObj).length === 1 && innerObj.hasOwnProperty('rowNumber')
          )
      );

      if (result == true) {
        return { dataWithOutNull: [], importIds: [] };
      }

      return {
        dataWithOutNull: data.filter((obj) => {
          // Get all the keys of the object
          const keys = Object.keys(obj.data);
          // Return true if the object has more than one key or does not only have the 'rowNumber' key
          return !(keys.length === 1 && keys[0] === 'rowNumber');
        }),
        importIds,
      };
    } catch (error) {
      throw error;
    }
  };

  private removeRelatedId = (data: IDataInput[], relatedIdToRemove: string[]) => {
    try {
      return data.map((item: IDataInput) => {
        // Filter the dataItems and collect Related Ids for items that are going to be removed
        item.data = item.data.filter((dataItem) => {
          if (!dataItem.hasOwnProperty('Related Id') && item.index == 0) {
            return true;
          }
          if (
            dataItem.hasOwnProperty('Related Id') &&
            !relatedIdToRemove.includes(dataItem['Related Id'])
          ) {
            return true;
          }
          return false;
        });
        return item;
      });
    } catch (error) {
      throw error;
    }
  };

  private mapKeysToFieldNames(
    completeData: {
      entity: string;
      data: any[];
      index: number;
    }[],
    columnInfo: {
      entity: string;
      config: IColumnInfo[];
      index: number;
    }[]
  ): {
    entity: string;
    data: any[];
    index: number;
  }[] {
    return completeData.map((item) => {
      const newData = item.data.map((value: any) => {
        const newItem: any = {};
        columnInfo.map((info) => {
          for (const key in value) {
            const column = info.config.find((c) => c.alias === key);
            if (column && column.name) {
              newItem[column.name] = value[key];
            }
            newItem.rowNumber = value.rowNumber;
            if (
              'Import Id' in value &&
              newItem['Import Id'] &&
              'data Change' in value &&
              newItem['data Change']
            ) {
              newItem.importId = value['Import Id'];
              newItem.dataChange = value['Data Change'];
            }
            if ('Related Id' in value && value['Related Id']) {
              newItem.relatedId = value['Related Id'];
            }
          }
        });
        return newItem;
      });
      item.data = newData;
      return item;
    });
  }

  private convertEastingAndNorthing(data: IDataInput[]): IDataInput[] {
    try {
      data.map((item) => {
        const newData = item.data.map((value) => {
          if (
            'easting' in value &&
            'northing' in value &&
            !isNaN(value.easting) &&
            !isNaN(value.northing)
          ) {
            const result: any = convertEastingNorthingToLatLong(
              Number(value.easting),
              Number(value.northing)
            );
            if (result.longitude && result.latitude) {
              value.longitude = result.longitude;
              value.latitude = result.latitude;
            }
          }
          return value;
        });
        item.data = newData;
      });
      return data;
    } catch (error) {
      throw error;
    }
  }

  getColumnInfo = async (
    data: any[],
    projectId: string
  ): Promise<
    {
      entity: string;
      config: IColumnInfo[];
      index: number;
    }[]
  > => {
    try {
      const DCECoordinatesConfigurationRepository = getManager().getRepository(
        DCECoordinatesConfiguration
      );
      const mainDCE = await dceModel.findByEntity(data[0].entity);
      if (!mainDCE) {
        throw new Error('DCE not found');
      }
      const coordinatesData = await DCECoordinatesConfigurationRepository.findOne({
        where: { dceId: mainDCE.id, projectId, isDelete: false },
      });
      const results = await Promise.all(
        data.map(async (value) => {
          if (value.index === 0) {
            const dceData = await dceModel.findByEntity(value.entity);
            if (dceData) {
              const dceConfigModel = new MetaDCEConfigurationModel();
              let dceConfig = await dceConfigModel.getData(dceData);
              const excludedColumns = ['longitude', 'latitude', 'easting', 'northing'];
              dceConfig = dceConfig.filter((value) => !excludedColumns.includes(value.columnName));

              if (coordinatesData) {
                if (coordinatesData.isLatLong) {
                  dceConfig.push({
                    columnName: 'longitude',
                    alias: 'Longitude',
                    editable: true,
                    id: 'longitude',
                    hide: false,
                    updatedBy: 'system',
                    order: dceConfig.length + 1,
                  });
                  dceConfig.push({
                    columnName: 'latitude',
                    alias: 'Latitude',
                    editable: true,
                    id: 'longitude',
                    hide: false,
                    updatedBy: 'system',
                    order: dceConfig.length,
                  });
                } else if (!coordinatesData.isLatLong) {
                  dceConfig.push({
                    columnName: 'easting',
                    alias: 'Easting',
                    editable: true,
                    id: 'easting',
                    updatedBy: 'system',
                    hide: false,
                    order: dceConfig.length + 1,
                  });
                  dceConfig.push({
                    columnName: 'northing',
                    alias: 'Northing',
                    editable: true,
                    id: 'northing',
                    updatedBy: 'system',
                    hide: false,
                    order: dceConfig.length,
                  });
                }
              }
              return {
                entity: value.entity,
                config: dceConfig
                  ? dceConfig.map((value) => ({
                      name: value.columnName || '',
                      alias: value.alias || '',
                      isRelation: value.isRelation || false,
                      type: value.type || '',
                    }))
                  : [],
                index: value.index,
              };
            }
          } else {
            const subDCEModel = new SubDCEModel();
            const subDceData = await subDCEModel.findByEntity(value.entity);
            if (subDceData) {
              const columns = await subDCEModel.getColumnsBySubDce(subDceData);

              const dCERelationModel = new DCERelationModel();
              const dceRelationData = await dCERelationModel.getBySubDceId(subDceData.id);
              return {
                entity: value.entity,
                config: columns
                  ? columns.map((value) => {
                      const type = typeConversionForDceConfig(value.type);

                      return {
                        name: value.name || '',
                        alias: value.name.includes('Id')
                          ? camelCaseToTitleCase(value.name.replace('Id', ''))
                          : camelCaseToTitleCase(value.name || ''),
                        isRelation: dceRelationData.some(
                          (relation) => relation.columnName === value.name
                        ),
                        type: type,
                        dceRelation:
                          dceRelationData.find((relation) => relation.columnName === value.name) ||
                          {},
                      };
                    })
                  : [],
                index: value.index,
              };
            }
          }
          return null; // Return null for items that do not match criteria
        })
      );

      // Filter out null values
      return results.filter((result): result is NonNullable<typeof result> => result !== null);
    } catch (error) {
      throw error;
    }
  };

  private isProjectId(data: IDataInput[], projectId: string): any[] {
    try {
      const final = data.map((value) => {
        if (value.index == 0) {
          const newData = value.data.map((object) => {
            object.projectId = projectId;
            return object;
          });
          value.data = newData;
        }
        return value;
      });
      return final;
    } catch (error) {
      throw error;
    }
  }

  private replaceForeignkey = async (
    data: IDataInput[],
    columnInfo: {
      entity: string;
      config: IColumnInfo[];
      index: number;
    }[],
    projectId: string,
    userId: string
  ): Promise<IDataInput[]> => {
    try {
      const getPurposeByUser = await purposeModel.getByUserIdForDropdown(userId, projectId, false);
      const finalData: IDataInput[] = [];
      for (const entities of data) {
        const updatedData: any[] = [];
        for (const row of entities.data) {
          if (!row?.purposeId && entities.index == 0) {
            throw new Error(`Purpose column not found in templete`);
          }
          let purposeId = '';
          const value = columnInfo.find((column) => column.entity == entities.entity);
          if (value) {
            for (const item of value.config) {
              if (item.isRelation) {
                if (item.name == 'purposeId') {
                  if (!getPurposeByUser || getPurposeByUser.length <= 0) {
                    throw new Error(
                      `Invalid Purpose in row ${row.rowNumber} and column Purpose ${entities.entity}`
                    );
                  }
                  const valueIntsert = row[item.name];

                  if (!valueIntsert && entities.index == 0) {
                    throw new Error(
                      `Purpose is required in row ${row.rowNumber} and column Purpose ${entities.entity}`
                    );
                  }
                  const purposeDetails = getPurposeByUser.find(
                    (purposeData) =>
                      purposeData?.name?.toLocaleLowerCase() == valueIntsert?.toLocaleLowerCase()
                  );
                  if (purposeDetails?.id) {
                    purposeId = purposeDetails?.id;
                  }
                  if (!purposeDetails && entities.index == 0) {
                    throw new Error(
                      `Invalid Purpose in row ${row.rowNumber} and column Purpose ${entities.entity}`
                    );
                  }
                }
                if (purposeId && purposeId != '' && item.name == 'purposeId') {
                  row[item.name] = purposeId;
                } else if (item.name !== 'purposeId' && row[item.name]) {
                  const dCERelationModel = new DCERelationModel();
                  const dceData = await dceModel.findByEntity(entities.entity);
                  const replaceValue = await dCERelationModel.getDataForImport(
                    dceData?.id || '',
                    item.name,
                    row[item.name],
                    purposeId || '',
                    projectId
                  );

                  if (replaceValue) {
                    row[item.name] = replaceValue;
                  } else {
                    delete row[item.name];
                  }
                }
              }
            }
          }
          updatedData.push(row);
        }
        entities.data = updatedData;
        finalData.push(entities);
      }

      return finalData;
    } catch (error) {
      throw error;
    }
  };

  private addCreatedAndUpdatedBy(
    data: IDataInput[],
    name: string,
    userId: string,
    sampleId?: string
  ): IDataInput[] {
    try {
      return data.map((value) => {
        const newData = value.data.map((items) => {
          items.createdBy = name;
          items.createdBy = name;
          items.updatedBy = name;
          items.createdUserId = userId;
          if (sampleId && sampleId != '') {
            items.sampleId = sampleId;
          }
          return items;
        });
        value.data = newData;
        return value;
      });
    } catch (error) {
      throw error;
    }
  }

  private stationFormattingMethod = (data: IDataInput[]): IDataInput[] => {
    try {
      const final = data.map((item) => {
        let columnsWithStation: string[] = [];
        const newData = item.data.map((value) => {
          columnsWithStation = Object.keys(value).filter((key) =>
            key.toLowerCase().includes('station')
          );
          const newItems = columnsWithStation.map((key) => {
            if (key in value && value[key]) {
              const station = value[key];
              if (!station.includes('+') || station.length > 2) {
                value[key] = stationFormatting(station);
              }
            }
            return value;
          });
          return newItems.length <= 0 ? value : newItems[0];
        });
        item.data = newData;
        return item;
      });
      return final.length <= 0 ? data : final;
    } catch (error) {
      throw error;
    }
  };

  private async addToDatabase(
    createdUserId: string,
    data: DeepPartial<T>[],
    entityName: string,
    importIds: string[],
    submittal?: DeepPartial<Submittal>,
    submittalVersion?: SubmittalVersion
  ): Promise<DeepPartial<T>[]> {
    try {
      const entityManager = getManager();
      const output: DeepPartial<T>[] = [];
      const createTemplate = new CreateTemplate(entityName);
      const dceData = await dceModel.findByEntity(entityName);
      const subDCEItems = await createTemplate.addSubDceToTemplate(dceData?.id || '');
      await entityManager.transaction(async (transactionalEntityManager) => {
        if (submittal && submittalVersion) {
          const version = await transactionalEntityManager.save(SubmittalVersion, submittalVersion);
          await transactionalEntityManager.update(Submittal, submittal.id, submittal);
          data.map((value) => {
            (value as any).submittalVersionId = version.id;
          });
        }
        if (entityName == 'sampleManagement') {
          const outputMap: Record<number, number> = {};

          for (const item of data) {
            if ((item as any).sampledDate) {
              const sampledYear = new Date((item as any).sampledDate).getFullYear();
              if (outputMap[sampledYear] !== undefined) {
                outputMap[sampledYear] += 1;
              } else {
                const nextSampleNo = await sampleModel.getNextSampleNo((item as any).sampledDate);
                outputMap[sampledYear] = parseInt(nextSampleNo.split('-')[1], 10);
              }
              (item as any).sampleNo = `${sampledYear.toString().slice(-2)}-${String(
                outputMap[sampledYear]
              ).padStart(5, '0')}`;
            }
          }
        }
        if (entityName === 'ctLog') {
          for (const item of data) {
            const purposeId = (item as any).purposeId;
            const projectId = (item as any).projectId;
            if (!purposeId) {
              throw new Error('Purpose is mandatory');
            }
            if (purposeId) {
              const currentTestNo = await ctLogModel.getTestNoByProjectIdAndPurposeId(
                projectId,
                purposeId
              );
              const testLogPrefix =
                await testLogPrefixModel.getByTestLogPrefixByProjectIdAndPurposeId(
                  projectId,
                  purposeId
                );
              const nextTestNo = currentTestNo + 1;

              const formattedTestNo = `${testLogPrefix}-${String(nextTestNo).padStart(5, '0')}`;
              (item as any).testNo = formattedTestNo;
            }
          }
        }

        await Promise.all(
          data.map(async (value: DeepPartial<T>, index: number) => {
            try {
              const entity = this.repository.create(value);
              const result = await transactionalEntityManager.save(entity);

              output.push(result);
            } catch (error) {
              throw new Error(`${(error as any).message} in row ${index + 1}`);
            }
          })
        );
        const importIdMapping = await transactionalEntityManager.find(ImportMapping, {
          where: { importId: In(importIds), entity: entityName },
        });
        const ids = importIdMapping.map((value) => value.rowId);
        let relation: string[] = [];
        if (entityName in entityRelationList) {
          relation = entityRelationList[entityName].relation;
        }
        const exisitingRows = await this.repository.find({
          where: { id: In(ids), isDelete: false } as any,
          relations: relation,
        });

        const exisitingRowsWithImport = exisitingRows.map((value) => {
          const importId = importIdMapping.find((item) => {
            item.rowId == value.id;
          });
          if (importId) {
            (value as any).importId = importId.importId;
          }
          subDCEItems?.map((subDce) => {
            if (subDce.entity in value && value[subDce.entity].length > 0) {
              // Initialize the newSubData array before the map function
              const newSubData: any[] = value[subDce.entity].map((sub: any) => {
                const importId = importIdMapping.find((item) => item.rowId == sub.id);

                // Check if importId is found, and if so, assign the importId to sub
                if (importId) {
                  (sub as any).importId = importId.importId;
                }

                return sub; // Make sure to return the modified sub element
              });

              // Assign the newSubData back to the value entity
              (value as any)[subDce.entity] = newSubData;
            }
          });
          return value;
        });
        await Promise.all(
          output.map(async (item: any) => {
            try {
              await transactionalEntityManager.save(ImportMapping, {
                rowId: item.id,
                importId: await stringToUniqueNumber(item.id, entityName),
                entity: entityName,
              });
              if (subDCEItems) {
                await Promise.all(
                  subDCEItems.map(async (subDce) => {
                    if (subDce.entity in item && item[subDce.entity].length > 0) {
                      // Initialize the newSubData array before the map function
                      await Promise.all(
                        item[subDce.entity].map(async (sub: any) => {
                          await transactionalEntityManager.save(ImportMapping, {
                            rowId: sub.id,
                            importId: await stringToUniqueNumber(sub.id, entityName),
                            entity: entityName,
                          });
                        })
                      );
                    }
                  })
                );
              }
            } catch (error) {
              throw error;
            }
          })
        );
        output.unshift(...exisitingRowsWithImport);
      });
      await Promise.all(
        output.map(async (item) => {
          try {
            await addNewRelation(entityName, item);
            await getAuditDetails(createdUserId, entityName, item, 'add');
          } catch (error) {
            throw error;
          }
        })
      );
      await dceValidationBeforeApproval(entityName, output);
      return output;
    } catch (error) {
      throw error;
    }
  }

  private parseCustomDateFormat = (
    data: IDataInput[],
    columnInfo: {
      entity: string;
      config: IColumnInfo[];
      index: number;
    }[],
    projectTimeZone: string
  ): IDataInput[] => {
    try {
      const result = data.map((item) => {
        const newData = item.data.map((value) => {
          const out = { ...value }; // Use spread operator to create a shallow copy
          for (const key in value) {
            const validColumnInfo = columnInfo.find((col) => col.entity == item.entity);
            const column = validColumnInfo?.config.find((col) => col.name === key);
            if (column && column.type.toLocaleLowerCase().includes('date')) {
              const input = value[key];
              if (input) {
                const parts: string[] = input.split(/\/|-|\s+/);

                if (parts.length >= 3) {
                  // eslint-disable-next-line prefer-const
                  let [month, day, year] = parts.slice(0, 3).map((part) => parseInt(part, 10));

                  if (year < 100) {
                    year += year < 50 ? 2000 : 1900;
                  }

                  if (isNaN(month) || isNaN(day) || isNaN(year)) {
                    const parsedDate = new Date(input);

                    if (!isNaN(parsedDate.getTime())) {
                      // out[key] = parsedDate;
                      out[key] = this.dateZoneConversion(parsedDate, projectTimeZone);
                    } else {
                      throw new Error(
                        `Invalid Date format in row ${value.rowNumber} and column ${key}`
                      );
                    }
                  } else {
                    let hours = 0;
                    let minutes = 0;
                    let seconds = 0;
                    let milliseconds = 0;

                    if (parts.length >= 4) {
                      const timeParts: string[] = parts[3].split(':');
                      hours = parseInt(timeParts[0], 10);
                      minutes = parseInt(timeParts[1], 10) || 0;
                      if (timeParts.length > 2) {
                        const secondsPart = timeParts[2].split('.');
                        seconds = parseInt(secondsPart[0], 10) || 0;
                        milliseconds = parseInt(secondsPart[1], 10) || 0;
                      }
                    }

                    const parsedDate = new Date(
                      year,
                      month - 1,
                      day,
                      hours,
                      minutes,
                      seconds,
                      milliseconds
                    );

                    if (isNaN(parsedDate.getTime())) {
                      const parsedDate = new Date(input);

                      if (!isNaN(parsedDate.getTime())) {
                        // out[key] = parsedDate;
                        out[key] = this.dateZoneConversion(parsedDate, projectTimeZone);
                      } else {
                        throw new Error(
                          `Invalid Date format in row ${value.rowNumber} and column ${key}`
                        );
                      }
                    } else {
                      const isoString = parsedDate.toISOString();
                      out[key] = this.dateZoneConversion(isoString, projectTimeZone);
                      // out[key] = isoString;
                    }
                  }
                } else {
                  const parsedDate = new Date(input);

                  if (!isNaN(parsedDate.getTime())) {
                    out[key] = this.dateZoneConversion(parsedDate, projectTimeZone);
                    // out[key] = parsedDate;
                  } else {
                    throw new Error(
                      `Invalid Date format in row ${value.rowNumber} and column ${key}`
                    );
                  }
                }
              }
            }
          }
          return out;
        });
        item.data = newData;
        return item;
      });

      return result;
    } catch (error) {
      throw error;
    }
  };

  private dateZoneConversion(parsedDate: Date | string, timezone: string) {
    try {
      const originalDate = moment.utc(parsedDate);

      // Extract the date and time components in the original time zone
      const originalDateTime = originalDate.format('YYYY-MM-DDTHH:mm:ss.SSS');

      // Create a new date string with the desired time zone offset
      const newDate = moment
        .tz(originalDateTime, timezone || 'America/New_York')
        .format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
      const updatedDate = new Date(newDate);
      return updatedDate;
    } catch (error) {
      throw error;
    }
  }

  private returnExcel = async (
    dataInput: any[],
    projectId: string,
    entity: string,
    userId: string,
    columnInfo: {
      entity: string;
      config: IColumnInfo[];
      index: number;
    }[],
    originalFileName: string
  ) => {
    try {
      let data = dataInput;
      const createTemplate = new CreateTemplate(entity);
      const workbook = new ExcelJS.Workbook();
      const parentValues: any[] = [];

      const worksheet = workbook.addWorksheet(camelCaseToTitleCase(entity));
      const dceData = await dceModel.findByEntity(entity);
      if (dceData) {
        const dceConfigModel = new DCEConfigurationModel();
        const dceConfigData = await dceConfigModel.getData(dceData, projectId);

        const dceConfig = await dceConfigModel.getData(dceData, projectId);
        if (!dceConfig || dceConfig.length <= 0) {
          throw new Error('Template config not found');
        }
        const row = dceConfig
          .sort((a, b) => (a.order || 0) - (b.order || 0))
          .filter((column) => column.hide === false)
          .map((column) => column.alias);

        // Add data to the worksheet
        const subDCEItems = await createTemplate.addSubDceToTemplate(dceData.id || '');
        if (subDCEItems && subDCEItems.length > 0) {
          row.unshift('Related Id');
          await Promise.all(
            subDCEItems.map(async (item) => {
              const columns = item.columns as string[];
              columns.unshift('Related Id');
              columns.unshift('Import Id');
              const subWorksheet = workbook.addWorksheet(item.worksheet);
              subWorksheet.addRow(item.columns);
              const subDceData = data.flatMap((value, index) => {
                if (item.entity in value && value[item.entity].length > 0) {
                  return value[item.entity].map((work: any) => {
                    work.relatedId = index + 1;
                    work.importId = value.importId;
                    return work;
                  });
                }
                return []; // Return an empty array for elements that don't match the condition
              });
              const columnInfoUpdate = columnInfo.find((value) => value.entity == item.entity);
              const subDCEConvertedData = await convertSubdceToExcel(
                columnInfoUpdate?.config || [],
                subDceData,
                projectId,
                userId
              );

              await Promise.all(
                subDCEConvertedData.map(async (current: any, index) => {
                  if (!('Related Id' in current) && subDCEItems && subDCEItems.length > 0) {
                    current['Related Id'] = index + 1;
                  }
                  if (!('Import Id' in current) && subDCEItems && subDCEItems.length > 0) {
                    const importMappingModel = new ImportMappingModel();
                    current['Import Id'] = await importMappingModel.findByAll(entity, current.id);
                  }
                  const rowValues = item.columns.map((key: any) => current[key as string]);

                  subWorksheet.addRow(rowValues);
                })
              );
            })
          );
        }
        row.unshift('Import Id');

        data = await Promise.all(
          data.map(async (value, index) => {
            if (subDCEItems && subDCEItems.length > 0) {
              value.relatedId = index + 1;
            }
            if (!value.importId) {
              value.importId = await stringToUniqueNumber(value.id, entity);
            }
            return value;
          })
        );
        worksheet.addRow(row);

        workbook.worksheets.forEach((ws) => {
          // Find the "Related Id" in the first row (or adjust if needed)
          const materialIdCell = ws.getCell('A1'); // Assuming "Related Id" is in column A, row 1

          // Apply font styling
          materialIdCell.font = {
            bold: true,
            color: { argb: 'FFFF0000' }, // Red color for highlighting
          };

          materialIdCell.note = `Only for system purpose`;
          if (subDCEItems && subDCEItems.length > 0) {
            // Find the "Related Id" in the first row (or adjust if needed)
            const relatedIdCell = ws.getCell('B1'); // Assuming "Related Id" is in column A, row 1

            // Apply font styling
            relatedIdCell.font = {
              bold: true,
              color: { argb: 'FFFF0000' }, // Red color for highlighting
            };

            relatedIdCell.note = `This ID links the records to the associated worksheets.`;
          }
        });

        const convertedData = await dceConfigToExcelConversion(
          dceConfigData || [],
          data,
          projectId,
          userId,
          true
        );
        convertedData.forEach((item: any) => {
          const rowValues = row.map((key) => item[key as string]);

          parentValues.push(...rowValues);
          worksheet.addRow(rowValues);
        });
        // Save the workbook to a file
        const excelBuffer = await workbook.xlsx.writeBuffer();
        console.log('Excel file created successfully!');

        const azureBlobStorageService = new AzureBlobStorageService(
          process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
          process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
        );
        const dayDirectoryPath: string = `excel/import/${Date.now()}/${originalFileName}`;
        const finalPath = await azureBlobStorageService.uploadFileToBlobUploadBufferInput(
          projectId,
          dayDirectoryPath,
          excelBuffer
        );
        return finalPath;
      }
    } catch (error) {
      throw error;
    }
  };

  private attachStationAndOffset = async (entityString: string, data: any[], projectId: string) => {
    if (entityString) {
      const checkStationExist = await dceModel.checkColumnExistByDce(this.Entity, 'station');
      const checkOffsetExist = await dceModel.checkColumnExistByDce(this.Entity, 'offset');
      if (!checkStationExist && !checkOffsetExist) {
        return data;
      }
      return await Promise.all(
        data.map(async (value) => {
          if (
            (checkStationExist || checkOffsetExist) &&
            projectId &&
            value.longitude &&
            value.latitude
          ) {
            const { station, offset } = await getStationAndOffset(value.projectId, {
              longitude: value.longitude,
              latitude: value.latitude,
            });
            if (checkStationExist && !value.station) {
              value.station = station;
            }
            if (checkOffsetExist && !value.offset) {
              value.offset = offset;
            }
          }
        })
      );
    }
  };
}

export default ImportData;
