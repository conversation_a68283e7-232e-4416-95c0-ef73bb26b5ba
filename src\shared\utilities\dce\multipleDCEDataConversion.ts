import { entityList } from '@utils/entity/entityList';

const submitDataConversion = (data: Record<string, string>) => {
  const convertedData: Record<string, any> = {};

  Object.entries(data).forEach(([key, value]) => {
    const [entity, column] = key.split('.');

    if (entityList[entity as keyof typeof entityList]) {
      convertedData[entity] = convertedData[entity] || {};
      convertedData[entity][column] = value;
    }
  });

  const dataAsArray = Object.entries(convertedData).map(([entity, columns]) => {
    return {
      entity,
      data: columns,
    };
  });

  return dataAsArray;
};

export default submitDataConversion;
