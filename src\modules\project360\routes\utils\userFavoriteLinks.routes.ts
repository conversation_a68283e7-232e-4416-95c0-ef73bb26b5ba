import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { UserFavoriteLink } from '../../../../entities/p_utils/UserFavoriteLink';
import UserFavoriteLinkController from '../../controllers/utils/userFavoriteLink.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUD = new CrudController<UserFavoriteLink>(UserFavoriteLink);
const controller = new UserFavoriteLinkController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.put('/:id', authenticateToken, (req, res) => CRUD.update(req, res));
router.get('/by/project/:id', authenticateToken, (req, res) =>
  CRUD.findByProjectId(req, res, 'userFav')
);
router.get('/by/user/project/:projectId', authenticateToken, controller.getByPorjectAndUser);
router.post('/', authenticateToken, (req, res) => CRUD.create(req, res));
router.delete('/:id', authenticateToken, (req, res) => CRUD.softDelete(req, res));

export default router;
