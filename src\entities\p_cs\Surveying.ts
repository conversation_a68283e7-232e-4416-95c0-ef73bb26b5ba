import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Point,
  AfterLoad,
} from 'typeorm';

import { DCEColumns } from '@entities/common/DCEColumns';

import { Project } from '../p_gen/Project';
import { Area } from '../p_gen/Area';
import { Feature } from '../p_gen/Feature';
import Structure from '@entities/p_gen/Structure';
import { SurveyType } from '@entities/p_domain/SurveyType';
import { InstrumentType } from '@entities/p_domain/InstrumentType';
import { StationAlignment } from '@entities/p_map/StationAlignment';
import { Purpose } from '@entities/p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { WorkPackageActivity } from '@entities/p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { IUser } from 'src/shared/server/platformApi/interface/IUser';
import { getUserById } from 'src/shared/server/platformApi/user';
import { SurveyPhase } from '@entities/p_domain/SurveyPhase';
@Entity({ schema: 'p_cs', name: 'surveying' })
export class Surveying extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  genralProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'genralProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  // New personnelIds column added here
  @Column({ type: 'jsonb', nullable: true })
  personnelIds?: string[];

  personnel?: IUser[] = []; // Initialize as empty array

  @Column({ nullable: true, length: 255 })
  surveyName?: string;

  @Column({ nullable: true })
  surveyTypeId?: string;

  @ManyToOne(() => SurveyType, { nullable: true })
  @JoinColumn({ name: 'surveyTypeId' })
  surveyType?: SurveyType;

  @Column({ type: 'jsonb', nullable: true })
  instrumentIds?: string[]; // Changed from instrumentTypeId to instrumentIds as array

  // Survey phase (from the new p_domain.survey_phase table)
  @Column({ nullable: true })
  executionPhaseId?: string; // Matches "executionPhaseld" in requirements

  @ManyToOne(() => SurveyPhase)
  @JoinColumn({ name: 'executionPhaseId' })
  executionPhase?: SurveyPhase; // Renamed from 'phase' to be explicit

  // Survey execution details
  @Column({ type: 'date', nullable: true })
  date?: Date; // Survey date (DATE type as per requirements)

  @Column({ type: 'enum', enum: ['Draft', 'Final', 'Signed'], default: 'Draft' })
  status!: 'Draft' | 'Final' | 'Signed'; // ENUM field with 3 possible states

  @Column({ type: 'text', nullable: true })
  notes?: string; // Optional field notes/comments

  @ManyToOne(() => InstrumentType, { nullable: true })
  @JoinColumn({ name: 'instrumentTypeId' })
  instrumentType?: InstrumentType;

  @Column({ nullable: true })
  stationAlignmentId?: string;

  @ManyToOne(() => StationAlignment, { nullable: true })
  @JoinColumn({ name: 'stationAlignmentId' })
  stationAlignment?: StationAlignment;

  @Column({ type: 'timestamp', nullable: true })
  surveyDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  updatedBy?: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @AfterLoad()
  async loadPersonnelData() {
    try {
      // Initialize as empty array if null/undefined
      this.personnel = [];

      if (!this.personnelIds || this.personnelIds.length === 0) {
        return;
      }

      // Fetch all users in parallel
      const userPromises = this.personnelIds.map((id) => getUserById(id).catch(() => null));

      const users = await Promise.all(userPromises);

      // Filter out null values and type cast
      this.personnel = users.filter((user): user is IUser => user !== null);
    } catch (error) {
      console.error('Failed to load personnel data:', error);
      this.personnel = []; // Fallback to empty array
    }
  }
}
