import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilGrainAnalysis } from '../../../../../../entities/p_cs/StSoilGrainAnalysis';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilGrainAnalysis>(StSoilGrainAnalysis);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'grainSizeAnalysis')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'grainSizeAnalysis')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'grainSizeAnalysis')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'grainSizeAnalysis')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'grainSizeAnalysis')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'grainSizeAnalysis')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'grainSizeAnalysis')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'grainSizeAnalysis')
);

export default router;
