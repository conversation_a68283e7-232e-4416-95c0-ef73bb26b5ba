import { getManager } from 'typeorm';
import { DCERelationship } from '../../../entities/p_utils/DCERelationship';
import { entityList, EntityListInterface } from '../entity/entityList';
import { entityRelationList } from '../entity/entityRelation';

const getTestTableForSampleTestRelation = async (
  sampleId: string,
  relationShipData: DCERelationship[]
) => {
  try {
    const finalOut: { [key: string]: any[] } = {};
    await Promise.all(
      relationShipData.map(async (value) => {
        if (value.targetDCE) {
          const entityString = value.targetDCE.entity;
          if (entityString && entityString in entityList) {
            const entity = entityList[entityString as keyof EntityListInterface] as any;

            const repository = getManager().getRepository(entity);
            if (entityString in entityRelationList) {
              const output = await repository.find({
                where: { sampleId, isDelete: false } as any,
                relations: entityRelationList[entityString].relation,
              });

              if (output !== null) {
                const capitalizedEntity = entityString.charAt(0).toUpperCase() + entityString.slice(1);
                finalOut[capitalizedEntity] = output;
              }
            }
          }
        }
      })
    );
    return finalOut;
  } catch (error) {
    throw error;
  }
};

export default getTestTableForSampleTestRelation;
