import { getManager } from 'typeorm';
import { TaskPriority } from '../../../../entities/p_utils/TaskPriority';
import { TaskStatus } from '../../../../entities/p_utils/TaskStatus';
import { TaskType } from '../../../../entities/p_utils/TaskType';
import { TaskAttachment } from '../../../../entities/p_utils/TaskAttachment';
import { Bucket } from '../../../../entities/p_utils/Bucket';
import { Task } from '../../../../entities/p_utils/Task';

class TaskModel {
  constructor() {}

  async addPorjectAttachment(newAttachment: TaskAttachment) {
    try {
      let attachment = new TaskAttachment();
      attachment = newAttachment;
      const projectAttachmentRepository = getManager().getRepository(TaskAttachment);
      const addedAttachment = projectAttachmentRepository.create(attachment);
      await projectAttachmentRepository.save(addedAttachment);
      return addedAttachment;
    } catch (error) {
      throw error;
    }
  }
  async findTaskByProjectId(projectId: string) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('task')
        .where('task.projectId = :projectId', { projectId })
        .andWhere('task.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('task.type', 'type')
        .leftJoinAndSelect('task.purpose', 'purpose')
        .leftJoinAndSelect('task.status', 'status')
        .leftJoinAndSelect('task.priority', 'priority')
        .orderBy('task.createdAt', 'DESC')
        .leftJoinAndSelect('task.bucket', 'bucket') // Join with the bucket entity
        .select([
          'task.id',
          'task.subject',
          'task.content',
          'task.createdBy',
          'task.createdAt',
          'task.updatedAt',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }
  async findTaskByuserId(userId: string) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('task')
        .where('task.assignedUserId = :userId', { userId })
        .andWhere('task.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('task.type', 'type')
        .leftJoinAndSelect('task.purpose', 'purpose')
        .leftJoinAndSelect('task.status', 'status')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.priority', 'priority')
        .orderBy('task.createdAt', 'DESC')
        .leftJoinAndSelect('task.bucket', 'bucket') // Join with the bucket entity
        .select([
          'task.id',
          'task.subject',
          'task.content',
          'task.createdBy',
          'task.createdAt',
          'task.updatedAt',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'Project',
          'bucket.name', // Access the name property from the bucket entity
        ])

        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }

  async findTaskByuserIdByStatus(userId: string, status: string) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('task')
        .where('task.assignedUserId = :userId', { userId })
        .andWhere('task.isDelete = :isDelete', { isDelete: false })
        .andWhere('task.status.name = :status', { status })
        .leftJoinAndSelect('task.type', 'type')
        .leftJoinAndSelect('task.purpose', 'purpose')
        .leftJoinAndSelect('task.status', 'status')
        .leftJoinAndSelect('task.project', 'project')
        .leftJoinAndSelect('task.priority', 'priority')
        .orderBy('task.createdAt', 'DESC')
        .leftJoinAndSelect('task.bucket', 'bucket') // Join with the bucket entity
        .select([
          'task.id',
          'task.subject',
          'task.content',
          'task.createdBy',
          'task.createdAt',
          'task.updatedAt',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'Project',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }

  async findTaskByTypeId(projectId: string, typeId: string) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('task')
        .where('task.projectId = :projectId', { projectId })
        .andWhere('task.isDelete = :isDelete', { isDelete: false })
        .andWhere('task.typeId = :typeId', { typeId })
        .leftJoinAndSelect('task.type', 'type')
        .leftJoinAndSelect('task.purpose', 'purpose')
        .leftJoinAndSelect('task.status', 'status')
        .leftJoinAndSelect('task.priority', 'priority')
        .orderBy('task.createdAt', 'DESC')
        .leftJoinAndSelect('task.bucket', 'bucket') // Join with the bucket entity
        .select([
          'task.id',
          'task.subject',
          'task.content',
          'task.createdBy',
          'task.createdAt',
          'task.updatedAt',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }

  async findTaskByProjectIdAndStatus(projectId: string) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('ticket')
        .where('ticket.projectId = :projectId', { projectId })
        .andWhere('ticket.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('ticket.type', 'type')
        .leftJoinAndSelect('ticket.purpose', 'purpose')
        .leftJoinAndSelect('ticket.status', 'status')
        .leftJoinAndSelect('ticket.priority', 'priority')
        .andWhere('status.name = :statusName', { statusName: 'Todo' })
        .orderBy('ticket.createdAt', 'DESC')
        .leftJoinAndSelect('ticket.bucket', 'bucket')
        .orderBy('ticket.createdAt', 'DESC')
        .select([
          'ticket.id',
          'ticket.subject',
          'ticket.content',
          'ticket.createdBy',
          'ticket.createdAt',
          'ticket.updatedAt',
          'ticket.assignedUserId',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }

  async findTaskByProjectIdAndBucket(projectId: string, bucketId: number) {
    try {
      const TaskData = await getManager()
        .getRepository(Task)
        .createQueryBuilder('ticket')
        .where('ticket.projectId = :projectId', { projectId })
        .andWhere('ticket.bucketId = :bucketId', { bucketId })
        .andWhere('ticket.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('ticket.type', 'type')
        .leftJoinAndSelect('ticket.purpose', 'purpose')
        .leftJoinAndSelect('ticket.status', 'status')
        .leftJoinAndSelect('ticket.priority', 'priority')
        .orderBy('ticket.createdAt', 'DESC')
        .leftJoinAndSelect('ticket.bucket', 'bucket') // Join with the bucket entity
        .select([
          'ticket.id',
          'ticket.subject',
          'ticket.content',
          'ticket.createdBy',
          'ticket.createdAt',
          'ticket.updatedAt',
          'ticket.assignedUserId',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.id',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getMany();

      return TaskData;
    } catch (error) {
      throw error;
    }
  }

  async findBucketByProjectId(projectId: string) {
    try {
      const TaskBucketData = await getManager()
        .getRepository(Bucket)
        .find({ where: { projectId: projectId } });

      return TaskBucketData;
    } catch (error) {
      throw error;
    }
  }

  async findBucketByProjectIdWithTask(projectId: string) {
    try {
      const TaskBucketData = await getManager()
        .getRepository(Bucket)
        .find({ where: { projectId: projectId, isDelete: false }, relations: ['task'] });

      return TaskBucketData;
    } catch (error) {
      throw error;
    }
  }

  async findPriorityByProjectId(projectId: string) {
    try {
      const structure = await getManager()
        .getRepository(TaskPriority)
        .find({ where: { projectId: projectId, isDelete: false } });

      return structure;
    } catch (error) {
      throw error;
    }
  }

  async findStatusByProjectId(projectId: string) {
    try {
      const structure = await getManager()
        .getRepository(TaskStatus)
        .find({ where: { projectId: projectId, isDelete: false } });

      return structure;
    } catch (error) {
      throw error;
    }
  }

  async findTypeByProjectId(projectId: string) {
    try {
      const structure = await getManager()
        .getRepository(TaskType)
        .find({ where: { projectId: projectId, isDelete: false } });

      return structure;
    } catch (error) {
      throw error;
    }
  }
  async findTaskById(id: number) {
    try {
      const structure = await getManager()
        .getRepository(Task)
        .createQueryBuilder('ticket')
        .where('ticket.id = :id', { id })
        .andWhere('ticket.isDelete = :isDelete', { isDelete: false })
        .leftJoinAndSelect('ticket.type', 'type')
        .leftJoinAndSelect('ticket.purpose', 'purpose')
        .leftJoinAndSelect('ticket.status', 'status')
        .leftJoinAndSelect('ticket.priority', 'priority')
        .leftJoinAndSelect('ticket.comments', 'comments')
        .leftJoinAndSelect('ticket.attachments', 'attachments')
        .leftJoinAndSelect('ticket.bucket', 'bucket') // Join with the bucket entity
        .select([
          'ticket.id',
          'ticket.subject',
          'ticket.content',
          'ticket.createdBy',
          'ticket.updatedAt',
          'attachments',
          'comments',
          'purpose',
          'status',
          'type',
          'priority',
          'bucket.name', // Access the name property from the bucket entity
        ])
        .getOne();
      // .createQueryBuilder("ticket")
      // .where("ticket.id = :id", { id: id })
      // .leftJoinAndSelect("ticket.type", "type")
      // .leftJoinAndSelect("ticket.purpose", "purpose")
      // .leftJoinAndSelect("ticket.status", "status")
      // .leftJoinAndSelect("ticket.priority", "priority")
      // .leftJoinAndSelect("ticket.comments", "comments")
      // .leftJoinAndSelect("ticket.attachments", "attachments")
      // .leftJoinAndSelect("ticket.bucket", "bucket")
      // .select([
      //   "ticket.*",
      //   "type.*",
      //   "purpose.*",
      //   "status.*",
      //   "priority.*",
      //   "comments.*",
      //   "attachments.*",
      //   "bucket.name",
      // ])
      // .getOne();

      return structure;
    } catch (error) {
      throw error;
    }
  }
}

const taskModel = new TaskModel();
export default taskModel;
