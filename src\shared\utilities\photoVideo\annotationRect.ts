import sharp from 'sharp';

import puppeteer from 'puppeteer';

export const createSVGWithForeignObject = (
  station: string | null,
  size: any,
  formattedDate: string,
  fontSize: number,
  newDescriptionObj: any
) => {
  const containerWidth = size.width;

  const descriptionRow = newDescriptionObj.additionaldescription
    ? `
      <div style="padding: 5px; box-sizing: border-box;">
        <strong style="color: #0056b3;">Description:</strong> ${newDescriptionObj.additionaldescription}
      </div>
    `
    : '';

  const descriptionRowLandscape = newDescriptionObj.additionaldescription
    ? `
        <strong style="color: #0056b3; padding-left: 20px;">Description:</strong> ${newDescriptionObj.additionaldescription}
    `
    : '';

  // Generate HTML based on width
  let detailsHTML;

  if (containerWidth === 1200) {
    detailsHTML = `
      <!-- Project and Description -->
      <div style="padding: 5px; box-sizing: border-box;">
        <strong style="color: #0056b3;">Project:</strong> ${newDescriptionObj.projectname || ''}
        <strong style="color: #0056b3; padding-left: 200px;">Purpose:</strong> ${
          newDescriptionObj.purpose || ''
        }
      </div>

      <!-- Line 1 -->
      <div style="padding: 5px; box-sizing: border-box; display: flex;">
        <div style="flex: 0 0 30%;">
          <strong style="color: #0056b3;">Contractor:</strong> ${
            newDescriptionObj.contractor || ''
          },
        </div>
        <div style="flex: 0 0 35%;">
          <strong style="color: #0056b3;">Contract No:</strong> ${
            newDescriptionObj.contractorNumber || ''
          }
        </div>
        <div style="flex: 0 0 35%;">
          <strong style="color: #0056b3;">Project Area:</strong> ${
            newDescriptionObj.generalProjectArea || ''
          }
        </div>
      </div>

      <!-- Line 2 -->
      <div style="padding: 5px; box-sizing: border-box; display: flex;">
        <div style="flex: 0 0 40%;">
          <strong style="color: #0056b3;">Date:</strong> ${formattedDate}
        </div>
        <div style="flex: 0 0 60%;">
          <strong style="color: #0056b3;">Work Element:</strong> ${
            newDescriptionObj.workElement || ''
          }
        </div>

      </div>

      <!-- Line 3 -->
      <div style="padding: 5px; box-sizing: border-box; display: flex; gap: 10px;">
        <div style="flex: 0 0 30%;">
          <strong style="color: #0056b3;">Direction:</strong> ${
            newDescriptionObj?.imageDirection || ''
          }
        </div>
        <div style="flex: 0 0 35%;">
          <strong style="color: #0056b3;">Location:</strong> ${station || ''}, ${
            newDescriptionObj?.offset || ''
          }
        </div>
        <div style="flex: 0 0 35%;">
          <strong style="color: #0056b3;">Photo #:</strong> ${newDescriptionObj?.photoNumber || ''}
        </div>
      </div>
      ${descriptionRow}
    `;
  } else if (containerWidth === 1600) {
    detailsHTML = `
      <!-- Project and Description -->
      <div style="padding: 5px; box-sizing: border-box; font-size: ${fontSize - 1}px;">
        <strong style="color: #0056b3;">Project:</strong> ${newDescriptionObj.projectname || ''}
      </div>

      <!-- Line 1 -->
      <div style="padding: 5px; box-sizing: border-box; display: flex;">
        <div style="flex: 0 0 22%;">
          <strong style="color: #0056b3;">Contractor:</strong> ${newDescriptionObj.contractor || ''}
        </div>
        <div style="flex: 0 0 25%;">
          <strong style="color: #0056b3;">Contract No:</strong> ${
            newDescriptionObj.contractorNumber || ''
          }
        </div>
        <div style="flex: 0 0 30%;"> <!-- Date occupies 25% -->
          <strong style="color: #0056b3;">Date:</strong> ${formattedDate}
        </div>

        <div style="flex: 0 0 28%;">
          <strong style="color: #0056b3;">Project Area:</strong> ${
            newDescriptionObj.generalProjectArea || ''
          }
        </div>
      </div>

      <!-- Line 2 -->
      <div style="padding: 5px; box-sizing: border-box; display: flex; gap: 10px;">

        <div style="flex: 0 0 22%;"> <!-- Direction occupies 25% -->
          <strong style="color: #0056b3;">Direction:</strong> ${
            newDescriptionObj?.imageDirection || ''
          }
        </div>
        <div style="flex: 0 0 22%;"> <!-- Location occupies 30% -->
          <strong style="color: #0056b3;">Location:</strong> ${station || ''}, ${
            newDescriptionObj?.offset || ''
          }
        </div>
        <div style="flex: 0 0 11%;"> <!-- Photo # occupies 20% -->
          <strong style="color: #0056b3;">Photo #:</strong> ${newDescriptionObj?.photoNumber || ''}
        </div>
        <div style="flex: 0 0 45%;  font-size: ${fontSize - 1}px;">
          <strong style="color: #0056b3;">Work Element:</strong> ${
            newDescriptionObj.workElement || ''
          }
        </div>
      </div>
      <div style="padding: 5px; box-sizing: border-box;">
       <strong style="color: #0056b3;">Purpose:</strong> ${newDescriptionObj.purpose || ''}
       ${descriptionRowLandscape}
       </div>
    `;
  }

  const divHTML = `
    <div xmlns="http://www.w3.org/1999/xhtml" style="font-family: 'Roboto', Arial, sans-serif; font-size: ${fontSize}px; width: ${containerWidth}px; background-color: #f9f9f9; padding: 15px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); box-sizing: border-box;">
      ${detailsHTML}
    </div>
  `;

  return divHTML;
};

export const renderSVGToImage = async (svgContent: any, outputPath: any, size: any) => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  // Set the content with a wrapper div for accurate height measurement
  await page.setContent(`
    <html>
      <body style="margin: 0; padding: 0; display: flex; justify-content: center; align-items: flex-start; box-sizing: border-box;">
        <div id="wrapper" style="display: inline-block;">
          ${svgContent}
        </div>
      </body>
    </html>
  `);

  // Wait for the content to render
  await page.waitForSelector('#wrapper'); // Ensure wrapper is rendered

  // Calculate the actual height of the wrapper
  const dynamicHeight = await page.evaluate(() => {
    const wrapper = document.getElementById('wrapper');
    if (!wrapper) {
      return 0;
    }
    return wrapper.offsetHeight; // Get the rendered height
  });

  // Adjust viewport height
  await page.setViewport({
    width: size.width,
    height: dynamicHeight,
  });

  // Take a screenshot of the rendered content
  const element = await page.$('#wrapper');
  if (!element) {
    throw new Error('Failed to find the wrapper element for rendering SVG.');
  }

  await element.screenshot({ path: outputPath, type: 'png' });

  await browser.close();
};

export const createSVGAnnotation = async (
  station: string | null,
  metadata: sharp.Metadata,
  formattedDate: string,
  size: any,
  fontSize: number,
  newDescriptionObj: any,
  outputPath: string
) => {
  // Step 1: Generate the SVG with a foreignObject table
  const svgContent = createSVGWithForeignObject(
    station,
    size,
    formattedDate,
    fontSize,
    newDescriptionObj
  );

  // Step 2: Render the SVG to an image using Puppeteer
  await renderSVGToImage(svgContent, outputPath, size);
  return outputPath;
};
