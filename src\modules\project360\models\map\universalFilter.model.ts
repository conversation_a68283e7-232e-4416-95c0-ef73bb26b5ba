import { getManager } from 'typeorm';
import { UniversalFilterMapping } from '../../../../entities/p_map/UniversalFilterMapping';
import { UniversalFilter } from '../../../../entities/p_map/UniversalFilter';

class UniversalFilterModel {
  Repo = getManager().getRepository(UniversalFilter);
  findByFilterId = async (layerId: string, projectId: string) => {
    try {
      const alreadyUsedFilter = await getManager()
        .getRepository(UniversalFilterMapping)
        .find({
          where: { layerId: layerId, projectId, isDelete: false },
        });
      const data = await this.Repo.find({
        where: { projectId, isDelete: false },
        relations: ['layerMapping', 'layerMapping.layer'],
        order: { name: 'ASC' },
      });

      return data.filter((value) => !alreadyUsedFilter.find((item) => item.filterId === value.id));
    } catch (error) {
      throw error;
    }
  };
  getByProjectId = async (projectId: string) => {
    try {
      const data = await this.Repo.find({
        where: { projectId, isDelete: false },
        relations: ['layerMapping', 'layerMapping.layer'],
        order: { name: 'ASC' },
      });

      return data;
    } catch (error) {
      throw error;
    }
  };
  delete = async (id: string) => {
    try {
      const entityManager = getManager();
      const result = await entityManager.transaction(async (transactionalEntityManager) => {
        const data = await transactionalEntityManager.findOne(UniversalFilter, {
          where: { id, isDelete: false },
          relations: ['layerMapping', 'layerMapping.layer'],
          order: { name: 'ASC' },
        });
        await transactionalEntityManager.update(UniversalFilter, { id }, { isDelete: true });
        if (data?.layerMapping && data?.layerMapping?.length > 0) {
          for (const element of data?.layerMapping) {
            await transactionalEntityManager.update(
              UniversalFilterMapping,
              { id: element.id },
              { isDelete: true }
            );
          }
        }
        return data;
      });
      return result;
    } catch (error) {
      throw error;
    }
  };
}

export default UniversalFilterModel;
