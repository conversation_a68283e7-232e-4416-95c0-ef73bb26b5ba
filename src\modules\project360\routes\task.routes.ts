import express, { Router } from 'express';
import CrudController from '../../generic/crudDriver.controller';
// import { Ticket } from '../../../entities/g_supp/Ticket';
// import ticketController from '../../platform/controllers/Ticket.controller';
// import { TicketComment } from '../../../entities/g_supp/TicketComment';
// import { Bucket } from '../../../entities/g_supp/Bucket';
// import { TicketPriority } from '../../../entities/g_supp/TicketPriority';
// import { TicketStatus } from '../../../entities/g_supp/TicketStatus';
// import { TicketType } from '../../../entities/g_supp/TicketType';
import { sftpUploadMiddleware } from '../../../shared/middlewares/uploadFileSftp.middleware';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import { changeCreatedUserId } from '../../../shared/middlewares/task.middleware';
import { TaskComment } from '../../../entities/p_utils/TaskComment';
import { TaskPriority } from '../../../entities/p_utils/TaskPriority';
import { TaskStatus } from '../../../entities/p_utils/TaskStatus';
import { Bucket } from '../../../entities/p_utils/Bucket';
import { TaskType } from '../../../entities/p_utils/TaskType';
import taskController from '../controllers/task/Ticket.controller';
import { Task } from '../../../entities/p_utils/Task';

const router: Router = express.Router();

// Create a generic router for the User entity
const TaskController = new CrudController<Task>(Task);
const TaskCommentController = new CrudController<TaskComment>(TaskComment);
const TaskPriorityController = new CrudController<TaskPriority>(TaskPriority);
const TaskStatusController = new CrudController<TaskStatus>(TaskStatus);
const BucketController = new CrudController<Bucket>(Bucket);
const TaskTypeController = new CrudController<TaskType>(TaskType);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/general', authenticateToken, changeCreatedUserId, (req, res) =>
  TaskController.create(req, res)
);
router.get('/general', authenticateToken, TaskController.findAll);
router.put('/general/:id', authenticateToken, (req, res) => TaskController.update(req, res));
router.get('/by/project/:id', authenticateToken, taskController.findTaskByProjectId);
router.get('/by/user/:id', authenticateToken, taskController.findTaskByUserId);
router.get('/by/user/:id/:status', authenticateToken, taskController.findTaskByUserIdWithStatus);
router.get('/by/project/type/:id/:typeId', authenticateToken, taskController.findTaskByTypeId);
router.get(
  '/by/project/bucket/:id/:bucketId',
  authenticateToken,
  taskController.findTaskByProjectIdAndBucketId
);
router.get('/by/:id', authenticateToken, taskController.findTaskById);
router.post('/comment', authenticateToken, (req, res) => TaskCommentController.create(req, res));
router.put('/comment/:id', authenticateToken, (req, res) => TaskCommentController.update(req, res));
router.post('/priority', authenticateToken, (req, res) => TaskPriorityController.create(req, res));
router.put('/priority/:id', authenticateToken, (req, res) =>
  TaskPriorityController.update(req, res)
);
router.get('/priority/by/project/:id', authenticateToken, taskController.findpriorityByProjectId);
router.use('/status', authenticateToken, TaskStatusController.getRouter());
router.get('/status/by/project/:id', authenticateToken, taskController.findStatusByProjectId);
// type
router.post('/type', authenticateToken, (req, res) => TaskTypeController.create(req, res));
router.put('/type/:id', authenticateToken, (req, res) => TaskTypeController.update(req, res));
router.get('/type/:id', authenticateToken, TaskTypeController.findById);
router.delete('/type/:id', authenticateToken, (req, res) =>
  TaskTypeController.softDelete(req, res)
);
router.get('/type/by/project/:id', authenticateToken, taskController.findTypeByProjectId);
// bucket
router.post('/bucket/project', authenticateToken, (req, res) => BucketController.create(req, res));
router.put('/bucket/project/:id', authenticateToken, (req, res) =>
  BucketController.update(req, res)
);
router.delete('/bucket/project/:id', authenticateToken, (req, res) =>
  BucketController.softDelete(req, res)
);
router.get('/bucket/project/:id', authenticateToken, BucketController.findById);
router.get('/get/bucket/:id', authenticateToken, taskController.findBucketByProjectId);
router.get(
  '/get/bucket/by/project/with/task/:id',
  authenticateToken,
  taskController.findBucketByProjectIdWithTask
);
router.post(
  '/attachment/create',
  authenticateToken,
  sftpUploadMiddleware,
  taskController.addTaskAttachment
);

export default router;
