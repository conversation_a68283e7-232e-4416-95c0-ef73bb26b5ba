import {
  <PERSON><PERSON><PERSON>,
  Colum<PERSON>,
  <PERSON>To<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Test } from './Test'; // Import the Tests entity
import { TestMethod } from './TestMethod'; // Import the Tests entity
import { SpecAgency } from './SpecAgency';

@Entity({ schema: 'p_meta' })
export class TestVariant {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  testId?: string;

  @ManyToOne(() => Test, (test) => test.testVariant) 
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true }) 
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  variantCode?: string;

  @Column()
  variantName?: string;

  @Column({ nullable: true })
  variantAlias?: string;

  @Column()
  description?: string;

  @Column({ nullable: true })
  specAgencyId?: string;

  @JoinColumn({ name: 'specAgencyId' })
  @ManyToOne(() => SpecAgency, { nullable: true }) 
  specAgency?: SpecAgency;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
