import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { Borehole } from '../../../../../entities/p_cs/Borehole';
import { UploadBoreholeFileMiddleware } from '../../../../../shared/middlewares/uploadFileSftp.middleware';
import BoreholeController from '../../../controllers/cs/borehole/borehole.controller';

const router: Router = express.Router();

const GenricController = new CrudController<Borehole>(Borehole);

const controller = new BoreholeController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'borehole')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'borehole')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'borehole')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'borehole')
);
router.post('/', authenticateToken, UploadBoreholeFileMiddleware, (req, res) =>
  GenricController.create(req, res, 'borehole')
);
router.put('/:id', authenticateToken, UploadBoreholeFileMiddleware, (req, res) =>
  GenricController.update(req, res, 'borehole')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'borehole')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'borehole')
);

router.get(
  '/all/verification',
  authenticateToken,
  controller.getAllBoreholesWithVerificationPurpose
);
router.get(
  '/by/stations/:stationStart/:stationEnd/:projectId',
  authenticateToken,
  controller.getBoreholesByStationsRange
);

export default router;
