import express, { Router } from 'express';
import dashboardRouters from './dashboard.routes';
import projectRouters from './project.routes';
import stakeholerRouters from './stakeholder.routes';
import stakeUserRouters from './stakeUser.routes';
import featureRouters from './feature.routes';
import structureRouters from './structure.routes';
import structureTypeRouters from './structureType.routes';
import projectMaterialRouters from './projectMaterial.routes';
import materialRouters from './material.routes';
import milestoneRouters from './milestone.routes';
import projectOrgStructureRouters from './organizationStructure.routes';
import taskRouters from './task.routes';
import projectFileRouters from './projectFile.routes';
import auditRouters from './auditLog.routes';
import testRouters from './meta/test.routes';
import stageRouters from './meta/stage.routes';
import testMethodRouters from './meta/testMethod.routes';
import testVariantRouters from './meta/testVariant.routes';
import unitsRouters from './meta/units.routes';
import unitCategoriesRouters from './meta/unitCategories.routes';
import phaseRouters from './phase.routes';
import testConfigRoutes from './testConfig.routes';
import approvalSetupRoutes from './approvalSetup.routes';
import approverSetupRoutes from './approverSetup.routes';
import approvalRoutes from './approval.routes';
import sampleRoutes from './cs/sample.routes';
import importRoutes from './import.routes';
import importTemplateRoutes from './importTemplate.routes';
import stieRoutes from './site.routes';
import purposeRoutes from './purpose.routes';
import sampleTypeRoutes from './sampleType.routes';
import specAgencyRoutes from './specAgency.routes';
import photoVideoRoutes from './photoVideo.routes';
import mediaRoutes from './mediaType.routes';
import projectActivityRoutes from './projectActivity.routes';
import dceRoutes from './meta/dce.routes';
import sftpConfigRoutes from './sftpConfig.routes';
import submittalRoutes from './submittal.routes';
import submittalDocumentRoutes from './submittalDocument.routes';
import reportDocumentRoutes from './reportDocument.routes';
import reportRoutes from './report.routes';
import projectFormRoute from './projectForm.routes';
import customFormDataRoute from './gen/customFormData.routes';
import generalProjectAreaRoute from './gen/generalProjectArea.routes';
import clsmRoute from './gen/clsm/clsm.routes';
import ctLogRoute from './gen/ctLog.routes';
import surveyFileRoutes from './gen/surveyFile.routes';
import graphConfigRoutes from './gen/graphConfig.routes';
import ctLogConfigRoute from './gen/ctLogConfig.routes';
import testLogPrefixRoute from './gen/testLogPrefix.routes';
import mediaConfigRoute from './gen/mediaConfig.routes';
import ProjectLogoRoute from './gen/projectLogo.routes';
import reportListRoute from './gen/reportList.routes';
import reportCategoryRoute from './gen/reportCategory.route';
import { SurveyPhase } from '@entities/p_domain/SurveyPhase';

// utils
import projectConfigRoute from './utils/projectConfig.routes';
import dataGridRoutes from './utils/dataGrid.routes';
import dceRelationshipRoutes from './utils/dceRelationship.routes';
import emailNotificationConfigurationRoutes from './utils/emailNotificationConfiguration.routes';
import projectNotificationRoutes from './utils/projectNotificaiton.routes';
import reportsAuthRoutes from './utils/reportAuth.routes';
import userFavoriteLinkRoutes from './utils/userFavoriteLinks.routes';
// DCE Configuration
import dceConfigurationRotues from './dceConfiguration.routes';
import adminDCEConfigRoutes from './meta/adminDCEConfiguration.routes';
import dceValidationRotues from './dceValidation.routes';

// work package
import equipmentRotues from './equipment.routes';
import workPackageRotues from './workPackage/workPackage.routes';
import workPackageActivityRotues from './workPackage/workPackageActivity.routes';
import eventLogRotues from './workPackage/eventLog.routes';

// cs
import observationRoutes from './cs/observation.routes';
import sampleSpecimenRoutes from './cs/sampleSpecimen.routes';
import panelInformationRoutes from './cs/panelInformation/panelInformation.routes';
import cutInformationRoutes from './cs/cutInformation/cutInformation.routes';
import pvCutInformationRoutes from './cs/cutInformation/pvCutInformation.routes';
import pvCutMasterRoutes from './cs/cutInformation/pvCutMaster.routes';
import pvCLSMPlacementRoutes from './cs/cutInformation/pvCLSMPlacement.routes';
import  surveyingRoutes from './cs/surveying.routes';
import clearingGrubbingRoutes from './cs/clearingGrubbing.routes'

// HCtest
import hydraulicConductivityRoutes from './cs/hydraulicConductivity.routes';
import hydraulicConductivityWorksheetRoutes from './cs/hydraulicConductivityWorksheet.routes';
import hydraulicConductivityCellinfoRoutes from './cs/hcTestCellInfo.routes';
import seepageBarrierRoutes from './cs/seepageBarrier.routes';
// drilling
import boreholeRoutes from './cs/drilling/borehole.routes';

//clsmConcrete
import concreteMixDesignRoutes from './cs/clsmConcrete/concreteMixDesign.routes';
import stDensityCLSMRoutes from './cs/clsmConcrete/stDensityCLSM.routes';
import stConcreteBleedingRoutes from './cs/clsmConcrete/stConcreteBleeding.routes';
import stConcreteBleedingWorksheetRoutes from './cs/clsmConcrete/stConcreteBleedingWorksheet.routes';
import concreteBatchTicketRoutes from './cs/clsmConcrete/concreteBatchTicket.routes';
import concreteTemperatureRoutes from './cs/clsmConcrete/stConcreteTemperature.routes';
import concreteCompressiveStrengthnRoutes from './cs/clsmConcrete/stConcreteCompressiveStrength.routes';
import unconfinedCompressiveStrengthRoutes from './qms/stSoilUCS.routes';
import slumpTestRoutes from './cs/clsmConcrete/stConcreteSlump.routes';
import concreteSettingTimeRotues from './cs/clsmConcrete/stConcreteSettingTime.routes';
import concreteDensityRotues from './cs/clsmConcrete/stConcreteDensity.routes';
import cylindricalSpecimenHeightChangeRoute from './cs/clsmConcrete/stCylindricalSpecimenHeightChange.routes';

// blast
import blastReportRoutes from './cs/blast/blastReport.routes';
import blastHoleRoutes from './cs/blast/blastHole.routes';
import blastEvaluationRoutes from './cs/blast/blastEvaluation.routes';
import deckLoadingRoutes from './cs/blast/deckLoading.routes';
import explosiveTicketRoutes from './cs/blast/explosiveTicket.routes';
import penetrationRateRoutes from './cs/blast/penetrationRate.routes';
import remedialTreatmentRoutes from './cs/blast/remedialTreatment.routes';
import vibrationRoutes from './cs/blast/vibration.routes';
// Soil grain analysis
import soilGrainAnalysisRoutes from './cs/soilGrainAnalysis.routes';
import soilGrainAnalysisWorksheetRoutes from './cs/soilGrainAnalysisWorksheet.routes';
import soilGrainAnalysisHydrometerRoutes from './cs/soilGrainAnalysisHydrometerReading.routes';
// Sieve size
import postBlastVideoRoutes from './cs/blast/postBlastVideo.routes';
import blastDamageSurveyRoutes from './cs/blast/blastDamageSurvey.routes';
import fieldSurveyRoutes from './cs/fieldSurvey.routes';
import surveyFeatureRoutes from './cs/surveyFeature.routes';
import concreteIssuesCutoffWallRoutes from './cs/concreteIssuesCutoffWall.routes';
import foundationPreparationRoutes from './cs/foundationPreparation.routes';
// monitoring
import turbidityTestingRoutes from './cs/monitoring/turbidityTesting.routes';
// insturmentation
import piezometerRoutes from './cs/instrumentation/piezometer.routes';
import seismographRoutes from './cs/instrumentation/seismograph.routes';
// soil
// field test
import stSoilNuclearGaugeTestRoutes from './cs/soil/fieldTest/stSoilNuclearGaugeTest.routes';
import stSoilSandConeTestRoutes from './cs/soil/fieldTest/stSoilSandConeTest.routes';
// lab test
import stSoilGrainAnalysisRoutes from './cs/soil/labTest/stSoilGrainAnalysis.routes';
import stAggregateSieveAnalysisRoutes from './qms/stAggregateSieveAnalysis.routes';
import stSoilGrainAnalysisWorksheetRoutes from './cs/soil/labTest/stSoilGrainAnalysisWorksheet.routes';
import soilGrainAnalysisHydrometerReadingRoutes from './cs/soilGrainAnalysisHydrometerReading.routes';
import stSoilMoistureContentRoutes from './cs/soil/labTest/stSoilMoistureContent.routes';
import stSoilOrganicContentRoutes from './cs/soil/labTest/stSoilOrganicContent.routes';
import stSoilPassing200SieveRoutes from './cs/soil/labTest/stSoilPassing200Sieve.routes';
import stSoilProctorTestRoutes from './cs/soil/labTest/stSoilProctorTest.routes';
import stSoilSampleClassificationRoutes from './cs/soil/labTest/stSoilSampleClassification.routes';
import stSoilAtterbergLimitRoutes from './cs/soil/labTest/stSoilAtterbergLimit.routes';
import stSoilAbsorptionSpecificGravityRoutes from './cs/soil/labTest/stSoilAbsorptionSpecificGravity.routes';

// domian
import materialTypeRoutes from './domian/materialType.routes';
import featureTypeRoutes from './domian/featureType.routes';
import featureLocationRoutes from './domian/featureLocation.routes';
import featureSubLocationRoutes from './domian/featureSubLocation.routes';
import instrumentStatusRoutes from './domian/instrumentStatus.routes';
import monitoringAspectRoutes from './domian/monitoringAspect.routes';
import monitoringInstrumentRoutes from './domian/monitoringInstrument.routes';
import surveyTypeRoutes from './domian/surveyType.routes';
import actionCodeRoutes from './domian/actionCode.routes';
import submittalClassificationRoutes from './domian/submittalClassification.routes';
import submittalType from './domian/submittalType.routes';
import submittalList from './domian/submittalList.routes';
import submittalReviewCode from './domian/submittalReviewCode.routes';
import reportTypeRoute from './domian/reportType.routes';
import blastTypeRoute from './domian/blastType.routes';
import overbreakTypeRoute from './domian/overbreakType.routes';
import movementRoute from './domian/movement.routes';
import flyRockAssessmentRoute from './domian/flyRockAssessment.routes';
import remedialAnchorTypeRoute from './domian/remedialAnchorType.routes';
import remedialTreatmentTypeRoute from './domian/remedialTreatmentType.routes';
import specimenTypeRoute from './domian/specimenType.routes';
import designStrengthRoute from './domian/designStrength.routes';
// meta
import subSpecificationRoutes from './subSpecification.routes';
import submittalFormListRoutes from './submittalFormList.routes';
import formRoutes from './meta/formConfiguration/form.routes';
import dceRelationRoutes from './meta/dceRelation.routes';
import relationShipRoutes from './meta/relationship.routes';
import keyValueStoreRoutes from './meta/keyValueStore.routes';
import sieveConfigRoutes from './meta/sieveConfig.routes';
import approvalStatusRoutes from './meta/approvalStatus.routes';
// role
import projectRoleRoute from './role and permission/projectRole.routes';
import RoleDataccessRoute from './auth/roleDataAccess.routes';
import MobileRoute from './auth/mobileAccess.routes';
import stakeholderRoleRoutes from './stakeholderRoles.routes';
// map
import geomLayerRoutes from './map/geomLayer.routes';
import layerRoutes from './map/layers.routes';
import legendRoutes from './map/legend.routes';
import layerParentRoutes from './map/layerParent.routes';
import mapConfigRoutes from './map/mapConfig.routes';
import mgeoJSONConfigRoutes from './map/geoJSONConfig.routes';
import stationRoutes from './map/station.routes';
import fieldDensityRouters from './cs/soil/fieldDensity.routes';
import universalFilterRoutes from './map/universalFilter.routes';
import universalFilterMappingRoutes from './map/universalFilterMapping.routes';
import stationAlignmentRoutes from './map/stationAlignment.routes'
import testResultPopupRoutes from './map/testResultPopup.routes';
import healthCheckRouters from './healthCheck.routes';

import createCrudRoutes from './routes';
import { ProctorMold } from '@entities/p_meta/ProctorMold';
import { ProctorMold as ProctorMoldDomain } from '@entities/p_domain/ProctorMold';
import { SieveSize } from '@entities/p_domain/SieveSize';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { CTPhase } from '@entities/p_meta/CTPhase';

const router: Router = express.Router();

router.use('/health', healthCheckRouters);

// dashboard
router.use('/dashboard', dashboardRouters);

// gen
router.use('/gen/Project', projectRouters);
router.use('/gen/phase', phaseRouters);
router.use('/gen/feature', featureRouters);
router.use('/gen/structure', structureRouters);
router.use('/gen/type/structure', structureTypeRouters);
router.use('/gen/projectmaterial', projectMaterialRouters);
router.use('/gen/milestone', milestoneRouters);
router.use('/gen/general/project/area', generalProjectAreaRoute);
router.use('/gen/clsm', clsmRoute);
router.use('/gen/ctlog', ctLogRoute);
router.use('/gen/testlog', ctLogRoute);
router.use('/gen/ctlogconfig', ctLogConfigRoute);
router.use('/gen/testlogprefix', testLogPrefixRoute);
router.use('/gen/mediaconfig', mediaConfigRoute);
router.use('/gen/projectlogo', ProjectLogoRoute);
router.use('/gen/reportlist', reportListRoute);
router.use('/gen/reportcategory', reportCategoryRoute);
router.use('/gen/surveyFile',surveyFileRoutes);

// router.use('/gen/task', taskRouters);
router.use('/gen/photovideo', photoVideoRoutes);
router.use('/gen/site', stieRoutes);
router.use('/gen/file', projectFileRouters);
router.use('/gen/audit', auditRouters);
router.use('/gen/testconfig', testConfigRoutes);
router.use('/gen/submittal', submittalRoutes);
router.use('/gen/activity', projectActivityRoutes);
router.use('/gen/submittaldocument', submittalDocumentRoutes);
router.use('/gen/report', reportRoutes);
router.use('/gen/reportdocument', reportDocumentRoutes);
router.use('/gen/sftp/config', sftpConfigRoutes);
router.use('/gen/form', projectFormRoute);
router.use('/gen/custom/formdata', customFormDataRoute);
// DCE Configuration
router.use('/gen/dce/configuration', dceConfigurationRotues);
router.use('/gen/dce/validation', dceValidationRotues);

// Graph Config
router.use('/gen/graph/config', graphConfigRoutes);

// work package
router.use('/gen/equipment', equipmentRotues);
router.use('/gen/workpackage', workPackageRotues);
router.use('/gen/workpackageactivity', workPackageActivityRotues);
router.use('/gen/eventlog', eventLogRotues);
// meta
router.use('/meta/material', materialRouters);
router.use('/meta/materialType', createCrudRoutes(MaterialType));
router.use('/meta/ctphase', createCrudRoutes(CTPhase));
router.use('/meta/test', testRouters);
router.use('/meta/stage', stageRouters);
router.use('/meta/testmethod', testMethodRouters);
router.use('/meta/testvariant', testVariantRouters);
router.use('/meta/units', unitsRouters);
router.use('/meta/unit/categories', unitCategoriesRouters);
router.use('/meta/purpose', purposeRoutes);
router.use('/meta/sampleType', sampleTypeRoutes);
router.use('/meta/specagency', specAgencyRoutes);
router.use('/meta/mediatype', mediaRoutes);
router.use('/meta/dce', dceRoutes);
router.use('/meta/relation/dce', dceRelationRoutes);
router.use('/meta/relationship', relationShipRoutes);
router.use('/meta/sieveconfig', sieveConfigRoutes);
router.use('/meta/dce/configuration', adminDCEConfigRoutes);
router.use('/meta/subspecification', subSpecificationRoutes);
router.use('/meta/submittal/form/list', submittalFormListRoutes);
router.use('/meta/form', formRoutes);
router.use('/meta/approval/status', approvalStatusRoutes);
router.use('/meta/proctormold', createCrudRoutes(ProctorMold));
router.use('/meta/keyvaluestore', keyValueStoreRoutes);
// util
router.use('/util/approvalsetup', approvalSetupRoutes);
router.use('/util/approversetup', approverSetupRoutes);
router.use('/util/approval', approvalRoutes);
router.use('/util/config', projectConfigRoute);
router.use('/util/task', taskRouters);
router.use('/util/dataGrid', dataGridRoutes);
router.use('/util/relationship', dceRelationshipRoutes);
router.use('/util/email/notification', emailNotificationConfigurationRoutes);
router.use('/util/project/notification', projectNotificationRoutes);
router.use('/util/reports/auth', reportsAuthRoutes);
router.use('/util/favorite/links', userFavoriteLinkRoutes);
// cs
router.use('/cs/samplemanagement', sampleRoutes);
router.use('/cs/samplespecimen', sampleSpecimenRoutes);
router.use('/cs/observation', observationRoutes);
router.use('/cs/hydraulicconductivity', hydraulicConductivityRoutes);
router.use('/cs/hydraulicconductivity/worksheet', hydraulicConductivityWorksheetRoutes);
router.use('/cs/hydraulicconductivity/cellinfo', hydraulicConductivityCellinfoRoutes);
router.use('/cs/grainanalysis/test', soilGrainAnalysisRoutes);
router.use('/cs/grainanalysis/worksheet', soilGrainAnalysisWorksheetRoutes);
router.use('/cs/grainanalysis/hydrometer', soilGrainAnalysisHydrometerRoutes);
router.use('/cs/fieldsurvey', fieldSurveyRoutes);
router.use('/cs/surveyfeature', surveyFeatureRoutes);
router.use('/cs/concreteissuescutoffwall', concreteIssuesCutoffWallRoutes);
router.use('/cs/foundationpreparation', foundationPreparationRoutes);
router.use('/cs/seepagebarrier', seepageBarrierRoutes);
router.use('/cs/panelinformation', panelInformationRoutes);
router.use('/cs/cutinformation', cutInformationRoutes);
router.use('/cs/pv/cutinformation', pvCutInformationRoutes);
router.use('/cs/pv/cutmaster', pvCutMasterRoutes);
router.use('/cs/cutmaster', pvCutMasterRoutes);
router.use('/cs/pv/clsmplacement', pvCLSMPlacementRoutes);
router.use('/cs/clsmplacement', pvCLSMPlacementRoutes);
router.use('/cs/surveying',surveyingRoutes)
router.use('/cs/clearingGrubbing',clearingGrubbingRoutes)
// router.use('/cs/seismograph', ucsTestRoutes);
// monitoring
router.use('/cs/turbiditytesting', turbidityTestingRoutes);
// instrumentaion
router.use('/cs/piezometer', piezometerRoutes);
router.use('/cs/seismograph', seismographRoutes);
// drilling
router.use('/cs/borehole', boreholeRoutes);

router.use('/cs/compressivestrength', concreteCompressiveStrengthnRoutes);
router.use('/cs/unconfinedcompressivestrength', unconfinedCompressiveStrengthRoutes);
router.use('/cs/stdensityclsm', stDensityCLSMRoutes);
router.use('/cs/concretebleed', stConcreteBleedingRoutes);
router.use('/cs/stconcretebleedingworksheet', stConcreteBleedingWorksheetRoutes);
router.use('/cs/concretebatchticket', concreteBatchTicketRoutes);
router.use('/cs/concretemixdesign', concreteMixDesignRoutes);
router.use('/cs/concretetemperature', concreteTemperatureRoutes);
router.use('/cs/slumptest', slumpTestRoutes);
router.use('/cs/concretesettingtime', concreteSettingTimeRotues);
router.use('/cs/concretedensity', concreteDensityRotues);
router.use('/cs/shrinkage', cylindricalSpecimenHeightChangeRoute);

// blast
router.use('/cs/postblastvideo', postBlastVideoRoutes);
router.use('/cs/blastdamagesurvey', blastDamageSurveyRoutes);
router.use('/cs/blastreport', blastReportRoutes);
router.use('/cs/blasthole', blastHoleRoutes);
router.use('/cs/blastevaluation', blastEvaluationRoutes);
router.use('/cs/deckloading', deckLoadingRoutes);
router.use('/cs/explosiveticket', explosiveTicketRoutes);
router.use('/cs/penetrationrate', penetrationRateRoutes);
router.use('/cs/penetrationrate', penetrationRateRoutes);
router.use('/cs/remedialtreatment', remedialTreatmentRoutes);
router.use('/cs/vibration', vibrationRoutes);
// soil
// field test
router.use('/cs/soil/sandconetest', stSoilSandConeTestRoutes);
router.use('/cs/soil/nucleargaugetest', stSoilNuclearGaugeTestRoutes);
router.use('/cs/sandconetest', stSoilSandConeTestRoutes);
router.use('/cs/nucleargaugetest', stSoilNuclearGaugeTestRoutes);
// lab test
router.use('/cs/soil/grainsizeanalysis', stSoilGrainAnalysisRoutes);
router.use('/cs/soil/grainsizeanalysisworksheet', stSoilGrainAnalysisWorksheetRoutes);
router.use('/cs/soil/moisturecontent', stSoilMoistureContentRoutes);
router.use('/cs/soil/organiccontent', stSoilOrganicContentRoutes);
router.use('/cs/soil/passing200sieve', stSoilPassing200SieveRoutes);
router.use('/cs/soil/proctor', stSoilProctorTestRoutes);
router.use('/cs/soil/sampleclassification', stSoilSampleClassificationRoutes);
router.use('/cs/soil/atterberglimits', stSoilAtterbergLimitRoutes);
router.use('/cs/soil/absorptionandspecificgravity', stSoilAbsorptionSpecificGravityRoutes);
router.use('/cs/grainsizeanalysis', stSoilGrainAnalysisRoutes);
router.use('/cs/sieveanalysis', stAggregateSieveAnalysisRoutes);
router.use('/cs/grainanalysishydrometerreading', soilGrainAnalysisHydrometerReadingRoutes);
router.use('/cs/moisturecontent', stSoilMoistureContentRoutes);
router.use('/cs/organiccontent', stSoilOrganicContentRoutes);
router.use('/cs/passing200sieve', stSoilPassing200SieveRoutes);
router.use('/cs/proctor', stSoilProctorTestRoutes);
router.use('/cs/sampleclassification', stSoilSampleClassificationRoutes);
router.use('/cs/atterberglimits', stSoilAtterbergLimitRoutes);
router.use('/cs/absorptionandspecificgravity', stSoilAbsorptionSpecificGravityRoutes);
router.use('/cs/absorptionandspecificgravity', stSoilAbsorptionSpecificGravityRoutes);
router.use('/cs/fielddensity', fieldDensityRouters);
// domain
router.use('/domain/sievesize', createCrudRoutes(SieveSize));
router.use('/domain/materialtype', materialTypeRoutes);
router.use('/domain/featuretype', featureTypeRoutes);
router.use('/domain/featurelocation', featureLocationRoutes);
router.use('/domain/featuresublocation', featureSubLocationRoutes);
router.use('/domain/instrumentstatus', instrumentStatusRoutes);
router.use('/domain/monitoringaspect', monitoringAspectRoutes);
router.use('/domain/monitoringinstrument', monitoringInstrumentRoutes);
router.use('/domain/surveytype', surveyTypeRoutes);
router.use('/domain/actioncode', actionCodeRoutes);
router.use('/domain/submittal/reviewcode', submittalReviewCode);
router.use('/domain/submittal/type', submittalType);
router.use('/domain/submittal/list', submittalList);
router.use('/domain/submittal/classification', submittalClassificationRoutes);
router.use('/domain/reporttype', reportTypeRoute);
router.use('/domain/blast/type', blastTypeRoute);
router.use('/domain/overbreak/type', overbreakTypeRoute);
router.use('/domain/movement', movementRoute);
router.use('/domain/fly/rock/assessment', flyRockAssessmentRoute);
router.use('/domain/remedial/anchor/type', remedialAnchorTypeRoute);
router.use('/domain/remedial/treatment/type', remedialTreatmentTypeRoute);
router.use('/domain/proctormold', createCrudRoutes(ProctorMoldDomain));
router.use('/domain/specimentype', specimenTypeRoute);
router.use('/domain/designstrength', designStrengthRoute);
router.use('/domain/surveyPhase', createCrudRoutes(SurveyPhase))
// auth
router.use('/auth/orgstructure', projectOrgStructureRouters);
router.use('/auth/stakeuser', stakeUserRouters);
router.use('/auth/stakeholder', stakeholerRouters);
router.use('/auth/project/role', projectRoleRoute);
router.use('/auth/data/access', RoleDataccessRoute);
router.use('/auth/mobile/access', MobileRoute);
router.use('/auth/stakeholder/role', stakeholderRoleRoutes);
// others
router.use('/import', importRoutes);
router.use('/importtemplate', importTemplateRoutes);
// map
router.use('/map/geom/layer', geomLayerRoutes);
router.use('/map/layer', layerRoutes);
router.use('/map/legend', legendRoutes);
router.use('/map/parent/layer', layerParentRoutes);
router.use('/map/config', mapConfigRoutes);
router.use('/map/geojson/config', mgeoJSONConfigRoutes);
router.use('/map/station', stationRoutes);
router.use('/map/global/filter', universalFilterRoutes);
router.use('/map/global/mapping/filter', universalFilterMappingRoutes);
router.use('/map/stationalignment', stationAlignmentRoutes)
router.use('/map/testresult', testResultPopupRoutes);

module.exports = router;
