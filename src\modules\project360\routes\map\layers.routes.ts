import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { Layer } from '../../../../entities/p_map/Layer';
import LayerController from '../../controllers/map/layer.controller';
import { addIconForMapConfig } from '../../../../shared/middlewares/uploadFileSftp.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const genericController = new CrudController<Layer>(Layer);
const controller = new LayerController();

// Mount the userRouter for CRUD operations at /auth/user/crud

router.post('/', authenticateToken, addIconForMapConfig, controller.addLayer);
router.get('/:id', authenticateToken, controller.getById);
router.get('/geojson/by/layer/:id', authenticateToken, controller.getLayerGeoJson);
router.post('/geojson/by/layer/:id', authenticateToken, controller.getLayerGeoJson);
router.get('/download/by/layer/:id', authenticateToken, controller.downloadByLayerGeoJson);
router.get('/geojson/by/entity/:entity/:id', authenticateToken, controller.getLayerGeoJsonByEntity);
router.get('/by/project/:id', authenticateToken, controller.getByProjectId);
router.get('/keys/by/project/:id', authenticateToken, controller.getByProjectIdAndLayerKeys); // passing layerKey's as queryParam in this
router.get('/get/column/:id', authenticateToken, controller.getByColumnNamesById);
router.put('/:id', authenticateToken, addIconForMapConfig, controller.edit);
router.delete('/:id', authenticateToken, (req, res) => genericController.softDelete(req, res));
router.put('/name/edit/:id', authenticateToken, (req, res) => genericController.update(req, res));
router.patch('/change/order', authenticateToken, controller.changeOrder);

export default router;
