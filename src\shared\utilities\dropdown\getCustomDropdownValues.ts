import purposeModel from '../../../modules/project360/models/meta/purpose.model';
import stakeUserModel from '../../../modules/project360/models/stakeUser.model';
import { getAllUsers, getUserById } from '../../server/platformApi/user';

const getCustomDropdownValues = async (
  custom: string,
  params: any,
  id?: string,
  requestedUserId?: string
) => {
  if (custom == 'userByProject' && params.projectId) {
    const data = (await stakeUserModel.findByUserListProjectIdForDropdown(params.projectId)) || [];

    if (data.length > 0 && id) {
      const user = await getUserById(id);
      return `${user.firstName} ${user.lastName}`;
    }
    return data;
  }
  if (custom == 'purposeByUser' && params.projectId && requestedUserId) {
    return await purposeModel.getByUserIdForDropdown(requestedUserId, params.projectId, false);
  }
};
export const getCustomImportValues = async (custom: string, name: string) => {
  if (custom == 'userByProject') {
    const users = await getAllUsers();
    return users.filter(
      (user) =>
        `${user?.firstName?.toLocaleLowerCase().trim()}${user?.lastName
          ?.toLocaleLowerCase()
          .trim()}` == name?.toLocaleLowerCase().trim()
    );
  }
};

export default getCustomDropdownValues;
