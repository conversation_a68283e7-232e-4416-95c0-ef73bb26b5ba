import isUUID from '../custom/isUUID';
import { getUserById } from '../../server/platformApi/user';
import { AzureBlobStorageService } from '../sftpBlobAndFiles/azureBlobStorageService';
import getValue from './getData';

const validPhotoExtensions = ['.jpg', '.jpeg', '.png'];
const validVideoExtensions = ['.mp4', '.avi', '.mov'];

export const photoVideoUploadFunction = async (files: any, projectId: string, data: any) => {
  try {
    if (!files) {
      throw new Error('No files uploaded.');
    }
    let photographer = data.photographer;
    if (isUUID(photographer)) {
      const photographerDetails = await getUserById(photographer);
      photographer = `${photographerDetails.firstName} ${photographerDetails.lastName}`;
    }
    const uploadedFiles: any[] = [];
    const createdDir: any[] = [];

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    if (Array.isArray(files)) {
      for (const file of Object.values(files)) {
        const fileExtension = file.name.split('.').pop();

        if (
          !validPhotoExtensions.includes(`.${fileExtension.toLowerCase()}`) &&
          !validVideoExtensions.includes(`.${fileExtension.toLowerCase()}`)
        ) {
          throw new Error(
            'Invalid file type. Supported types: Photo (jpg, jpeg, png, gif), Video (mp4, avi, mov)'
          );
        }
        const currentDate = new Date();
        const currentDay = currentDate.getDate();
        const currentYear = currentDate.getFullYear();
        const currentMonth = currentDate.getMonth();

        const fileType = validPhotoExtensions.includes(`.${fileExtension.toLowerCase()}`)
          ? 'photo'
          : 'video';

        const dayDirectoryPath: string = `media/${fileType}/${currentYear}/${
          currentMonth + 1
        }/${currentDay}`;
        const fileName: string = `${Date.now()}`;
        const filePath = `${dayDirectoryPath}/${fileName}.${fileExtension.toLowerCase()}`;
        // const annotatedFilePath = `${dayDirectoryPath}/annotated/${fileName}.${fileExtension.toLowerCase()}`;

        try {
          const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
            projectId,
            filePath,
            files
          );

          uploadedFiles.push({
            path: fileFinalPath,
            fileType: fileType,
            dateTime: currentDate,
            ...data,
          });
        } catch (err) {
          console.error(err);
          throw new Error('Failed to upload one or more files to SFTP server.');
        }
      }
    } else {
      const fileExtension = files.name.split('.').pop();

      if (
        !validPhotoExtensions.includes(`.${fileExtension.toLowerCase()}`) &&
        !validVideoExtensions.includes(`.${fileExtension.toLowerCase()}`)
      ) {
        throw new Error(
          'Invalid file type. Supported types: Photo (jpg, jpeg, png), Video (mp4, avi, mov)'
        );
      }

      const currentDate = new Date();
      const currentDay = currentDate.getDate();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      const fileType = validPhotoExtensions.includes(`.${fileExtension.toLowerCase()}`)
        ? 'photo'
        : 'video';

      const dayDirectoryPath: string = `media/${fileType}/${currentYear}/${
        currentMonth + 1
      }/${currentDay}`;
      const fileName: string = `${Date.now()}`;
      const filePath = `${dayDirectoryPath}/original/${fileName}.${fileExtension.toLowerCase()}`;

      try {
        const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          projectId,
          `${filePath}`,
          files
        );

        let station = data.station;
        let offset = data.offset;
        if (
          validPhotoExtensions.includes(`.${fileExtension.toLowerCase()}`) &&
          fileType == 'photo'
        ) {
          if (!station || !offset) {
            const dataValue = await getValue(projectId, data);
            if (dataValue.station) {
              station = dataValue.station;
            }
            if (dataValue.offset) {
              offset = dataValue.offset;
            }
          }
        }

        uploadedFiles.push({
          path: fileFinalPath,
          name: data.name,
          projectId,
          fileType: fileType,
          photographer: photographer,
          typeId: data?.typeId,
          longitude: data.longitude,
          latitude: data.latitude,
          dateTime: data.dateTime || currentDate,
          imgDegrees: data.imgDegrees,
          elevation: data.elevation,
          station,
          offset,
          createdBy: data.createdBy,
          ...data,
        });
      } catch (err) {
        console.error(err);
        throw err;
      }
    }

    return { uploadedFiles, createdDir };
  } catch (error) {
    throw error;
  }
};
