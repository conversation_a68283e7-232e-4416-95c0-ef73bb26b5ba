import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { InstrumentStatus } from '../p_domain/InstrumentStatus';
import { MonitoringAspect } from '../p_domain/MonitoringAspect';
import { MonitoringInstrument } from '../p_domain/MonitoringInstrument';
import { FeatureConstruction } from './FeatureConstruction';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class MonitoringLocation extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Feature Name',
      fieldName: 'featureConstructionId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_cs.feature_construction WHERE name = $1 AND "projectId" = $2',
      isProjectId: true,
    },
  })
  @Column()
  featureConstructionId?: string;

  @ManyToOne(() => FeatureConstruction, { nullable: true })
  @JoinColumn({ name: 'featureConstructionId' })
  featureConstruction?: FeatureConstruction;

  @ColumnInfo({
    customData: {
      name: 'Feature Description',
      fieldName: 'featureDescription',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  featureDescription?: string;

  @ColumnInfo({
    customData: {
      name: 'Monitoring Aspect',
      fieldName: 'monitoringAspectId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.mointoring_aspect WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  monitoringAspectId?: string;

  @ManyToOne(() => MonitoringAspect, { nullable: true })
  @JoinColumn({ name: 'monitoringAspectId' })
  monitoringAspect?: MonitoringAspect;

  @ColumnInfo({
    customData: {
      name: 'Monitoring Instrument Type',
      fieldName: 'instrumentTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.mointoring_instrument WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  instrumentTypeId?: string;

  @ManyToOne(() => MonitoringInstrument, { nullable: true })
  @JoinColumn({ name: 'instrumentTypeId' })
  monitoringInstrument?: MonitoringInstrument;

  @ColumnInfo({
    customData: {
      name: 'Instrumentation Specific Type',
      fieldName: 'instrumentationSpecificType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  instrumentationSpecificType?: string;

  @ColumnInfo({
    customData: {
      name: 'Longitude',
      fieldName: 'longitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Latitude',
      fieldName: 'latitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting',
      fieldName: 'easting',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing',
      fieldName: 'northing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Feature Base Elevation',
      fieldName: 'featureBaseElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  featureBaseElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Instrument Status',
      fieldName: 'instrumentStatusId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.instrument_status WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  instrumentStatusId?: string;

  @ManyToOne(() => InstrumentStatus, { nullable: true })
  @JoinColumn({ name: 'instrumentStatusId' })
  instrumentStatus?: InstrumentStatus;

  @ColumnInfo({
    customData: {
      name: 'Monitoring Frequency of Instrument',
      fieldName: 'monitoringFrequency',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  monitoringFrequency?: string;

  @ColumnInfo({
    customData: {
      name: 'Automated/Manual',
      fieldName: 'automatedManual',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  automatedManual?: string;

  @ColumnInfo({
    customData: {
      name: 'Installation date',
      fieldName: 'dateInstalled',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  dateInstalled?: Date;

  @ColumnInfo({
    customData: {
      name: 'Link for GIS Data',
      fieldName: 'linkGISData',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  linkGISData?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
