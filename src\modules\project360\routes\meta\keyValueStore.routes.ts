import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { KeyValueStore } from '@entities/p_meta/KeyValueStore';
import keyValueStoreController from '@controllers//meta/keyValueStore.controller';

const router: Router = express.Router();

const genericController = new CrudController<KeyValueStore>(KeyValueStore);

router.post('/', authenticateToken, (req, res) => genericController.create(req, res));
router.get('/', authenticateToken, (req, res) => genericController.findAll(req, res));
router.get('/:id', authenticateToken, genericController.findById);
router.put('/:id', authenticateToken, (req, res) => genericController.update(req, res));
router.delete('/:id', authenticateToken, (req, res) => genericController.softDelete(req, res));

router.get('/by/key/:key', authenticateToken, (req, res) =>
  keyValueStoreController.findByKey(req, res)
);

export default router;
