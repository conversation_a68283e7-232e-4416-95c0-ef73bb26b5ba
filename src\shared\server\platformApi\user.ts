import axios from 'axios';
import { IUser } from './interface/IUser';
import { setHeaderPlatformServerKeyHeader } from '../setHeader';

const userCache: { [key: string]: IUser } = {};

export const getAllUsers = async (): Promise<IUser[]> => {
  try {
    const res = await axios.get(
      `${process.env.PLATFORM_BASE_URL}/auth/user`,
      setHeaderPlatformServerKeyHeader()
    );
    res.data.data.map((user: IUser) => {
      userCache[user.id] = user;
    });
    return res.data.data;
  } catch (error) {
    return [];
  }
};

// Function to clear the cache
const clearCache = () => {
  for (const key in userCache) {
    if (userCache.hasOwnProperty(key)) {
      delete userCache[key];
    }
  }
};

export const refreshUserById = (userId: string, data: IUser) => {
  userCache[userId] = data;
};

// Clear the cache every 24 hours (24 hours = 86400000 milliseconds)
setInterval(clearCache, 86400000);

export const getUserById = async (userId: string): Promise<IUser> => {
  // Check if the result is already in the cache
  if (userCache[userId]) {
    return userCache[userId];
  }

  try {
    const res = await axios.get(
      `${process.env.PLATFORM_BASE_URL}/auth/user/server/${userId}`,
      setHeaderPlatformServerKeyHeader()
    );

    const user = res.data.data || {};

    // Store the result in the cache
    userCache[userId] = user;

    return user;
  } catch (error) {
    throw error;
  }
};

export const addUserByInvite = async (
  token: string,
  body: {
    email: any;
    firstName: any;
    lastName: any;
    organizationId: string | undefined;
  }
): Promise<{ commit: boolean; user: IUser }> => {
  try {
    const res = await axios.post(
      `${process.env.PLATFORM_BASE_URL}/auth/user/invite/with/project`,
      body,
      {
        headers: { authorization: token },
      }
    );

    const user = res.data.data || {};

    return user;
  } catch (error) {
    throw error;
  }
};
export const addUserByInviteRoleBack = async (
  token: string,
  body: {
    userId: string;
  }
): Promise<boolean> => {
  try {
    await axios.post(
      `${process.env.PLATFORM_BASE_URL}/auth/user/rollback/invite/with/project`,
      body,
      {
        headers: { authorization: token },
      }
    );

    return true;
  } catch (error) {
    throw error;
  }
};

export const sendInviteLink = async (
  token: string,
  body: IUser
): Promise<{ commit: boolean; user: IUser }> => {
  try {
    const res = await axios.post(
      `${process.env.PLATFORM_BASE_URL}/auth/user/send/invite/link/for/project`,
      body,
      {
        headers: { authorization: token },
      }
    );

    const user = res.data.data || {};

    return user;
  } catch (error) {
    throw error;
  }
};
export const checkEmailExist = async (
  token: string,
  email: string
): Promise<{ user: IUser | null }> => {
  try {
    const res = await axios.get(
      `${process.env.PLATFORM_BASE_URL}/auth/user/check/mail/exist/${email}`,
      {
        headers: { authorization: token },
      }
    );

    const user = res.data.data || null;

    return { user };
  } catch (error) {
    throw error;
  }
};
