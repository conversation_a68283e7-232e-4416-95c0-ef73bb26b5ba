import { getManager } from 'typeorm';
import { DCEValidation } from '../../../entities/p_gen/DCEValidation';
import { dceConfiguration } from '../../../entities/p_gen/dceConfiguration';
import { DCEValidationCriteria } from '../../../entities/p_gen/DCEValidationCriteria';

import { dceValidationBeforeApproval } from '../../../shared/utilities/Approval/dceValidation';
import { EntityListInterface, entityList } from '../../../shared/utilities/entity/entityList';

class DCEValidationModel {
  async getByDCEConfigurationId(id: string) {
    try {
      const repo = getManager().getRepository(DCEValidation);
      return await repo.find({ where: { id, isDelete: false } });
    } catch (error) {
      throw error;
    }
  }

  async create(
    dceConfigId: string,
    required: boolean,
    createdBy: string,
    criteria: DCEValidationCriteria[]
  ) {
    try {
      const config = await getManager()
        .getRepository(dceConfiguration)
        .findOne({ where: { id: dceConfigId }, relations: ['validation'] });

      if (config) {
        const dceValidation = new DCEValidation();
        dceValidation.required = required;
        dceValidation.createdBy = createdBy;
        dceValidation.updatedBy = createdBy;
        dceValidation.dceConfig = config;

        await getManager().transaction(async (transactionalEntityManager) => {
          const data = await transactionalEntityManager.save(DCEValidation, dceValidation);
          criteria.map((value) => {
            value.dceValidationId = data.id;
            return value;
          });
          await transactionalEntityManager.save(DCEValidationCriteria, criteria);
        });
      } else {
        console.log(`Configuration with ID ${dceConfigId} not found.`);
      }
    } catch (error) {
      throw error;
    }
  }

  async update(
    dceValidationId: string,
    required: boolean,
    createdBy: string,
    criteria: DCEValidationCriteria[]
  ) {
    try {
      const validation = await getManager()
        .getRepository(DCEValidation)
        .findOne({ where: { id: dceValidationId }, relations: ['criteria'] });

      if (validation) {
        const criteriaToDelete = validation.criteria?.map((value) => {
          return { id: value.id };
        });
        criteria.map((value) => {
          value.dceValidationId = dceValidationId;
          return value;
        });
        validation.required = required;
        // validation.createdBy = createdBy;
        validation.updatedBy = createdBy;
        // validation.criteria = criteria;

        await getManager().transaction(async (transactionalEntityManager) => {
          await getManager().getRepository(DCEValidation).save(validation);

          await transactionalEntityManager.delete(DCEValidationCriteria, criteriaToDelete);
          await transactionalEntityManager.save(DCEValidationCriteria, criteria);
        });
      } else {
        throw new Error('Validation not found');
      }
    } catch (error) {
      throw error;
    }
  }

  async dataValidation(ids: string[], projectId: string, entity: string) {
    try {
      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const repository = getManager().getRepository(entityValue);
        const tableData = await Promise.all(
          ids.map(async (id) => {
            try {
              const data = await repository.findOne({
                where: { id: id, approvalStatus: 'Draft', isDelete: false } as any,
              });
              if (data) {
                return data;
              }
            } catch (error) {
              throw error;
            }
          })
        );
        if (tableData.length > 0) {
          const result = await dceValidationBeforeApproval(entity, tableData);

          return result;
        }
      }
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      throw error;
    }
  }
}

export default DCEValidationModel;
