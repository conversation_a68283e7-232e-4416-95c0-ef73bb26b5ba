import { Request, Response } from 'express';
import testMethodModel from '../../models/meta/testMethod.model';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class TestMethodController {
  constructor() {}

  async all(req: Request, res: Response) {
    try {
      const { testId } = req.query;
      let testMethodData;
      if (testId) {
        testMethodData = await testMethodModel.getByTestId(testId as string);
      } else {
        testMethodData = await testMethodModel.getAll();
      }
      // checking if data is found with the id
      if (testMethodData && testMethodData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testMethodData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async getByTestId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const testMethodData = await testMethodModel.getByTestId(req.params.id);
      // checking if data is found with the id
      if (testMethodData && testMethodData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testMethodData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async getByTestStandard(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const testMethodData = await testMethodModel.getByTestStandard(
        Number(req.params.testId),
        req.params.projectId
      );
      // checking if data is found with the id
      if (testMethodData && testMethodData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: testMethodData,
          msg: 'Test Method',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No Test Method found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const testMethodController = new TestMethodController();
export default testMethodController;
