import stSoilNuclearGaugeTestModel from '@models/cs/stSoilNuclearGaugeTest.model';
import { Request, Response } from 'express';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';
import successResponse from 'src/shared/middlewares/response/successResponse.middleware';

class StSoilNuclearGaugeTestController {
  async getDataByStation(req: Request, res: Response) {
    try {
      const { stationStart, stationEnd, projectId } = req.params;
      const data = await stSoilNuclearGaugeTestModel.getDataByStationRange(
        Number(stationStart),
        Number(stationEnd),
        projectId
      );
      return successResponse(data, 'Data found', res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default StSoilNuclearGaugeTestController;
