import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  BeforeInsert,
  Point,
  AfterLoad,
} from 'typeorm';

import { getRepository } from 'typeorm';
import { CTLogConfig } from './CTLogConfig';
import { SubSpecification } from '../p_meta/SubSpecification';
import { Feature } from './Feature';
import { ProjectMaterial } from './ProjectMaterial';
import { Project } from './Project';
import { ProjectReportList } from './ReportList';
import { Purpose } from '../p_meta/Purpose';
import { SampleSource } from './SampleSource';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { MaterialType } from '@entities/p_meta/MaterialType';
import Structure from './Structure';
import { CTPhase } from '@entities/p_meta/CTPhase';
import { Area } from './Area';
import { DCEColumns } from '@entities/common/DCEColumns';
import { EmailList } from './EmailList';
import { WorkPackage } from './WorkPackage';

@Entity({ schema: 'p_gen', name: 'ct_log' })
export class CTLog extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid', nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  location?: string;

  @Column({ nullable: true })
  sourceId?: string;

  @ManyToOne(() => SampleSource, { nullable: true })
  @JoinColumn({ name: 'sourceId' })
  source?: SampleSource;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  phaseId?: string;

  @ManyToOne(() => CTPhase, { nullable: true })
  @JoinColumn({ name: 'phaseId' })
  ctPhase?: CTPhase;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @Column({ nullable: true })
  ctLogConfigId?: string;

  @ManyToOne(() => CTLogConfig, { nullable: true })
  @JoinColumn({ name: 'ctLogConfigId' })
  ctLogConfig?: CTLogConfig;

  @Column({ nullable: true })
  relatedCTId?: string;

  @ManyToOne(() => CTLog, { nullable: true })
  @JoinColumn({ name: 'relatedCTId' })
  relatedCT?: CTLog;

  @Column({ nullable: true })
  subSpecificationId?: string;

  @ManyToOne(() => SubSpecification, { nullable: true })
  @JoinColumn({ name: 'subSpecificationId' })
  subSpecification?: SubSpecification;

  @Column({ nullable: true })
  firmResponsible?: string;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ type: 'timestamp', nullable: true })
  sampleDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  scheduleFrom?: Date;

  @Column({ type: 'timestamp', nullable: true })
  scheduleTo?: Date;

  @Column({ type: 'uuid', nullable: true })
  sampledBy?: string;

  @Column({ type: 'uuid', nullable: true })
  testReportId?: string;

  @ManyToOne(() => ProjectReportList, { nullable: true })
  @JoinColumn({ name: 'testReportId' })
  reportList?: ProjectReportList;

  @Column({ nullable: true })
  report?: string;

  @Column({ type: 'uuid', nullable: true })
  emailListId?: string;

  @ManyToOne(() => EmailList, { nullable: true })
  @JoinColumn({ name: 'emailListId' })
  emailList?: EmailList;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @AfterLoad()
  async assignReportFromReportList() {
    if (this.testReportId) {
      try {
        const reportListRepository = getRepository(ProjectReportList);

        const reportList = await reportListRepository.findOne({
          where: { id: this.testReportId },
        });

        if (reportList) {
          this.report = reportList.filePath;
        }
      } catch (error) {
        console.error('Error fetching report path:', error);
      }
    }
  }

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  workPackageId?: string;

  @ManyToOne(() => WorkPackage, { nullable: true })
  @JoinColumn({ name: 'workPackageId' })
  workPackage?: WorkPackage;

  @Column({ nullable: true })
  comments?: string;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
    default: false,
  })
  calendarInvite?: boolean;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
