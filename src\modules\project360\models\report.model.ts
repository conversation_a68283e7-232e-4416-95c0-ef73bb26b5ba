import { DeepPartial, getManager } from 'typeorm';
import Submittal from '../../../entities/p_gen/Submittal';
import Report from '../../../entities/p_gen/Report';
import ReportVersion from '../../../entities/p_gen/ReportVersion';
import ReportDocument from '../../../entities/p_gen/ReportDocument';
import st from 'string-template';
import { projectDetailsQuery } from '@utils/reports/querys/common';
import { inPlaceDensityProctor, inPlaceDensityQuery } from '@utils/reports/querys/inPlaceDensity';
import {
  calculateSoilComposition,
  correctedCurveQuery,
  grainSizeGraphQuery,
  proctorQuery,
  sieveQuery,
  uncorrectedCurveQuery,
} from '@utils/reports/querys/proctorReport';
import { IProctorTestReport } from '@utils/reports/querys/types';
import isUUID from '@utils/custom/isUUID';
import { getAllUsers } from 'src/shared/server/platformApi/user';
import { qcReportQuery } from '@utils/reports/querys/qcReport';
import { FlowConsistency } from '@utils/reports/querys/FlowConsistencyCLSM';
import { ucsQuery } from '@utils/reports/querys/umsQuery';
import { compressiveStrengthQuery } from '@utils/reports/querys/compressiveStrengthQuery';
import { clsmReportQuery } from '@utils/reports/querys/clsmQuery';
import { clsmLabAndFieldTestingReport } from '@utils/reports/querys/clsmLabAndFieldTestingReportQuery';
class ReportModel {
  constructor() {}
  async findByProjectId(projectId: string) {
    try {
      const data = await getManager()
        .getRepository(Report)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['version', 'version.documents', 'reportType'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getById(ReprotId: string) {
    try {
      const data = await getManager()
        .getRepository(Report)
        .find({
          where: { id: ReprotId, isDelete: false },
          relations: ['version', 'version.documents', 'reportType'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findVersionbyReport(reportId: string) {
    try {
      const data = await getManager()
        .getRepository(ReportVersion)
        .find({
          where: { reportId },
          relations: ['documents'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdDistinct(projectId: string) {
    try {
      const data = await getManager()
        .getRepository(Report)
        .find({
          where: { projectId: projectId },
        });

      const uniqueData = Array.from(new Map(data.map((item) => [item.id, item])).values());

      return uniqueData;
    } catch (error) {
      throw error;
    }
  }

  async findByIdLastVersion(id: string) {
    try {
      const data = await getManager()
        .getRepository(ReportVersion)
        .createQueryBuilder('report')
        .leftJoinAndSelect('report.documents', 'documents')
        .where('report.reportId = :id', { id })
        .orderBy('report.version', 'DESC')
        .limit(1)
        .getOne();

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByIdAndVersion(id: string, version: number) {
    try {
      const data = await getManager()
        .getRepository(ReportVersion)
        .findOne({
          where: { reportId: id, version },
          relations: ['documents'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findById(id: string) {
    try {
      const data = await getManager()
        .getRepository(Report)
        .findOne({
          where: { id },
          relations: ['version', 'version.documents', 'reportType'],
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async addSubmittalAndDocumentAndChangeStatus(
    submital: DeepPartial<Submittal>,
    documents: ReportDocument[],
    submittalVersion: ReportVersion
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          await transactionalEntityManager.save(ReportVersion, submittalVersion);
          await transactionalEntityManager.save(ReportDocument, documents);
          // await transactionalEntityManager.update(Report, submital.id, submital);
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  async addReport(documents: ReportDocument[], submittalVersion: ReportVersion) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          // const report = await transactionalEntityManager.save(Report, submital);
          // submittalVersion.reportId = report.id;
          await transactionalEntityManager.save(ReportVersion, submittalVersion);
          await transactionalEntityManager.save(ReportDocument, documents);
        } catch (error) {
          throw error as Error;
        }
      });

      return true;
    } catch (error) {
      throw error;
    }
  }

  // async updateAcceptStatus(
  //   updateDataForSubmittal: DeepPartial<Submittal>,
  //   data: DeepPartial<ReportVersion>,
  // ) {
  //   try {
  //     const entityManager = getManager();
  //     return await entityManager.transaction(async (transactionalEntityManager) => {
  //       try {
  //         await transactionalEntityManager.update(
  //           Submittal,
  //           updateDataForSubmittal.id,
  //           updateDataForSubmittal
  //         );
  //         await transactionalEntityManager.update(SubmittalVersion, data.id, data);
  //         if (document.length > 0) {
  //           await transactionalEntityManager.save(SubmittalDocument, document);
  //         }
  //       } catch (error) {
  //         throw error as Error;
  //       }
  //       return [];
  //     });
  //     // Try to find the updated entity
  //   } catch (error) {
  //     throw error;
  //   }
  // }

  async update(id: string, data: DeepPartial<Submittal>): Promise<Submittal | undefined | null> {
    try {
      // Attempt to update the entity
      await getManager().getRepository(Submittal).update({ id }, data);

      // Try to find the updated entity
      const updatedEntity = await getManager().getRepository(Submittal).findOne({ where: { id } });

      return updatedEntity;
    } catch (error) {
      // Handle the error here
      console.error('Error occurred while updating entity:', error);
      throw error; // Optionally, rethrow the error for further handling by the caller
    }
  }

  async countByStatus(projectId: string, status: string) {
    try {
      const data = await getManager().getRepository(Report).count({
        where: { projectId, status },
      });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async qcReport(projectId: string, from: string, to: string) {
    try {
      const data: { sheetName: string; data: Record<string, string>[] }[] = await Promise.all(
        qcReportQuery.map(async (item) => {
          const values = await getManager().query(
            st(item.query, {
              projectId: `'${projectId}'`,
              from: `'${from} 00:00:00'`,
              to: `'${to} 23:59:59'`,
            })
          );

          return { sheetName: item.name, data: values };
        })
      );

      return data;
    } catch (error) {
      throw error;
    }
  }

  async inPlaceDensity(projectId: string, purposeId: string, from: string, to: string) {
    try {
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const sampleIds: string[] = [];
      const data: {
        gaugeNo: string;
        gaugeId: string;
        gStandardDensityCount: string;
        gStandardMoistureCount: string;
        dateTested: string;
        [key: string]: unknown; // if there are other unknown fields
      }[] = await getManager().query(
        st(inPlaceDensityQuery, {
          projectId: `'${projectId}'`,
          purposeId: `'${purposeId}'`,
          from: `'${from}'`,
          to: `'${to}'`,
        })
      );
      const gaugeData = data
        .filter(
          (value) =>
            typeof value === 'object' &&
            value !== null &&
            'gaugeNo' in value &&
            'gaugeId' in value &&
            typeof value.gaugeId === 'string' &&
            value.gaugeId !== 'N/A'
        )
        .map((value) => ({
          gStandardDensityCount: value.gStandardDensityCount,
          gStandardMoistureCount: value.gStandardMoistureCount,
          gDateTested: new Date(value.dateTested).toLocaleDateString('en-US'),
          gaugeId: value.gaugeId,
          gGaugeNo: value.gaugeNo,
        }));
      const getAllUser = await getAllUsers();
      data.forEach((element: any) => {
        if ('sampleId' in element && element.sampleId && element.sampleId != 'N/A') {
          sampleIds.push(element.sampleId);
        }
      });
      const groupedData = data.reduce((acc: any, item: any) => {
        // Convert date to MM/DD/YYYY format
        const date = new Date(item.dateTested).toLocaleDateString('en-US');

        if (!acc[date]) {
          acc[date] = { tests: [], gaugeNo: 'N/A' };
        }

        if (item.gaugeNo !== 'N/A') {
          acc[date].gaugeNo = item.gaugeNo;
        }

        const user = getAllUser.find((user) => user.id === item?.technician);
        acc[date].technician = user ? `${user.firstName} ${user.lastName}` : item?.technician;

        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { dateTested, ...testWithoutDate } = item;
        acc[date].tests.push(testWithoutDate);

        return acc;
      }, {});

      // Convert object to array format
      const result = Object.keys(groupedData).map((date) => ({
        dateTested: date,
        gaugeNo: groupedData[date].gaugeNo,
        tests: groupedData[date].tests,
        technician: groupedData[date].technician || '',
      }));
      let proctor: any[] = [];
      if (sampleIds.length > 0) {
        proctor = await getManager().query(
          st(inPlaceDensityProctor, {
            sampleIds: sampleIds.map((id) => `'${id}'`).join(','),
          })
        );
      }

      return {
        reprot: result,
        proctor,
        project,
        logo: `https://sandbox.smartinfrahub.com/api-sand/project360/get/file/${projectId}?path=${project[0].logoPath}`,
        gaugeNo: data.length > 0 ? data[0].gaugeNo : '',
        gaugeData,
      };
    } catch (error) {
      throw error;
    }
  }

  async proctorReport(sampleId: string, projectId: string) {
    try {
      const datitems = {
        updateData: [
          {
            sampleNo: '24-00197',
            testNo: 'CT-00671',
            sampledDate: '2024-12-20T10:00:00.000Z',
            sampledBy: 'Venkata Bottu',
            soilDescription: null,
            sampleLocation: 'Station 185+00, Stockpile S4-33',
            sampleSource: 'Stockpile S4-33',
            materialName: 'Select Fill',
            materialId: '3f59e909-ee49-46e1-b670-194e0b847bec',
            northing: null,
            easting: null,
            depth: null,
            soilClassification: 'Silty Gravel with Sand  (GM)',
            testMethod: 'ASTM D2487',
            mcMoistureContent: null,
            mcTestMethod: null,
            alLiquidLimit: 'NP',
            alPlasticLimit: 'NP',
            alPlasticityIndex: 'NP',
            alTestMethod: 'ASTM D4318',
            alTestVariant: 'A',
            orOrganicContent: '2.1',
            orTestMethod: 'ASTM D2974',
            asSpecificGravity: '2.514',
            asAbsorption: '5',
            asTestMethod: 'ASTM C127',
            pMaximumDryDensity: '127.4',
            pOptimumMoisture: '9.9',
            pCorrectedMaximumDryDensity: '130.1',
            pCorrectedOptimumMoisture: '8.7',
            pCorrectedTestMethod: 'ASTM D4718',
            pTestMethod: 'ASTM D1557',
            pp200: '14.9',
            ppTestMethod: 'ASTM C117',
            dateTested: '2025-03-05T10:00:00.000Z',
            testedBy: 'Venkata Bottu',
          },
        ],
        project: [
          {
            id: '754a0e54-4546-4af5-948a-3eed2998c127',
            name: 'CEPP 10A EAA Seepage Inflow Outflow Canal',
            client: 'US Army Corps of Engineers',
            projectNumber: 'W912EP21C0021',
            logoPath: null,
          },
        ],
        sieve: [
          {
            sieve: '3.5"',
            sieveSize: '88.9',
            weightRetained: '0',
            percentageRetained: '0',
            percentagePassing: '100',
          },
          {
            sieve: '3"',
            sieveSize: '76.2',
            weightRetained: '658',
            percentageRetained: '0.8',
            percentagePassing: '99.2',
          },
          {
            sieve: '3/4"',
            sieveSize: '19',
            weightRetained: '20071',
            percentageRetained: '24.1',
            percentagePassing: '75.9',
          },
          {
            sieve: '#4',
            sieveSize: '4.76',
            weightRetained: '39009',
            percentageRetained: '46.8',
            percentagePassing: '53.2',
          },
          {
            sieve: '#10',
            sieveSize: '2',
            weightRetained: '5573.81',
            percentageRetained: '53.5',
            percentagePassing: '46.5',
          },
          {
            sieve: '#20',
            sieveSize: '0.841',
            weightRetained: '12070',
            percentageRetained: '61.3',
            percentagePassing: '38.7',
          },
          {
            sieve: '#40',
            sieveSize: '0.42',
            weightRetained: '17824',
            percentageRetained: '68.2',
            percentagePassing: '31.8',
          },
          {
            sieve: '#60',
            sieveSize: '0.25',
            weightRetained: '21926',
            percentageRetained: '73.1',
            percentagePassing: '26.9',
          },
          {
            sieve: '#100',
            sieveSize: '0.149',
            weightRetained: '25787',
            percentageRetained: '77.7',
            percentagePassing: '22.3',
          },
          {
            sieve: '#140',
            sieveSize: '0.105',
            weightRetained: '29806',
            percentageRetained: '82.5',
            percentagePassing: '17.5',
          },
          {
            sieve: '#200',
            sieveSize: '0.074',
            weightRetained: '31917',
            percentageRetained: '85.1',
            percentagePassing: '14.9',
          },
          {
            sieve: 'PAN',
            sieveSize: '0',
            weightRetained: '33048',
            percentageRetained: '86.4',
            percentagePassing: '13.6',
          },
        ],
        uncorrectedCurve: [
          {
            moistureContent: 4.8,
            dryDensity: 123,
          },
          {
            moistureContent: 6.8,
            dryDensity: 124.3,
          },
          {
            moistureContent: 9.1,
            dryDensity: 128.7,
          },
          {
            moistureContent: 10.9,
            dryDensity: 126.1,
          },
          {
            moistureContent: 13.9,
            dryDensity: 121,
          },
        ],
        correctedCurve: [
          {
            moistureContent: 4.8,
            dryDensity: 123,
            absorption: 5,
            bulkSpecificGravity: 2.233,
            specificGravity: 2.65,
            passingPercentage: 75.9,
            retainedPercentage: 24.1,
            correctedMoistureContent: 4.8482,
            correctedDryDensity: 126.58596749602191,
            zeroAirVoidsDryDensity: 146.74680624556424,
          },
          {
            moistureContent: 6.8,
            dryDensity: 124.3,
            absorption: 5,
            bulkSpecificGravity: 2.233,
            specificGravity: 2.65,
            passingPercentage: 75.9,
            retainedPercentage: 24.1,
            correctedMoistureContent: 6.3662,
            correctedDryDensity: 127.62862710740362,
            zeroAirVoidsDryDensity: 140.15675309269616,
          },
          {
            moistureContent: 9.1,
            dryDensity: 128.7,
            absorption: 5,
            bulkSpecificGravity: 2.233,
            specificGravity: 2.65,
            passingPercentage: 75.9,
            retainedPercentage: 24.1,
            correctedMoistureContent: 8.1119,
            correctedDryDensity: 131.12219415376225,
            zeroAirVoidsDryDensity: 133.27397977681989,
          },
          {
            moistureContent: 10.9,
            dryDensity: 126.1,
            absorption: 5,
            bulkSpecificGravity: 2.233,
            specificGravity: 2.65,
            passingPercentage: 75.9,
            retainedPercentage: 24.1,
            correctedMoistureContent: 9.478100000000001,
            correctedDryDensity: 129.06438957117265,
            zeroAirVoidsDryDensity: 128.3415447879893,
          },
          {
            moistureContent: 13.9,
            dryDensity: 121,
            absorption: 5,
            bulkSpecificGravity: 2.233,
            specificGravity: 2.65,
            passingPercentage: 75.9,
            retainedPercentage: 24.1,
            correctedMoistureContent: 11.755100000000002,
            correctedDryDensity: 124.97242263710261,
            zeroAirVoidsDryDensity: 120.88500749077357,
          },
        ],
        grainSizeGraph: [
          {
            sieveSize: 1.9489017609702137,
            percentageRetained: 0,
          },
          {
            sieveSize: 1.8819549713396004,
            percentageRetained: 0.8,
          },
          {
            sieveSize: 1.2787536009528289,
            percentageRetained: 24.1,
          },
          {
            sieveSize: 0.6776069527204931,
            percentageRetained: 46.8,
          },
          {
            sieveSize: 0.3010299956639812,
            percentageRetained: 53.5,
          },
          {
            sieveSize: -0.07520400420208784,
            percentageRetained: 61.3,
          },
          {
            sieveSize: -0.37675070960209955,
            percentageRetained: 68.2,
          },
          {
            sieveSize: -0.6020599913279624,
            percentageRetained: 73.1,
          },
          {
            sieveSize: -0.826813731587726,
            percentageRetained: 77.7,
          },
          {
            sieveSize: -0.978810700930062,
            percentageRetained: 82.5,
          },
          {
            sieveSize: -1.1307682802690238,
            percentageRetained: 85.1,
          },
          {
            sieveSize: null,
            percentageRetained: 86.4,
          },
        ],
        graph: {},
        CobbleData: {
          cobble: 0.8,
          gravel: 46.0,
          sand: 38.3,
          siltClay: 14.9,
        },
        logo: 'https://sandbox.smartinfrahub.com/api-sand/project360/get/file/754a0e54-4546-4af5-948a-3eed2998c127?path=null',
      };

      const newCorrected = datitems.correctedCurve.map((item) => {
        return {
          ...item,
          moistureContentValue:
            (item.passingPercentage * item.moistureContent +
              item.retainedPercentage * item.absorption) /
            100,
        };
      });
      datitems.correctedCurve = newCorrected;
      return datitems;
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const data: IProctorTestReport[] = await getManager().query(
        st(proctorQuery, {
          sampleId: `'${sampleId}'`,
        })
      );

      let updateData = data;

      if (data.length > 0) {
        const getAllUser = await getAllUsers();
        updateData = data.map((item) => {
          let obj = item;
          if (item.testedBy && isUUID(item.testedBy)) {
            const user = getAllUser.find((user) => user.id === item.testedBy);
            obj = {
              ...item,
              testedBy: user ? `${user.firstName} ${user.lastName}` : item.testedBy,
            };
          }
          if (item.sampledBy && isUUID(item.sampledBy)) {
            const user = getAllUser.find((user) => user.id === item.testedBy);
            obj = {
              ...item,
              sampledBy: user ? `${user.firstName} ${user.lastName}` : item.testedBy,
            };
          }
          return obj;
        });
      }

      const sieve = await getManager().query(
        st(sieveQuery, {
          sampleId: `'${sampleId}'`,
        })
      );

      const CobbleData = calculateSoilComposition(sieve);
      const uncorrectedCurveData: { moistureContent: number; dryDensity: number }[] =
        await getManager().query(
          st(uncorrectedCurveQuery, {
            sampleId: `'${sampleId}'`,
          })
        );

      const uncorrectedCurve: {
        moistureContent: number;
        dryDensity: number;
      }[] = uncorrectedCurveData.map((item) => ({
        moistureContent: Number(item?.moistureContent),
        dryDensity: Number(item?.dryDensity),
      }));

      const correctedCurveData: {
        moistureContent: number;
        dryDensity: number;
        specificGravity: number;
        bulkSpecificGravity: number;
        absorption: number;
        passingPercentage: number;
        retainedPercentage: number;
      }[] = await getManager().query(
        st(correctedCurveQuery, {
          sampleId: `'${sampleId}'`,
        })
      );

      const correctedCurve: {
        moistureContent: number;
        dryDensity: number;
        specificGravity: number;
        bulkSpecificGravity: number;
        absorption: number;
        passingPercentage: number;
        retainedPercentage: number;
        correctedMoistureContent: number;
        correctedDryDensity: number;
        zeroAirVoidsDryDensity: number;
      }[] = correctedCurveData.map((item) => {
        const moistureContent = Number(item?.moistureContent);
        const dryDensity = Number(item?.dryDensity);
        const absorption = Number(item?.absorption);
        const bulkSpecificGravity = Number(item?.bulkSpecificGravity);
        const specificGravity = Number(item?.specificGravity);
        const passingPercentage = Number(item?.passingPercentage);
        const retainedPercentage = Number(item?.retainedPercentage);

        // Corrected Moisture Content Calculation
        const correctedMoistureContent =
          (passingPercentage * moistureContent + retainedPercentage * absorption) / 100;

        // Corrected Dry Density Calculation
        const numerator = 100 * dryDensity * bulkSpecificGravity * 62.42;
        const denominator =
          dryDensity * retainedPercentage + bulkSpecificGravity * 62.42 * passingPercentage;
        const correctedDryDensity = numerator / denominator;

        // Zero Air Voids Dry Density Calculation
        const zeroAirVoidsDryDensity =
          (specificGravity * 62.42) / (1 + 0.01 * moistureContent * specificGravity);

        return {
          moistureContent,
          dryDensity,
          absorption,
          bulkSpecificGravity,
          specificGravity,
          passingPercentage,
          retainedPercentage,
          correctedMoistureContent,
          correctedDryDensity,
          zeroAirVoidsDryDensity,
        };
      });

      const grainSizeGraphData: { percentageRetained: number; sieveSize: number }[] =
        await getManager().query(
          st(grainSizeGraphQuery, {
            sampleId: `'${sampleId}'`,
          })
        );

      const grainSizeGraph: {
        percentageRetained: number;
        sieveSize: number;
      }[] = grainSizeGraphData.map((item) => ({
        sieveSize: Number(Math.log10(Number(item?.sieveSize))),
        percentageRetained: Number(item?.percentageRetained),
      }));

      return {
        updateData,
        project,
        sieve,
        uncorrectedCurve,
        correctedCurve,
        grainSizeGraph,
        CobbleData,
        logo: `https://sandbox.smartinfrahub.com/api-sand/project360/get/file/${projectId}?path=${project[0].logoPath}`,
      };
    } catch (error) {
      throw error;
    }
  }

  async flowConsistencyReports(sampleId: string, projectId: string) {
    const project = await getManager().query(
      st(projectDetailsQuery, {
        projectId: `'${projectId}'`,
      })
    );
    // const sampleIds: string[] = [];
    const data = await getManager().query(
      st(FlowConsistency, {
        sampleId: `'${sampleId}'`,
      })
    );
    if (project || data) {
      return { project, data };
    }
  }
  async ucsReport(sampleId: string, projectId: string) {
    try {
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const data = await getManager().query(
        st(ucsQuery, {
          sampleId: `'${sampleId}'`,
        })
      );
      if (project || data) {
        return { project, data };
      }
    } catch (error) {
      throw error;
    }
  }

  async compressiveStrengthReport(sampleId: string, projectId: string) {
    try {
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const data = await getManager().query(
        st(compressiveStrengthQuery, {
          sampleId: `'${sampleId}'`,
        })
      );
      if (project || data) {
        return { project, data };
      }
    } catch (error) {
      throw error;
    }
  }

  async clsmReport(sampleId: string, projectId: string) {
    try {
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const data = await getManager().query(
        st(clsmReportQuery, {
          sampleId: `'${sampleId}'`,
        })
      );
      if (project || data) {
        return { project, data };
      }
    } catch (error) {
      throw error;
    }
  }

  async clsmLabAndFieldTestingReport(sampleId: string, projectId: string) {
    try {
      const project = await getManager().query(
        st(projectDetailsQuery, {
          projectId: `'${projectId}'`,
        })
      );
      const data = await getManager().query(
        st(clsmLabAndFieldTestingReport, {
          sampleId: `'${sampleId}'`,
        })
      );
      if (project || data) {
        return { project, data };
      }
    } catch (error) {
      throw error;
    }
  }
}

const reportModel = new ReportModel();
export default reportModel;
