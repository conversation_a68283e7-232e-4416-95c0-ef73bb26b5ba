import { getManager } from 'typeorm';
import { ProjectMaterial } from '../../../entities/p_gen/ProjectMaterial';

class ProjectMaterialModel {
  constructor() {}

  async findById(configId: string) {
    try {
      return await getManager()
        .getRepository(ProjectMaterial)
        .findOne({
          where: { id: configId, isDelete: false },
          relations: ['materialType', 'material'],
        });
    } catch (error) {
      throw error;
    }
  }

  async findByProjectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(ProjectMaterial)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['materialType', 'material'],
        });
    } catch (error) {
      throw error;
    }
  }

  async findByMaterialTypeAndProjectId(materialTypeId: string, projectId: string) {
    try {
      return await getManager()
        .getRepository(ProjectMaterial)
        .find({
          where: { projectId: projectId, materialTypeId: materialTypeId, isDelete: false },
        });
    } catch (error) {
      throw error;
    }
  }

  async deleteById(id: string) {
    try {
      return await getManager()
        .getRepository(ProjectMaterial)
        .update({ id, isDelete: false }, { isDelete: true });
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdAndName(projectId: string, name: string) {
    try {
      return await getManager()
        .getRepository(ProjectMaterial)
        .findOne({
          where: { projectId: projectId, name, isDelete: false },
          relations: ['materialType', 'material'],
        });
    } catch (error) {
      throw error;
    }
  }

  async addmaterial(newProjectMaterial: ProjectMaterial) {
    try {
      const projectRepository = getManager().getRepository(ProjectMaterial);
      const addedProject = projectRepository.create(newProjectMaterial);
      await projectRepository.save(addedProject);
      return addedProject;
    } catch (error) {
      throw error;
    }
  }
}

const projectMaterialModel = new ProjectMaterialModel();
export default projectMaterialModel;
