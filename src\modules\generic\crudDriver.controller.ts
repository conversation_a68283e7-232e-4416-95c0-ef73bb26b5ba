import { Router, Request, Response } from 'express';
import { GenericModel } from './crudDriver.model';
import { ObjectLiteral } from 'typeorm/common/ObjectLiteral';
import { EntityTarget } from 'typeorm/common/EntityTarget';
import submittalController from '../project360/controllers/submittal.controller';
import { getRelatedSelectBoxDataByProject } from '../../shared/utilities/custom/getRelatedSelectBoxDataByProject';
import {
  dataEditableAccessChange,
  getUserRoleDetail,
} from '../../shared/utilities/role/getUserRoleDetails';
import errorMiddleware from '../../shared/middlewares/error/error.middleware';
import { convertEastingNorthingToLatLong } from '../../shared/utilities/spatialCoordinates/eastingAndNothingConversion';
import { entityRelationList } from '../../shared/utilities/entity/entityRelation';
import { convertLatAndLongToEastingAndNorthingOnGetDataByProjectId } from '../../shared/utilities/spatialCoordinates/convertLatAndLongOnGetData';
import dceModel from '../project360/models/meta/dce.model';
import SortAndFilter from '../../shared/utilities/sortAndFilter/sortFilterForGetByProjectId';
import keyValueStoreModel from '@models/meta/keyValueStore.model';
import dceRelationshipModel from '@models/utils/dceRelationship.model';
import { checkNameByIdWithRequireForValidationErrors } from '@utils/dce/checkNameByIdWithRequire';

class CrudController<T extends ObjectLiteral> {
  private service: GenericModel<T>;
  private router: Router;

  constructor(entity: EntityTarget<T>) {
    this.service = new GenericModel<T>(entity);
    this.router = Router();

    // this.router.post('/', this.create);
    this.router.get('/', this.findAll);
    this.router.get('/:id', this.findById);
    // this.router.put('/:id', this.update);
    // this.router.delete('/:id', this.softDelete);
  }

  public create = async (req: Request, res: Response, entity?: string) => {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }

      let data = req.body;
      data = this.convertEastingAndNorthingToLatAndLong(data);
      const result = await this.service.create(data, (req as any).user.id || '', entity);

      res.json({ isSucceed: true, data: result, msg: 'Data added' });
    } catch (error) {
      console.error('Error occurred while creating the entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public createWithSubmittal = async (req: Request, res: Response) => {
    const data = req.body;
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }
      if (req.query.submittalId && req.query.screen == 'sbumittal') {
        const submittalVersion = await submittalController.addSubmittalVersionBeforeFormForImport(
          req,
          req.query.submittalId as string
        );
        const result = await this.service.createWithSubmittal(
          data,
          submittalVersion.submitalVersion,
          submittalVersion.submittal
        );
        return res.json({ isSucceed: true, data: result, msg: 'Data added' });
      } else {
        const result = await this.service.createWithSubmittal(data);
        return res.json({ isSucceed: true, data: result, msg: 'Data added' });
      }
    } catch (error) {
      console.error('Error occurred while creating the entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public findAll = async (req: Request, res: Response) => {
    try {
      const page = Number(req.query.page) || 0;
      const size = Number(req.query.size) || 0;
      const result = await this.service.findAll(page, size);
      const totalCount = await this.service.getCount();
      res.json({
        isSucceed: true,
        data: result,
        count: totalCount,
        msg: 'Data found',
      });
    } catch (error) {
      console.error('Error occurred while retrieving entities:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public findById = async (req: Request, res: Response) => {
    let id: number | string = req.params.id;
    if (!isNaN(Number(req.params.id))) {
      id = Number(req.params.id);
    }

    try {
      const result = await this.service.findById(id);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data found' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while finding entity by ID:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  // To get the records by projectId
  public getByProjectId = async (req: Request, res: Response) => {
    const id: string = req.params.id;

    try {
      const result = await this.service.getByProjectId(id);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data found' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while finding entity by Project ID:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public findBySpecId = async (req: Request, res: Response) => {
    let id: number | string = req.params.id;
    if (!isNaN(Number(req.params.id))) {
      id = Number(req.params.id);
    }

    try {
      const result = await this.service.findBySpecId(id);

      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data found' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while finding entity by ID:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public findByProjectId = async (req: Request, res: Response, entity: string) => {
    try {
      const id: string = req.params.id;
      const isSubmittal = req.query.submittal == 'true' ? true : false || false;
      const isSubmittalEdit = req.query.edit == 'true' ? true : false || false;
      const submittalVersionId = req.query.submittalVersionId || null;
      const updatedAfter = req.query.updatedAfter;
      const query = req.query;
      const dataCount: {
        page: number;
        pageSize: number;
        totalItems: number;
        totalPages: number;
        rowCount: number;
      } = {
        page: 0,
        pageSize: 0,
        totalItems: 0,
        totalPages: 0,
        rowCount: 0,
      };
      let relation: string[] = [];

      if (entity in entityRelationList) {
        relation = entityRelationList[entity].relation;
      }
      let result = [];
      const dceData = await dceModel.findByEntity(entity);
      let dataControl: any[] = [];
      if (dceData) {
        dataControl = await getUserRoleDetail((req as any).user, id);
        if (dataControl.length <= 0) {
          return res.json({
            isSucceed: true,
            data: [],
            dataCounts: dataCount,
            dropDown: [],
            msg: 'Data found',
          });
        }
      }
      result =
        (await this.service.findByProjectId(
          id,
          relation,
          isSubmittal,
          dataControl,
          updatedAfter as any,
          isSubmittalEdit,
          submittalVersionId as string,
          entity
        )) || [];

      if (dceData && result && entity != 'cutMaster') {
        const latAndLongConversion =
          await convertLatAndLongToEastingAndNorthingOnGetDataByProjectId(result, dceData.id, id);
        result = latAndLongConversion;
      }
      if (dceData && result) {
        result = await dataEditableAccessChange((req as any).user, id, result);
      }

      const finalResult = result;

      dataCount.totalItems = finalResult.length;

      const sortAndFilter = new SortAndFilter();
      let filteredResult = [];
      if (dceData) {
        filteredResult = await sortAndFilter.sortFilterForGetByProjectId(
          query,
          finalResult,
          dceData
        );
      } else {
        filteredResult = await sortAndFilter.sortFilterForGetByProjectId(query, finalResult);
      }
      dataCount.rowCount = filteredResult.length;
      if (req?.query?.page && req?.query?.pageSize) {
        const page = Number(req?.query?.page) || 1;
        const pageSize = Number(req?.query?.pageSize) || 10;
        dataCount.page = page;
        dataCount.pageSize = pageSize;
        dataCount.totalPages = Math.ceil(filteredResult.length / pageSize);
        filteredResult = this.paginate(filteredResult, page, pageSize);
      }
      if (dceData?.id && filteredResult.length > 0) {
        filteredResult = await checkNameByIdWithRequireForValidationErrors<T>(
          dceData?.id,
          filteredResult,
          id
        );
      }
      return res.json({
        isSucceed: true,
        data: filteredResult || [],
        dataCounts: dataCount,
        dropDown: [],
        msg: 'Data found',
      });
    } catch (error) {
      console.error('Error occurred while finding entity by ID:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };
  public getDataCountByProjectId = async (req: Request, res: Response, entity: string) => {
    try {
      const id: string = req.params.id;
      const dceData = await dceModel.findByEntity(entity);
      let dataControl: any[] = [];
      if (dceData) {
        dataControl = await getUserRoleDetail((req as any).user, id);
        if (dataControl.length <= 0) {
          return res.json({
            isSucceed: true,
            data: [],
            dropDown: [],
            msg: 'Data found',
          });
        }
      }
      const result = (await this.service.getDataCountByProjectId(id, dataControl)) || [];

      return res.json({
        isSucceed: true,
        data: result || [],
        msg: 'Data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  public findByWorkActivityId = async (req: Request, res: Response, entity: string) => {
    try {
      const id: string = req.params.id;
      const projectId: string = req.params.projectId;

      let relation: string[] = [];

      if (entity in entityRelationList) {
        relation = entityRelationList[entity].relation;
      }

      const dropDown = await getRelatedSelectBoxDataByProject(entity, projectId);
      const result = await this.service.findByWorkActivityId(id, relation);

      const finalResult = result as any;

      return res.json({
        isSucceed: true,
        data: finalResult || [],
        dropDown: dropDown,
        msg: 'Data found',
      });
    } catch (error) {
      console.error('Error occurred while finding entity by ID:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public update = async (req: Request, res: Response, entity?: string) => {
    try {
      const id: number | string = req.params.id;
      const form = req.body.form;
      let data = this.replaceEmptyStringWithNull(req.body);
      data = this.convertEastingAndNorthingToLatAndLong(data);
      if (form) {
        data.form = form;
      }
      // const data = await this.findDateAndChangeToUtc(filteredData, req.body.projectId);
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      const result = await this.service.update(id, data, (req as any).user.id || '', entity);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data updated' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  private replaceEmptyStringWithNull(data: any): any {
    for (const key in data) {
      if (key == 'dropDown') {
        delete data.dropDown;
      }
      if (typeof data[key] === 'object' && data[key] !== null && key != 'enable' && key != 'form') {
        delete data[key];
      }
      if (typeof data[key] === 'string' && data[key] === '') {
        data[key] = null;
      }
    }
    return data;
  }

  public sendForApproval = async (req: Request, res: Response, entity: string) => {
    if ((req as any).user) {
      req.body.updatedBy = (req as any).user.name;
    }

    try {
      await this.service.sendForApproval(
        req.body.ids,
        req.body.projectId,
        entity,
        (req as any).user.name
      );

      res.json({
        isSucceed: true,
        data: [],
        msg: 'Data send for approval',
      });
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public delete = async (req: Request, res: Response) => {
    const id = parseInt(req.params.id, 10);
    try {
      const deleted = await this.service.delete(id);
      if (deleted) {
        res.status(204).json({ isSucceed: true, data: [], msg: 'Item deleted' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while deleting entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public softDelete = async (req: Request, res: Response, entity?: string) => {
    try {
      const id: number | string = req.params.id;
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      if (req.body.sampleId) {
        try {
          await dceRelationshipModel.deleteRelationBySourceAndTargetDataId(req.body.sampleId, id);
        } catch (err) {
          console.warn('Failed to delete DCE relation:', err);
        }
      }
      const result = await this.service.softDelete(id, (req as any).user.id || '', entity);
      if (result) {
        res.json({ isSucceed: true, data: result, msg: 'Data updated' });
      } else {
        res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'Entity not found',
        });
      }
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public multiSoftDelete = async (req: Request, res: Response, entity?: string) => {
    try {
      const id: string[] = req.body.ids;

      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      if (id.length > 0) {
        const testData = await keyValueStoreModel.getByKey('sample_tests');
        const isSampleTest = testData?.value?.includes(entity);
        const result = await Promise.all(
          id.map(async (value) => {
            if (isSampleTest) {
              const data = await this.service.findById(value);
              if (data?.sampleId) {
                try {
                  await dceRelationshipModel.deleteRelationBySourceAndTargetDataId(
                    data.sampleId,
                    value
                  );
                } catch (err) {
                  console.warn('Failed to delete DCE relation:', err);
                }
              }
            }
            return await this.service.softDelete(value, (req as any).user.id || '', entity);
          })
        );

        if (result.length > 0) {
          res.json({ isSucceed: true, data: result, msg: 'Data updated' });
        } else {
          res.status(404).json({
            isSucceed: false,
            data: [],
            msg: 'Entity not found',
          });
        }
      }
    } catch (error) {
      console.error('Error occurred while updating entity:', error);
      return errorMiddleware(error, 500, res, req);
    }
  };

  public getRouter() {
    return this.router;
  }

  private findDateAndChangeToUtc = async (data: any) => {
    try {
      // const projectDetails = await projectModel.getProjectId(projectId);
      const timeZone = '-05:00';
      if (Array.isArray(data)) {
        data.map((value) => {
          Object.keys(value).map((key) => {
            const date = new Date(value[key]);
            if (date instanceof Date) {
              const formatedDate = value[key].replace('Z', timeZone);
              const offset = formatedDate.getTimezoneOffset();
              const utcTime = new Date(date.getTime() + offset * 60 * 1000);
              value[key] = utcTime;
            }
          });
          return value;
        });
      } else {
        Object.keys(data).map((key) => {
          const date = new Date(data[key]);
          if (date instanceof Date) {
            const formatedDate = data[key].replace('Z', timeZone);
            const offset = formatedDate.getTimezoneOffset();
            const utcTime = new Date(date.getTime() + offset * 60 * 1000);
            data[key] = utcTime;
          }
        });
      }
      return data;
    } catch (error) {
      throw error;
    }
  };
  private convertEastingAndNorthingToLatAndLong = (data: any) => {
    try {
      if ('easting' in data && 'northing' in data) {
        if (data.easting && data.northing) {
          const result: any = convertEastingNorthingToLatLong(
            Number(data.easting),
            Number(data.northing)
          );
          if (result && result.latitude && result.longitude) {
            data.longitude = result.longitude;
            data.latitude = result.latitude;
          }
        }
      }
      return data;
    } catch (error) {
      throw error;
    }
  };

  private removeEmptyKeys(obj: any) {
    for (const key in obj) {
      if (obj[key] === null || obj[key] === undefined || obj[key] === '') {
        delete obj[key];
      } else if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        this.removeEmptyKeys(obj[key]);
      }
    }
    return obj;
  }

  private paginate = (data: any[], page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };
}

export default CrudController;
