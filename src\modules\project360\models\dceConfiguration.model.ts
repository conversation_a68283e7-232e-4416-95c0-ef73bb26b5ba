import { getManager } from 'typeorm';
import { dceConfiguration } from '../../../entities/p_gen/dceConfiguration';
import { DataCaptureElements } from '../../../entities/p_meta/DataCaptureElements';
import { entityList } from '../../../shared/utilities/entity/entityList';
import { DCECoordinatesConfiguration } from '../../../entities/p_gen/DCECoordinatesConfig';
import MetaDCEConfigurationModel from './meta/adminDceConfiguration.model';

//**
// * Get the admin dce configuration data for the dceId
// * Get the project level dce configuration data based on dceId and projectId
// * Get easting and northing or latitude and longitude based on dceId and projectId
// * Get isUrl, is
//  */

const excludedColumns = ['projectId', 'id', 'easting', 'northing', 'latitude', 'longitude'];

class DCEConfigurationModel {
  adminDCEModel = new MetaDCEConfigurationModel();
  async getData(dceData: DataCaptureElements, projectId: string) {
    try {
      const Repo = getManager().getRepository(dceConfiguration);
      const DCECoordinatesConfigurationRepository = getManager().getRepository(
        DCECoordinatesConfiguration
      );
      if (dceData.entity && dceData.entity in entityList) {
        let adminData = await this.adminDCEModel.getData(dceData);
        const adminConfigData = adminData ?? [];
        const configurationData = await Repo.find({
          where: { dceId: dceData.id, projectId },
          relations: ['validation', 'validation.criteria', 'route', 'dce', 'adminDceConfig'],
          order: { order: 'ASC' },
        });

        const coordinatesData = await DCECoordinatesConfigurationRepository.findOne({
          where: { dceId: dceData.id, projectId, isDelete: false },
        });
        if (dceData.isGeoJson) {
          adminData =
            coordinatesData && !coordinatesData.isLatLong
              ? adminData.filter(
                  (value) => value.columnName !== 'latitude' && value.columnName !== 'longitude'
                )
              : adminData.filter(
                  (value) => value.columnName !== 'easting' && value.columnName !== 'northing'
                );
        }

        if (configurationData.length > 0) {
          const existingColumns = configurationData.map(
            (value) => value.adminDceConfig?.columnName
          );
          // dbColumnName contains easting northing or latitude and longitude

          const newColumnToAdd = adminConfigData.filter(
            (item) =>
              !existingColumns.includes(item.columnName) &&
              !excludedColumns.includes(item.columnName)
          );
          const columnToRemove = existingColumns.filter((item) => {
            const found = adminConfigData.find((value) => value.columnName == item);
            if (!found) {
              return item;
            }
          });

          if (newColumnToAdd.length > 0) {
            const newConfig = newColumnToAdd
              .filter((column) => column.columnName !== 'projectId' && column.columnName !== 'id')
              .map((column) => {
                const baseConfig = {
                  columnName: column.columnName,
                  alias: column.alias,
                  dceId: dceData.id,
                  projectId,
                  adminDceConfigId: column.id,
                  order: column.order,
                  hide: column.hide,
                };

                return baseConfig;
              });

            await getManager().transaction(async (transactionalEntityManager) => {
              try {
                await transactionalEntityManager.save(dceConfiguration, newConfig);
                if (dceData.isGeoJson && !coordinatesData) {
                  const newCoordinatesConfig: any = {
                    projectId,
                    dceId: dceData.id,
                    isLatLong: true,
                  };
                  await transactionalEntityManager.save(
                    DCECoordinatesConfiguration,
                    newCoordinatesConfig
                  );
                }
              } catch (error) {
                throw error;
              }
            });
          }
          if (columnToRemove.length > 0) {
            const idsToDelete: string[] = [];
            columnToRemove.map((value) => {
              if (value) {
                const itemToDelete = configurationData.find((item) => item.columnName == value);
                idsToDelete.push(itemToDelete?.id || '');
              }
            });
            await Repo.delete(idsToDelete);
          }
        } else {
          if (adminConfigData) {
            const newConfig = adminConfigData
              .filter((column) => column.columnName !== 'projectId' && column.columnName !== 'id')
              .map((column) => {
                const baseConfig = {
                  columnName: column.columnName,
                  alias: column.alias,
                  dceId: dceData.id,
                  projectId,
                  adminDceConfigId: column.id,
                  order: column.order,
                  hide: column.hide,
                };
                return baseConfig;
              });

            const finalConfig = newConfig.map((value, index) => ({
              ...value,
              order: index,
            }));

            await getManager().transaction(async (transactionalEntityManager) => {
              try {
                await transactionalEntityManager.save(dceConfiguration, finalConfig);
                if (dceData.isGeoJson && !coordinatesData) {
                  const newCoordinatesConfig: any = {
                    projectId,
                    dceId: dceData.id,
                    isLatLong: true,
                  };
                  await transactionalEntityManager.save(
                    DCECoordinatesConfiguration,
                    newCoordinatesConfig
                  );
                }
              } catch (error) {
                throw error;
              }
            });
          }
        }
        const newDCEConfigurationData = await Repo.find({
          where: { dceId: dceData.id, projectId },
          relations: [
            'validation',
            'validation.criteria',
            'route',
            'dce',
            'adminDceConfig',
            'units',
          ],
          order: { order: 'ASC' },
        });
        return newDCEConfigurationData.map((value) => {
          const columnName = value.adminDceConfig?.columnName ?? value.columnName; // Default to existing columnName if relation is missing
          const columnType = value.adminDceConfig?.type ?? value.type;
          const found = adminConfigData.find(
            (item) => item.id == value.adminDceConfigId && item.type == 'singleSelect'
          );
          if (found) {
            (value as any).dropdownURL = found.dropdownURL;
            if (value.route) {
              (value as any).route = value.route.route;
              value.isRelation = true;
            }
            if (found.entity) {
              (value as any).entity = found.entity;
            }
          }
          if (value.dce) {
            delete value.dce;
          }
          return {
            ...value,
            columnName,
            isURL: value.adminDceConfig?.isURL ?? false,
            isRelation: value.adminDceConfig?.isRelation ?? false,
            decimalPointsNeeded:
              value?.decimalPointsNeeded ?? value.adminDceConfig?.decimalPointsNeeded,
            width: value?.width ?? value.adminDceConfig?.width,
            type: columnType,
            unitCategoryId: value.adminDceConfig?.unitCategoryId,
            adminDceConfig: undefined, // Ensures it's not included in the final response
          };
        });
      }
    } catch (error) {
      throw error;
    }
  }

  async getById(id: string) {
    try {
      const Repo = getManager().getRepository(dceConfiguration);

      const configurationData = await Repo.findOne({
        where: { id },
        relations: ['validation', 'validation.criteria', 'route', 'dce'],
        order: { order: 'ASC' },
      });
      return configurationData;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdAndDceForGeoJsonGetData(projectId: string, dceId: string) {
    try {
      const Repo = getManager().getRepository(dceConfiguration);
      const configurationData = await Repo.find({
        where: { dceId, projectId },
        order: { order: 'ASC' },
        relations: ['validation', 'validation.criteria', 'route', 'dce'],
      });
      return configurationData;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectIdAndDceIsURLForGeoJsonGetData(projectId: string, dceId: string) {
    try {
      const Repo = getManager().getRepository(dceConfiguration);
      const configurationData = await Repo.find({
        where: { dceId, projectId, isURL: true },
        order: { order: 'ASC' },
      });
      return configurationData;
    } catch (error) {
      throw error;
    }
  }

  async getDataByDCEIdAndProjectId(dceId: string, projectId: string) {
    try {
      const Repo = getManager().getRepository(dceConfiguration);
      return await Repo.find({
        where: { dceId, projectId },
        relations: ['adminDceConfig'],
        order: { order: 'ASC' },
      });
    } catch (error) {
      throw error;
    }
  }
  async changeColumnOrder(newOrder: { id: string; order: number }[]) {
    try {
      await getManager().transaction(async (transactionalEntityManager) => {
        try {
          Promise.all(
            newOrder.map(async (value) => {
              try {
                await transactionalEntityManager.update(
                  dceConfiguration,
                  { id: value.id },
                  { order: value.order }
                );
              } catch (error) {
                throw error;
              }
            })
          );
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  findCoordinateDetailsAndAddCoordinatesDetails = async (dceId: string, projectId: string) => {
    try {
      const DCECoordinatesConfigurationRepository = getManager().getRepository(
        DCECoordinatesConfiguration
      );
      const coordinatesData = await DCECoordinatesConfigurationRepository.findOne({
        where: { dceId, projectId, isDelete: false },
      });
      if (coordinatesData?.isLatLong) {
        return [{}];
      }
    } catch (error) {
      throw error;
    }
  };

  async findDceElementByIdandProjectId(dceId: string, projectId: string) {
    try {
      const dceConfigRepo = getManager().getRepository(dceConfiguration);
      const dceElements = await dceConfigRepo
        .createQueryBuilder('dce')
        .select(['dce.columnName', 'dce.alias'])
        .where('dce.dceId = :dceId', { dceId })
        .andWhere('dce.projectId = :projectId', { projectId })
        .andWhere('dce.required = :required', { required: true })
        .andWhere('dce.isDelete = :isDelete', { isDelete: false })
        .getMany();
      return dceElements;
    } catch (error) {
      throw error;
    }
  }
}

export default DCEConfigurationModel;
