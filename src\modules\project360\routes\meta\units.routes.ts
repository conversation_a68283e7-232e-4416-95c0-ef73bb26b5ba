import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { Units } from '@entities/p_meta/Units';
import unitsController from '@controllers//meta/units.controller';


const router: Router = express.Router();

const GenericController = new CrudController<Units>(Units);

// Mount the userRouter for CRUD operations at /auth/user/crud

router.get('/', GenericController.findAll);
router.get('/:id', authenticateToken, GenericController.findById);
router.get('/get/by/unit-category/:id', unitsController.getByUnitCategory);
router.post('/', authenticateToken, (req, res) => GenericController.create(req, res));
router.put('/:id', authenticateToken, (req, res) => GenericController.create(req, res));
router.delete('/:id', authenticateToken, (req, res) =>
  GenericController.softDelete(req, res)
);

export default router;
