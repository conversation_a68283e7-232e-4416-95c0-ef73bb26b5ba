import { Request, Response } from 'express';
import sampleModel from '../models/sample.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import successResponse from 'src/shared/middlewares/response/successResponse.middleware';
class SampleController {
  constructor() {}

  addSample = async (req: Request, res: Response) => {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }
      const reqData = this.removeEmptyKeys(req.body);
      const sample = await sampleModel.addSample(reqData);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: sample,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  async addMultipleSample(req: Request, res: Response) {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }

      const { noOfSamples, ...sampleData } = req.body;

      const sample = await sampleModel.addMultipleSample(sampleData, noOfSamples);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: sample,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  private removeEmptyKeys = (obj: any) => {
    for (const key in obj) {
      if (obj[key] === null || obj[key] === undefined || obj[key] === '' || obj[key] === 'null') {
        delete obj[key];
      } else if (typeof obj[key] === 'object' && !Array.isArray(obj[key])) {
        this.removeEmptyKeys(obj[key]);
      }
    }
    return obj;
  };

  addSampleStep = async (req: Request, res: Response) => {
    try {
      const id = req.params.id;
      const reqData = this.removeEmptyKeys(req.body);
      const sampleDetails = await sampleModel.findById(id);

      const keysToRemove = ['sampleSpecimens', 'editable', 'currentStep'];
      keysToRemove.forEach((key) => delete reqData[key]);

      if (!sampleDetails) {
        return res
          .status(404)
          .json({ response: { isSucceed: false, data: [], msg: 'Sample not found' } });
      }
      const sample = await sampleModel.updateSample(id, reqData, (req as any).user.id);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: sample,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const sampleData = await sampleModel.findById(req.params.id);
      // checking if data is found with the id
      if (sampleData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: sampleData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          response: {
            isSucceed: true,
            data: [],
            msg: message,
          },
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async findByDate(req: Request, res: Response) {
    try {
      const { projectId } = req.params;
      const { from, to } = req.query;
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);

      // Format dates as "YYYY-MM-DD"

      // Set default values if empty
      const fromDate = from ? new Date(from as string) : yesterday;
      const toDate = to ? new Date(to as string) : today;
      const sampleData = await sampleModel.sampleByDate(projectId, fromDate, toDate);
      // checking if data is found with the id
      if (sampleData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: sampleData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          response: {
            isSucceed: true,
            data: [],
            msg: message,
          },
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByProjectId(req: Request, res: Response) {
    try {
      const sampleData = await sampleModel.findByProjectId(req.params.id);
      // getting the data from database with the given id

      if (sampleData && sampleData.length > 0) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: sampleData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async uploadAFile(req: Request, res: Response) {
    try {
      const { filePath } = req.body;
      let newFile = '';
      const sampleData = await sampleModel.findById(req.body.sampleId);
      if (sampleData?.file) {
        newFile = `${sampleData.file},${filePath}`;
      } else {
        newFile = filePath;
      }
      if (newFile != '' || !filePath) {
        const update = await sampleModel.updateFile(Number(req.body.sampleId), newFile);
        const message = req.__('UpdatedSuccess');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: update,
          msg: message,
        });
      } else {
        const message = req.__('UpdatedUnSuccess');
        throw new Error(message);
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findByUserId(req: Request, res: Response) {
    try {
      const sampleData = await sampleModel.getSampleByUserId(req.params.id);
      // getting the data from database with the given id

      if (sampleData && sampleData.length > 0) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: sampleData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async findSampleForCheckIn(req: Request, res: Response) {
    try {
      const {
        bagId,
        sampleId,
        ctNoId,
        sampleNo,
      }: { bagId?: string; sampleId?: string; ctNoId?: string; sampleNo?: string } = req.query;
      // getting the data from database with the given id
      const sampleData = await sampleModel.getSampleForCheckIn(
        bagId,
        sampleId,
        ctNoId,
        sampleNo,
        req.params.id
      );

      if (sampleData) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: sampleData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getDataByStationRange(req: Request, res: Response) {
    try {
      const { stationStart, stationEnd, projectId } = req.params;
      const data = await sampleModel.getDataByStationRange(
        Number(stationStart),
        Number(stationEnd),
        projectId
      );
      return successResponse(data, 'Data found', res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  // TODO: Commented because of column changes. Need to update the code

  // async getSamplesandUCSByBoreNumbers(req: Request, res: Response) {
  //   try {
  //     const { boreNumbers } = req.body;

  //     if (!Array.isArray(boreNumbers) || boreNumbers.length === 0) {
  //       return res.status(400).json({
  //         isSucceed: false,
  //         data: { samples: [], tests: [] },
  //         msg: 'Invalid or no boreNumbers provided',
  //       });
  //     }

  //     const sampleData = await sampleModel.getSamplesByBoreholes(boreNumbers);

  //     const sampleIds = sampleData.map((sample) => sample.id);

  //     const ucsTests =
  //       sampleIds.length > 0 ? await stSoilUCSTestModel.getTestsBySampleIds(sampleIds) : [];

  //     const response = {
  //       isSucceed: true,
  //       data: {
  //         samples: sampleData,
  //         tests: ucsTests,
  //       },
  //       msg: ucsTests?.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage'),
  //     };

  //     return res.status(200).json(response);
  //   } catch (error) {
  //     return errorMiddleware(error, 500, res, req);
  //   }
  // }
}

export default SampleController;
