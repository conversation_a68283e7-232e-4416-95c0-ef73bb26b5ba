import StationModel from '../../../modules/project360/models/map/station.model';

export const nearestStationFromLatLong = async (
  latitude: number,
  longitude: number,
  projectId: string
) => {
  try {
    const stationModel = new StationModel();
    const stations = await stationModel.getStationByProjectId(projectId);
    let nearestStation = null;
    let nearestDirection = '';
    let minDistance = Number.MAX_VALUE;
    for (const station of stations) {
      const distance = getDistance(
        Number(station.latitude),
        Number(station.longitude),
        latitude,
        longitude
      );

      if (distance < minDistance) {
        minDistance = distance;

        nearestStation = station;
        const direction = calculateDirection(
          latitude,
          longitude,
          Number(station.latitude),
          Number(station.longitude)
        );
        nearestDirection = direction;
      }
    }
    return { minDistance, nearestStation, nearestDirection };
  } catch (error) {
    throw error;
  }
};

export const getDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
  try {
    const dLat = ((lat2 - lat1) * Math.PI) / 180.0;
    const dLon = ((lon2 - lon1) * Math.PI) / 180.0;

    lat1 = (lat1 * Math.PI) / 180.0;
    lat2 = (lat2 * Math.PI) / 180.0;

    const a =
      Math.pow(Math.sin(dLat / 2), 2) +
      Math.pow(Math.sin(dLon / 2), 2) * Math.cos(lat1) * Math.cos(lat2);
    const rad = 6371;
    const c = 2 * Math.asin(Math.sqrt(a));

    const distanceInKm = rad * c;
    const distanceInFeet = distanceInKm * 3280.84;

    return distanceInFeet;
  } catch (error) {
    throw error;
  }
};

export const calculateDirection = (lat1: number, lon1: number, lat2: number, lon2: number) => {
  try {
    // Validate inputs
    if (lat1 < -90 || lat1 > 90 || lat2 < -90 || lat2 > 90) {
      throw new Error('Latitude must be in the range [-90, 90].');
    }
    if (lon1 < -180 || lon1 > 180 || lon2 < -180 || lon2 > 180) {
      throw new Error('Longitude must be in the range [-180, 180].');
    }

    // Convert latitudes to radians
    const lat1Rad = lat1 * (Math.PI / 180);
    const lat2Rad = lat2 * (Math.PI / 180);

    // Calculate the longitudinal difference in radians
    const dLon = (lon2 - lon1) * (Math.PI / 180);

    // Calculate bearing components
    const y = Math.sin(dLon) * Math.cos(lat2Rad);
    const x =
      Math.cos(lat1Rad) * Math.sin(lat2Rad) -
      Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLon);

    // Compute bearing in degrees
    let brng = Math.atan2(y, x);
    brng = (brng * 180) / Math.PI;

    // Normalize bearing to [0, 360)
    brng = (brng + 360) % 360;

    // Map bearing to cardinal direction
    const bearings = [
      'North,N',
      'NorthEast,NE',
      'East,E',
      'SouthEast,SE',
      'South,S',
      'SouthWest,SW',
      'West,W',
      'NorthWest,NW',
    ];
    const index = Math.round(brng / 45) % bearings.length;

    return bearings[index];
  } catch (error) {
    throw new Error(`Error in calculateDirection: ${error}`);
  }
};

export const getOffsetSign = (
  latitude: string,
  longitude: string,
  neareststationlatitude: number,
  neareststationlongitude: number,
  neareststationCoordinates: string
) => {
  if (isNaN(Number(latitude)) || isNaN(Number(neareststationlatitude))) {
    throw new Error('Invalid latitude or neareststationlatitude value.');
  }

  if (isNaN(Number(longitude)) || isNaN(Number(neareststationlongitude))) {
    throw new Error('Invalid latitude or neareststationlatitude value.');
  }

  let sign = '';
  if (neareststationCoordinates === 'right') {
    if (Number(neareststationlongitude) > Number(longitude)) {
      sign = '+';
    } else {
      sign = '-';
    }
  } else if (neareststationCoordinates === 'left') {
    if (Number(neareststationlongitude) > Number(longitude)) {
      sign = '-';
    } else {
      sign = '+';
    }
  } else if (neareststationCoordinates === 'top') {
    if (Number(neareststationlatitude) > Number(latitude)) {
      sign = '+';
    } else {
      sign = '-';
    }
  } else if (neareststationCoordinates === 'bottom') {
    if (Number(neareststationlatitude) > Number(latitude)) {
      sign = '-';
    } else {
      sign = '+';
    }
  }

  return sign;
};
