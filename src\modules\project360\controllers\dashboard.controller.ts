import { Request, Response } from 'express';
import projectModel from '../models/project.model';
import dashboardModel from '../models/dashboard.model';
import { checkIsSuperAdmin } from '../../../shared/server/platformApi/role';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';

class DashboardController {
  constructor() {}

  async project360Overview(req: Request, res: Response) {
    try {
      const final: any = {
        projectCount: 0,
        testCount: 0,
        taskCount: 0,
        recentTask: [],
        projectList: [],
      };
      const userId = req.params.id;
      // getting the data from the database with the given id
      const isSuperAdmin = await checkIsSuperAdmin(userId, req.headers.authorization);
      let projects = await projectModel.getProjectByUserId(userId);
      const firstFiveProject = projects.slice(0, 5);
      if (isSuperAdmin === true) {
        projects = await projectModel.getAll();
      }
      const testCount = await dashboardModel.getTestCount();
      const taskCount = await dashboardModel.getTaskByUserId(userId);
      const recentTask = await dashboardModel.getRecentTaskByUserId(userId);
      const projectCount = projects.length;
      final.projectCount = projectCount;
      final.testCount = testCount;
      final.taskCount = taskCount;
      final.recentTask = recentTask;
      final.projectList = firstFiveProject;

      if (isSuperAdmin) {
        final.projectList = projects;
      }

      // if true, data will be sent as a response
      return res.status(200).json({
        isSucceed: true,
        data: final,
        msg: 'Data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const dashboardController = new DashboardController();
export default dashboardController;
