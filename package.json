{"name": "api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "cross-env NODE_ENV=dev nodemon --watch src --exec ts-node -r tsconfig-paths/register src/index.ts", "local": "cross-env NODE_ENV=local nodemon --watch src --exec ts-node -r tsconfig-paths/register src/index.ts", "start": "cross-env NODE_ENV=prod npm run typeorm:run && nodemon --watch src --exec ts-node -r tsconfig-paths/register src/index.ts", "sandbox": "cross-env NODE_ENV=sandbox nodemon --watch src --exec ts-node -r tsconfig-paths/register src/index.ts", "test": "echo \"Error: no test specified\" && exit 1", "typeorm:migrate": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -d src/migrationConnection.ts", "typeorm:run": "npx ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -d src/migrationConnection.ts", "build": "tsc"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/bcrypt": "^5.0.0", "@types/cors": "^2.8.14", "@types/express": "^4.17.21", "@types/express-fileupload": "^1.4.3", "@types/i18n": "^0.13.8", "@types/jsonwebtoken": "^9.0.3", "@types/mime-types": "^2.1.4", "@types/morgan": "^1.9.7", "@types/multer": "^1.4.9", "@types/node": "^20.10.2", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/proj4": "^2.5.5", "@types/shapefile": "^0.6.4", "@types/shpjs": "^3.4.7", "@types/ssh2": "^1.11.15", "@types/ssh2-sftp-client": "^9.0.2", "@types/string-template": "^1.0.6", "@types/unzipper": "^0.10.9", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.10.0", "cross-env": "^7.0.3", "dotenv": "^16.3.1", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-config-standard-with-typescript": "^39.1.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-n": "^16.3.0", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-promise": "^6.1.1", "nodemon": "^3.0.1", "prettier": "^3.0.3", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "dependencies": {"@azure/storage-blob": "^12.17.0", "@mapbox/shp-write": "^0.4.3", "@types/chart.js": "^2.9.41", "apollo-server-express": "^3.13.0", "axios": "^1.6.5", "bcrypt": "^5.1.1", "bottleneck": "^2.19.5", "class-validator": "^0.14.2", "cors": "^2.8.5", "csv-parse": "^5.6.0", "exceljs": "^4.4.0", "exifreader": "^4.23.3", "exiftool-vendored": "^23.6.0", "express": "^4.18.2", "express-fileupload": "^1.4.3", "geojson": "^0.5.0", "geojson2shp": "^0.5.0", "graphql": "^16.11.0", "i18n": "^0.15.1", "ical-generator": "^8.1.1", "interpolate": "^0.1.0", "jsonwebtoken": "^9.0.2", "mime-types": "^2.1.35", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.6", "ogr2ogr": "^5.0.0", "pg": "^8.11.3", "piexifjs": "^1.0.6", "proj4": "^2.10.0", "puppeteer": "^23.9.0", "querystring": "^0.2.1", "reflect-metadata": "^0.1.13", "shapefile": "^0.6.6", "sharp": "^0.33.5", "shp-write": "^0.3.2", "ssh2": "^1.14.0", "ssh2-sftp-client": "^9.1.0", "string-template": "^1.0.0", "type-graphql": "^2.0.0-rc.2", "typeorm": "^0.3.17", "unzipper": "^0.12.1", "uuid": "^9.0.1"}}