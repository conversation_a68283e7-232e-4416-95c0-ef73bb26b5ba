const convertObjectStringsToNumbers = (
  obj: Record<string, any>,
  exclusionList: string[] = []
): Record<string, any> => {
  const result: Record<string, any> = {};

  // Create case-insensitive exclusion list check
  const isExcluded = (key: string): boolean => {
    // Direct comparison first
    if (exclusionList.includes(key)) return true;

    // Case-insensitive comparison
    const keyLower = key.toLowerCase().trim();
    return exclusionList.some((item) => item.toLowerCase().trim() === keyLower);
  };

  for (const key in obj) {
    const value = obj[key];
    if (typeof value === 'string') {
      // Skip number conversion for fields in exclusion list
      if (isExcluded(key)) {
        result[key] = value;
      } else {
        const num = Number(value);
        result[key] = isNaN(num) ? value : num;
      }
    } else {
      result[key] = value;
    }
  }
  return result;
};

export default convertObjectStringsToNumbers;
