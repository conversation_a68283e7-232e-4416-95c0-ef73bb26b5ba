import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { BlastHole } from '../../../../../entities/p_cs/BlastHole';

const router: Router = express.Router();

const GenricController = new CrudController<BlastHole>(BlastHole);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'blastHole')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'blastHole')
);
router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'blastHole')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'blastHole')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'blastHole'));
router.put('/:id', authenticateToken, (req, res) => GenricController.update(req, res, 'blastHole'));
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'blastHole')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'blastHole')
);

export default router;
