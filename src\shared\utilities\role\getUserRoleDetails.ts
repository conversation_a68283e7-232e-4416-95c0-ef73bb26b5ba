import { FindOperator, In } from 'typeorm';
import RoleDataAccessModel from '../../../modules/project360/models/auth/roleDataAccess.model';
import projectRoleModel from '../../../modules/project360/models/role and permission/projectRole.model';
import { checkIsSuperAdmin } from '../../server/platformApi/role';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';

export const getUserRoleDetail = async (
  user: any,
  projectId: string
): Promise<{ purposeId: string; approvalStatusId: FindOperator<any> }[]> => {
  try {
    const isAdmin = await checkIsSuperAdmin(user.id);
    if (projectId && user.id) {
      const roleDetials = await projectRoleModel.getByProjectIdAndUser(projectId, user.id);
      if (roleDetials) {
        const dataAccessModel = new RoleDataAccessModel();
        const accessDetails = await dataAccessModel.getByRoleId(roleDetials.id);

        if (accessDetails && accessDetails.length > 0) {
          return accessDetails
            .filter(
              (value) =>
                value.purposeId &&
                value.access !== 'No Access' &&
                value.status &&
                value.status.length > 0
            )
            .map((value) => ({
              purposeId: value.purposeId || '',
              approvalStatusId: In(value.status || []),
              projectId: projectId,
              isDelete: false,
            }));
        }
      }
    }
    if (isAdmin) {
      return [
        {
          projectId: projectId,
          isDelete: false,
        },
      ] as any;
    }
    return [];
  } catch (error) {
    throw error;
  }
};
export const dataEditableAccessChange = async (user: any, projectId: string, data: any[]) => {
  try {
    if (projectId && user.id) {
      const isAdmin = await checkIsSuperAdmin(user.id);
      const roleDetials = await projectRoleModel.getByProjectIdAndUser(projectId, user.id);
      const approvalStatusmodel = new ApprovalStatusModel();
      const nonEditableStatus = await approvalStatusmodel.getNoEditableStatus();
      if (roleDetials) {
        const dataAccessModel = new RoleDataAccessModel();
        const accessDetails = await dataAccessModel.getByRoleId(roleDetials.id);

        return data.map((value) => {
          if (nonEditableStatus.includes(value.approvalStatusId)) {
            value.editable = false;
          } else if (isAdmin) {
            value.editable = true;
          } else {
            const accessDetail = accessDetails.find(
              (item) =>
                item.purposeId == value.purposeId && item.status?.includes(value.approvalStatusId)
            );
            if (accessDetail && accessDetail.access && accessDetail.access === 'Full Access') {
              value.editable = true;
            } else {
              value.editable = false;
            }
          }
          return value;
        });
      }
      if (isAdmin) {
        return data.map((value) => {
          if (nonEditableStatus.includes(value.approvalStatusId)) {
            value.editable = false;
          } else if (isAdmin) {
            value.editable = true;
          }
          return value;
        });
      }
    }
    return [];
  } catch (error) {
    throw error;
  }
};
