import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Point,
  BeforeInsert,
  OneToMany,
  BeforeUpdate,
} from 'typeorm';
import { Site } from '../p_gen/Site';
import { Purpose } from '../p_meta/Purpose';
import { Project } from '../p_gen/Project';
import { Feature } from '../p_gen/Feature';
import { ProjectMaterial } from '../p_gen/ProjectMaterial';
import Structure from '../p_gen/Structure';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { ConcreteMixDesign } from './ConcreteMixDesign';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { DesignStrength } from '../p_domain/DesignStrength';
import { CTLog } from '../p_gen/CTLog';
import { SampleSource } from '../p_gen/SampleSource';
import { PvCutMaster } from './PvCutMaster';
import { PvCLSMPlacement } from './PvCLSMPlacement ';
import { SampleSpecimen } from './SampleSpecimen';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { Borehole } from './Borehole';
import { EventLog } from '@entities/p_gen/EventLog';
import { Area } from '@entities/p_gen/Area';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { StockpileSize } from '@entities/p_domain/StockpileSize';
import { CuringMethod } from '@entities/p_meta/CuringMethod';
import { StationAlignment } from '@entities/p_map/StationAlignment';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class Sample extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  sampleNo?: string;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ nullable: true })
  testNoId?: string;

  @ManyToOne(() => CTLog, { nullable: true })
  @JoinColumn({ name: 'testNoId' })
  ctLog?: CTLog;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  mixId?: string;

  @ManyToOne(() => ConcreteMixDesign, { nullable: true })
  @JoinColumn({ name: 'mixId' })
  concreteMixDesign?: ConcreteMixDesign;

  @Column({ type: 'decimal', nullable: true })
  slump?: number;

  @Column({ type: 'decimal', nullable: true })
  temperature?: number;

  @Column({ type: 'decimal', nullable: true })
  airContent?: number;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;

  @Column({ nullable: true })
  externalSampleNo?: string;

  @Column({ nullable: true })
  location?: string;

  @Column({ type: 'decimal', nullable: true })
  depth?: number;

  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @Column({ type: 'decimal', nullable: true })
  weight?: number;

  @Column({ type: 'decimal', nullable: true })
  unitWeight?: number;

  @Column({ nullable: true })
  noOfBags?: number;

  @Column({ type: 'decimal', nullable: true })
  volume?: number;

  @Column({ nullable: true })
  concreteBatchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'concreteBatchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @Column({ nullable: true })
  panelNumberId?: string;

  @ManyToOne(() => PvCutMaster, { nullable: true })
  @JoinColumn({ name: 'panelNumberId' })
  pvCutMaster?: PvCutMaster;

  @Column({ nullable: true })
  placementId?: string;

  @ManyToOne(() => PvCLSMPlacement, { nullable: true })
  @JoinColumn({ name: 'placementId' })
  pvCLSMPlacement?: PvCLSMPlacement;

  @Column({ nullable: true })
  sourceId?: string;

  @ManyToOne(() => SampleSource, { nullable: true })
  @JoinColumn({ name: 'sourceId' })
  source?: SampleSource;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  retest?: boolean;

  @Column({ nullable: true })
  failedTestId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'failedTestId' })
  sample?: Sample;

  @Column({ nullable: true })
  station?: string;

  @Column({ nullable: true })
  offset?: string;

  @Column({ nullable: true })
  boreholeId?: string;

  @ManyToOne(() => Borehole, { nullable: true })
  @JoinColumn({ name: 'boreholeId' })
  borehole?: Borehole;

  @Column({ type: 'varchar', nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  designStrengthId?: string;

  @ManyToOne(() => DesignStrength, { nullable: true })
  @JoinColumn({ name: 'designStrengthId' })
  designStrength?: DesignStrength;

  @Column({ nullable: true })
  stockpileSizeId?: string;

  @ManyToOne(() => StockpileSize, { nullable: true })
  @JoinColumn({ name: 'stockpileSizeId' })
  stockpileSize?: StockpileSize;

  @Column({ nullable: true })
  curingMethodId?: string;

  @ManyToOne(() => CuringMethod, { nullable: true })
  @JoinColumn({ name: 'curingMethodId' })
  curingMethod?: CuringMethod;

  @Column({ nullable: true })
  stationAlignmentId?: string;

  @ManyToOne(() => StationAlignment, { nullable: true })
  @JoinColumn({ name: 'stationAlignmentId' })
  stationAlignment?: StationAlignment;

  @Column({ nullable: true })
  description?: string;

  @Column({ type: 'uuid', nullable: true })
  sampledBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  sampledDate?: Date;

  @Column({ nullable: true })
  sampleYear?: number;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  hasSpecimens?: boolean;

  @Column({ nullable: true })
  noOfSpecimens?: number;

  @OneToMany(() => SampleSpecimen, (sampleSpecimen) => sampleSpecimen.sample, {
    cascade: true,
  })
  sampleSpecimen?: SampleSpecimen[];

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @BeforeInsert()
  @BeforeUpdate()
  setSampleYear() {
    if (this.sampledDate) {
      const date = new Date(this.sampledDate);
      if (!isNaN(date.getTime())) {
        this.sampleYear = date.getFullYear();
      }
    }
  }

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
