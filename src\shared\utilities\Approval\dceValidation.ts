import { entityList, EntityListInterface } from '@utils/entity/entityList';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import { getManager, In } from 'typeorm';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import approvalSetupModel from '@models/approvalSetup.model';
import DCEConfigurationModel from '@models/dceConfiguration.model';

/**
 * Validates data before approval based on DCE (Data Configuration Entity) rules.
 *
 * @param {string} entity - The entity name to find the DCE data.
 * @param {any[]} data - The array of data objects to be validated.
 * @returns {Promise<any[]>} - Returns the original data array after validation.
 * @throws {Error} - Throws an error if the validation process fails.
 *
 * This function performs the following steps:
 * 1. Fetches the DCE data for the given entity.
 * 2. Retrieves the DCE configuration data based on the DCE ID.
 * 3. Filters the configuration data to find columns with required criteria.
 * 4. Maps through the input data to check if required columns are valid.
 * 5. Updates the approval status of the data based on the validation results.
 */
export const dceValidationBeforeApproval = async (entity: string, data: any[]) => {
  try {
    const dceData = await dceModel.findByEntity(entity);
    const projectId = data.length > 0 ? data[0]?.projectId : null;
    if (dceData && projectId) {
      const dceConfigModel = new DCEConfigurationModel();
      const dceConfigData = await dceConfigModel.getDataByDCEIdAndProjectId(dceData.id, projectId);
      if (dceConfigData && dceConfigData.length > 0) {
        const idsToChange: { id: string; valid: boolean; purposeId: string }[] = [];
        const columnWithCriteria = dceConfigData.filter((value) => value.required);
        data.map((value: any) => {
          let valid = true;
          columnWithCriteria.map((column) => {
            if (column.adminDceConfig?.columnName && column.adminDceConfig.columnName in value) {
              if (
                !value[column.adminDceConfig.columnName] ||
                value[column.adminDceConfig.columnName] == ''
              ) {
                valid = false;
              }
            }
          });
          idsToChange.push({ id: value.id, valid, purposeId: value.purposeId });
        });
        await editApprovalStatusData(idsToChange, entity, dceData.id, data[0].projectId);
      }
    }
    return data;
  } catch (error) {
    throw error;
  }
};

const editApprovalStatusData = async (
  idsToChange: { id: string; valid: boolean; purposeId: string }[],
  entity: string,
  dceId: string,
  projectId: string
) => {
  try {
    const { validIds, invalidIds } = idsToChange.reduce<DataReduction>(
      (acc: DataReduction, item: IdsToChange) => {
        item.valid
          ? acc.validIds.push({ id: item.id, purposeId: item.purposeId })
          : acc.invalidIds.push(item.id);
        return acc;
      },
      { validIds: [], invalidIds: [] }
    );

    if (entity in entityList) {
      const entityClass = entityList[entity as keyof EntityListInterface] as any;
      if (entityClass) {
        const entityManager = getManager();
        const approvalStatusModel = new ApprovalStatusModel();
        const inProgressStatusId = await approvalStatusModel.getByNameStatusId('In Progress');
        const completeStatusId = await approvalStatusModel.getByNameStatusId('Complete');
        const readyForSubmissionStatusid = await approvalStatusModel.getNextStatusId(
          inProgressStatusId || ''
        );
        const purposeWithApprovalSetup = await approvalSetupModel.checkSubmoduleWithFOrAllPurpose(
          dceId,
          projectId
        );
        const idsForWithApproval: string[] = [];
        const idsForWithoutApproval: string[] = [];
        validIds.forEach((value) => {
          (purposeWithApprovalSetup.includes(value.purposeId)
            ? idsForWithApproval
            : idsForWithoutApproval
          ).push(value.id);
        });

        await entityManager.transaction(async (transactionalEntityManager) => {
          if (idsForWithApproval.length > 0) {
            await transactionalEntityManager.update(
              entityClass,
              { id: In(idsForWithApproval) },
              { approvalStatusId: readyForSubmissionStatusid }
            );
          }

          if (idsForWithoutApproval.length > 0) {
            await transactionalEntityManager.update(
              entityClass,
              { id: In(idsForWithoutApproval) },
              { approvalStatusId: completeStatusId }
            );
          }

          if (invalidIds.length > 0) {
            await transactionalEntityManager.update(
              entityClass,
              { id: In(invalidIds) },
              { approvalStatusId: inProgressStatusId }
            );
          }
        });
      }
    }
  } catch (error) {
    throw error;
  }
};

export const dceValidationWithFieldErrors = async (
  entity: string,
  data: any[]
): Promise<{ columnName: string; error?: string; isRowValid: boolean }[]> => {
  try {
    const dceData = await dceModel.findByEntity(entity);
    if (!dceData) {
      return [{ columnName: 'entity', error: 'DCE Data not found', isRowValid: false }];
    }
    if (data.length === 0 || !data[0] || !data[0].projectId) {
      return [{ columnName: 'data', error: 'No data provided', isRowValid: false }];
    }

    const dceConfigModel = new DCEConfigurationModel();
    const dceConfigData = await dceConfigModel.getDataByDCEIdAndProjectId(
      dceData.id,
      data[0].projectId
    );

    if (!dceConfigData || dceConfigData.length === 0) {
      return [
        { columnName: 'configuration', error: 'DCE Configuration not found', isRowValid: false },
      ];
    }

    const requiredColumns = dceConfigData
      .filter((value) => value.required && value.adminDceConfig?.columnName)
      .map((c) => c.adminDceConfig!.columnName);

    const validationResults: { columnName: string; error?: string; isRowValid: boolean }[] = [];

    data.forEach((row: any) => {
      requiredColumns.forEach((column) => {
        if (!(column in row) || row[column] === '' || !row[column]) {
          validationResults.push({
            columnName: column,
            error: 'Field is required but missing or empty',
            isRowValid: false,
          });
        } else {
          validationResults.push({
            columnName: column,
            isRowValid: true,
          });
        }
      });
    });

    return validationResults.map((value) => {
      const alias = dceConfigData.find((item) => item.columnName === value.columnName)?.alias;
      if (alias) {
        value.columnName = alias;
      }
      return value;
    });
  } catch (error) {
    throw error;
  }
};

interface IdsToChange {
  id: string;
  valid: boolean;
  purposeId: string;
}

interface DataReduction {
  validIds: { id: string; purposeId: string }[];
  invalidIds: string[];
}
