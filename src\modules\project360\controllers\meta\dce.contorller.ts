import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import dceModel from '../../models/meta/dce.model';
import { EntityListInterface, entityList } from '../../../../shared/utilities/entity/entityList';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import { dceValidationWithFieldErrors } from '@utils/Approval/dceValidation';
import submitDataConversion from '@utils/dce/multipleDCEDataConversion';
import { sanitizeRequestData } from '@utils/validation/sanitizeRequestData';

class DCEController {
  constructor() {}

  async findByActivityId(req: Request, res: Response) {
    try {
      const result = await dceModel.findByActivityId(req.params.id);
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async findByEntity(req: Request, res: Response) {
    try {
      const result = await dceModel.findByEntity(req.params.id);
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async findByGeoJson(req: Request, res: Response) {
    try {
      const result = await dceModel.findByGeoJson();
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getColumnNameByDceId(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const dce = await dceModel.findById(id);
      if (dce) {
        const data = await dceModel.getColumnsByDce(dce);
        res.json({ isSucceed: true, data: data || [], msg: 'Order Changed' });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }
  async getRelationByDceId(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const dce = await dceModel.findById(id);
      let data: any = [];
      if (dce?.entity == 'photoVideo') {
        data = [
          {
            api: '/project360/meta/mediatype/by/project/{projectId}',
            relatedColumnName: 'name',
          },
          {
            api: '/project360/auth/stakeholder/user/by/project/{projectId}',
            relatedColumnName: '{user_firstName} {user_lastName}',
          },
        ];
      }
      res.json({ isSucceed: true, data: data || [], msg: 'Order Changed' });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }

  submitFormByDCE = async (req: Request, res: Response): Promise<any> => {
    try {
      const dceData = await dceModel.findById(req.params.dceId);
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if (dceData.entity === 'photoVideo') {
        throw new Error('Photo video entity not allowed for this API');
      }
      const approvalStatusModel = new ApprovalStatusModel();
      const approvalStatusId = approvalStatusModel.getUserStatusId();
      req.body.approvalStatusId = approvalStatusId;
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }
      const entity = dceData.entity;

      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const data = sanitizeRequestData(req.body);
        const response = await dceModel.addDataByEntity(
          data,
          entityValue,
          (req as any).user.id,
          entity
        );

        const message = response ? req.__('DataInputSuccess') : req.__('DataInputFail');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || {},
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  multipleDCESubmit = async (req: Request, res: Response): Promise<any> => {
    try {
      const data = submitDataConversion(sanitizeRequestData(req.body));
      const { projectId } = req.body;
      if (data.length <= 0) {
        throw new Error('Invalid body');
      }
      const response = await dceModel.addDataByMultipleEntity(
        data,
        (req as any).user.id,
        (req as any).user.name,
        projectId
      );

      const message = response.length > 0 ? req.__('DataInputSuccess') : req.__('DataInputFail');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: response || {},
        msg: message,
      });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };

  submitFormByDCEByEntity = async (req: Request, res: Response) => {
    try {
      if (req.params.entity === 'photoVideo') {
        throw new Error('Photo video entity not allowed for this API');
      }
      const dceData = await dceModel.findByEntity(req.params.entity);
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }
      const entity = dceData.entity;
      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const data = sanitizeRequestData(req.body);
        const response = await dceModel.addDataByEntity(
          data,
          entityValue,
          (req as any).user.id,
          entity
        );
        const message = response ? req.__('DataInputSuccess') : req.__('DataInputFail');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || {},
          msg: message,
        });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };

  editFormByDCE = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findById(req.params.dceId);
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      const entity = dceData.entity;
      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const data = sanitizeRequestData(req.body);
        const response = await dceModel.editDataByEntity(
          req.params.id,
          data,
          entityValue,
          (req as any).user.id,
          (req as any).user.name,
          entity
        );
        const message = response ? req.__('UpdatedSuccess') : req.__('UpdatedUnSuccess');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || {},
          msg: message,
        });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };
  getValidationResult = async (req: Request, res: Response) => {
    try {
      const { entity, id } = req.params;
      const dceData = await dceModel.findByEntity(entity);
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if (entity in entityList) {
        const entityClass = entityList[entity as keyof EntityListInterface] as any;
        const data = await dceModel.getDataByEntity(id, 'id', entityClass, true);
        const response = await dceValidationWithFieldErrors(entity, [data]);
        const message = response ? req.__('UpdatedSuccess') : req.__('UpdatedUnSuccess');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || {},
          msg: message,
        });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };
  editFormByDCEByEntity = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findByEntity(req.params.entity);
      if (req.body?.editable) {
        delete req.body.editable;
      }
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if ((req as any).user) {
        req.body.updatedBy = (req as any).user.name;
      }
      const entity = dceData.entity;
      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const data = sanitizeRequestData(req.body);
        const response = await dceModel.editDataByEntity(
          req.params.id,
          data,
          entityValue,
          (req as any).user.id,
          (req as any).user.name,
          entity
        );
        const message = response ? req.__('UpdatedSuccess') : req.__('UpdatedUnSuccess');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || {},
          msg: message,
        });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };
  getFormByDCE = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findById(req.params.dceId);
      if (!dceData || !dceData.entity) {
        throw new Error('Dce not found');
      }
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }
      const entity = dceData.entity;
      if (entity in entityList) {
        const entityValue: any = entityList[entity as keyof EntityListInterface];
        const columnExist = await dceModel.checkColumnExistByDce(entityValue, req.params.column);
        if (!columnExist) {
          throw new Error('Column not found');
        }

        const response = await dceModel.getDataByEntity(
          req.params.value,
          req.params.column,
          entityValue,
          req?.query?.single ? true : false
        );

        const message = response
          ? !req?.query?.single
            ? response.length > 0
              ? req.__('DataFoundMessage')
              : req.__('DataNotFoundMessage')
            : req.__('DataFoundMessage')
          : req.__('DataNotFoundMessage');

        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: response || (req?.query?.single ? {} : []),
          msg: message,
        });
      }
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };
  getAll = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findAll();

      const message =
        dceData.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');

      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: dceData,
        msg: message,
      });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  };
  getTypesById = async (req: Request, res: Response) => {
    try {
      const dceData = await dceModel.findById(req.params.id);

      if(dceData){
        const columnTypes = await dceModel.getColumnType(dceData);
        return res.status(200).json({
          isSucceed: true,
          data: columnTypes,
          msg: 'DCE Columns and Types found',
        });
      }

      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: dceData,
        msg: 'No data found for DCE',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const dceController = new DCEController();
export default dceController;
