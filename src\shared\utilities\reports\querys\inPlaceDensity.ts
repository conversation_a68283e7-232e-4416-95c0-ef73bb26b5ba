// inplace query
const inPlaceDensityQuery = `
SELECT 
    CAST(p.name AS TEXT) as purpose,
    CAST('Nuclear Gauge Depth: ' || din.description AS TEXT) as "densityTypeTestDepth",
    CAST(m."sampleId" as TEXT) as "sampleId",
    CAST(m."testLocation" AS TEXT) as "generalLocation",
    CAST(cl."testNo" AS TEXT) as "controlTest",
    CAST(m2."name" AS TEXT) as "course",
    CAST(m."moistureCount" AS TEXT) as "moistureCount",
    CAST(m."densityCount" AS TEXT) as "densityCount",
    CAST(m.elevation AS TEXT) as "elev",
    CAST(s."testNo" AS TEXT) as "sampleCT",
    CAST(m."maximumLabDensity" AS TEXT) as "labMaxDryDensity",
    CAST(m."optimumMoistureContent" AS TEXT) as "optimumMoisture",
    CAST(m."wetDensity" AS TEXT) as "fieldWetDensity",
    CAST(m."moistureContent" AS TEXT) as "fieldMoisture",
    CAST(m.northing AS TEXT) as "Northing",
    CAST(m.easting AS TEXT) as "Easting",
    CAST(m."dryDensity" AS TEXT) as "filedDryDensity",
    CAST(m."actualCompaction" AS TEXT) as "CMPT",
    CAST(m."requiredCompaction" AS TEXT) as "reqCMPT",
    CAST(ss."name" AS TEXT) as "status",
    CAST(ngi."gaugeNo" AS TEXT) as "gaugeNo",
    CAST(ngi."id" AS TEXT) as "gaugeId",
    CAST(m."standardMoistureCount" AS TEXT) as "gStandardMoistureCount",
    CAST(m."standardDensityCount" AS TEXT) as "gStandardDensityCount",
    CAST(m."station" AS TEXT) as "station",
    CAST(m."offset" AS TEXT) as "offset",
    CAST(sal."name" AS TEXT) as "alignment",
    CAST(m."testedBy" AS TEXT) as "technician",
    CAST('nuclearGaugeTest' AS TEXT) as "sourceEntity",
    CAST(m.id AS TEXT) as "sourceEntityId",
    m."dateTested"
FROM p_cs.st_soil_nuclear_gauge_test m 
JOIN p_meta.purpose p ON m."purposeId" = p.id
JOIN p_domain.depth_info_ngsc din ON m."depthId" = din.id
JOIN p_gen.material m2 ON m."materialId" = m2.id
JOIN p_cs.sample s ON m."sampleId" = s.id
JOIN p_meta.test_result ss ON m."testResultId" = ss.id
JOIN p_gen.ct_log cl on m."testNoId" = cl.id
JOIN p_domain.nuclear_gauges_info ngi ON m."gaugeId" = ngi.id
LEFT JOIN p_map.station_alignment sal ON m."stationAlignmentId" = sal.id
WHERE m."projectId" = {projectId} 
AND m."purposeId" = {purposeId} 
AND m."dateTested" BETWEEN {from} AND {to}
AND m."isDelete" = false

UNION ALL

SELECT 
    CAST(p.name AS TEXT) as purpose,
    CAST('Sand Cone' AS TEXT) as "densityTypeTestDepth",
    CAST('N/A' as TEXT) as "sampleId",
    CAST(m."testLocation" AS TEXT) as "generalLocation",
    CAST(cl."testNo" AS TEXT) as "controlTest",
    CAST(m2."name" AS TEXT) as "course",
    CAST('N/A' AS TEXT) as "moistureCount",
    CAST('N/A' AS TEXT) as "densityCount",
    CAST(m.elevation AS TEXT) as "elev",
    CAST(s."testNo" AS TEXT) as "sampleCT",
    CAST(m."maximumLabDensity" AS TEXT) as "labMaxDryDensity",
    CAST(m."optimumMoistureContent" AS TEXT) as "optimumMoisture",
    CAST('N/A' AS TEXT) as "fieldWetDensity",
    CAST(m."moistureContent" AS TEXT) as "fieldMoisture",
    CAST(m.northing AS TEXT) as "Northing",
    CAST(m.easting AS TEXT) as "Easting",
    CAST('N/A' AS TEXT) as "filedDryDensity",
    CAST(m."actualCompaction" AS TEXT) as "CMPT",
    CAST(m."requiredCompaction" AS TEXT) as "reqCMPT",
    CAST(ss."name" AS TEXT) as "status",
    CAST('N/A' AS TEXT) as "gaugeNo",
    CAST('N/A' AS TEXT) as "gaugeId",
    CAST('N/A' AS TEXT) as "gStandardMoistureCount",
    CAST('N/A' AS TEXT) as "gStandardDensityCount",
    CAST(m."station" AS TEXT) as "station",
    CAST(m."offset" AS TEXT) as "offset",
    CAST(sal."name" AS TEXT) as "alignment",
    CAST(m."testedBy" AS TEXT) as "technician",
    CAST('sandConeTest' AS TEXT) as "sourceEntity",
    CAST(m.id AS TEXT) as "sourceEntityId",
    m."dateTested"
FROM p_cs.st_soil_sand_cone_test m 
JOIN p_meta.purpose p ON m."purposeId" = p.id
JOIN p_domain.depth_info_ngsc din ON m."depthId" = din.id
JOIN p_gen.material m2 ON m."materialId" = m2.id
JOIN p_cs.sample s ON m."sampleId" = s.id
JOIN p_meta.test_result ss ON m."testResultId" = ss.id
JOIN p_gen.ct_log cl on m."testNoId" = cl.id
LEFT JOIN p_map.station_alignment sal ON m."stationAlignmentId" = sal.id
WHERE m."projectId" = {projectId} 
AND m."purposeId" = {purposeId} 
AND m."dateTested" BETWEEN {from} AND {to}
AND m."isDelete" = false

UNION ALL

SELECT 
    CAST(p.name AS TEXT) as purpose,
    CAST('Moisture Content' AS TEXT) as "densityTypeTestDepth",
    CAST('N/A' as TEXT) as "sampleId",
    CAST('N/A' AS TEXT) as "generalLocation",
    CAST(cl."testNo" AS TEXT) as "controlTest",
    CAST(m2."name" AS TEXT) as "course",
    CAST('N/A' AS TEXT) as "moistureCount",
    CAST('N/A' AS TEXT) as "densityCount",
    CAST('N/A' AS TEXT) as "elev",
    CAST(s."testNo" AS TEXT) as "sampleCT",
    CAST('N/A' AS TEXT) as "labMaxDryDensity",
    CAST('N/A' AS TEXT) as "optimumMoisture",
    CAST('N/A' AS TEXT) as "fieldWetDensity",
    CAST(m."moistureContent" AS TEXT) as "fieldMoisture",
    CAST('N/A' AS TEXT) as "Northing",
    CAST('N/A' AS TEXT) as "Easting",
    CAST('N/A' AS TEXT) as "filedDryDensity",
    CAST('N/A' AS TEXT) as "CMPT",
    CAST('N/A' AS TEXT) as "reqCMPT",
    CAST(ss."name" AS TEXT) as "status",
    CAST('N/A' AS TEXT) as "gaugeNo",
    CAST('N/A' AS TEXT) as "gaugeId",
    CAST('N/A' AS TEXT) as "gStandardMoistureCount",
    CAST('N/A' AS TEXT) as "gStandardDensityCount",
    CAST(m."testedBy" AS TEXT) as "technician",
    CAST('moistureContent' AS TEXT) as "sourceEntity",
    CAST(m.id AS TEXT) as "sourceEntityId",
    CAST('N/A' AS TEXT) as "station",
    CAST('N/A' AS TEXT) as "offset",
    CAST('N/A' AS TEXT) as "alignment",
    m."dateTested"
FROM p_cs.st_soil_moisture_content m 
JOIN p_meta.purpose p ON m."purposeId" = p.id
JOIN p_gen.material m2 ON m."materialId" = m2.id
JOIN p_cs.sample s ON m."sampleId" = s.id
JOIN p_meta.test_result ss ON m."testResultId" = ss.id
JOIN p_gen.ct_log cl on m."testNoId" = cl.id
WHERE m."projectId" = {projectId} 
AND m."purposeId" = {purposeId} 
AND m."dateTested" BETWEEN {from} AND {to}
AND m."isDelete" = false

GROUP BY m."dateTested", "purpose", "densityTypeTestDepth", "generalLocation",
  "controlTest", "course", "moistureCount", "densityCount", "elev", 
  "sampleCT", "labMaxDryDensity", "optimumMoisture", "fieldWetDensity", 
  "fieldMoisture", "Northing", "Easting", "filedDryDensity", "CMPT", 
  "reqCMPT", "status", "gaugeNo", technician, m.id
ORDER BY "dateTested", "gaugeNo";
`;

const inPlaceDensityProctor = `
Select 
    pt."testNo" as controlTest, 
    pt.id as "proctorTestId",
    pt."correctedMaximumDryUnitWeight" as "maxDensity",
    pt."optimumMoistureContent" as "optimumMoisture",
    pt."correctedOptimumMoistureContent" as "correctedOptimumMoisture",
    m2."name" as "material",
    sc."soilDescription" as "description",
    s."sampleNo" as "sampleId",
    ss.name as "soilClassification"
from p_cs.st_soil_proctor_test pt
LEFT JOIN p_cs.st_soil_sample_classification sc on sc."sampleId" = pt."sampleId"
LEFT JOIN p_domain.soil_symbol ss on ss."id" = sc."soilSymbolId" 
JOIN p_cs.sample s on s."id" = pt."sampleId"
LEFT JOIN p_gen.material m2 ON pt."materialId" = m2.id
where pt."sampleId" in ({sampleIds})
AND pt."isDelete" = false`;

export { inPlaceDensityQuery, inPlaceDensityProctor };
