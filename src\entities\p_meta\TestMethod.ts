import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { StandardAgency } from './StandardAgency'; // Import the StandardAgency entity
import { SpecAgency } from './SpecAgency'; // Import the SpecAgency entity
import { Test } from './Test'; // Import the Tests entity

@Entity({ schema: 'p_meta' })
export class TestMethod {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, (test) => test.testMethod) 
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  standardCode?: string;

  @Column({ nullable: true })
  standardName?: string;

  @Column({ nullable: true })
  standardVersion?: string;

  @Column({ nullable: true })
  versionDate?: Date;

  @Column({ nullable: true })
  type?: string;

  @Column({ nullable: true })
  procedure?: string;

  @Column({ nullable: true })
  criteria?: string;

  @Column({ nullable: true })
  duration?: number;

  @Column({ nullable: true })
  step?: string;

  @Column({ nullable: true })
  standardAgencyId?: string;

  @ManyToOne(() => StandardAgency, { nullable: true }) 
  @JoinColumn({ name: 'standardAgencyId' })
  standardAgency?: StandardAgency;

  @Column({ nullable: true })
  specAgencyId?: string;

  @ManyToOne(() => SpecAgency, { nullable: true }) 
  @JoinColumn({ name: 'specAgencyId' })
  specAgency?: SpecAgency;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
