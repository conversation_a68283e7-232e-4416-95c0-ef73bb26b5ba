// import { DeepPartial, getManager } from 'typeorm';
// import { StSoilUCSTest } from '../../../../entities/p_cs/StSoilUCSTest';

// class UCSTestModel {
//   constructor() {}

//   async findById(SampleId: string) {
//     try {
//       return await getManager()
//         .getRepository(StSoilUCSTest)
//         .findOne({
//           where: { id: SampleId, isDelete: false },
//           relations: ['reading'],
//         });
//     } catch (error) {
//       throw error;
//     }
//   }

//   async add(newUCSTest: StSoilUCSTest) {
//     try {
//       const UCSTestRepository = getManager().getRepository(StSoilUCSTest);
//       const addedUCSest = UCSTestRepository.create(newUCSTest);
//       await UCSTestRepository.save(addedUCSest);
//       return addedUCSest;
//     } catch (error) {
//       throw error;
//     }
//   }

//   async updateFailLoad(id: string, data: DeepPartial<StSoilUCSTest>) {
//     try {
//       await getManager().getRepository(StSoilUCSTest).update(id, data);

//       // Try to find the updated entity
//       const updatedEntity = await this.findById(id);
//       return updatedEntity;
//     } catch (error) {
//       throw error;
//     }
//   }
// }

// const ucsTestModel = new UCSTestModel();
// export default ucsTestModel;
