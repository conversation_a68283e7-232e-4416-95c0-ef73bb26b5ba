import { getManager } from 'typeorm';
import { TestVariant } from '../../../../entities/p_meta/Testvariant';

class TestVariantModel {
  constructor() {}
  async getAll() {
    try {
      const testMethod = await getManager()
        .getRepository(TestVariant)
        .find({
          relations: ['standardAgency', 'specAgency'],
          where: { isDelete: false },
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByTestId(testId: string) {
    try {
      const testMethod = await getManager()
        .getRepository(TestVariant)
        .find({
          where: { testId: testId, isDelete: false },
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByTestMethodId(testMethodId: string) {
    try {
      const testMethod = await getManager()
        .getRepository(TestVariant)
        .find({
          where: { testMethodId: testMethodId, isDelete: false },
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }

  async getByVariantName(variantName: string) {
    try {
      const testMethod = await getManager()
        .getRepository(TestVariant)
        .findOne({
          where: { variantName: variantName, isDelete: false },
        });

      return testMethod;
    } catch (error) {
      throw error;
    }
  }
}

const testVariantModel = new TestVariantModel();
export default testVariantModel;
