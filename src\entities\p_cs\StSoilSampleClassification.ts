import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Sample } from './Sample'; // Import the Samples entity
import { TestMethod } from '../p_meta/TestMethod'; // Import the Tests entity
import { Project } from '../p_gen/Project';
import { SoilSymbol } from '../p_domain/SoilSymbol';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { Test } from '@entities/p_meta/Test';
import { EventLog } from '@entities/p_gen/EventLog';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilSampleClassification extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ type: 'varchar', nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  ProjectId?: Project;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  soilColor?: string;

  @Column({ nullable: true })
  soilSymbolId?: string;

  @ManyToOne(() => SoilSymbol, { nullable: true })
  @JoinColumn({ name: 'soilSymbolId' })
  soilSymbol?: SoilSymbol;

  @Column({ nullable: true })
  soilDescription?: string;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  passFail?: string;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
