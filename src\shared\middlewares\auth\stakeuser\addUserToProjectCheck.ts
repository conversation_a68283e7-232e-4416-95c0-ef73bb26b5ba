import { NextFunction, Request, Response } from 'express';
import errorMiddleware from '../../error/error.middleware';
import stakeholderModel from '@models/stakeholder.model';
import stakeUserModel from '@models/stakeUser.model';

export const checkUserAlreadyInProject = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const stakeholderDetails = await stakeholderModel.findById(req.body.stakeholderId);
    if (
      await stakeUserModel.checkUserInProject(req.body.userId, stakeholderDetails?.projectId || '')
    ) {
      throw new Error('User already added in project');
    }
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
