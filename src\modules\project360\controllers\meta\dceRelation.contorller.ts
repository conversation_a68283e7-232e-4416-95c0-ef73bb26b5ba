import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import DCERelationModel from '../../models/meta/dceRelation.model';

class DCERelationController {
  constructor() {}
  private model = new DCERelationModel();
  getDropdown = async (req: Request, res: Response) => {
    try {
      const result = await this.model.getDataByDceRelationId(
        req.params.dceId,
        req.params.columnName,
        req.query,
        (req as any).user.id,
        req.query?.view as string
      );
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };
  getDropdownByProject = async (req: Request, res: Response) => {
    try {
      const result = await this.model.getDropdownByProject(
        req.params.projectId,
        (req as any).user.id,
        req.query.updatedAfter as any
      );
      // getting the data from database with the given id

      // checking if data is found with the id
      if (result) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: result,
          msg: 'data found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No data found',
        });
      }
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default DCERelationController;
