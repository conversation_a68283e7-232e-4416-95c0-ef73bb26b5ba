import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import importController from '../../../controllers/import.controller';
import { BlastReport } from '../../../../../entities/p_cs/BlastReport';
// import { MapKeysToFieldName } from '../../../../../shared/middlewares/mapKeysToFieldName.middleware';

const router: Router = express.Router();

const GenricController = new CrudController<BlastReport>(BlastReport);
// const ImportDatMap = new MapKeysToFieldName<BlastReport>(BlastReport);

// Mount the userRouter for CRUD operations at /auth/user/crud
// router.post('/', ImportDatMap.mapFields, authenticateToken, GenricController.createWithSubmittal);
router.get('/:id', authenticateToken, GenricController.findById);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'blastReport')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'blastReport')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'blastReport')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'blastReport')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'blastReport'));
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'blastReport')
);
router.get('/get/by/project/:entity/:id', authenticateToken, importController.getData);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'blastReport')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'blastReport')
);

export default router;
