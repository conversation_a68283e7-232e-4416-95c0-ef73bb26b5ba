import express, { Router } from 'express';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import CrudController from '../../generic/crudDriver.controller';
import approvalSetupController from '../controllers/approvalSetup.controller';
import { ApprovalLevel } from '../../../entities/p_utils/ApprovalLevel';
import { ApprovalSetup } from '../../../entities/p_utils/ApprovalSetup';
import { ApprovalPurposeType } from '../../../entities/p_utils/ApprovalPurposeType';

const router: Router = express.Router();

// Create a generic router for the User entity
const ApprovalCRUD = new CrudController<ApprovalSetup>(ApprovalSetup);
const ApprovalPurposeTypeCRUD = new CrudController<ApprovalPurposeType>(ApprovalPurposeType);

const ApprovalLevelController = new CrudController<ApprovalLevel>(ApprovalLevel);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.use('/level', authenticateToken, ApprovalLevelController.getRouter());
router.get('/by/project/:id', authenticateToken, approvalSetupController.findByProjectId);
router.get(
  '/setup/:project/:id',
  authenticateToken,
  approvalSetupController.allApprovalSetupByPurpose
);
router.get('/purpose/type', authenticateToken, ApprovalPurposeTypeCRUD.findAll);
router.post('/', authenticateToken, approvalSetupController.addApproval);
router.put('/:id', authenticateToken, approvalSetupController.editApproval);
router.delete('/:id', authenticateToken, (req, res) => ApprovalCRUD.softDelete(req, res));

export default router;
