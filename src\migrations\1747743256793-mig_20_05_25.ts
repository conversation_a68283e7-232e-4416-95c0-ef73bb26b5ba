import { MigrationInterface, QueryRunner } from 'typeorm';

export class Mig2005251747743256793 implements MigrationInterface {
  name = 'Mig2005251747743256793';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "p_cs"."surveying" ("file" jsonb, "signature" character varying, "metadata" jsonb, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" uuid, "genralProjectAreaId" uuid, "featureId" uuid, "structureId" uuid, "surveyName" character varying(255), "surveyTypeId" uuid, "instrumentTypeId" uuid, "stationAlignmentId" uuid, "surveyDate" TIMESTAMP, "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying, "updatedBy" character varying, "isDelete" boolean NOT NULL DEFAULT false, "geom" geometry(Point,4326), "qcVerifier" character varying, "approvalStatusId" uuid, "workPackageActivityId" uuid, "eventLogId" uuid, "purposeId" uuid, "qcDate" TIMESTAMP, "qaVerifier" character varying, "qaDate" TIMESTAMP, CONSTRAINT "PK_91fb592420a50ac744c4376b431" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `CREATE TABLE "p_gen"."clearing_grubbing" ("file" jsonb, "signature" character varying, "metadata" jsonb, "id" uuid NOT NULL DEFAULT uuid_generate_v4(), "projectId" uuid, "generalProjectAreaId" uuid, "featureId" uuid, "typeOfWork" character varying, "area" numeric, "date" TIMESTAMP, "qcVerifier" character varying, "approvalStatusId" uuid, "workPackageActivityId" uuid, "eventLogId" uuid, "purposeId" uuid, "qcDate" TIMESTAMP, "qaVerifier" character varying, "qaDate" TIMESTAMP, CONSTRAINT "PK_f0fe38cf8f7c19a1376657ef2e0" PRIMARY KEY ("id"))`
    );
    await queryRunner.query(
      `ALTER TABLE "p_map"."layer" ALTER COLUMN "applyDateFilter" DROP NOT NULL`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_b0d67106cb624417be8ca4a8529" FOREIGN KEY ("projectId") REFERENCES "p_gen"."project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_a6532a439fea4af3fca3f3547ad" FOREIGN KEY ("genralProjectAreaId") REFERENCES "p_gen"."area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_12f41bc5c5664f71c1644a26246" FOREIGN KEY ("featureId") REFERENCES "p_gen"."feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_a0ee9c40f6b28e25b30de5bb52d" FOREIGN KEY ("structureId") REFERENCES "p_gen"."structure"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_d3274fe6148adbe9cc03a877ee3" FOREIGN KEY ("surveyTypeId") REFERENCES "p_domain"."survey_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_2a5aa78476eb2ad4e8781043285" FOREIGN KEY ("instrumentTypeId") REFERENCES "p_domain"."instrument_type"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_cf20abe8ce0e0feaf03159fbca5" FOREIGN KEY ("stationAlignmentId") REFERENCES "p_map"."station_alignment"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_2da748938f356d75e89f47f8afd" FOREIGN KEY ("approvalStatusId") REFERENCES "p_meta"."approval_status"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_57ade99286308ed138ee21ad6c9" FOREIGN KEY ("workPackageActivityId") REFERENCES "p_gen"."work_package_activity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_3f1b5825fa060d97102450b3a76" FOREIGN KEY ("eventLogId") REFERENCES "p_gen"."event_log"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_d9c73ebf67b947f846afc0e1382" FOREIGN KEY ("purposeId") REFERENCES "p_meta"."purpose"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_184e0f78c0edf740740484cc8f6" FOREIGN KEY ("projectId") REFERENCES "p_gen"."project"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_1fa93846f5ac91aeeaf3f777833" FOREIGN KEY ("generalProjectAreaId") REFERENCES "p_gen"."area"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_9caf0c6656b812b9304c5ee8a61" FOREIGN KEY ("featureId") REFERENCES "p_gen"."feature"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_8b57435785a3a4dcbb5aa06d5f6" FOREIGN KEY ("approvalStatusId") REFERENCES "p_meta"."approval_status"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_dbf7f1ff43b2b84c96b4eb84e7a" FOREIGN KEY ("workPackageActivityId") REFERENCES "p_gen"."work_package_activity"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_5d0fa05c0658896596bd5680782" FOREIGN KEY ("eventLogId") REFERENCES "p_gen"."event_log"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" ADD CONSTRAINT "FK_717d305bb6f5c716b41a665e14e" FOREIGN KEY ("purposeId") REFERENCES "p_meta"."purpose"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_717d305bb6f5c716b41a665e14e"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_5d0fa05c0658896596bd5680782"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_dbf7f1ff43b2b84c96b4eb84e7a"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_8b57435785a3a4dcbb5aa06d5f6"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_9caf0c6656b812b9304c5ee8a61"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_1fa93846f5ac91aeeaf3f777833"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_gen"."clearing_grubbing" DROP CONSTRAINT "FK_184e0f78c0edf740740484cc8f6"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_d9c73ebf67b947f846afc0e1382"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_3f1b5825fa060d97102450b3a76"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_57ade99286308ed138ee21ad6c9"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_2da748938f356d75e89f47f8afd"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_cf20abe8ce0e0feaf03159fbca5"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_2a5aa78476eb2ad4e8781043285"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_d3274fe6148adbe9cc03a877ee3"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_a0ee9c40f6b28e25b30de5bb52d"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_12f41bc5c5664f71c1644a26246"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_a6532a439fea4af3fca3f3547ad"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_b0d67106cb624417be8ca4a8529"`
    );
    await queryRunner.query(
      `ALTER TABLE "p_map"."layer" ALTER COLUMN "applyDateFilter" SET NOT NULL`
    );
    await queryRunner.query(`DROP TABLE "p_gen"."clearing_grubbing"`);
    await queryRunner.query(`DROP TABLE "p_cs"."surveying"`);
  }
}
