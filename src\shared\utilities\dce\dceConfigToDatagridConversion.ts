import { dceConfiguration } from '../../../entities/p_gen/dceConfiguration';
import { DataCaptureElemetRelation } from '../../../entities/p_meta/DataCaptureElemetRelation';
import DCERelationModel from '../../../modules/project360/models/meta/dceRelation.model';
import projectModel from '../../../modules/project360/models/project.model';
import { normalizeAlias } from '@utils/custom/aliasFieldNameFallBack';
import moment from 'moment-timezone';
import getCustomDropdownValues from '../dropdown/getCustomDropdownValues';
import convertObjectStringsToNumbers from '@utils/excel/convertObjectStringsToNumbers';
import keyValueStoreModel from '@models/meta/keyValueStore.model';

const dceConfigToExcelConversion = async (
  dceConfig: dceConfiguration[],
  data: any[],
  projectId: string,
  userId: string,
  isImport: boolean = false
) => {
  try {
    const dropdownValueList: {
      dceId: string;
      columnName: string;
      value: string;
      id: string | string[];
    }[] = [];

    // Get exclusion list directly from database
    const exclusionListData = await keyValueStoreModel.getByKey('excel_exclusion_list');

    const exclusionList = exclusionListData?.value || [];

    const result = [];

    for (const originalValue of data) {
      const value = JSON.parse(JSON.stringify(originalValue));
      const orderedValue: Record<string, any> = {};

      for (const dce of dceConfig) {
        const key = dce.columnName ?? '';
        if (!key) continue;

        const dceConfigCheck = dceConfig.find((item) => item.columnName === key);
        if (!dceConfigCheck || dceConfigCheck.hide) continue;

        const alias = normalizeAlias(dceConfigCheck.alias, key);
        const rawValue = key in value ? value[key] : undefined;

        let transformedValue = rawValue;

        if (dceConfigCheck.type === 'singleSelect' && rawValue) {
          if (
            dceConfigCheck.columnName === 'testedBy' ||
            dceConfigCheck.columnName === 'sampledBy'
          ) {
            const userName = await getCustomDropdownValues(
              'userByProject',
              { projectId },
              rawValue
            );
            if (userName) {
              transformedValue = userName;
            }
          } else {
            const dceRelationModel = new DCERelationModel();
            const checkDropDownValue = dropdownValueList.find(
              (dropdown) =>
                dropdown.dceId === dceConfigCheck.dceId &&
                dropdown.columnName === key &&
                dropdown.id === rawValue
            );
            if (checkDropDownValue) {
              transformedValue = checkDropDownValue.value;
            } else {
              if (dceConfigCheck.isMultiSelect && Array.isArray(rawValue) && rawValue.length > 0) {
                const uuidValue = await dceRelationModel.getDataByDceRelationIdMulit(
                  dceConfigCheck.dceId || '',
                  key,
                  rawValue
                );
                if (uuidValue && uuidValue.length > 0) {
                  const combined = uuidValue.map((item: any) => item.name).join(', ');
                  dropdownValueList.push({
                    dceId: dceConfigCheck.dceId || '',
                    columnName: key,
                    value: combined,
                    id: rawValue,
                  });
                  transformedValue = combined;
                }
              } else {
                const uuidValue = await dceRelationModel.getDataByDceRelationIdSingleData(
                  dceConfigCheck.dceId || '',
                  key,
                  rawValue,
                  userId
                );
                if (uuidValue && uuidValue.length > 0) {
                  dropdownValueList.push({
                    dceId: dceConfigCheck.dceId || '',
                    columnName: key,
                    value: uuidValue[0].name,
                    id: rawValue,
                  });
                  transformedValue = uuidValue[0].name;
                }
              }
            }
          }
        } else if (dceConfigCheck.type === 'number') {
          transformedValue = Number(rawValue).toFixed(dceConfigCheck.decimalPointsNeeded ?? 2);
        } else if (dceConfigCheck.type === 'date') {
          const timezone = await projectModel.getTimezoneByProjectId(projectId);
          transformedValue = new Date(moment.tz(new Date(rawValue), timezone).format('MM-DD-YYYY'));
        } else if (dceConfigCheck.type === 'dateTime') {
          const timezone = await projectModel.getTimezoneByProjectId(projectId);
          transformedValue = new Date(
            moment.tz(new Date(rawValue), timezone).format('MM-DD-YYYY hh:mm A')
          );
        }

        orderedValue[alias] = transformedValue;
      }

      if (isImport) {
        if (value.importId) orderedValue['Import Id'] = value.importId;
        if (value.relatedId) orderedValue['Related Id'] = value.relatedId;
      }

      result.push(convertObjectStringsToNumbers(orderedValue, exclusionList));
    }

    return result;
  } catch (error) {
    throw error;
  }
};

export const convertSubdceToExcel = async (
  dceConfig: {
    name: string;
    alias: string;
    type: string;
    dceRelation?: DataCaptureElemetRelation;
  }[],
  data: any[],
  projectId: string,
  userId: string
) => {
  try {
    const dropdownValueList: {
      dceId: string;
      columnName: string;
      value: string;
      id: string | string[];
    }[] = [];

    // Get exclusion list directly from database
    const exclusionListData = await keyValueStoreModel.getByKey('excel_exclusion_list');

    const exclusionList = exclusionListData?.value || [];

    const result = [];

    for (const originalValue of data) {
      const value = JSON.parse(JSON.stringify(originalValue));
      const orderedValue: Record<string, any> = {};

      for (const dce of dceConfig) {
        const key = dce.name ?? '';
        if (!key) continue;

        const dceConfigCheck = dceConfig.find((item) => item.name === key);
        if (!dceConfigCheck) continue;

        const alias = normalizeAlias(dceConfigCheck.alias, key);
        const rawValue = key in value ? value[key] : undefined;

        let transformedValue = rawValue;

        if (dceConfigCheck.type === 'singleSelect' && rawValue) {
          const dceRelationModel = new DCERelationModel();
          const checkDropDownValue = dropdownValueList.find(
            (dropdown) =>
              dropdown.dceId === dceConfigCheck?.dceRelation?.dceId &&
              dropdown.columnName === key &&
              dropdown.id === rawValue
          );
          if (checkDropDownValue) {
            transformedValue = checkDropDownValue.value;
          } else {
            const uuidValue = await dceRelationModel.getDataByDceRelationIdSingleDataForSub(
              dceConfigCheck.dceRelation?.subDceId || '',
              key,
              rawValue,
              userId
            );
            if (uuidValue && uuidValue.length > 0) {
              dropdownValueList.push({
                dceId: dceConfigCheck.dceRelation?.dceId || '',
                columnName: key,
                value: uuidValue[0].name,
                id: rawValue,
              });
              transformedValue = uuidValue[0].name;
            }
          }
        } else if (dceConfigCheck.type === 'number') {
          transformedValue = Number(Number(rawValue).toFixed(2));
        } else if (dceConfigCheck.type === 'date') {
          const timezone = await projectModel.getTimezoneByProjectId(projectId);
          transformedValue = new Date(moment.tz(new Date(rawValue), timezone).format('MM-DD-YYYY'));
        } else if (dceConfigCheck.type === 'dateTime') {
          const timezone = await projectModel.getTimezoneByProjectId(projectId);
          transformedValue = new Date(
            moment.tz(new Date(rawValue), timezone).format('MM-DD-YYYY hh:mm A')
          );
        }

        orderedValue[alias] = transformedValue;
      }

      if (value.id) orderedValue['id'] = value.id;
      if (value.importId) orderedValue['Import Id'] = value.importId;
      if (value.relatedId) orderedValue['Related Id'] = value.relatedId;

      result.push(convertObjectStringsToNumbers(orderedValue, exclusionList));
    }

    return result;
  } catch (error) {
    throw error;
  }
};

export default dceConfigToExcelConversion;
