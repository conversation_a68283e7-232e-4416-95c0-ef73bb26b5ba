import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import { Sample } from '../../../../entities/p_cs/Sample';
import SampleController from '../../controllers/sample.controller';
import { SampleFileUploadMiddleware } from '../../../../shared/middlewares/uploadFileSftp.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUDController = new CrudController<Sample>(Sample);
const controller = new SampleController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  CRUDController.findByProjectId(req, res, 'sampleManagement')
);
router.get('/by/date/:projectId', authenticateToken, (req, res) => controller.findByDate(req, res));
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  CRUDController.sendForApproval(req, res, 'sampleManagement')
);
router.get('/by/station/:stationStart/:stationEnd/:projectId', authenticateToken, (req, res) =>
  controller.getDataByStationRange(req, res)
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  CRUDController.getDataCountByProjectId(req, res, 'sampleManagement')
);
router.post('/', authenticateToken, (req, res) =>
  CRUDController.create(req, res, 'sampleManagement')
);
router.put('/:id', authenticateToken, (req, res) =>
  CRUDController.update(req, res, 'sampleManagement')
);
router.get('/:id', authenticateToken, (req, res) => controller.findById(req, res));

router.patch('/multiple', authenticateToken, (req, res) =>
  CRUDController.multiSoftDelete(req, res, 'sampleManagement')
);
router.delete('/:id', authenticateToken, (req, res) =>
  CRUDController.softDelete(req, res, 'sampleManagement')
);

router.post('/stepper/add', authenticateToken, SampleFileUploadMiddleware, controller.addSample);
router.post(
  '/multiple/add',
  authenticateToken,
  SampleFileUploadMiddleware,
  controller.addMultipleSample
);
router.put(
  '/stepper/update/:id',
  authenticateToken,
  SampleFileUploadMiddleware,
  controller.addSampleStep
);
router.post('/upload/file', authenticateToken, controller.uploadAFile);
router.get('/by/user/:id', authenticateToken, controller.findByUserId);
// TODO: Commented because of column changes. Need to update the code
// router.post('/by/borenumbers', authenticateToken, controller.getSamplesandUCSByBoreNumbers);
router.get('/checkin/by/project/:id', authenticateToken, controller.findSampleForCheckIn);

export default router;
