import { EntityTarget, ObjectLiteral } from 'typeorm';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import { entityRelationList } from '../entity/entityRelation';
import { getUserRoleDetail } from '../role/getUserRoleDetails';
import { convertLatAndLongToEastingAndNorthingOnGetDataByProjectId } from '../spatialCoordinates/convertLatAndLongOnGetData';
import { GenericModel } from '../../../modules/generic/crudDriver.model';
import SortAndFilter from '../sortAndFilter/sortFilterForGetByProjectId';

class FindData<T extends ObjectLiteral> {
  private service: GenericModel<T>;
  constructor(entity: EntityTarget<T>) {
    this.service = new GenericModel<T>(entity);
  }
  findByProjectIdWithDataAccess = async (entity: any, user: any, projectId: string, query: any) => {
    {
      try {
        let relation: string[] = [];

        if (entity in entityRelationList) {
          relation = entityRelationList[entity].relation;
        }
        let result = [];
        let selectedRows = [];
        if (query?.selectedRows) {
          selectedRows = JSON.parse(query?.selectedRows);
        }
        const dceData = await dceModel.findByEntity(entity);

        const dataControl = await getUserRoleDetail(user, projectId);
        result =
          (await this.service.findByProjectId(projectId, relation, false, dataControl)) || [];

        if (dceData && result) {
          const latAndLongConversion =
            await convertLatAndLongToEastingAndNorthingOnGetDataByProjectId(
              result,
              dceData.id,
              projectId
            );
          result = latAndLongConversion;
        }
        const sortAndFilter = new SortAndFilter();
        let finalResult = result as any;
        if (dceData && selectedRows && selectedRows.length > 0) {
          finalResult = sortAndFilter.getOnlySelectRows(selectedRows, result);
        } else if (dceData) {
          finalResult = await sortAndFilter.sortFilterForGetByProjectId(query, result, dceData);
        }

        return {
          data: finalResult || [],
        };
      } catch (error) {
        throw error;
      }
    }
  };

  private paginate = (data: any[], page: number, pageSize: number) => {
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return data.slice(startIndex, endIndex);
  };
}

export default FindData;
