import moment from 'moment-timezone';

export const getCurrentDateInDDMMYYYYFormat = (date: Date): string => {
  const currentDate: Date = date || new Date();

  // Get the day, month, and year components
  const day: string = String(currentDate.getDate()).padStart(2, '0');
  const month: string = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const year: string = String(currentDate.getFullYear());

  // Concatenate the components in the desired format
  const formattedDate: string = `${month}/${day}/${year}`;

  return formattedDate;
};
export const getCurrentDateInDDMMYYYYFormatForPhotoVideo = (date: Date): string => {
  const currentDate: Date = date || new Date();

  // Get the day, month, and year components
  const day: string = String(currentDate.getDate()).padStart(2, '0');
  const month: string = String(currentDate.getMonth() + 1).padStart(2, '0'); // Months are zero-based
  const year: string = String(currentDate.getFullYear());

  // Concatenate the components in the desired format
  const formattedDate: string = `${month}${day}${year}`;

  return formattedDate;
};

export const getDateInyyyymmFormat = (date: Date): string => {
  const now = new Date(date);
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // Ensure two-digit month
  return `${year}${month}`;
};

export const dateZoneConversion = (parsedDate: Date | string, timezone: string) => {
  try {
    const originalDate = moment.utc(parsedDate);

    // Extract the date and time components in the original time zone
    const originalDateTime = originalDate.format('YYYY-MM-DDTHH:mm:ss.SSS');

    // Create a new date string with the desired time zone offset
    const newDate = moment
      .tz(originalDateTime, timezone || 'America/New_York')
      .format('YYYY-MM-DDTHH:mm:ss.SSSZZ');
    const updatedDate = new Date(newDate);
    return updatedDate;
  } catch (error) {
    throw error;
  }
};

export const convertToTimeZone = (date: Date | string, timeZone: string): Date => {
  return moment.utc(date).tz(timeZone).toDate(); // Customize format if needed
};
