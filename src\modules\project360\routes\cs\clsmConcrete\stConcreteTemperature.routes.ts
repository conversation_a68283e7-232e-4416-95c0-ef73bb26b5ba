import express, { Router } from 'express';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import { StConcreteTemperature } from '../../../../../entities/p_cs/StConcreteGroutTemperature';

const router: Router = express.Router();

const GenricController = new CrudController<StConcreteTemperature>(StConcreteTemperature);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'concreteTemperature')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'concreteTemperature')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'concreteTemperature')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'concreteTemperature')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'concreteTemperature')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'concreteTemperature')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'concreteTemperature')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'concreteTemperature')
);

export default router;
