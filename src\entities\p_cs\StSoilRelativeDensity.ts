import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';

import { Sample } from './Sample';
import { TestVariant } from '../p_meta/Testvariant';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilRelativeDensity extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column()
  testNo?: string;

  @Column()
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column()
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column()
  passFail?: string;

  @Column()
  preparationMethod?: string;

  @Column({ type: 'decimal', nullable: true })
  moldMass?: number;

  @Column({ type: 'decimal', nullable: true })
  moldHeight?: number;

  @Column({ type: 'decimal', nullable: true })
  moldDiameter?: number;

  @Column({ type: 'decimal', nullable: true })
  doubleAmplitude?: number;

  @Column({ type: 'decimal', nullable: true })
  specimenMass?: number;

  @Column({ type: 'decimal', nullable: true })
  specimenHeight?: number;

  @Column({ type: 'decimal', nullable: true })
  initialPlateThickness?: number;

  @Column({ type: 'decimal', nullable: true })
  finalPlateThickness?: number;

  @Column({ type: 'decimal', nullable: true })
  maximumIndexDensity?: number;

  @Column({ type: 'decimal', nullable: true })
  moldMassWithSoilBeforeVibration?: number;

  @Column({ type: 'decimal', nullable: true })
  moldMassWithSoilAfterVibration?: number;

  @Column({ type: 'decimal', nullable: true })
  initialGuageReading?: number;

  @Column({ type: 'decimal', nullable: true })
  finalGuageReading?: number;

  @Column({ type: 'decimal', nullable: true })
  specificGravity?: number;

  @Column({ type: 'decimal', nullable: true })
  unitWeight?: number;

  @Column()
  testedBy?: string;

  @Column({ type: 'timestamp' })
  dateTested?: Date;

  @Column({ type: 'varchar' })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
