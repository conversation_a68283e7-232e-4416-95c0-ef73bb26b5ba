import express, { Router } from 'express';
// import { authenticateToken } from "../../../shared/middlewares/auth.middleware";
// import crudDriver from "../../generic/crudDriver.controller";
import CrudController from '../../generic/crudDriver.controller';
import { StakeUser } from '../../../entities/p_auth/StakeUser';
import stakeUserController from '../controllers/stakeUser.controller';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import { checkUserAlreadyInProject } from 'src/shared/middlewares/auth/stakeuser/addUserToProjectCheck';

const router: Router = express.Router();

// Create a generic router for the User entity
const StakeUserDetailController = new CrudController<StakeUser>(StakeUser);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/', StakeUserDetailController.findAll);
router.put('/:id', authenticateToken, (req, res) => StakeUserDetailController.update(req, res));
router.post('/', authenticateToken, checkUserAlreadyInProject, (req, res) =>
  StakeUserDetailController.create(req, res)
);
router.get('/user/list/by/project/:id', stakeUserController.findByUserListProjectId);
router.post('/refresh/user/by/:id', stakeUserController.refreshUserData);
router.get('/by/stakeholder/:id', (req, res) => StakeUserDetailController.update(req, res));
router.get('/all/user/project/list', stakeUserController.findAllUserListProject);
router.post('/invite/user', authenticateToken, stakeUserController.inviteUser);
router.delete('/:id', authenticateToken, stakeUserController.softDelete);
router.get('/get/user/roles/by/project/:id', authenticateToken,  stakeUserController.getUserRolesAndStakeHoldersByProjectId);
router.get('/get/pageroutes/by/user/project/:userId/:projectId', authenticateToken, stakeUserController.getPageRoutesByUserIdAndProjectId);
export default router;
