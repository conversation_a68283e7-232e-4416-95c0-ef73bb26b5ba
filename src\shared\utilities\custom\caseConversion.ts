export const camelCaseToNormalCase = (camelCaseStr: string) => {
  // Insert space before each uppercase letter, except for the first letter
  const normalCaseStr = camelCaseStr.replace(/([A-Z])/g, ' $1');

  // Capitalize the first letter and convert the rest to lowercase
  return normalCaseStr.charAt(0).toUpperCase() + normalCaseStr.slice(1);
};

export const camelCaseToTitleCase = (input: string): string => {
  if (input.length === 0) {
    return input;
  }

  const spacedInput = input.replace(/([a-z])([A-Z])/g, '$1 $2');

  return spacedInput.charAt(0).toUpperCase() + spacedInput.slice(1);
};
