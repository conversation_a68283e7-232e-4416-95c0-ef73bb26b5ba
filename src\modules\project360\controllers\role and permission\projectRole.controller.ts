import { Request, Response } from 'express';
import projectRoleModel from '../../models/role and permission/projectRole.model';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';

class ProjectRolesController {
  constructor() {}

  // find by id
  async addPermissionToRole(req: Request, res: Response) {
    try {
      const data = await projectRoleModel.addPermissionToRole(
        req.body.roleId,
        req.body.permissions
      );
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async findById(req: Request, res: Response) {
    try {
      const { roleId } = req.params;
      const data = await projectRoleModel.getById(roleId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getPermissionByRole(req: Request, res: Response) {
    try {
      const permissions = await projectRoleModel.getPermissionByRole(req.params.roleId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: permissions,
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getRoleIdByUser(req: Request, res: Response) {
    try {
      const data = await projectRoleModel.getPermissionByUser(req.params.userId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data || [],
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getRoutesByUser(req: Request, res: Response) {
    try {
      const data = await projectRoleModel.findByRotuesUserId(req.params.userId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data || [],
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getCompleteRoutesByUser(req: Request, res: Response) {
    try {
      const { userId, projectId } = req.params;
      const data = await projectRoleModel.findByOnlyRotuesUserId(userId, projectId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data || [],
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getRoutesByUserByProject(req: Request, res: Response) {
    try {
      const { userId, projectId } = req.params;
      const data = await projectRoleModel.findByRotuesUserIdWithProjectId(userId, projectId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: data || [],
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async getByProjectId(req: Request, res: Response) {
    try {
      const permissions = await projectRoleModel.getByProjectId(req.params.id);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: permissions,
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
  async getByProjectIdAndUser(req: Request, res: Response) {
    try {
      const projectId = req.params.id;
      const userId = req.params.userId;
      const permissions = await projectRoleModel.getByProjectIdAndUser(projectId, userId);
      const message = req.__('DataFoundMessage');
      // if false send error as response
      return res.status(200).json({
        isSucceed: true,
        data: permissions,
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

const projectRolesController = new ProjectRolesController();
export default projectRolesController;
