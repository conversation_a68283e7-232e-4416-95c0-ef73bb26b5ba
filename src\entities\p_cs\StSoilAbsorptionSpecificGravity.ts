import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { Sample } from './Sample';
import { Project } from '../p_gen/Project';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { TestMethod } from '@entities/p_meta/TestMethod';
import { Test } from '@entities/p_meta/Test';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilAbsorptionSpecificGravity extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'SSD Sample Weight',
      fieldName: 'sampleWeightSSD',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sampleWeightSSD?: number;

  @ColumnInfo({
    customData: {
      name: 'Submerged Sample Weight',
      fieldName: 'sampleWeightSubmerged',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sampleWeightSubmerged?: number;

  @ColumnInfo({
    customData: {
      name: 'Dry Sample Weight',
      fieldName: 'drySampleWeight',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  drySampleWeight?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Flask Weight',
  //     fieldName: 'flaskWeight',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // flaskWeight?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Flask Weight with Water',
  //     fieldName: 'flaskWeightWater',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // flaskWeightWater?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Flask Weight with Water and Sample',
  //     fieldName: 'flaskWeightWaterSample',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'string',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // flaskWeightWaterSample?: number;

  @ColumnInfo({
    customData: {
      name: 'Test Temperature',
      fieldName: 'testTemperature',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  testTemperature?: number;

  @Column({ type: 'decimal', nullable: true })
  pycnometerSpecimenWaterMass?: number;

  @Column({ type: 'decimal', nullable: true })
  pycnometerWaterMass?: number;

  @ColumnInfo({
    customData: {
      name: 'Bulk Specific Gravity',
      fieldName: 'bulkSpecificGravity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bulkSpecificGravity?: number;

  @ColumnInfo({
    customData: {
      name: 'Bulk Specific Gravity SSD',
      fieldName: 'bulkSpecificGravitySSD',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bulkSpecificGravitySSD?: number;

  @ColumnInfo({
    customData: {
      name: 'Apparent Specific Gravity',
      fieldName: 'apparentSpecificGravity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  apparentSpecificGravity?: number;

  @ColumnInfo({
    customData: {
      name: 'Absorption',
      fieldName: 'absorption',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  absorption?: string;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
