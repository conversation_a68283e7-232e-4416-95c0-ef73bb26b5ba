import {
  <PERSON><PERSON>ty,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
  AfterLoad,
  OneToMany,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { convertEastingNorthingToLatLongOnGetData } from '../../shared/utilities/spatialCoordinates/convertEastingOnGetData';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { PvCutInformation } from './PvCutInformation';
import { Purpose } from '../p_meta/Purpose';
import { DCEColumns } from '@entities/common/DCEColumns';
import { Area } from '@entities/p_gen/Area';
import { ObjectType, Field, ID } from 'type-graphql';

@ObjectType()
@Entity('pv_cut_master', { schema: 'p_cs' })
export class PvCutMaster extends DCEColumns {
  @Field(() => ID)
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Field(() => String, { nullable: true })
  @Column({ type: 'uuid', nullable: true })
  projectId?: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Unique Label',
      fieldName: 'uniqueLabel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  uniqueLabel?: string;

  @Column({ nullable: true })
  areaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'areaId' })
  area?: Area;

  @ColumnInfo({
    customData: {
      name: 'ExcavatorFacing',
      fieldName: 'excavatorFacing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  excavatorFacing?: string;

  @ColumnInfo({
    customData: {
      name: 'Technology',
      fieldName: 'technology',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  technology?: string;

  @ColumnInfo({
    customData: {
      name: 'Cut_#',
      fieldName: 'cut',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('int8', { nullable: true })
  cut?: number;

  @ColumnInfo({
    customData: {
      name: 'Mix Design',
      fieldName: 'mixDesign',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  mixDesign?: string;

  @ColumnInfo({
    customData: {
      name: 'Northing_Start',
      fieldName: 'northingStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  northingStart?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting_Start',
      fieldName: 'eastingStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  eastingStart?: number;

  @ColumnInfo({
    customData: {
      name: 'Stn_Start',
      fieldName: 'stationStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  stationStart?: string;

  @ColumnInfo({
    customData: {
      name: 'Northing_End',
      fieldName: 'northingEnd',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  northingEnd?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting_End',
      fieldName: 'eastingEnd',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  eastingEnd?: number;

  @ColumnInfo({
    customData: {
      name: 'Stn_End',
      fieldName: 'stationEnd',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  stationEnd?: string;

  @ColumnInfo({
    customData: {
      name: 'PanelLength',
      fieldName: 'panelLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  panelLength?: number;

  @ColumnInfo({
    customData: {
      name: 'PanelWidth',
      fieldName: 'panelWidth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  panelWidth?: number;

  @ColumnInfo({
    customData: {
      name: 'NumBites',
      fieldName: 'numBites',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  numBites?: string;

  @ColumnInfo({
    customData: {
      name: 'Platform Level',
      fieldName: 'platformLevel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  platformLevel?: number;

  @ColumnInfo({
    customData: {
      name: 'ContractTopWallElev',
      fieldName: 'contractTopWallElev',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  contractTopWallElev?: number;

  @ColumnInfo({
    customData: {
      name: 'ContractBottomWallElev',
      fieldName: 'contractBottomWallElev',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  contractBottomWallElev?: number;

  @ColumnInfo({
    customData: {
      name: 'Avg Bottom Wall Elev',
      fieldName: 'avgBottomWallElev',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  avgBottomWallElev?: number;

  @ColumnInfo({
    customData: {
      name: 'Panel Start Date',
      fieldName: 'panelStartDate',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  panelStartDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Date Excavated',
      fieldName: 'dateExcavated',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  dateExcavated?: Date;

  @ColumnInfo({
    customData: {
      name: 'Excavator ID',
      fieldName: 'excavatorId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  excavatorId?: string;

  @ColumnInfo({
    customData: {
      name: 'Excavation Start Date',
      fieldName: 'excavationStartDate',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  excavationStartDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Excavation End Date',
      fieldName: 'excavationEndDate',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  excavationEndDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'Excavator Duration',
      fieldName: 'excavatorDuration',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  excavatorDuration?: number;

  @ColumnInfo({
    customData: {
      name: 'Excavator Depth',
      fieldName: 'excavatorDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('numeric', { nullable: true })
  excavatorDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Depth LC',
      fieldName: 'hydromillDepthLC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillDepthLC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Depth C',
      fieldName: 'hydromillDepthC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillDepthC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Depth RC',
      fieldName: 'hydromillDepthRC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillDepthRC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Depth R',
      fieldName: 'hydromillDepthR',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillDepthR?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Theor Vol L',
      fieldName: 'hydromillTheorVolL',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillTheorVolL?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Theor Vol LC',
      fieldName: 'hydromillTheorVolLC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillTheorVolLC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Theor Vol C',
      fieldName: 'hydromillTheorVolC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillTheorVolC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Theor Vol RC',
      fieldName: 'hydromillTheorVolRC',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillTheorVolRC?: string;

  @ColumnInfo({
    customData: {
      name: 'Hydromill Theor Vol R2',
      fieldName: 'hydromillTheorVolR2',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  hydromillTheorVolR2?: string;

  @ColumnInfo({
    customData: {
      name: 'Planned Post Placement',
      fieldName: 'plannedPostPlacement',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  plannedPostPlacement?: string;

  @ColumnInfo({
    customData: {
      name: 'Approx Stn ID',
      fieldName: 'approxStationId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  approxStationId?: string;

  @ColumnInfo({
    customData: {
      name: 'Backfill Mix Design',
      fieldName: 'backfillMixDesign',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  backfillMixDesign?: string;

  @ColumnInfo({
    customData: {
      name: 'Excavation Width Checked',
      fieldName: 'excavationWidthChecked',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  excavationWidthChecked?: string;

  @ColumnInfo({
    customData: {
      name: 'Stn Excavaton Width Checked',
      fieldName: 'stationExcavationWidthChecked',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  stationExcavationWidthChecked?: string;

  @ColumnInfo({
    customData: {
      name: 'Excavation Width',
      fieldName: 'excavationWidth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  excavationWidth?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation L pct',
      fieldName: 'deviationXLpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXLpct?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation L pct',
      fieldName: 'deviationYLpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYLpct?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation C pct',
      fieldName: 'deviationXCpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXCpct?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation C pct',
      fieldName: 'deviationYCpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYCpct?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation R pct',
      fieldName: 'deviationXRpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXRpct?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation R pct',
      fieldName: 'deviationYRpct',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYRpct?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation L in',
      fieldName: 'deviationXLin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXLin?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation L in',
      fieldName: 'deviationYLin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYLin?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation C in',
      fieldName: 'deviationXCin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXCin?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation C in',
      fieldName: 'deviationYCin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYCin?: string;

  @ColumnInfo({
    customData: {
      name: 'X-Deviation R in',
      fieldName: 'deviationXRin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationXRin?: string;

  @ColumnInfo({
    customData: {
      name: 'Y-Deviation R in',
      fieldName: 'deviationYRin',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deviationYRin?: string;

  @ColumnInfo({
    customData: {
      name: '28-D Curing Date',
      fieldName: 'curingD28Date',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  curingD28Date?: Date;

  @ColumnInfo({
    customData: {
      name: 'Check Length W Overlap',
      fieldName: 'checkLengthWOverlap',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkLengthWOverlap?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Start',
      fieldName: 'checkStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkStart?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Overlap',
      fieldName: 'checkOverlap',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkOverlap?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Overlap On Stn ID',
      fieldName: 'checkOverlapOnStnId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkOverlapOnStnId?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Delta Overlap',
      fieldName: 'checkDeltaOverlap',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkDeltaOverlap?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Net Length',
      fieldName: 'checkNetLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkNetLength?: string;

  @ColumnInfo({
    customData: {
      name: 'Check Delta Length',
      fieldName: 'checkDeltaLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  checkDeltaLength?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch ID',
      fieldName: 'batchId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  batchId?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Volume',
      fieldName: 'batchVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  batchVolume?: string;

  @ColumnInfo({
    customData: {
      name: 'Pumped Fresh SHS',
      fieldName: 'pumpedFreshShs',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  pumpedFreshShs?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Cement',
      fieldName: 'totalCement',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  totalCement?: string;

  @ColumnInfo({
    customData: {
      name: 'Delta Theor Vol',
      fieldName: 'deltaTheorVol',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deltaTheorVol?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Slag',
      fieldName: 'totalSlag',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  totalSlag?: string;

  @ColumnInfo({
    customData: {
      name: 'Delta Theor Slag',
      fieldName: 'deltaTheorSlag',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deltaTheorSlag?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Admix',
      fieldName: 'totalAdmix',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  totalAdmix?: string;

  @ColumnInfo({
    customData: {
      name: 'Delta Theor Admix',
      fieldName: 'deltaTheorAdmix',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deltaTheorAdmix?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Slurry',
      fieldName: 'totalSlurry',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  totalSlurry?: string;

  @ColumnInfo({
    customData: {
      name: 'Delta Theor Slurry',
      fieldName: 'deltaTheorSlurry',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  deltaTheorSlurry?: string;

  @ColumnInfo({
    customData: {
      name: 'Total Water',
      fieldName: 'totalWater',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  totalWater?: string;

  @ColumnInfo({
    customData: {
      name: 'Date Poured',
      fieldName: 'datePoured',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column('timestamp', { nullable: true })
  datePoured?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comment',
      fieldName: 'comment',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  comment?: string;

  @ColumnInfo({
    customData: {
      name: 'PP Depth',
      fieldName: 'ppDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  ppDepth?: string;

  @ColumnInfo({
    customData: {
      name: 'PP Type',
      fieldName: 'ppType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column('varchar', { nullable: true })
  ppType?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @Column('geometry', { nullable: true })
  geom?: any;

  longitudeStart?: string | null;

  latitudeStart?: string | null;

  longitudeEnd?: string | null;

  latitudeEnd?: string | null;

  editable?: boolean;

  @OneToMany(() => PvCutInformation, (worksheet) => worksheet.cutMaster, {
    cascade: true,
  })
  cutInformation?: PvCutInformation[];

  @AfterLoad()
  async afterLoad() {
    try {
      this.editable = true;
      if (this.eastingStart && this.northingStart) {
        const data = convertEastingNorthingToLatLongOnGetData(
          this.eastingStart,
          this.northingStart
        );
        if (data && data?.latitude && data.longitude) {
          this.latitudeStart = data?.latitude;
          this.longitudeStart = data.longitude;
        }
      }
      if (this.eastingEnd && this.northingEnd) {
        const data = convertEastingNorthingToLatLongOnGetData(this.eastingEnd, this.northingEnd);
        if (data && data?.latitude && data.longitude) {
          this.latitudeEnd = data?.latitude;
          this.longitudeEnd = data.longitude;
        }
      }
    } catch (error) {
      this.latitudeStart = null;
      this.longitudeStart = null;
      this.latitudeEnd = null;
      this.longitudeEnd = null;
    }
  }
}
