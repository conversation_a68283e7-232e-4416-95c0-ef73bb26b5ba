import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { Project } from './Project';
import { Stakeholder } from '../p_auth/Stakeholder';
import { SubSpecification } from '../p_meta/SubSpecification';
import { SubmittalClassification } from '../p_domain/SubmittalClassification';
import { SubmittalReviewCode } from '../p_domain/SubmittalReviewCode';
import { ActionCode } from '../p_domain/ActionCode';
import { SubmittalType } from '../p_domain/SubmittalType';
import { SubmittalList } from '../p_domain/SubmittalList';
import SubmittalVersion from './SubmittalVersion';
import { SubmittalFormAttached } from './SubmittalFormAttached';
import { Purpose } from '../p_meta/Purpose';

@Entity({ schema: 'p_gen' })
class Submittal {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  classificationId?: string;

  @ManyToOne(() => SubmittalClassification, { nullable: true })
  @JoinColumn({ name: 'classificationId' })
  submittalClassification?: SubmittalClassification;

  @Column({ nullable: true })
  reviewCodeId?: string;

  @ManyToOne(() => SubmittalReviewCode, { nullable: true })
  @JoinColumn({ name: 'reviewCodeId' })
  submittalReviewCode?: SubmittalReviewCode;

  @Column({ nullable: true })
  itemNo?: string;

  @Column({ nullable: true })
  stakeholderId?: string;

  @ManyToOne(() => Stakeholder, { nullable: true })
  @JoinColumn({ name: 'stakeholderId' })
  stakeholder?: Stakeholder;

  @Column({ nullable: true })
  section?: string;

  @Column({ nullable: true })
  specificationId?: string;

  @ManyToOne(() => SubSpecification, { nullable: true })
  @JoinColumn({ name: 'specificationId' })
  specification?: SubSpecification;

  @Column({ nullable: true })
  sdNo?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  status?: string;

  @Column({ nullable: true })
  drawingSheetNumber?: string;

  @Column({ nullable: true })
  currentVersion?: string;

  @Column({ nullable: true })
  lastSubmissionDate?: Date;

  @Column({ nullable: true })
  lastResponseDate?: Date;

  @Column({ nullable: true })
  lastOwnerCodeId?: Date;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true }) 
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ManyToOne(() => ActionCode, { nullable: true }) 
  @JoinColumn({ name: 'lastOwnerCodeId' })
  ownerCode?: ActionCode;

  @Column({ nullable: true })
  submittalTypeId?: string;

  @ManyToOne(() => SubmittalType, { nullable: true })
  @JoinColumn({ name: 'submittalTypeId' })
  submittalType?: SubmittalType;

  @Column({ nullable: true })
  submittalListId?: string;

  @ManyToOne(() => SubmittalList, { nullable: true })
  @JoinColumn({ name: 'submittalListId' })
  submittalList?: SubmittalList;

  @Column({ nullable: true })
  moveToSFTP?: boolean;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => SubmittalVersion, (version) => version.submittal)
  version?: SubmittalVersion[];

  @OneToMany(() => SubmittalFormAttached, (formAttached) => formAttached.submittal)
  submittalForm?: SubmittalFormAttached[];
}

export default Submittal;
