import { ApolloServer } from 'apollo-server-express';
import { Express } from 'express';
import { typeDefs } from './schema';
import { resolvers } from './resolvers';

export const setupGraphQL = async (app: Express) => {
  // Create Apollo Server
  const server = new ApolloServer({
    typeDefs,
    resolvers,
    context: ({ req }) => {
      return { req };
    },
    introspection: true,
  });

  // Start the Apollo Server
  await server.start();

  const version = 'api';

  server.applyMiddleware({ app, path: `/${version}/project360/graphql` });
};
