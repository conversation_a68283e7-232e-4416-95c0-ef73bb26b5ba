import { Request, Response } from 'express';
import submittalDocumentModel from '../models/submittalDocument.model';
import SubmittalDocument from '../../../entities/p_gen/SubmittalDocument';
import { submittalPushInterface } from '../../../shared/middlewares/uploadFileSftp.middleware';
import path from 'path';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';

class SubmittalDocumentController {
  constructor() {}

  async findBySubmittalRevisionId(req: Request, res: Response) {
    try {
      const data = await submittalDocumentModel.findBySubmittalRevisionId(req.params.id);
      data.map((value) => {
        if (value.documentURL) {
          (value as any).name = path.basename(value?.documentURL);
        }
        return value;
      });

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findBySubmittalRevisionIdFeedback(req: Request, res: Response) {
    try {
      const data = await submittalDocumentModel.findBySubmittalRevisionIdFeedBack(req.params.id);

      data.map((value) => {
        if (value.documentURL) {
          (value as any).name = path.basename(value?.documentURL);
        }
        return value;
      });

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findBySubmittalId(req: Request, res: Response) {
    try {
      const data = await submittalDocumentModel.findBySubmittalRevisionId(req.params.id);

      data.map((value) => {
        if (value.documentURL) {
          (value as any).name = path.basename(value?.documentURL);
        }
        return value;
      });

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async addSubmittalWithDocument(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;

      const files: submittalPushInterface[] = req.body.filePath || [];
      const document = files.map((value) => {
        const newDocument: SubmittalDocument = {
          updatedBy,
          createdBy,
          documentURL: value.path,
          documentType: value.documentType,
          submittalRevisionId: req.body.submittalRevisionId,
        };
        return newDocument;
      });

      const out = await submittalDocumentModel.addSubmittalDocument(document);

      // getting the data from database with the given id
      if (out) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: out,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const submittalDocumentController = new SubmittalDocumentController();
export default submittalDocumentController;
