import { MigrationInterface, QueryRunner } from "typeorm";

export class Mig2805251748443092290 implements MigrationInterface {
    name = 'Mig2805251748443092290'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "p_domain"."survey_phase" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "createdAt" TIMESTAMP NOT NULL DEFAULT now(), "createdBy" character varying, "createdUserId" character varying, "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "updatedBy" character varying, "isDelete" boolean NOT NULL DEFAULT false, "name" character varying, "alias" character varying, CONSTRAINT "PK_725623599bb3774234a6de7be27" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "p_cs"."survey_file_filetype_enum" AS ENUM('Surface', 'Raw', 'Report', 'Other')`);
        await queryRunner.query(`CREATE TABLE "p_cs"."survey_file" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "survey" character varying NOT NULL, "fileName" character varying(255) NOT NULL, "fileType" "p_cs"."survey_file_filetype_enum" NOT NULL, "uploadedBy" character varying NOT NULL, "uploadedAt" TIMESTAMP NOT NULL DEFAULT now(), "sftpPath" character varying(255) NOT NULL, "updatedAt" TIMESTAMP NOT NULL DEFAULT now(), "isDelete" boolean NOT NULL DEFAULT false, "surveyId" uuid NOT NULL, CONSTRAINT "PK_948410e7d01fe38c0867a34f95b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "personnelIds" jsonb`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "instrumentIds" jsonb`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "executionPhaseId" uuid`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "date" date`);
        await queryRunner.query(`CREATE TYPE "p_cs"."surveying_status_enum" AS ENUM('Draft', 'Final', 'Signed')`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "status" "p_cs"."surveying_status_enum" NOT NULL DEFAULT 'Draft'`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD "notes" text`);
        await queryRunner.query(`ALTER TABLE "p_cs"."blast_report" ALTER COLUMN "geom" TYPE geometry`);
        await queryRunner.query(`ALTER TABLE "p_cs"."pv_cut_master" ALTER COLUMN "geom" TYPE geometry`);
        await queryRunner.query(`ALTER TABLE "p_cs"."pv_clsm_placement" ALTER COLUMN "geom" TYPE geometry`);
        await queryRunner.query(`ALTER TABLE "p_map"."geom_layer" ALTER COLUMN "geom" TYPE geometry`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" ADD CONSTRAINT "FK_3d51a1c2d83f132305f392babd5" FOREIGN KEY ("executionPhaseId") REFERENCES "p_domain"."survey_phase"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "p_cs"."survey_file" ADD CONSTRAINT "FK_ec3ef9817573acbcead4d351ccc" FOREIGN KEY ("surveyId") REFERENCES "p_cs"."surveying"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "p_cs"."survey_file" DROP CONSTRAINT "FK_ec3ef9817573acbcead4d351ccc"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP CONSTRAINT "FK_3d51a1c2d83f132305f392babd5"`);
        await queryRunner.query(`ALTER TABLE "p_map"."geom_layer" ALTER COLUMN "geom" TYPE geometry(GEOMETRY,0)`);
        await queryRunner.query(`ALTER TABLE "p_cs"."pv_clsm_placement" ALTER COLUMN "geom" TYPE geometry(GEOMETRY,0)`);
        await queryRunner.query(`ALTER TABLE "p_cs"."pv_cut_master" ALTER COLUMN "geom" TYPE geometry(GEOMETRY,0)`);
        await queryRunner.query(`ALTER TABLE "p_cs"."blast_report" ALTER COLUMN "geom" TYPE geometry(GEOMETRY,0)`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "notes"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "status"`);
        await queryRunner.query(`DROP TYPE "p_cs"."surveying_status_enum"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "date"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "executionPhaseId"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "instrumentIds"`);
        await queryRunner.query(`ALTER TABLE "p_cs"."surveying" DROP COLUMN "personnelIds"`);
        await queryRunner.query(`DROP TABLE "p_cs"."survey_file"`);
        await queryRunner.query(`DROP TYPE "p_cs"."survey_file_filetype_enum"`);
        await queryRunner.query(`DROP TABLE "p_domain"."survey_phase"`);
    }

}
