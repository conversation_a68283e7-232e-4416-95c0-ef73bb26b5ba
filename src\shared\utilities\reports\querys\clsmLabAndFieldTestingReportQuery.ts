 const clsmLabAndFieldTestingReport = `SELECT 
    COALESCE(ss.label, 'N/A') AS "label",
    COALESCE(cs."cylinderLength1"::TEXT, 'N/A') AS "cylinderLength1",
    COALESCE(cs."cylinderDiameter1"::TEXT, 'N/A') AS "cylinderDiameter1",
    COALESCE(cs."cylinderDiameter"::TEXT, 'N/A') AS "cylinderDiameter",
    COALESCE(cs."cylinderLength2"::TEXT, 'N/A') AS "cylinderLength2",
    COALESCE(cs."cylinderLength"::TEXT, 'N/A') AS "cylinderLength",
    COALESCE(cs."specimenAge"::TEXT, 'N/A') AS "specimenAge",
    COALESCE(cs."dateTested"::TEXT, 'N/A') AS "dateTested",
    COALESCE(cs."cappingMethodId"::TEXT, 'N/A') AS "cappingMethodId",
    COALESCE(cs."failureLoad"::TEXT, 'N/A') AS "failureLoad",
    COALESCE(cs."unitWeight"::TEXT, 'N/A') AS "density",
    COALESCE(cs."crossSectionalArea"::TEXT, 'N/A') AS "crossSectionalArea",
    COALESCE(cs."compressiveStrength"::TEXT, 'N/A') AS "compressiveStrength",
    COALESCE(cs."comments", 'N/A') AS "comments",
    COALESCE(cs."cappingMethodId"::TEXT, 'N/A') AS "cappingMethodId",
    COALESCE(cs."cylinderDiameter2"::TEXT, 'N/A') AS "cylinderDiameter2"
    FROM 
    p_cs.st_compressive_strength cs
LEFT JOIN 
    p_cs.sample_specimen ss ON cs."specimenId" = ss.id
WHERE 
    cs."isDelete" = false
    AND ss."isDelete" = false
    and cs."sampleId" = {sampleId};`
export { clsmLabAndFieldTestingReport };