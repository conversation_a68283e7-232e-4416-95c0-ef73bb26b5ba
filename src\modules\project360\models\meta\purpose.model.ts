import { getManager } from 'typeorm';
import { Purpose } from '../../../../entities/p_meta/Purpose';
import projectRoleModel from '../role and permission/projectRole.model';
import { checkIsSuperAdmin } from '../../../../shared/server/platformApi/role';

class PurposeModel {
  constructor() {}
  getByName = async (name: string) => {
    try {
      const approverSetupRepository = getManager().getRepository(Purpose);
      const nuclearGaugesData = await approverSetupRepository.findOne({
        where: { name: name, isDelete: false },
      });
      return nuclearGaugesData;
    } catch (error) {
      throw error;
    }
  };

  getById = async (id: string) => {
    try {
      const approverSetupRepository = getManager().getRepository(Purpose);
      const data = await approverSetupRepository.findOne({
        where: { id: id, isDelete: false },
      });
      return data;
    } catch (error) {
      throw error;
    }
  };
  getAll = async () => {
    try {
      const approverSetupRepository = getManager().getRepository(Purpose);
      const data = await approverSetupRepository.find({
        where: { isDelete: false },
      });
      return data;
    } catch (error) {
      throw error;
    }
  };
  getQCId = async () => {
    try {
      const approverSetupRepository = getManager().getRepository(Purpose);
      const nuclearGaugesData = await approverSetupRepository.findOne({
        where: { name: 'QC', isDelete: false },
      });
      return nuclearGaugesData;
    } catch (error) {
      throw error;
    }
  };
  getByUserIdForDropdown = async (userId: string, projectId: string, view: boolean) => {
    try {
      const isAdmin = await checkIsSuperAdmin(userId);
      if (view || isAdmin) {
        const data = await this.getAll();
        return data.map((value) => ({ value: value.id, name: value.name, id: value.id }));
      }
      const role = await projectRoleModel.findRoleByUserAndProject(projectId, userId);
      const purposeByRole: string[] =
        role?.dataAccess
          ?.filter((value) => value.access === 'Full Access')
          .map((value) => value.purposeId)
          .filter((id): id is string => id !== undefined) || [];
      const allPurpose = await this.getAll();

      return allPurpose
        .filter((value) => purposeByRole.includes(value.id))
        .map((value) => ({ value: value.id, name: value.name, id: value.id }))
        .sort((a, b) => a?.name.localeCompare(b?.name));
    } catch (error) {
      throw error;
    }
  };
  getByUserIdForMapFilter = async (userId: string, projectId: string, view: boolean) => {
    try {
      const isAdmin = await checkIsSuperAdmin(userId);
      if (view || isAdmin) {
        const data = await this.getAll();
        return data.map((value) => ({ value: value.id, name: value.name, id: value.id }));
      }
      const role = await projectRoleModel.findRoleByUserAndProject(projectId, userId);
      const puposeByRole: string[] =
        role?.dataAccess
          ?.filter((value) => value.access !== 'No Access')
          .map((value) => value.purposeId)
          .filter((id): id is string => id !== undefined) || [];
      const allPurpose = await this.getAll();

      return allPurpose
        .filter((value) => puposeByRole.includes(value.id))
        .map((value) => ({ value: value.id, name: value.name, id: value.id }))
        .sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      throw error;
    }
  };
}

const purposeModel = new PurposeModel();
export default purposeModel;
