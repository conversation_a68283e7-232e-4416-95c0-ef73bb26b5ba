import * as proj4 from 'proj4';

export function convertEastingNorthingToLatLong(easting: number, northing: number) {
  try {
    if (easting && northing && !isNaN(easting) && !isNaN(northing)) {
      // https://spatialreference.org
      const WGS84 = 'EPSG:4326';
      const fromProjection =
        '+proj=tmerc +lat_0=24.3333333333333 +lon_0=-81 +k=0.999941177 +x_0=200000.0001016 +y_0=0 +datum=NAD83 +units=us-ft +no_defs +type=crs';

      const [lon, lat]: [number, number] = proj4.default(fromProjection, WGS84, [
        Number(easting),
        Number(northing),
      ]);

      return { latitude: lat, longitude: lon };
    }
  } catch (error) {
    return error;
  }
}

export function convertLatLongToEastingNorthing(latitude: number, longitude: number) {
  try {
    if (latitude && longitude && !isNaN(longitude) && !isNaN(latitude)) {
      const WGS84 = 'EPSG:4326';
      const toProjection =
        '+proj=tmerc +lat_0=24.3333333333333 +lon_0=-81 +k=0.999941177 +x_0=200000.0001016 +y_0=0 +datum=NAD83 +units=us-ft +no_defs +type=crs';

      const [easting, northing]: [number, number] = proj4.default(WGS84, toProjection, [
        Number(longitude),
        Number(latitude),
      ]);
      return { easting, northing };
    }
  } catch (error) {
    return error;
  }
}
