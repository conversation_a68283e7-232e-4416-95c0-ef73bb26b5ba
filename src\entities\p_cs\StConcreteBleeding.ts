import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { Sample } from './Sample';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StConcreteBleeding extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Batch Id',
      fieldName: 'batchId',
      needed: true,
      inTemplate: false,
      type: 'string',
      fkey: true,
      // batchNumber - ticketNumber
      query: `SELECT id FROM p_cs.concrete_batch_ticket WHERE "ticketNumber" = $1`,
      getListQuery: `SELECT id,"ticketNumber" as name FROM p_cs.concrete_batch_ticket WHERE "projectId" = $1 ORDER BY "updatedAt" DESC;`,
      listParams: 'id',
      listName: 'batchIdList',
    },
  })
  @Column({ nullable: true })
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'diameter',
      fieldName: 'diameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  diameter?: number;

  @ColumnInfo({
    customData: {
      name: 'exposed Area',
      fieldName: 'exposedArea',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  exposedArea?: number;

  @ColumnInfo({
    customData: {
      name: 'filled SampleHeight',
      fieldName: 'filledSampleHeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  filledSampleHeight?: number;

  @ColumnInfo({
    customData: {
      name: 'sample Volume',
      fieldName: 'sampleVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  sampleVolume?: number;

  @ColumnInfo({
    customData: {
      name: '',
      fieldName: 'specimenMass',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  specimenMass?: number;

  @ColumnInfo({
    customData: {
      name: 'Bleed Water Volume Per Unit Surface Area in ml ',
      fieldName: 'volumeBleed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  volumeBleed?: number;

  @ColumnInfo({
    customData: {
      name: 'Accumulated Bleed Water Percentage',
      fieldName: 'accumulatedBleedPercent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  accumulatedBleedPercent?: number;

  @ColumnInfo({
    customData: {
      name: 'Bleeding Elapsed Time in Minutes',
      fieldName: 'bleedingElapsedTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  bleedingElapsedTime?: number;

  @ColumnInfo({
    customData: {
      name: 'Tested By',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  testedBy?: string;

  @Column({ nullable: true })
  dateTested?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'QA or QC',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
