import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import changeOrderSorting from '../../../../shared/utilities/custom/changeOrder';
import GeoJsonConfigurationModel from '../../models/map/geoJSONConfig.model';
import LayerModel from '../../models/map/layer.model';
import filterByUpdateAt from '../../../../shared/utilities/custom/filterDataByUpdateAt';

class GeoJsonConfigurationController {
  async getData(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const layerModel = new LayerModel();
      const layerId = req.params.id;
      const updatedAfter = req.query.updatedAfter;
      const projectId = req.params.projectId;
      const columns = await layerModel.getColumnName(layerId);
      const data = await model.getData(columns || [], projectId, layerId, (req as any).user.name);
      if (updatedAfter) {
        const givenDate = new Date(updatedAfter as any);
        const finalData = filterByUpdateAt(givenDate, data);
        return res.json({ isSucceed: true, data: finalData, msg: 'Data found' });
      }
      return res.json({ isSucceed: true, data: data, msg: 'Data found' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async changeOrder(req: Request, res: Response) {
    try {
      const { oldIndex, targetIndex, layerId, projectId } = req.body;
      const model = new GeoJsonConfigurationModel();
      const dceConfigs = await model.getDataByDCEIdAndProjectId(layerId, projectId);
      if (dceConfigs.length > 0) {
        const newOrderArray = changeOrderSorting(dceConfigs, oldIndex, targetIndex);
        const dceConfigForOrderChange = newOrderArray.map((value, index: number) => {
          return { id: value.id, order: index + 1 };
        });
        await model.changeColumnOrder(dceConfigForOrderChange);
      }
      res.json({ isSucceed: true, data: [], msg: 'Order Changed' });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }

  async getColumnAndType(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const layerModel = new LayerModel();
      const layerId = req.params.layerId;
      const layer = await layerModel.getById(layerId);
      if (!layer) {
        throw new Error('Layer not found');
      }
      const data = await model.getColumnNameAndType(layer);
      res.json({ isSucceed: true, data: data, msg: 'Data found' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getColumnAndTypeForUniversalFilter(req: Request, res: Response) {
    try {
      const model = new GeoJsonConfigurationModel();
      const layerModel = new LayerModel();
      const layerId = req.params.layerId;
      const layer = await layerModel.getById(layerId);
      if (!layer) {
        throw new Error('Layer not found');
      }
      const data = await model.getColumnNameAndTypeUniversalFilter(layer);
      res.json({ isSucceed: true, data: data, msg: 'Data found' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async downloadLayer(req: Request, res: Response) {
    try {
      const layerId = req.params.layerId;
      const layerModel = new LayerModel();
      const geoJson = await layerModel.getLayerGeoJson(layerId, (req as any).user);
      const layerName = await layerModel.getLayerName(layerId);
      const htmlContent = `
      <!doctype html>
      <html lang="en">
        <head>
          <meta charset="UTF-8" />
          <meta name="viewport" content="width=device-width, initial-scale=1.0" />
          <title>Shapefile Export</title>
          <!-- Import FileSaver.js -->
          <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
          <!-- Import shp-write.js -->
          <script src="https://unpkg.com/@mapbox/shp-write@latest/shpwrite.js"></script>
        </head>

        <body>
          <button onclick="exportShapefile()">Export Shapefile</button>

          <script>
            function exportShapefile() {
              // Use the GeoJSON data passed from the server
              var data = ${JSON.stringify(geoJson)};

              // Save the zip file
              shpwrite.zip(data, { outputType: 'blob' }).then(function (zipBlob) {
                saveAs(zipBlob,'${layerName}.zip');
              });
            }
          </script>
        </body>
      </html>
    `;

      // Send the HTML content as the response
      res.setHeader('Content-Type', 'text/html');
      res.send(htmlContent);
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }
}

export default GeoJsonConfigurationController;
