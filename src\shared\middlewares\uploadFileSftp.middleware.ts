import { Request, Response, NextFunction } from 'express';
import projectModel from '../../modules/project360/models/project.model';
import { AzureBlobStorageService } from '../utilities/sftpBlobAndFiles/azureBlobStorageService';
import errorMiddleware from './error/error.middleware';
import { File } from '@entities/p_gen/File';

// TODO: add the project as file permission

export const validFileExtensions = [
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.csv',
  '.jpg',
  '.jpeg',
  '.png',
  '.gif',
  '.jfif',
  '.mp4',
  '.avi',
  '.mov',
];

const validImageExtensions = ['.jpg', '.jpeg', '.png'];

export const sftpUploadMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // const { file } = req;
    const file = req.files?.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    const fileExtension = (file as any).name.split('.').pop();

    if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
      return res.status(400).json({
        error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
      });
    }
    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid project Id',
      });
    }

    const projectDetails = await projectModel.getProjectId(req.body.projectId);
    if (!projectDetails) {
      return res.status(400).json({
        error: 'Invalid project Id',
      });
    }
    const fileName = `ticket/${req.body.ticketId}-${Date.now()}-${sanitizeFileName(
      (file as any).name
    )}`;
    const finalName = await azureBlobStorageService.uploadFileFromRequest(
      req.body.projectId,
      fileName,
      file
    );
    req.body.filePath = finalName;
    req.body.fileName = sanitizeFileName((file as any).name);
    // const filePath = `${process.env.SFTP_ROOT_PATH}/project${projectDetails?.folderPath}/Task/${fileName}`;
    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
  // Close the SFTP connection when done
};

export const sftpUploadProjectFileMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const files = req.files?.file;
    if (!files) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }
    const uploadedFiles: Partial<File>[] = [];
    if (Array.isArray(files)) {
      for (const file of files) {
        const fileExtension = (file as any).name.split('.').pop();

        if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
          return res.status(400).json({
            error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
          });
        }
        const fileName: string = sanitizeFileName((file as any).name);
        const azureBlobStorageService = new AzureBlobStorageService(
          process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
          process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
        );
        const filePath = `projectfile/${Date.now()}_${fileName}`;

        const finalPath = await azureBlobStorageService.uploadFileFromRequestForProjectFile(
          req.body.projectId,
          filePath,
          file
        );

        uploadedFiles.push({
          filePath: finalPath,
          name: sanitizeFileName((file as any).name),
          type: 'file',
          createdBy: (req as any).user.name,
          updatedBy: (req as any).user.name,
          projectId: req.body.projectId,
        });
      }
    } else {
      const fileExtension = (files as any).name.split('.').pop();

      if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
        return res.status(400).json({
          error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
        });
      }
      const fileName: string = sanitizeFileName((files as any).name);
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );
      const filePath = `projectfile/${Date.now()}_${fileName}`;

      const finalPath = await azureBlobStorageService.uploadFileFromRequestForProjectFile(
        req.body.projectId,
        filePath,
        files
      );
      uploadedFiles.push({
        filePath: finalPath,
        name: sanitizeFileName((files as any).name),
        type: 'file',
        createdBy: (req as any).user.name,
        updatedBy: (req as any).user.name,
        projectId: req.body.projectId,
      });
    }

    req.body.files = uploadedFiles;
    next();
    // Close the SFTP connection when done
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadReportFileMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const file = req.files?.report;
    if (!file) {
      return next();
    }

    const fileExtension = (file as any).name.split('.').pop();

    if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
      return res.status(400).json({
        error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
      });
    }
    const fileName: string = sanitizeFileName((file as any).name);
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const filePath = `reports/${Date.now()}_${fileName}`;

    const finalPath = await azureBlobStorageService.uploadFileFromRequestForProjectFile(
      req.body.projectId,
      filePath,
      file
    );
    req.body.fileName = sanitizeFileName((file as any).name);
    req.body.filePath = finalPath;
    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadImageMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const file = req.files?.file;
    if (!file) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }

    const fileExtension = (file as any).name.split('.').pop();

    if (!validImageExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
      return res.status(400).json({
        error: 'Invalid file extension . Supported file extensions: Jpg , Jpeg , Png',
      });
    }
    const fileName: string = sanitizeFileName((file as any).name);
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const filePath = `projectfile/${Date.now()}_${fileName}`;

    const finalPath = await azureBlobStorageService.uploadFileFromRequestForProjectFile(
      req.body.projectId,
      filePath,
      file
    );
    req.body.filePath = finalPath;
    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const SampleFileUploadMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // const { files } = req;
    const files = req.files?.file;
    const uploadedFiles: string[] = [];
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    if (files) {
      if (Array.isArray(files)) {
        for (const file of Object.values(files)) {
          const fileExtension = file.name.split('.').pop();

          if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
            return res.status(400).json({
              error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
            });
          }
          if (!req.body.projectId) {
            return res.status(400).json({
              error: 'Invalid project Id',
            });
          }

          const projectDetails = await projectModel.getProjectId(req.body.projectId);
          if (!projectDetails) {
            return res.status(400).json({
              error: 'Invalid project Id',
            });
          }

          const directoryPath: string = `${projectDetails.id}/sample`;

          const fileName: string = `${new Date()}-${sanitizeFileName(file.name)}`;
          const filePath = `${directoryPath}/${fileName}`;

          const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
            req.body.projectId,
            filePath,
            file
          );
          uploadedFiles.push(fileFinalPath);
        }
      } else {
        const fileExtension = files.name.split('.').pop();

        if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
          return res.status(400).json({
            error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
          });
        }

        if (!req.body.projectId) {
          return res.status(400).json({
            error: 'Invalid project Id',
          });
        }

        const projectDetails = await projectModel.getProjectId(req.body.projectId);
        if (!projectDetails) {
          return res.status(400).json({
            error: 'Invalid project Id',
          });
        }

        const directoryPath: string = `${projectDetails.id}/sample`;

        const fileName: string = `${new Date()}-${sanitizeFileName(files.name)}`;
        const filePath = `${directoryPath}/${fileName}`;

        try {
          const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
            req.body.projectId,
            filePath,
            files
          );
          uploadedFiles.push(fileFinalPath);
        } catch (err) {
          console.error(err);
          return errorMiddleware(err, 500, res, req);
        }

        const resultString = uploadedFiles.join(', ');

        // Pass information about the uploaded files to the next middleware or route handler
        req.body.file = resultString;
      }
    }

    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const CutMasterFileUploadMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // const { files } = req;
    const { entity } = req.params;
    if (entity != 'cutMaster' || !req.body.file || req.body.file.length <= 0) {
      next();
    }
    const files = Buffer.from(req.body.file[0].content, 'base64');
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    if (files) {
      const fileExtension = req.body.file[0].name.split('.').pop();

      if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
        return res.status(400).json({
          error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
        });
      }

      if (!req.body.projectId) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const projectDetails = await projectModel.getProjectId(req.body.projectId);
      if (!projectDetails) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const directoryPath: string = `${projectDetails.id}/cut`;

      const fileName: string = `${Date.now()}-${sanitizeFileName(req.body.file[0].name)}`;
      const filePath = `${directoryPath}/${fileName}`;

      try {
        req.body.file = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          filePath,
          { data: files, name: req.body.file[0].name }
        );
      } catch (err) {
        console.error(err);
        return errorMiddleware(err, 500, res, req);
      }
    }

    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
const validPhotoExtensions = ['.jpg', '.jpeg', '.png', '.gif'];
const validVideoExtensions = ['.mp4', '.avi', '.mov'];

export const photoVideoUploadMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const files = req.files?.file;

  if (!files) {
    return res.status(400).json({ error: 'No files uploaded.' });
  }
  const azureBlobStorageService = new AzureBlobStorageService(
    process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
    process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
  );

  const uploadedFiles: string[] = [];

  if (Array.isArray(files)) {
    for (const file of Object.values(files)) {
      const fileExtension = file.name.split('.').pop();

      if (
        !validPhotoExtensions.includes(`.${fileExtension?.toLowerCase()}`) &&
        !validVideoExtensions.includes(`.${fileExtension?.toLowerCase()}`)
      ) {
        return res.status(400).json({
          error:
            'Invalid file type. Supported types: Photo (jpg, jpeg, png, gif), Video (mp4, avi, mov)',
        });
      }
      const currentDate = new Date();
      const currentDay = currentDate.getDate();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();
      const timestampFormatted = currentDate.toISOString().replace(/T/, ' ').replace(/Z$/, '');
      const fileType = validPhotoExtensions.includes(`.${fileExtension?.toLowerCase()}`)
        ? 'photo'
        : 'video';

      const dayDirectoryPath: string = `media/${fileType}/${currentYear}/${
        currentMonth + 1
      }/${currentDay}`;
      const fileName: string = `${timestampFormatted}`;
      const filePath = `${dayDirectoryPath}/${fileName}`;

      try {
        await azureBlobStorageService.uploadFileFromRequest(req.body.projectId, filePath, file);
        uploadedFiles.push(filePath);
      } catch (err) {
        console.error(err);
        return res
          .status(500)
          .json({ error: 'Failed to upload one or more files to SFTP server.' });
      }
    }
  } else {
    const fileExtension = files.name.split('.').pop();

    if (
      !validPhotoExtensions.includes(`.${fileExtension?.toLowerCase()}`) &&
      !validVideoExtensions.includes(`.${fileExtension?.toLowerCase()}`)
    ) {
      return res.status(400).json({
        error:
          'Invalid file type. Supported types: Photo (jpg, jpeg, png, gif), Video (mp4, avi, mov)',
      });
    }

    const currentDate = new Date();
    const currentDay = currentDate.getDate();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const timestampFormatted = currentDate.toISOString().replace(/T/, ' ').replace(/Z$/, '');
    const fileType = validPhotoExtensions.includes(`.${fileExtension?.toLowerCase()}`)
      ? 'photo'
      : 'video';
    const dayDirectoryPath: string = `media/${fileType}/${currentYear}/${
      currentMonth + 1
    }/${currentDay}`;
    const fileName: string = `${timestampFormatted}`;
    const filePath = `${dayDirectoryPath}/${fileName}`;

    try {
      await azureBlobStorageService.uploadFileFromRequest(req.body.projectId, filePath, files);
      uploadedFiles.push(filePath);
    } catch (err) {
      console.error(err);
      return res.status(500).json({ error: 'Failed to upload one or more files to SFTP server.' });
    }
  }

  const resultString = uploadedFiles.join(', ');

  // Pass information about the uploaded files to the next middleware or route handler
  req.body.filePath = resultString;

  next();
};

const validForVideo = ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.pdf'];

export const sftpUploadProjectReportForPostBlastMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // const { file } = req;
  try {
    const camOne = req.files?.camOne;
    const camTwo = req.files?.camTwo;
    const file = req.files?.report;

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    let fileName = '';
    if (file) {
      fileName = `${currentDate}-${sanitizeFileName((file as any).name)}`;
    }

    const reportFolderfilePath = `report`;
    const typeFolderPath = `${reportFolderfilePath}/common`;

    let filePath = `${typeFolderPath}/${fileName}`;
    if (file) {
      filePath = `${typeFolderPath}/${fileName}`;
    }

    if (file) {
      const finalFilePath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        filePath,
        file
      );
      req.body.reportPath = finalFilePath;
    }
    if (camOne) {
      const camOnefileExtension = (camOne as any).name.split('.').pop();
      if (camOne && !validForVideo.includes(`.${camOnefileExtension}`)) {
        return res.status(400).json({
          error: 'Invalid file type.',
        });
      }
      const camOneName: string = `${Date.now()}-${(camOne as any).name}`;
      const camOnePath = `${typeFolderPath}/${camOneName}`;
      const camTwoFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        camOnePath,
        camOne
      );
      req.body.blastVideo1FilePath = camTwoFinalPath;
    }

    if (camTwo) {
      const camTwofileExtension = (camTwo as any).name.split('.').pop();
      if (camTwo && !validForVideo.includes(`.${camTwofileExtension}`)) {
        return res.status(400).json({
          error: 'Invalid file type.',
        });
      }
      const camTwoName: string = `${Date.now()}-${(camTwo as any).name}`;
      const camTwoPath = `${typeFolderPath}/${camTwoName}`;
      const camOneFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        camTwoPath,
        camTwo
      );
      req.body.blastVideo2FilePath = camOneFinalPath;
    }

    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const sftpUploadProjectReportForBlastSurveyMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const photo = req.files?.photo;
    const videoOne = req.files?.videoOne;
    const videoTwo = req.files?.videoTwo;
    const file = req.files?.report;

    if (photo || videoOne || videoTwo || file) {
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );

      if (!req.body.projectId) {
        return res.status(400).json({
          error: 'Invalid request body',
        });
      }
      const projectDetails = await projectModel.getProjectId(req.body.projectId);
      const reportFolderfilePath = `report`;
      const typeFolderPath = `${reportFolderfilePath}/common`;
      if (!projectDetails || !projectDetails.folderPath) {
        return res.status(400).json({
          error: 'Invalid request body',
        });
      }
      if (photo) {
        const photofileExtension = (photo as any).name.split('.').pop();
        if (!validFileExtensions.includes(`.${photofileExtension}`)) {
          return res.status(400).json({
            error: 'Invalid file type for photo.',
          });
        }
        const photoName: string = `${Date.now()}-${(photo as any).name}`;
        const photoPath = `${typeFolderPath}/${photoName}`;
        const photoFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          photoPath,
          photo
        );
        req.body.photoLink = photoFinalPath;
      }

      if (videoOne) {
        const videoOnefileExtension = (videoOne as any).name.split('.').pop();
        if (!validForVideo.includes(`.${videoOnefileExtension}`)) {
          return res.status(400).json({
            error: 'Invalid file type for photo.',
          });
        }
        const videoONEName: string = `${Date.now()}-${(videoOne as any).name}`;
        const videoOnePath = `${typeFolderPath}/${videoONEName}`;
        const videoOneFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          videoOnePath,
          videoOne
        );
        req.body.videoLinkOne = videoOneFinalPath;
      }

      if (videoTwo) {
        const videoTwofileExtension = (videoTwo as any).name.split('.').pop();
        if (!validForVideo.includes(`.${videoTwofileExtension}`)) {
          return res.status(400).json({
            error: 'Invalid file type for photo.',
          });
        }
        const videoTwoName: string = `${Date.now()}-${(videoTwo as any).name}`;
        const videoTwoPath = `${typeFolderPath}/${videoTwoName}`;
        const videoTwoFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          videoTwoPath,
          videoTwo
        );
        req.body.videoLinkTwo = videoTwoFinalPath;
      }

      if (file) {
        const fileExtension = (file as any).name.split('.').pop();
        if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
          return res.status(400).json({
            error: 'Invalid file type for photo.',
          });
        }
        const currentDate = Date.now();
        const fileName: string = `${currentDate}-${sanitizeFileName((file as any).name)}`;
        const filePath = `${typeFolderPath}/${fileName}`;
        const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          filePath,
          file
        );

        req.body.reportLink = fileFinalPath;
      }
    }

    next();
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadProjectReportForFieldSurveyMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const file = req.files?.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }
    const fileExtension = (file as any).name.split('.').pop();
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
      return res.status(400).json({
        error: 'Invalid file type.',
      });
    }

    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails || !projectDetails.folderPath) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    const fileName: string = `${currentDate}-${sanitizeFileName((file as any).name)}`;

    const reportFolderfilePath = `report`;
    const typeFolderPath = `${reportFolderfilePath}/common`;
    const filePath = `${typeFolderPath}/${fileName}`;

    const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
      req.body.projectId,
      filePath,
      file
    );
    req.body.link = fileFinalPath;
    next();
    // Close the SFTP connection when done
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadProjectReportForSurveyFeatureMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const file = req.files?.file;

    if (!file) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const fileExtension = (file as any).name.split('.').pop();

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!validFileExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
      return res.status(400).json({
        error: 'Invalid file type.',
      });
    }
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails || !projectDetails.folderPath) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    const fileName: string = `${currentDate}-${sanitizeFileName((file as any).name)}`;

    const reportFolderfilePath = `report`;
    const typeFolderPath = `${reportFolderfilePath}/common`;
    const filePath = `${typeFolderPath}/${fileName}`;

    const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
      req.body.projectId,
      filePath,
      file
    );
    req.body.link = fileFinalPath;
    next();
    // Close the SFTP connection when done
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadProjectReportForCutoffIssueMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const defectIntervalBoringLog = req.files?.defectIntervalBoringLog;
    const defectIntervalATVLog = req.files?.defectIntervalATVLog;
    const defectIntervalOPTVLog = req.files?.defectIntervalOPTVLog;

    if (!defectIntervalBoringLog || !defectIntervalATVLog || !defectIntervalOPTVLog) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }
    const defectIntervalBoringLogExtension = (defectIntervalBoringLog as any).name.split('.').pop();

    const defectIntervalATVLogExtension = (defectIntervalATVLog as any).name.split('.').pop();
    const defectIntervalOPTVLogExtension = (defectIntervalOPTVLog as any).name.split('.').pop();

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (
      !validFileExtensions.includes(`.${defectIntervalBoringLogExtension}`) ||
      !validFileExtensions.includes(`.${defectIntervalATVLogExtension}`) ||
      !validFileExtensions.includes(`.${defectIntervalOPTVLogExtension}`)
    ) {
      return res.status(400).json({
        error: 'Invalid file type.',
      });
    }
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails || !projectDetails.folderPath) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    const defectIntervalBoringLogName: string = `${currentDate}-boringlog-${
      (defectIntervalBoringLog as any).name
    }`;
    const defectIntervalATVLogName: string = `${currentDate}-ATVLog-${
      (defectIntervalATVLog as any).name
    }`;
    const defectIntervalOPTVLogName: string = `${currentDate}-OPTVLog-${
      (defectIntervalOPTVLog as any).name
    }`;

    const reportFolderfilePath = `report`;
    const typeFolderPath = `${reportFolderfilePath}/common`;
    const defectIntervalBoringLogPath = `${typeFolderPath}/${defectIntervalBoringLogName}`;
    const defectIntervalATVLogNamePath = `${typeFolderPath}/${defectIntervalATVLogName}`;
    const defectIntervalOPTVLogNamePath = `${typeFolderPath}/${defectIntervalOPTVLogName}`;

    const defectIntervalBoringLogPathFinal =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalBoringLogPath,
        defectIntervalBoringLog
      );
    const defectIntervalATVLogNamePathFinal =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalATVLogNamePath,
        defectIntervalATVLog
      );
    const defectIntervalOPTVLogNamePathFinal =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalOPTVLogNamePath,
        defectIntervalOPTVLog
      );
    req.body.boringLogLink = defectIntervalBoringLogPathFinal;
    req.body.atvLogLink = defectIntervalATVLogNamePathFinal;
    req.body.optvLogLink = defectIntervalOPTVLogNamePathFinal;
    next();

    // Close the SFTP connection when done
  } catch (error) {
    return res.status(500).json({ error: 'Failed to upload the file.' });
  }
};

export const sftpUploadProjectReportForFoundationPreparationMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const defectIntervalBoringLog = req.files?.foundationCleaningSignOff;
    const defectIntervalATVLog = req.files?.aciCleaningSignOff;
    const defectIntervalOPTVLog = req.files?.imagery;

    if (!defectIntervalBoringLog || !defectIntervalATVLog || !defectIntervalOPTVLog) {
      return res.status(400).json({ error: 'No file uploaded.' });
    }
    const defectIntervalBoringLogExtension = (defectIntervalBoringLog as any).name.split('.').pop();
    const defectIntervalATVLogExtension = (defectIntervalATVLog as any).name.split('.').pop();
    const defectIntervalOPTVLogExtension = (defectIntervalOPTVLog as any).name.split('.').pop();

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    if (
      !validFileExtensions.includes(`.${defectIntervalBoringLogExtension}`) ||
      !validFileExtensions.includes(`.${defectIntervalATVLogExtension}`) ||
      !validFileExtensions.includes(`.${defectIntervalOPTVLogExtension}`)
    ) {
      return res.status(400).json({
        error: 'Invalid file type.',
      });
    }
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails || !projectDetails.folderPath) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    const defectIntervalBoringLogName: string = `/foundationPreparation/${currentDate}-foundationCleaningSignOff-${
      (defectIntervalBoringLog as any).name
    }`;
    const defectIntervalATVLogName: string = `/foundationPreparation/${currentDate}-aciCleaningSignOff-${
      (defectIntervalBoringLog as any).name
    }`;
    const defectIntervalOPTVLogName: string = `/foundationPreparation/${currentDate}-imagery-${
      (defectIntervalBoringLog as any).name
    }`;

    const defectIntervalBoringLogPath = `${defectIntervalBoringLogName}`;
    const defectIntervalATVLogNamePath = `${defectIntervalATVLogName}`;
    const defectIntervalOPTVLogNamePath = `${defectIntervalOPTVLogName}`;

    const defectIntervalBoringLogFinalPath =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalBoringLogPath,
        defectIntervalBoringLog
      );
    const defectIntervalATVLogFinalPath =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalATVLogNamePath,
        defectIntervalATVLog
      );
    const defectIntervalOPTVLogFinalPath =
      await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        defectIntervalOPTVLogNamePath,
        defectIntervalOPTVLog
      );

    req.body.foundationCleaningSignOff = defectIntervalBoringLogFinalPath;
    req.body.aciCleaningSignOff = defectIntervalATVLogFinalPath;
    req.body.imagery = defectIntervalOPTVLogFinalPath;
    next();
    // Close the SFTP connection when done
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

const submittalExtensions = [
  '.pdf',
  '.doc',
  '.docx',
  '.xls',
  '.xlsx',
  '.jpg',
  '.png',
  '.mp4',
  '.dwg',
];
export interface submittalPushInterface {
  path: string;
  documentType: string;
}
export const submittalDocumentMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const files = req.files?.file;

    const uploadedFiles: submittalPushInterface[] = [];
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    if (Array.isArray(files)) {
      await Promise.all(
        files.map(async (file) => {
          const fileExtension = file.name.split('.').pop();

          if (!submittalExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
            throw new Error('Invalid file type. Supported types: Word, Excel, PDF');
          }

          if (!req.body.projectId) {
            throw new Error('Invalid project Id');
          }

          const projectDetails = await projectModel.getProjectId(req.body.projectId);
          if (!projectDetails) {
            throw new Error('Invalid project Id');
          }

          const directoryPath: string = `submittal`;

          const fileName: string = `${Date.now()}_${sanitizeFileName(file.name)}`;
          const filePath = `${directoryPath}/${fileName}`;

          const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
            req.body.projectId,
            filePath,
            file
          );
          uploadedFiles.push({ path: fileFinalPath, documentType: 'Submittal' });

          // 'index' contains the current index of the iteration.
        })
      );
    } else if (files) {
      const fileExtension = files.name.split('.').pop();

      if (!submittalExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
        return res.status(400).json({
          error: 'Invalid file type. Supported types: Word, Excel, PDF',
        });
      }

      if (!req.body.projectId) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const projectDetails = await projectModel.getProjectId(req.body.projectId);
      if (!projectDetails) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const directoryPath: string = `submittal`;

      const fileName: string = `${Date.now()}_${sanitizeFileName(files.name)}`;
      const filePath = `${directoryPath}/${fileName}`;

      try {
        const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          filePath,
          files
        );
        uploadedFiles.push({ path: fileFinalPath, documentType: 'Submittal' });
      } catch (err) {
        console.error(err);
        return res.status(500).json({ error: 'Failed to upload one or more files server.' });
      }

      // Pass information about the uploaded files to the next middleware or route handler
    }
    req.body.filePath = uploadedFiles;
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const reportDocumentMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const files = req.files?.file;
    // if (!files) {
    //   return res.status(400).json({ error: 'No files uploaded.' });
    // }

    const uploadedFiles: string[] = [];
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    if (Array.isArray(files)) {
      await Promise.all(
        files.map(async (file) => {
          const fileExtension = file.name.split('.').pop();

          if (!submittalExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
            throw new Error('Invalid file type. Supported types: Word, Excel, PDF');
          }

          if (!req.body.projectId) {
            throw new Error('Invalid project Id');
          }

          const projectDetails = await projectModel.getProjectId(req.body.projectId);
          if (!projectDetails) {
            throw new Error('Invalid project Id');
          }

          const directoryPath: string = `report`;

          const fileName: string = `${Date.now()}_${file.name}`;
          const filePath = `${directoryPath}/${fileName}`;

          const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
            req.body.projectId,
            filePath,
            file
          );
          uploadedFiles.push(fileFinalPath);
        })
      );
    } else if (files) {
      const fileExtension = files.name.split('.').pop();

      if (!submittalExtensions.includes(`.${fileExtension?.toLowerCase()}`)) {
        return res.status(400).json({
          error: 'Invalid file type. Supported types: Word, Excel, PDF',
        });
      }

      if (!req.body.projectId) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const projectDetails = await projectModel.getProjectId(req.body.projectId);
      if (!projectDetails) {
        return res.status(400).json({
          error: 'Invalid project Id',
        });
      }

      const directoryPath: string = `report`;

      const fileName: string = `${Date.now()}_${files.name}`;
      const filePath = `${directoryPath}/${fileName}`;

      try {
        const fileFinalPath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
          req.body.projectId,
          filePath,
          files
        );
        uploadedFiles.push(fileFinalPath);
      } catch (err) {
        console.error(err);
        return res.status(500).json({ error: 'Failed to upload one or more files server.' });
      }

      // Pass information about the uploaded files to the next middleware or route handler
    }
    req.body.filePath = uploadedFiles;
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const projectEquipmentImageUploadMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // const { file } = req;

    const file = req.files?.image;
    // const uploadPath: string = req.body.uploadPath;
    if (file) {
      const fileExtension = (file as any).name.split('.').pop();

      const projectEquipmentvalidFileExtensions = ['.jpg', '.jpeg', '.png'];
      if (
        !projectEquipmentvalidFileExtensions.includes(`.${(fileExtension as string).toLowerCase()}`)
      ) {
        return res.status(400).json({
          error: 'Invalid file type. Supported types: Word, Excel, PDF, Image',
        });
      }
      const fileName: string = sanitizeFileName((file as any).name);
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );
      const filePath = `projectEquipment/${Date.now()}_${fileName}`;

      const finalPath = await azureBlobStorageService.uploadFileFromRequestForProjectFile(
        req.body.projectId,
        filePath,
        file
      );

      req.body.image = finalPath;
    }

    next();
    // Close the SFTP connection when done
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const addIconForMapConfig = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const file = req.files?.icon;

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    let fileName = '';
    if (file) {
      fileName = `${currentDate}-icon.${(file as any)?.name?.split('.').pop()}`;
    }

    const filePath = `icon/${fileName}`;

    if (file) {
      const finalFilePath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        filePath,
        file
      );
      req.body.path = finalFilePath;
    }

    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
export const addIconForMapConfigInLegend = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const file = req.files?.icon;

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    let fileName = '';
    if (file) {
      fileName = `${currentDate}-icon.${(file as any)?.name?.split('.').pop()}`;
    }

    const filePath = `icon/${fileName}`;

    if (file) {
      const finalFilePath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        filePath,
        file
      );
      req.body.path = finalFilePath;
    }
    if (req.body.legendCriteria) {
      req.body.legendCriteria = JSON.parse(req.body.legendCriteria);
    }
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export const UploadBoreholeFileMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  // const { file } = req;
  try {
    const file = req.files?.file;

    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );
    const projectDetails = await projectModel.getProjectId(req.body.projectId);

    if (!projectDetails) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    if (!req.body.projectId) {
      return res.status(400).json({
        error: 'Invalid request body',
      });
    }

    const currentDate = Date.now();
    let fileName = '';
    if (file) {
      fileName = `${currentDate}-${sanitizeFileName((file as any).name)}`;
    }

    const reportFolderfilePath = `borehole/report`;
    const typeFolderPath = `${reportFolderfilePath}/common`;

    let filePath = `${typeFolderPath}/${fileName}`;
    if (file) {
      filePath = `${typeFolderPath}/${fileName}`;
    }

    if (file) {
      const finalFilePath = await azureBlobStorageService.uploadFileFromRequestUploadInput(
        req.body.projectId,
        filePath,
        file
      );
      req.body.file = finalFilePath;
    }

    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};

export function sanitizeFileName(fileName: string): string {
  try {
    // Split the file name into name and extension
    const parts = fileName.split('.');
    const name = parts.slice(0, -1).join('.');
    const extension = parts.length > 1 ? `.${parts.pop()}` : '';

    // Replace all non-alphanumeric characters and spaces in the name with underscores
    const sanitizedBaseName = name.replace(/[^a-zA-Z0-9]/g, '_');

    // Return the sanitized name with the original extension
    return sanitizedBaseName + extension;
  } catch (error) {
    throw error;
  }
}
