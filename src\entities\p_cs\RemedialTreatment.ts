import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { BlastReport } from './BlastReport';
import { RemedialTreatmentType } from '../p_domain/RemedialTreatmentType';
import { RemedialAnchorType } from '../p_domain/RemedialAnchorType';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class RemedialTreatment extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Blast Report Id', // this column will be entered by the
      fieldName: 'blastReportId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.blast_report WHERE "designBlastId" = $1`,
      getListQuery:
        'SELECT id,"designBlastId" as name FROM p_cs.blast_report WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 20;',
      listParams: 'id',
      listName: 'blastReportList',
    },
  })
  @Column({ nullable: true })
  blastReportId?: string;

  @ManyToOne(() => BlastReport, { nullable: true })
  @JoinColumn({ name: 'blastReportId' })
  blastReport?: BlastReport;

  @ColumnInfo({
    customData: {
      name: 'Date Remedial Treatment Performed',
      fieldName: 'datePerformed',
      needed: true,
      inTemplate: true,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  datePerformed?: Date;

  @ColumnInfo({
    customData: {
      name: 'longitude',
      fieldName: 'longitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @ColumnInfo({
    customData: {
      name: 'latitude',
      fieldName: 'latitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting',
      fieldName: 'easting',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing',
      fieldName: 'northing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation (ft)',
      fieldName: 'elevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Remedial Treatment Type',
      fieldName: 'remedialTreatmentType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.remedial_treatment_type WHERE name = $1',
      getListQuery:
        'Select id, alias as name FROM p_domain.remedial_treatment_type WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'remedialTreatmentTypeList',
    },
  })
  @Column({ nullable: true })
  remedialTreatmentTypeId?: string;

  @ManyToOne(() => RemedialTreatmentType, { nullable: true })
  @JoinColumn({ name: 'remedialTreatmentTypeId' })
  remedialTreatmentType?: RemedialTreatmentType;

  @ColumnInfo({
    customData: {
      name: 'Dental Concrete Take Quantity (CY)',
      fieldName: 'dentalConcreteQuantity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  dentalConcreteQuantity?: number;

  @ColumnInfo({
    customData: {
      name: 'Anchor Type',
      fieldName: 'anchorType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.remedial_anchor_type WHERE name = $1',
      getListQuery:
        'Select id, alias as name FROM p_domain.remedial_anchor_type WHERE "specAgencyId" = $1',
      listParams: 'specAgencyId',
      listName: 'remedialAnchorTypeList',
    },
  })
  @Column({ nullable: true })
  anchorTypeId?: string;

  @ManyToOne(() => RemedialAnchorType, { nullable: true })
  @JoinColumn({ name: 'anchorTypeId' })
  anchorType?: RemedialAnchorType;

  @ColumnInfo({
    customData: {
      name: 'Material Volume Excavated Before Placing',
      fieldName: 'materialVolumeExcavated',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  materialVolumeExcavated?: number;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
