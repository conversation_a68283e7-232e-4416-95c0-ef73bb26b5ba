import { Between, DeepPartial, get<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Not } from 'typeorm';
import { Sample } from '../../../entities/p_cs/Sample';
import { StakeUser } from '../../../entities/p_auth/StakeUser';
import { Stakeholder } from '../../../entities/p_auth/Stakeholder';
import { getAuditDetails } from '../../../shared/utilities/auditLog/getAuditDetailsByEntity';
import stationConversionOnAddData from '../../../shared/utilities/spatialCoordinates/stationConversionOnAddData';
import convertEastingAndNorthingToLatAndLong from '../../../shared/utilities/spatialCoordinates/convertEastingOnAddData';
import convertStationToNumber from '@utils/custom/convertStationToNumber';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

class SampleModel {
  constructor() {}

  async parseStation(station: string) {
    return parseInt(station.replace('+', ''), 10);
  }

  async findById(SampleId: string) {
    try {
      return await getManager()
        .getRepository(Sample)
        .findOne({
          where: { id: SampleId, isDelete: false },
          relations: ['purpose', 'material', 'site', 'materialType', 'ctLog'],
        });
    } catch (error) {
      throw error;
    }
  }
  async sampleByDate(projectId: string, from: Date, to: Date) {
    try {
      return await getManager()
        .getRepository(Sample)
        .find({
          where: { projectId, isDelete: false, sampledDate: Between(from, to) },
        });
    } catch (error) {
      throw error;
    }
  }

  async findSampleIdsByDepthAndPlanelNumberId(
    depth: number,
    panelNumberId: string,
    projectId: string
  ) {
    try {
      const data = await getManager()
        .getRepository(Sample)
        .find({
          where: { depth, panelNumberId, projectId, isDelete: false },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async findByProjectId(projectId: string) {
    try {
      return await getManager()
        .getRepository(Sample)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['purpose', 'material', 'site', 'materialType'],
        });
    } catch (error) {
      throw error;
    }
  }

  async updateFile(id: number, filePath: string) {
    try {
      return await getManager()
        .getRepository(Sample)
        .createQueryBuilder()
        .update(Sample)
        .set({ file: [filePath] })
        .where('id = :id', { id })
        .execute();
    } catch (error) {
      throw error;
    }
  }

  async addSample(newSample: Sample) {
    try {
      const dataToAdd = await stationConversionOnAddData(
        newSample?.projectId || '',
        newSample,
        Sample
      );
      const sampleRepository = getManager().getRepository(Sample);
      const newSampleNo = await this.getNextSampleNo(newSample?.sampledDate);
      dataToAdd.sampleNo = newSampleNo;
      const addedSample = sampleRepository.create(dataToAdd as Sample);
      await sampleRepository.save(addedSample);
      await getAuditDetails(
        addedSample.createdUserId || '',
        'sampleManagement',
        addedSample,
        'add'
      );
      return addedSample;
    } catch (error) {
      throw error;
    }
  }

  async addMultipleSample(newSample: Sample, noOfSample: number) {
    try {
      const entityManager = getManager();

      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const nextSampleNo = await this.getNextSampleNo(newSample?.sampledDate);
          const sampleRepository = transactionalEntityManager.getRepository(Sample);
          const addedSamples = [];
          for (let i = 0; i < noOfSample; i++) {
            const sampleYear = nextSampleNo.split('-')[0];
            const sampleNumber = parseInt(nextSampleNo.split('-')[1], 10) + i;
            const paddedSampleNumber = String(sampleNumber).padStart(4, '0');
            const currentSampleNo = `${sampleYear}-${paddedSampleNumber}`;
            const sampleData = { ...newSample, sampleNo: currentSampleNo };
            const convertedSample = convertEastingAndNorthingToLatAndLong(sampleData);
            const processedSample = (await stationConversionOnAddData(
              newSample?.projectId || '',
              convertedSample,
              Sample
            )) as Sample;
            const addedSample: Sample = sampleRepository.create(processedSample);
            await sampleRepository.save(addedSample);
            await getAuditDetails(
              addedSample.createdUserId || '',
              'sampleManagement',
              addedSample,
              'add'
            );
            addedSamples.push(addedSample);
          }

          return addedSamples;
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async updateSample(id: string, data: DeepPartial<Sample>, userId: string) {
    try {
      let dataToAdd = data;
      dataToAdd = convertEastingAndNorthingToLatAndLong(dataToAdd);

      dataToAdd = await stationConversionOnAddData(data?.projectId || '', data, Sample);
      await getManager()
        .getRepository(Sample)
        .update(id, dataToAdd as QueryDeepPartialEntity<Sample>);

      // Try to find the updated entity
      const updatedEntity = await this.findById(id);
      await getAuditDetails(userId, 'sampleManagement', updatedEntity, 'edit');
      return updatedEntity;
    } catch (error) {
      throw error;
    }
  }

  async getSampleForCheckIn(
    bagId: string | undefined,
    sampleId: string | undefined,
    ctNoId: string | undefined,
    sampleNo: string | undefined,
    projectId: string
  ) {
    try {
      if (!projectId) {
        throw new Error('Project ID is mandatory and must be provided.');
      }

      const whereClause: any = { isDelete: false, projectId };
      if (bagId) whereClause.bagId = bagId;
      if (sampleId) whereClause.sampleId = sampleId;
      if (sampleNo) whereClause.sampleNo = sampleNo;

      return await getManager()
        .getRepository(Sample)
        .findOne({
          where: whereClause,
          relations: ['purpose', 'material', 'site', 'materialType'], // Adjust relations as necessary
        });
    } catch (error) {
      throw error;
    }
  }
  async getByBoreholeId(boreholeId: string[], projectId: string) {
    try {
      return await getManager()
        .getRepository(Sample)
        .find({
          where: { boreholeId: In(boreholeId), isDelete: false, projectId },
          relations: ['purpose', 'material', 'site', 'materialType'], // Adjust relations as necessary
        });
    } catch (error) {
      throw error;
    }
  }

  async getSampleByUserId(userId: string) {
    try {
      const stakeUsers = await getManager()
        .getRepository(StakeUser)
        .find({ where: { userId: userId } });

      // Extract the stakeholderIds from the StakeUser entries
      const stakeholderIds = stakeUsers.map((value) => value.stakeholderId);
      if (stakeholderIds.length <= 0) {
        return [];
      }
      const objectsWithIds = await getManager()
        .getRepository(Stakeholder)
        .createQueryBuilder('entity')
        .where('entity.id IN (:...ids)', { ids: stakeholderIds })
        .getMany();

      const projectIds = objectsWithIds.map((value) => value.projectId);
      if (projectIds.length > 0) {
        const sample = await getManager()
          .getRepository(Sample)
          .createQueryBuilder('sample')
          .leftJoinAndSelect('sample.purpose', 'purpose')
          .leftJoinAndSelect('sample.material', 'material')
          .leftJoinAndSelect('sample.project', 'project')
          .leftJoinAndSelect('sample.site', 'site')
          .leftJoinAndSelect('sample.materialType', 'materialType')
          .where('sample.projectId IN (:...ids)', {
            ids: projectIds,
          })
          .getMany();

        const filterSample = sample.filter((value) => value.isDelete === false);
        return filterSample;
      }

      // Find the projects associated with the stakeholderIds
      return [];
    } catch (error) {
      throw error;
    }
  }

  async getNextSampleNo(sampledDate?: Date): Promise<string> {
    try {
      const sampleYear = sampledDate
        ? new Date(sampledDate).getFullYear().toString().slice(-2)
        : new Date().getFullYear().toString().slice(-2);

      const result = await getManager()
        .getRepository(Sample)
        .createQueryBuilder('sample')
        .select("MAX(CAST(SPLIT_PART(sample.sampleNo, '-', 2) AS INTEGER))", 'maxSampleNumber')
        .where('sample.isDelete = :isDelete', { isDelete: false })
        .andWhere("SPLIT_PART(sample.sampleNo, '-', 1) = :sampleYear", { sampleYear }) // Filters by Year
        .getRawOne();

      const maxSampleNumber = result?.maxSampleNumber ?? 0;
      const nextSampleNumber = maxSampleNumber + 1;

      const nextSampleNo = `${sampleYear}-${String(nextSampleNumber).padStart(5, '0')}`;

      return nextSampleNo;
    } catch (error) {
      console.error('Error generating sampleNo:', error);
      throw error;
    }
  }

  async getNextSampleId(sampledDate?: Date) {
    try {
      const sampleYear = sampledDate
        ? new Date(sampledDate).getFullYear()
        : new Date().getFullYear();

      const result = await getManager()
        .getRepository(Sample)
        .createQueryBuilder('sample')
        .select('MAX(sample.sampleId)', 'maxSampleId')
        .where('sample.isDelete = :isDelete', { isDelete: false })
        .andWhere('sample.sampleYear = :sampleYear', { sampleYear })
        .getRawOne();

      const maxSampleId = result?.maxSampleId ?? 0;
      const nextSampleId = maxSampleId + 1;

      return nextSampleId;
    } catch (error) {
      throw error;
    }
  }

  async getSamplesByStationsRange(start: string, end: string) {
    try {
      const parsedStart = this.parseStation(start);
      const parsedEnd = this.parseStation(end);
      const allSamples = await getManager()
        .getRepository(Sample)
        .find({
          where: {
            station: Not(IsNull()),
            depth: Not(IsNull()),
          },
        });
      const filteredSamples = allSamples.filter((sample) => {
        const sampleStationParsed = this.parseStation(sample.station!);
        return sampleStationParsed >= parsedStart && sampleStationParsed <= parsedEnd;
      });
      return filteredSamples;
    } catch (error) {
      throw error;
    }
  }

  async getDataByStationRange(stationStart: number, stationEnd: number, projectId: string) {
    try {
      const data = await getManager()
        .getRepository(Sample)
        .find({
          where: { projectId, station: Not(IsNull()) },
        });

      const filteredData = data.filter((value) => {
        const stationValue = convertStationToNumber(value.station!);

        return stationValue >= stationStart && stationValue <= stationEnd;
      });

      return filteredData;
    } catch (error) {
      throw error;
    }
  }

  async getSampleByTestNo(testNoId: string) {
    try {
      const data = await getManager()
        .getRepository(Sample)
        .findOne({ where: { testNoId: testNoId, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  // TODO: Commented because of column changes. Need to update the code
  // async getSamplesByBoreholes(boreNumbers: string[]) {
  //   try {
  //     const samples = await getManager()
  //       .getRepository(Sample)
  //       .find({
  //         where: {
  //           boringNo: In(boreNumbers),
  //           depth: Not(IsNull()),
  //         },
  //       });

  //     return samples;
  //   } catch (error) {
  //     throw error;
  //   }
  // }
}

const sampleModel = new SampleModel();
export default sampleModel;
