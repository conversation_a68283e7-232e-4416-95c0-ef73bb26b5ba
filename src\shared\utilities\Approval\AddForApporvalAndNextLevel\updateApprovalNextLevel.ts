import { Approval } from '@entities/p_utils/Approval';
import { ApprovalLevelInstance } from '@entities/p_utils/ApprovalLevelInstance';
import approvalModel from '@models/approval.model';
import dceModel from '@models/meta/dce.model';
import { getManager } from 'typeorm';
import {
  blastDamageSurveySftp,
  photoVideoSftpWithDataAfterAnnotation,
  postBlastVideoSftp,
} from '../moveFileToSFTP';
import ApprovalServices from './ApprovalServices';
import { addToQueue } from 'src/shared/queue/queue';

const updateApprovalAndSendToNextLevel = async (
  status: string,
  approvalIds: { approvalId: string; levelInstanceId: string }[],
  comment: string,
  approvedUserName: string,
  approvedUserId: string
) => {
  try {
    const approvalServices = new ApprovalServices();
    if (status && approvalIds) {
      const date = new Date();
      for (const item of approvalIds) {
        try {
          const approvalData = await approvalModel.getById(item.approvalId);
          const levelInstanceData = await approvalModel.getLevelInstanceById(
            item.levelInstanceId,
            item.approvalId
          );
          const dceData = await dceModel.findById(approvalData?.dceId || '');
          if (!dceData) {
            throw new Error('DCE not found');
          }
          const isMicroservice = dceData && dceData?.isMicroservice ? true : false;
          if (levelInstanceData?.approverId != approvedUserId) {
            throw new Error('You do not have permission to access this resource');
          }

          if (
            approvalData &&
            approvalData.tableName &&
            approvalData.tableId &&
            approvalData.status == 'Pending' &&
            approvalData.dceId &&
            approvalData.currentLevelId
          ) {
            let addToSFTP = false;
            const approvalsInSameLevel = await approvalModel.getAllApprovalInSameLevel(
              item.approvalId,
              approvalData.currentLevelId
            );
            if (!approvalsInSameLevel.every((approval) => approval.status == 'Pending')) {
              throw new Error('Something went wrong...');
            }
            const entityManager = getManager();
            await entityManager.transaction(async (transactionalEntityManager) => {
              try {
                let approvalChanges: Partial<Approval> = {};

                await transactionalEntityManager.update(
                  ApprovalLevelInstance,
                  { levelId: approvalData?.currentLevelId, approvalId: item.approvalId },
                  {
                    status,
                    approverComment: comment || `Data ${status} by ${approvedUserName}`,
                    details: `${status} by ${approvedUserName}`,
                    approvedDate: date,
                    approvedUserId,
                  }
                );
                if (status == 'Approved') {
                  const approvedDataReturn = await approvalServices.handleApprove(
                    transactionalEntityManager,
                    approvalData,
                    item,
                    approvedUserName,
                    approvedUserId,
                    isMicroservice,
                    date,
                    dceData,
                    addToSFTP,
                    approvalChanges,
                    comment
                  );
                  approvalChanges = approvedDataReturn.approvalChanges;
                  addToSFTP = approvedDataReturn.addToSFTP;
                } else if (status == 'Rejected') {
                  const rejectDataReturn = await approvalServices.handleReject(
                    item,
                    transactionalEntityManager,
                    approvedUserName,
                    approvalData,
                    approvalChanges,
                    isMicroservice,
                    approvedUserId,
                    dceData,
                    comment
                  );
                  approvalChanges = rejectDataReturn.approvalChanges;
                }
                await transactionalEntityManager.update(Approval, item.approvalId, approvalChanges);
              } catch (error) {
                throw error;
              }
            });
            if (addToSFTP) {
              if (approvalData.tableName == 'p_cs.post_blast_video') {
                addToQueue(async () => {
                  await postBlastVideoSftp(approvalData.dceId || '', approvalData.tableId || '');
                });
              } else if (approvalData.tableName == 'p_cs.blast_video_damage_survey') {
                addToQueue(async () => {
                  await blastDamageSurveySftp(approvalData.dceId || '', approvalData.tableId || '');
                });
              } else if (approvalData.tableName == 'p_gen.photo_video') {
                addToQueue(async () => {
                  await photoVideoSftpWithDataAfterAnnotation(
                    approvalData?.dceId || '',
                    approvalData.tableId || ''
                  );
                });
              }
            }
          }
        } catch (error) {
          throw error;
        }
      }
    }

    return true;
  } catch (error) {
    throw error;
  }
};

export default updateApprovalAndSendToNextLevel;
