import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  AfterLoad,
} from 'typeorm';
import { Surveying } from '@entities/p_cs/Surveying';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

export enum SurveyFileType {
  SURFACE = 'Surface',
  RAW = 'Raw',
  REPORT = 'Report',
  OTHER = 'Other',
}

@Entity({ schema: 'p_cs' })
export class SurveyFile {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ name: 'survey', nullable: false })
  surveyId!: string;

  @ManyToOne(() => Surveying, { nullable: false })
  @JoinColumn({ name: 'surveyId' })
  survey!: Surveying;

  @Column({ type: 'varchar', length: 255, nullable: false })
  fileName!: string;

  @Column({
    type: 'enum',
    enum: SurveyFileType,
    nullable: false,
  })
  fileType!: SurveyFileType;

  @Column({ name: 'uploadedBy', nullable: false })
  uploadedById!: string;

  // Virtual property for user data
  uploadedByUser?: IUser | null;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  uploadedAt!: Date;

  @Column({ type: 'varchar', length: 255, nullable: false })
  sftpPath!: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @AfterLoad()
  async loadUserData() {
    try {
      if (this.uploadedById) {
        const data = await getUserById(this.uploadedById);
        this.uploadedByUser = data;
      } else {
        this.uploadedByUser = null;
      }
    } catch (error) {
      console.error('Failed to load user data:', error);
      this.uploadedByUser = null;
    }
  }
}