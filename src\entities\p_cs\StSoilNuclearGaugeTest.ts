// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Point,
  BeforeInsert,
} from 'typeorm';

import { TestVariant } from '../p_meta/Testvariant';
import { DepthInfoNGSC } from '../p_domain/DepthInfoNGSC';
import { NuclearGaugesInfo } from '../p_domain/NuclearGaugesInfo';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { Site } from '@entities/p_gen/Site';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { TestMethod } from '@entities/p_meta/TestMethod';
import { Test } from '@entities/p_meta/Test';
import { Sample } from './Sample';
import { ProjectNuclearGauges } from '@entities/p_gen/ProjectNuclearGauges';
import { EventLog } from '@entities/p_gen/EventLog';
import Structure from '@entities/p_gen/Structure';
import { Feature } from '@entities/p_gen/Feature';
import { Area } from '@entities/p_gen/Area';
import { TestAndDCEColumns } from '@entities/common/TestColumns';
import { StationAlignment } from '@entities/p_map/StationAlignment';
import { StSoilProctorTest } from './StSoilProctorTest';

@Entity({ schema: 'p_cs' })
export class StSoilNuclearGaugeTest extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  // TODO: Remove once we start using proctorId
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  proctorId?: string;

  @ManyToOne(() => StSoilProctorTest, { nullable: true })
  @JoinColumn({ name: 'proctorId' })
  proctor?: StSoilProctorTest;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @Column({ nullable: true })
  testLocation?: string;

  @Column({ type: 'decimal', nullable: true })
  optimumMoistureContent?: number;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  structureId?: string;

  @ManyToOne(() => Structure, { nullable: true })
  @JoinColumn({ name: 'structureId' })
  structure?: Structure;

  @Column({ nullable: true })
  featureId?: string;

  @ManyToOne(() => Feature, { nullable: true })
  @JoinColumn({ name: 'featureId' })
  feature?: Feature;

  @Column({ nullable: true })
  stationAlignmentId?: string;

  @ManyToOne(() => StationAlignment, { nullable: true })
  @JoinColumn({ name: 'stationAlignmentId' })
  stationAlignment?: StationAlignment;

  @Column({ nullable: true })
  generalProjectAreaId?: string;

  @ManyToOne(() => Area, { nullable: true })
  @JoinColumn({ name: 'generalProjectAreaId' })
  generalProjectArea?: Area;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  depthId?: string;

  @ManyToOne(() => DepthInfoNGSC, { nullable: true })
  @JoinColumn({ name: 'depthId' })
  depthInfoNGSC?: DepthInfoNGSC;

  @Column({ nullable: true })
  gaugeId?: string;

  @ManyToOne(() => NuclearGaugesInfo, { nullable: true })
  @JoinColumn({ name: 'gaugeId' })
  projectNuclearGauges?: NuclearGaugesInfo;

  @Column({ nullable: true })
  gaugeNo?: string;

  @ManyToOne(() => ProjectNuclearGauges, { nullable: true })
  @JoinColumn({ name: 'gaugeNo' })
  projectGauges?: ProjectNuclearGauges;

  @ColumnInfo({
    customData: {
      name: 'standard Moisture Count',
      fieldName: 'standardMoistureCount',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  standardMoistureCount?: number;

  @ColumnInfo({
    customData: {
      name: 'standard Density Count',
      fieldName: 'standardDensityCount',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  standardDensityCount?: number;

  @Column({ nullable: true })
  testSequence?: string;

  @ColumnInfo({
    customData: {
      name: 'Maximum Lab Density',
      fieldName: 'maximumLabDensity',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  maximumLabDensity?: number;

  @ColumnInfo({
    customData: {
      name: 'Moisture Count',
      fieldName: 'moistureCount',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moistureCount?: number;

  @ColumnInfo({
    customData: {
      name: 'Density Count',
      fieldName: 'densityCount',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  densityCount?: number;

  @ColumnInfo({
    customData: {
      name: 'Wet Density',
      fieldName: 'wetDensity',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  wetDensity?: number;

  @ColumnInfo({
    customData: {
      name: 'Moisture Content',
      fieldName: 'moistureContent',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moistureContent?: number;

  @ColumnInfo({
    customData: {
      name: 'Dry Density',
      fieldName: 'dryDensity',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  dryDensity?: number; // DryDensity = wetDensity/(1+watercontent)

  @ColumnInfo({
    customData: {
      name: 'Actual Compaction',
      fieldName: 'actualCompaction',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  actualCompaction?: number; //DryDensity/MaximumLabDensity

  @ColumnInfo({
    customData: {
      name: 'Required Compaction',
      fieldName: 'requiredCompaction',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  requiredCompaction?: number;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  retest?: boolean;

  @ColumnInfo({
    customData: {
      name: 'Elevation',
      fieldName: 'elevation',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Corrective Action',
      fieldName: 'correctiveAction',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  correctiveAction?: string;

  @ColumnInfo({
    customData: {
      name: 'Lot',
      fieldName: 'lot',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  lot?: string;

  @ColumnInfo({
    customData: {
      name: 'Lift Number',
      fieldName: 'liftNumber',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  liftNumber?: string;

  @Column({ nullable: true })
  failedTestId?: string;

  @ManyToOne(() => StSoilNuclearGaugeTest, { nullable: true })
  @JoinColumn({ name: 'failedTestId' })
  stSoilNuclearGaugeTest?: StSoilNuclearGaugeTest;

  @Column({ default: false })
  isRf?: boolean;

  @Column({ nullable: true })
  testedBy?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
