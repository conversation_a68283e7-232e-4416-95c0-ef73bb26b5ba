import { EntityTarget, getConnection, getManager, ObjectLiteral, Not, In } from 'typeorm';
import { ApprovalSetup } from '../../../entities/p_utils/ApprovalSetup';
import { Approval } from '../../../entities/p_utils/Approval';
// import st from 'string-template';
import { DataFromEntityInterface } from '../../../shared/utilities/entity/customDecorator';
import { ApprovalLevelInstance } from '../../../entities/p_utils/ApprovalLevelInstance';
import { ApprovalLevel } from '../../../entities/p_utils/ApprovalLevel';
import { entityRelationList } from '../../../shared/utilities/entity/entityRelation';
import { entityList, EntityListInterface } from '../../../shared/utilities/entity/entityList';
// import { Approver } from '../../../entities/p_utils/Approver';
interface GroupedByDceId {
  [key: string]: string[]; // Key is of type number (the dceId)
}
class ApprovalModel {
  constructor() {}
  async findByProjectId(projectId: string) {
    try {
      const approvalsetupData = await getManager()
        .getRepository(ApprovalSetup)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['trigger', 'level', 'test'],
        });
      return approvalsetupData;
    } catch (error) {
      throw error;
    }
  }
  findPedingByProjectId = async (projectId: string) => {
    try {
      const approvalsetupData = await getManager()
        .getRepository(Approval)
        .find({
          where: { projectId: projectId, isDelete: false, status: 'Pending' },
          relations: ['approvers', 'dce'],
        });
      const groupedByDceId: GroupedByDceId = approvalsetupData.reduce(
        (acc: GroupedByDceId, obj) => {
          // Only process objects with a defined dceId (not null or undefined)
          if (obj.dce?.entity !== undefined && obj.dce?.entity !== null) {
            // Check if the dceId already exists in the accumulator
            if (!acc[obj.dce?.entity]) {
              acc[obj.dce?.entity] = []; // Initialize an array for this dceId
            }
            acc[obj.dce?.entity].push(obj?.tableId || ''); // Push the current object into the array
          }
          return acc; // Return the accumulator for the next iteration
        },
        {}
      );
      const final: Approval[] = [];
      const keys = Object.keys(groupedByDceId);
      for (const entity of keys) {
        if (entity in entityList) {
          const entityClass: any = entityList[entity as keyof EntityListInterface];
          try {
            const data = await getManager()
              .getRepository(entityClass)
              .find({
                where: {
                  id: In(groupedByDceId[entity]),
                  approvalStatus: { name: 'Pending Review' },
                  isDelete: false,
                },
                select: ['id'],
              });
            const ids = new Set(data.map((item) => item.id));
            // Filter the results based on approval setup data
            const newData = approvalsetupData.filter(
              (approval) => ids.has(approval.tableId) && approval.dce?.entity === entity
            );

            final.push(...newData); // Return empty array if no new data
          } catch (error) {
            throw error;
          }
        }
      }
      return final || [];
    } catch (error) {
      throw error;
    }
  };

  async checkSubmodule(dceId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalSetup)
        .findOne({ where: { dceId, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getByApprovalListByuserId(approverId: string, type: string) {
    try {
      const status = `${type.charAt(0).toUpperCase() + type.slice(1)}`;
      const data = await getManager()
        .getRepository(Approval)
        .find({
          where: {
            approvers: {
              approverId,
              isDelete: false,
              status: status,
            },
            status: status,
            isDelete: false,
          },
          relations: ['project', 'dce', 'activity', 'approvers'],
          order: { updatedAt: 'DESC' },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getByApprovalListByUserIdAndProjectId(approverId: string, projectId: string) {
    try {
      const data = getManager()
        .getRepository(Approval)
        .find({
          where: { approvers: { approverId }, projectId },
          relations: ['project', 'dce', 'activity', 'approvers'],
          order: { updatedAt: 'DESC' },
        });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getAllApprovalOnTableAndId(tableName: string, tableId: string, id: string) {
    try {
      const data = getManager()
        .getRepository(Approval)
        .find({ where: { tableName, tableId, id: Not(id) } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getAllApprovalInSameLevel(approvalId: string, levelId: string) {
    try {
      const data = getManager().getRepository(ApprovalLevelInstance).find({
        where: { approvalId, levelId },
      });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getById(id: string) {
    try {
      const data = getManager()
        .getRepository(Approval)
        .findOne({ where: { id }, relations: ['approvers', 'level'] });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getLevelInstanceById(id: string, approvalId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalLevelInstance)
        .findOne({ where: { id, approvalId }, relations: ['approval'] });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getLevelInstanceByApprovalId(approvalId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalLevelInstance)
        .find({ where: { approvalId, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getLevelApprovalSetupByIdForSigner(setupId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalLevel)
        .findOne({ where: { approvalSetupId: setupId, signer: true, isDelete: false } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getLevelInstanceByLevelId(levelId: string, approvalId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalLevelInstance)
        .find({ where: { levelId, approvalId, isDelete: false }, relations: ['approval'] });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getLevelByApprovalId(approvalId: string) {
    try {
      const data = getManager()
        .getRepository(ApprovalLevelInstance)
        .find({ where: { approvalId }, relations: ['approval'] });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getPendingByProjectId(id: string) {
    try {
      const data = getManager()
        .getRepository(Approval)
        .find({ where: { id, status: 'Pending' } });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async doesColumnExist(
    tableNameAndSchema: string,
    column1: string,
    column2: string
  ): Promise<boolean> {
    const entityManager = getManager();

    const schema = tableNameAndSchema.split('.')[0];
    const tableName = tableNameAndSchema.split('.')[1];

    const result = await entityManager.query(
      `
      SELECT column_name
    FROM information_schema.columns
    WHERE table_schema = $1 AND table_name = $2 AND (column_name = $3 OR column_name = $4)
    `,
      [schema, tableName, column1, column2]
    );

    return result.length === 2;
  }

  async updateQCData(
    tableNameAndSchema: string,
    qcVerifier: string,
    qcDate: string,
    id: number
  ): Promise<boolean> {
    const entityManager = getManager();

    const result = await entityManager.query(
      `
      UPDATE $1
        SET qcVerifier = $2, qcDate = $3
        WHERE id = $4;
    `,
      [tableNameAndSchema, qcVerifier, qcDate, id]
    );
    if (result.affected && result.affected > 0) {
      return true;
    }
    return false;
  }

  async findTableData<T extends ObjectLiteral>(
    tableNameAndSchema: string,
    id: string,
    entity: EntityTarget<T>,
    entityName: string
  ) {
    try {
      const repository = getConnection().getRepository<T>(entity);
      if (entityName in entityRelationList) {
        const output = await repository.findOne({
          where: { id, isDelete: false } as any,
          relations: entityRelationList[entityName].relation,
        });

        if (output !== null) {
          return output;
        }
      }
    } catch (error) {
      throw error;
    }
  }

  private mapKeysToFieldNames(data: any, columnInfo: DataFromEntityInterface[]): any {
    const newData: any = {};
    if (data) {
      for (const key in data) {
        const column = columnInfo.find((c) => c.fieldName === key);

        if (column) {
          newData[column.name] = data[key];
        } else {
          newData[key] = data[key];
        }
      }
    }

    return newData;
  }
}

export interface updateApporvalStatusInterface {
  id: string;
  date: Date;
  comment: string;
  tableNameAndSchema: string;
  qcVerifier: string;
  tableId: string;
  status: string;
  doesColumnExist: boolean;
  allApproved: boolean;
  dceId: string;
  entityName?: string;
}

const approvalModel = new ApprovalModel();
export default approvalModel;
