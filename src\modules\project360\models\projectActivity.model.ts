import { getManager } from 'typeorm';
import { ProjectActivity } from '../../../entities/p_gen/Activity';
import { DataCaptureElements } from '../../../entities/p_meta/DataCaptureElements';
import { ProjectForm } from '@entities/p_gen/ProjectForm';

class ProjectActivityModel {
  async getByProjectId(projectId: string) {
    try {
      const activityRepository = getManager().getRepository(ProjectActivity);
      const data = await activityRepository.find({
        where: { projectId, isDelete: false },
        relations: ['dataCaptureElements', 'forms'],
        order: { createdAt: 'ASC' },
      });

      return data;
    } catch (error) {
      throw error;
    }
  }
  async addActivityWithDCE(activity: Partial<ProjectActivity>, dceId: string[], formId: string[]) {
    try {
      const dataCaptureElementsRepository = getManager().getRepository(DataCaptureElements);
      const projectFornRepository = getManager().getRepository(ProjectForm);
      const activityRepository = getManager().getRepository(ProjectActivity);
      const dceData = await dataCaptureElementsRepository.findByIds(dceId);
      const formData = await projectFornRepository.findByIds(formId);
      const newActivity: Partial<ProjectActivity> = {
        ...activity,
        dataCaptureElements: [...(dceData || [])],
        forms: [...(formData || [])],
      };

      const addedActivity = await activityRepository.create(newActivity);

      await activityRepository.save(addedActivity);

      return addedActivity;
    } catch (error) {
      throw error;
    }
  }

  async update(
    activityId: string,
    updateActivityData: Partial<ProjectActivity>,
    dceId: string[],
    formId: string[]
  ) {
    const queryRunner = getManager().connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const activityRepository = queryRunner.manager.getRepository(ProjectActivity);
      const dataCaptureElementsRepository = queryRunner.manager.getRepository(DataCaptureElements);
      const projectFormRepository = queryRunner.manager.getRepository(ProjectForm);

      const activityData = await activityRepository.findOne({
        where: { id: activityId },
        relations: ['dataCaptureElements', 'forms'],
      });

      if (!activityData) {
        throw new Error('Activity not found');
      }

      // **1. Remove old relations**
      await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from('activity_project_form')
        .where('activityId = :activityId', { activityId })
        .execute();

      await queryRunner.manager
        .createQueryBuilder()
        .delete()
        .from('activity_data_capture_elements')
        .where('activityId = :activityId', { activityId })
        .execute();

      // **2. Fetch new relations**
      const dceData = await dataCaptureElementsRepository.findByIds(dceId);
      const formData = await projectFormRepository.findByIds(formId);

      // **3. Assign new relations**
      activityData.dataCaptureElements = dceData;
      activityData.forms = formData;

      // **4. Save updated entity**
      await activityRepository.save({
        ...activityData,
        ...updateActivityData,
      });

      await queryRunner.commitTransaction();

      return await activityRepository.findOne({
        where: { id: activityId },
        relations: ['dataCaptureElements', 'forms'],
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }
}

export default ProjectActivityModel;
