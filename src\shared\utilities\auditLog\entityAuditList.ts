interface AuditLogEntityInterface {
  [key: string]: {
    method: string;
    details: string;
    detailsParameter?: string[];
    table: string;
    tableDetails: string;
  }[];
}

export const auditLogEntity: AuditLogEntityInterface = {
  photoVideo: [
    {
      table: 'p_gen.photo_video',
      method: 'add',
      details: 'Media added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_gen.photo_video',
      method: 'modile',
      details: 'Media added by {createdBy} (Mobile)',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_gen.photo_video',
      method: 'edit',
      details: 'Media edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_gen.photo_video',
      method: 'delete',
      details: 'Media deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_gen.photo_video',
      method: 'approval',
      details: 'Media submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_gen.photo_video',
      method: 'approved',
      details: 'Media {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  concreteBatchTicket: [
    {
      table: 'p_cs.concrete_batch_ticket',
      method: 'add',
      details: 'Concrete Batch Ticket added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.concrete_batch_ticket',
      method: 'edit',
      details: 'Concrete Batch Ticket edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.concrete_batch_ticket',
      method: 'delete',
      details: 'Concrete Batch Ticket deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.concrete_batch_ticket',
      method: 'approval',
      details: 'Concrete Batch Ticket submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.concrete_batch_ticket',
      method: 'approved',
      details: 'Concrete Batch Ticket {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  stCLSMCompressiveStrength: [
    {
      table: 'p_cs.st_clsm_compressive_strength',
      method: 'add',
      details: 'CLSM Compressive Strength added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength',
      method: 'edit',
      details: 'CLSM Compressive Strength edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength',
      method: 'delete',
      details: 'CLSM Compressive Strength deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength',
      method: 'approval',
      details: 'CLSM Compressive Strength submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_clsm_compressive_strength',
      method: 'approved',
      details: 'CLSM Compressive Strength {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  compressiveStrength: [
    {
      table: 'p_cs.st_concrete_compressive_strength',
      method: 'add',
      details: 'Concrete Compressive Strength added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength',
      method: 'edit',
      details: 'Concrete Compressive Strength edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength',
      method: 'delete',
      details: 'Concrete Compressive Strength deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength',
      method: 'approval',
      details: 'Concrete Compressive Strength submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_compressive_strength',
      method: 'approved',
      details: 'Concrete Compressive Strength {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  stConcreteCompressiveStrengthSpecimen: [
    {
      table: 'p_cs.st_concrete_compressive_strength_specimen',
      method: 'add',
      details: 'Concrete Compressive Strength Specimen added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength_specimen',
      method: 'edit',
      details: 'Concrete Compressive Strength Specimen edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength_specimen',
      method: 'delete',
      details: 'Concrete Compressive Strength Specimen deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_compressive_strength_specimen',
      method: 'approval',
      details: 'Concrete Compressive Strength Specimen submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_compressive_strength_specimen',
      method: 'approved',
      details: 'Concrete Compressive Strength Specimen {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  concreteDensity: [
    {
      table: 'p_cs.st_concrete_density',
      method: 'add',
      details: 'Concrete Density added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_density',
      method: 'edit',
      details: 'Concrete Density edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_density',
      method: 'delete',
      details: 'Concrete Density deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_density',
      method: 'approval',
      details: 'Concrete Density submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_density',
      method: 'approved',
      details: 'Concrete Density {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  concreteSettingTime: [
    {
      table: 'p_cs.st_concrete_setting_time',
      method: 'add',
      details: 'Concrete Setting Time added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_setting_time',
      method: 'edit',
      details: 'Concrete Setting Time edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_setting_time',
      method: 'delete',
      details: 'Concrete Setting Time deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_setting_time',
      method: 'approval',
      details: 'Concrete Setting Time submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_setting_time',
      method: 'approved',
      details: 'Concrete Setting Time {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  slumpTest: [
    {
      table: 'p_cs.st_concrete_slump',
      method: 'add',
      details: 'Concrete Slump added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_slump',
      method: 'edit',
      details: 'Concrete Slump edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_slump',
      method: 'delete',
      details: 'Concrete Slump deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_slump',
      method: 'approval',
      details: 'Concrete Slump submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_slump',
      method: 'approved',
      details: 'Concrete Slump {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  shrinkage: [
    {
      table: 'p_cs.st_cylindrical_specimen_height_change',
      method: 'add',
      details: 'Cylindrical Specimen Height Change added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_cylindrical_specimen_height_change',
      method: 'edit',
      details: 'Cylindrical Specimen Height Change edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_cylindrical_specimen_height_change',
      method: 'delete',
      details: 'Cylindrical Specimen Height Change deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_cylindrical_specimen_height_change',
      method: 'approval',
      details: 'Cylindrical Specimen Height Change submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_cylindrical_specimen_height_change',
      method: 'approved',
      details: 'Cylindrical Specimen Height Change {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  concreteMixDesign: [
    {
      table: 'p_cs.concrete_mix_design',
      method: 'add',
      details: 'Concrete Mix Design added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.concrete_mix_design',
      method: 'edit',
      details: 'Concrete Mix Design edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.concrete_mix_design',
      method: 'delete',
      details: 'Concrete Mix Design deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.concrete_mix_design',
      method: 'approval',
      details: 'Concrete Mix Design submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.concrete_mix_design',
      method: 'approved',
      details: 'Concrete Mix Design {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  stConcreteBleedingWorksheet: [
    {
      table: 'p_cs.st_concrete_bleeding_worksheet',
      method: 'add',
      details: 'New Concrete Bleeding Worksheet added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding_worksheet',
      method: 'edit',
      details: 'Concrete Bleeding Worksheet edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding_worksheet',
      method: 'delete',
      details: 'Concrete Bleeding Worksheet deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding_worksheet',
      method: 'approval',
      details: 'Concrete Bleeding Worksheet submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_bleeding_worksheet',
      method: 'approved',
      details: 'Concrete Bleeding Worksheet{status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],

  concreteBleed: [
    {
      table: 'p_cs.st_concrete_bleeding',
      method: 'add',
      details: 'New Concrete Bleeding added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding',
      method: 'edit',
      details: 'Concrete Bleeding edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding',
      method: 'delete',
      details: 'Concrete Bleeding deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_bleeding',
      method: 'approval',
      details: 'Concrete Bleeding submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_concrete_bleeding',
      method: 'approved',
      details: 'Concrete Bleeding {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],

  stDensityCLSM: [
    {
      table: 'p_cs.st_density_clsm',
      method: 'add',
      details: 'New Density CLSM added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_density_clsm',
      method: 'edit',
      details: 'Density CLSM edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_density_clsm',
      method: 'delete',
      details: 'Density CLSM deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_density_clsm',
      method: 'approval',
      details: 'Density CLSM submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_density_clsm',
      method: 'approved',
      details: 'Density CLSM {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],

  stCLSMCompressiveStrengthSpecimen: [
    {
      table: 'p_cs.st_clsm_compressive_strength_specimen',
      method: 'add',
      details: 'New CLSM Compressive Strength Specimen added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength_specimen',
      method: 'edit',
      details: 'CLSM Compressive Strength Specimen edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength_specimen',
      method: 'delete',
      details: 'CLSM Compressive Strength Specimen deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_clsm_compressive_strength_specimen',
      method: 'approval',
      details: 'CLSM Compressive Strength Specimen submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.st_clsm_compressive_strength_specimen',
      method: 'approved',
      details: 'CLSM Compressive Strength Specimen {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  blastReport: [
    {
      table: 'p_cs.blast_report',
      method: 'add',
      details: 'New Blast Reprot added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.blast_report',
      method: 'edit',
      details: 'Blast Report edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_report',
      method: 'delete',
      details: 'Blast Report deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_report',
      method: 'approval',
      details: 'Blast Report submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },

    {
      table: 'p_cs.blast_report',
      method: 'approved',
      details: 'Blast Report {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  blastHole: [
    {
      table: 'p_cs.blast_hole',
      method: 'add',
      details: 'New Blast Hole added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.blast_hole',
      method: 'edit',
      details: 'Blast Hole edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_hole',
      method: 'delete',
      details: 'Blast Hole deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_hole',
      method: 'approval',
      details: 'Blast Hole submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_hole',
      method: 'approved',
      details: 'Blast Hole {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  blastEvaluation: [
    {
      table: 'p_cs.blast_evaluation',
      method: 'add',
      details: 'New Blast Evaluation added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.blast_evaluation',
      method: 'edit',
      details: 'Blast Evaluation edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_evaluation',
      method: 'delete',
      details: 'Blast Evaluation deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_evaluation',
      method: 'approval',
      details: 'Blast Evaluation submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_evaluation',
      method: 'approved',
      details: 'Blast Evaluation {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  blastDamageSurvey: [
    {
      table: 'p_cs.blast_damage_survey',
      method: 'add',
      details: 'New Blast Damage Survey added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.blast_damage_survey',
      method: 'edit',
      details: 'Blast Damage Survey edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_damage_survey',
      method: 'delete',
      details: 'Blast Damage Survey deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_damage_survey',
      method: 'approval',
      details: 'Blast Damage Survey submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.blast_damage_survey',
      method: 'approved',
      details: 'Blast Damage Survey {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  deckLoading: [
    {
      table: 'p_cs.deck_loading',
      method: 'add',
      details: 'New Deck Loading added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.deck_loading',
      method: 'edit',
      details: 'Deck Loading edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.deck_loading',
      method: 'delete',
      details: 'Deck Loading deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.deck_loading',
      method: 'approval',
      details: 'Deck Loading submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.deck_loading',
      method: 'approved',
      details: 'Deck Loading {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  explosiveTicket: [
    {
      table: 'p_cs.explosive_ticket',
      method: 'add',
      details: 'New Explosive Ticket added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.explosive_ticket',
      method: 'edit',
      details: 'Explosive Ticket edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.explosive_ticket',
      method: 'delete',
      details: 'Explosive Ticket deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.explosive_ticket',
      method: 'approval',
      details: 'Explosive Ticket submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.explosive_ticket',
      method: 'approved',
      details: 'Explosive Ticket {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  penetrationRate: [
    {
      table: 'p_cs.penetration_rate',
      method: 'add',
      details: 'New Penetration Rate added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.penetration_rate',
      method: 'edit',
      details: 'Penetration Rate edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.penetration_rate',
      method: 'delete',
      details: 'Penetration Rate deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.penetration_rate',
      method: 'approval',
      details: 'Penetration Rate submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.penetration_rate',
      method: 'approved',
      details: 'Penetration Rate {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  postBlastVideo: [
    {
      table: 'p_cs.post_blast_video',
      method: 'add',
      details: 'New Post Blast Video added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.post_blast_video',
      method: 'edit',
      details: 'Post Blast Video edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.post_blast_video',
      method: 'delete',
      details: 'Post Blast Video deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.post_blast_video',
      method: 'approval',
      details: 'Post Blast Video submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.post_blast_video',
      method: 'approved',
      details: 'Post Blast Video {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  remedialTreatment: [
    {
      table: 'p_cs.remedial_treatment',
      method: 'add',
      details: 'New Remedial Treatment added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.remedial_treatment',
      method: 'edit',
      details: 'Remedial Treatment  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.remedial_treatment',
      method: 'delete',
      details: 'Remedial Treatment deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.remedial_treatment',
      method: 'approval',
      details: 'Remedial Treatment submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.remedial_treatment',
      method: 'approved',
      details: 'Remedial Treatment {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  vibration: [
    {
      table: 'p_cs.vibration',
      method: 'add',
      details: 'New Vibration added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.vibration',
      method: 'edit',
      details: 'Vibration  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.vibration',
      method: 'delete',
      details: 'Vibration deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.vibration',
      method: 'approval',
      details: 'Vibration submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.vibration',
      method: 'approved',
      details: 'Vibration {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  piezometer: [
    {
      table: 'p_cs.piezometer',
      method: 'add',
      details: 'New Piezometer added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.piezometer',
      method: 'edit',
      details: 'Piezometer  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.piezometer',
      method: 'delete',
      details: 'Piezometer deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.piezometer',
      method: 'approval',
      details: 'Piezometer submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.piezometer',
      method: 'approved',
      details: 'Piezometer {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  seismograph: [
    {
      table: 'p_cs.seismograph',
      method: 'add',
      details: 'New Seismograph added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.seismograph',
      method: 'edit',
      details: 'Seismograph  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.seismograph',
      method: 'delete',
      details: 'Seismograph deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.seismograph',
      method: 'approval',
      details: 'Seismograph submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.seismograph',
      method: 'approved',
      details: 'Seismograph {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  turbidityTesting: [
    {
      table: 'p_cs.turbidity_testing',
      method: 'add',
      details: 'New Turbidity added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.turbidity_testing',
      method: 'edit',
      details: 'Turbidity  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.turbidity_testing',
      method: 'delete',
      details: 'Turbidity deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.turbidity_testing',
      method: 'approval',
      details: 'Turbidity submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.turbidity_testing',
      method: 'approved',
      details: 'Turbidity {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  borehole: [
    {
      table: 'p_cs.borehole',
      method: 'add',
      details: 'New Borehole added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.borehole',
      method: 'edit',
      details: 'Borehole  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.borehole',
      method: 'delete',
      details: 'Borehole deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.borehole',
      method: 'approval',
      details: 'Borehole submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.borehole',
      method: 'approved',
      details: 'Borehole {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  sampleClassification: [
    {
      table: 'p_cs.st_soil_sample_classification',
      method: 'add',
      details: 'New Soil Sample Classification added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_sample_classification',
      method: 'edit',
      details: 'Soil Sample Classification  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sample_classification',
      method: 'delete',
      details: 'Soil Sample Classification deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sample_classification',
      method: 'approval',
      details: 'Soil Sample Classification submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sample_classification',
      method: 'approved',
      details: 'Soil Sample Classification {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  nuclearGaugeTest: [
    {
      table: 'p_cs.st_soil_nuclear_gauge_test',
      method: 'add',
      details: 'New Soil Nuclear Gauge Test added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_nuclear_gauge_test',
      method: 'edit',
      details: 'Soil Nuclear Gauge Test  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_nuclear_gauge_test',
      method: 'delete',
      details: 'Soil Nuclear Gauge Test deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_nuclear_gauge_test',
      method: 'approval',
      details: 'Soil Nuclear Gauge Test submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_nuclear_gauge_test',
      method: 'approved',
      details: 'Soil Nuclear Gauge Test {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  sandConeTest: [
    {
      table: 'p_cs.st_soil_sand_cone_test',
      method: 'add',
      details: 'New Soil Sand Cone Test added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_sand_cone_test',
      method: 'edit',
      details: 'Soil Sand Cone Tes  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sand_cone_test',
      method: 'delete',
      details: 'Soil Sand Cone Tes deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sand_cone_test',
      method: 'approval',
      details: 'Soil Sand Cone Tes submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_sand_cone_test',
      method: 'approved',
      details: 'Soil Sand Cone Tes {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  grainSizeAnalysis: [
    {
      table: 'p_cs.st_soil_grain_analysis',
      method: 'add',
      details: 'New Soil Grain Analysis added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis',
      method: 'edit',
      details: 'Soil Grain Analysis  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis',
      method: 'delete',
      details: 'Soil Grain Analysis deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis',
      method: 'approval',
      details: 'Soil Grain Analysis submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis',
      method: 'approved',
      details: 'Soil Grain Analysis {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  grainAnalysisWorksheet: [
    {
      table: 'p_cs.st_soil_grain_analysis_worksheet',
      method: 'add',
      details: 'New Soil Grain Analysis Worksheet added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis_worksheet',
      method: 'edit',
      details: 'Soil Grain Analysis Worksheet edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis_worksheet',
      method: 'delete',
      details: 'Soil Grain Analysis Worksheet deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis_worksheet',
      method: 'approval',
      details: 'Soil Grain Analysis Worksheet submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_grain_analysis_worksheet',
      method: 'approved',
      details: 'Soil Grain Analysis Worksheet {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  moistureContent: [
    {
      table: 'p_cs.st_soil_moisture_content',
      method: 'add',
      details: 'New Soil Moisture Content added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_moisture_content',
      method: 'edit',
      details: 'Soil Moisture Content  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_moisture_content',
      method: 'delete',
      details: 'Soil Moisture Content deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_moisture_content',
      method: 'approval',
      details: 'Soil Moisture Content submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_moisture_content',
      method: 'approved',
      details: 'Soil Moisture Content {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  organicContent: [
    {
      table: 'p_cs.st_soil_organic_content',
      method: 'add',
      details: 'New Soil Organic Content added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_organic_content',
      method: 'edit',
      details: 'Soil Organic Content  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_organic_content',
      method: 'delete',
      details: 'Soil Organic Content deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_organic_content',
      method: 'approval',
      details: 'Soil Organic Content submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_organic_content',
      method: 'approved',
      details: 'Soil Organic Content {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  passing200Sieve: [
    {
      table: 'p_cs.st_soil_passing200_sieve',
      method: 'add',
      details: 'New Soil Passing 200 Sieve added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_passing200_sieve',
      method: 'edit',
      details: 'Soil Passing 200 Sieve  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_passing200_sieve',
      method: 'delete',
      details: 'Soil Passing 200 Sieve deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_passing200_sieve',
      method: 'approval',
      details: 'Soil Passing 200 Sieve submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_passing200_sieve',
      method: 'approved',
      details: 'Soil Passing 200 Sieve {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  proctor: [
    {
      table: 'p_cs.st_soil_proctor_test',
      method: 'add',
      details: 'New Soil Proctor Test added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_proctor_test',
      method: 'edit',
      details: 'Soil Proctor Test  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_proctor_test',
      method: 'delete',
      details: 'Soil Proctor Test deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_proctor_test',
      method: 'approval',
      details: 'Soil Proctor Test submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_proctor_test',
      method: 'approved',
      details: 'Soil Proctor Test {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  sampleManagement: [
    {
      table: 'p_cs.sample',
      method: 'add',
      details: 'New Sample added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.sample',
      method: 'edit',
      details: 'Sample edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.sample',
      method: 'delete',
      details: 'Sample deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.sample',
      method: 'approval',
      details: 'Sample submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.sample',
      method: 'approved',
      details: 'Sample {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  absorptionAndSpecificGravity: [
    {
      table: 'p_cs.st_soil_absorption_specific_gravity',
      method: 'add',
      details: 'New Soil Absorption Apecific Gravity added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_absorption_specific_gravity',
      method: 'edit',
      details: 'Soil Absorption Apecific Gravity  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_absorption_specific_gravity',
      method: 'delete',
      details: 'Soil Absorption Apecific Gravity  deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_absorption_specific_gravity',
      method: 'approval',
      details: 'Sample submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_absorption_specific_gravity',
      method: 'approved',
      details: 'Soil Absorption Apecific Gravity {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  atterbergLimits: [
    {
      table: 'p_cs.st_soil_atterberg_limit',
      method: 'add',
      details: 'New Soil Atterberg Limit Test added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_soil_atterberg_limit',
      method: 'edit',
      details: 'Soil Atterberg Limit Test  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_atterberg_limit',
      method: 'delete',
      details: 'Soil Atterberg Limit Test deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_atterberg_limit',
      method: 'approval',
      details: 'Soil Atterberg Limit Test submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_soil_atterberg_limit',
      method: 'approved',
      details: 'Soil Atterberg Limit Test {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  concreteTemperature: [
    {
      table: 'p_cs.st_concrete_temperature',
      method: 'add',
      details: 'New Concrete Temperature Test added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_concrete_temperature',
      method: 'edit',
      details: 'Concrete Temperature Test  edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_temperature',
      method: 'delete',
      details: 'Concrete Temperature Test deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_temperature',
      method: 'approval',
      details: 'Concrete Temperature Test submitted for approval by {updatedBy}',
      tableDetails: 'Submitted for approval by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_concrete_temperature',
      method: 'approved',
      details: 'Concrete Temperature Test {status} by {updatedBy} level {level}',
      tableDetails: '{status} by {updatedBy} level {level}',
      detailsParameter: ['updatedBy', 'status', 'level'],
    },
  ],
  ctLog: [
    {
      table: 'p_gen.ct_log',
      method: 'add',
      details: 'Control Test Log added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_gen.ct_log',
      method: 'edit',
      details: 'Control Test Log edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_gen.ct_log',
      method: 'delete',
      details: 'Control Test Log deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
  reportList: [
    {
      table: 'p_gen.report_list',
      method: 'add',
      details: 'Test Report added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_gen.report_list',
      method: 'edit',
      details: 'Test Report edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_gen.report_list',
      method: 'delete',
      details: 'Test Report deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
  cutMaster: [
    {
      table: 'p_cs.pv_cut_master',
      method: 'add',
      details: 'Cut added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.pv_cut_master',
      method: 'edit',
      details: 'Cut edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.pv_cut_master',
      method: 'delete',
      details: 'Cut deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
  cutInformation: [
    {
      table: 'p_cs.pv_cut_information',
      method: 'add',
      details: 'Cut Depth added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.pv_cut_information',
      method: 'edit',
      details: 'Cut Depth edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.pv_cut_information',
      method: 'delete',
      details: 'Cut Depth deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
  observation: [
    {
      table: 'p_cs.observation',
      method: 'add',
      details: 'Observation added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.observation',
      method: 'edit',
      details: 'Observation edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.observation',
      method: 'delete',
      details: 'Observation deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
  hydraulicConductivity: [
    {
      table: 'p_cs.st_hydraulic_conductivity_test',
      method: 'add',
      details: 'Hydraulic Conductivity added by {createdBy}',
      tableDetails: 'Added by {createdBy}',
      detailsParameter: ['createdBy'],
    },
    {
      table: 'p_cs.st_hydraulic_conductivity_test',
      method: 'edit',
      details: 'Hydraulic Conductivity edited by {updatedBy}',
      tableDetails: 'Edited by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
    {
      table: 'p_cs.st_hydraulic_conductivity_test',
      method: 'delete',
      details: 'Hydraulic Conductivity deleted by {updatedBy}',
      tableDetails: 'Deleted by {updatedBy}',
      detailsParameter: ['updatedBy'],
    },
  ],
};
