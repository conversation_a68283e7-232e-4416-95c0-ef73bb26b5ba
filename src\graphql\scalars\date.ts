import { GraphQLScalarType, Kind } from 'graphql';

// Custom scalar for Date handling
export const DateScalar = new GraphQLScalarType({
  name: 'Date',
  description: 'Date custom scalar type',

  // Convert outgoing Date to ISO String
  serialize(value) {
    // Check if value is a Date object
    if (value instanceof Date) {
      return value.toISOString();
    }

    // Check if value is a timestamp (number or numeric string)
    if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
      return new Date(Number(value)).toISOString();
    }

    // If it's already an ISO string or other format, return as is
    return value;
  },

  // Convert incoming ISO String to Date
  parseValue(value) {
    if (typeof value === 'string' || typeof value === 'number' || value instanceof Date) {
      return new Date(value);
    }
    throw new Error('Invalid date value');
  },

  // Parse literal AST value to Date
  parseLiteral(ast) {
    if (ast.kind === Kind.STRING || ast.kind === Kind.INT) {
      return new Date(ast.value);
    }
    return null;
  },
});
