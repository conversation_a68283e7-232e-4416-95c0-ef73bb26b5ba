import { getManager } from 'typeorm';
import { StSoilSandConeTest } from '../../../../entities/p_cs/StSoilSandConeTest';

class StSoilSandConeTestModel {
  Repo = getManager().getRepository(StSoilSandConeTest);

  async getSandConeByTestNo(testNoId: string) {
    try {
      const data = await this.Repo.findOne({
        where: { testNoId: testNoId, isDelete: false },
        relations: ['test', 'testMethod', 'testResult', 'site'],
      });
      return data;
    } catch (error) {
      throw error;
    }
  }
}
const stSoilSandConeTestModel = new StSoilSandConeTestModel();
export default stSoilSandConeTestModel;
