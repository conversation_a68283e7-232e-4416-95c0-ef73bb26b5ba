import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';

export enum Cardinality {
  ONE_TO_MANY = 'One-to-Many',
  MANY_TO_MANY = 'Many-to-Many',
  ONE_TO_ONE = 'One-to-One',
}
@Entity({ schema: 'p_meta' })
export class RelationshipType {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  parentRelationTypeId?: string;

  @Column({ type: 'enum', enum: Cardinality, default: Cardinality.MANY_TO_MANY })
  cardinality?: Cardinality;

  @Column()
  sourceLabel!: string;

  @Column()
  targetLabel!: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  isSampleTestRelation?: boolean;

  @Column({ nullable: true })
  isDropdownRelation?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
