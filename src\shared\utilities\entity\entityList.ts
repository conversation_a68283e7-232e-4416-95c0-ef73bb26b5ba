import { Borehole } from '../../../entities/p_cs/Borehole';
import { EntityTarget } from 'typeorm';
import { Dewatering } from '../../../entities/p_cs/Dewatering';
import { Inclinometer } from '../../../entities/p_cs/Inclinometer';
import { TurbidityTesting } from '../../../entities/p_cs/TurbidityTesting';
import { BlastReport } from '../../../entities/p_cs/BlastReport';
import { BlastHole } from '../../../entities/p_cs/BlastHole';
import { ConcreteMixDesign } from '../../../entities/p_cs/ConcreteMixDesign';
import { DeckLoading } from '../../../entities/p_cs/DeckLoading';
import { ExplosiveTicket } from '../../../entities/p_cs/ExplosiveTicket';
import { GroutMixDesign } from '../../../entities/p_cs/GroutMixDesign';
import { PenetrationRate } from '../../../entities/p_cs/PenetrationRate';
import { RemedialTreatment } from '../../../entities/p_cs/RemedialTreatment';
import { Vibration } from '../../../entities/p_cs/Vibration';
import { Seismograph } from '../../../entities/p_cs/Seismograph';
import { MonitoringLocation } from '../../../entities/p_cs/MonitoringLocation';
import { BlastEvaluation } from '../../../entities/p_cs/BlastEvaluation';
import { Piezometer } from '../../../entities/p_cs/Piezometer';
import { FieldSurvey } from '../../../entities/p_cs/FieldSurvey';
import { ReboundSettleGauge } from '../../../entities/p_cs/ReboundSettleGauge';
import { ConcreteBatchTicket } from '../../../entities/p_cs/ConcreteBatchTicket';
import { GroutPlantTicket } from '../../../entities/p_cs/GroutPlantTicket';
import { StConcreteBleeding } from '../../../entities/p_cs/StConcreteBleeding';
import { StConcreteBleedingWorksheet } from '../../../entities/p_cs/StConcreteBleedingWorksheet';
import { StConcreteSettingTime } from '../../../entities/p_cs/StConcreteSettingTime';
import { WaterPressureTest } from '../../../entities/p_cs/WaterPressureTest';
import { SurveyFeature } from '../../../entities/p_cs/SurveyFeature';
import { StBleedExpansion } from '../../../entities/p_cs/StBleedExpansion';
import { StDensitySlurry } from '../../../entities/p_cs/StDensitySlurry';
import { StViscosityMarshFunnel } from '../../../entities/p_cs/StViscosityMarshFunnel';
import { StWaterContentOfSoil } from '../../../entities/p_cs/StWaterContentOfSoil';
import { StSoilCementContent } from '../../../entities/p_cs/StSoilCementContent';
import { FoundationPreparation } from '../../../entities/p_cs/FoundationPreparation';
import { MaterialHaulPlace } from '../../../entities/p_cs/MaterialHaulPlace';
import { PostBlastVideo } from '../../../entities/p_cs/PostBlastVideo';
import { SeepageBarrier } from '../../../entities/p_cs/SeepageBarrier';
import { SeepageBarrierVerticality } from '../../../entities/p_cs/SeepageBarrierVerticality';
import { BlastDamageSurvey } from '../../../entities/p_cs/BlastDamageSurvey';
import { StCylindricalSpecimenHeightChange } from '../../../entities/p_cs/StCylindricalSpecimenHeightChange';
import { StHardenedSoilCementContent } from '../../../entities/p_cs/StHardenedSoilCementContent';
import { StSandContentBentSlurry } from '../../../entities/p_cs/StSandContentBentSlurry';
import { ConcreteIssuesCutoffWall } from '../../../entities/p_cs/ConcreteIssuesCutoffWall';
import { FreshPropertyIssuesCutoffWall } from '../../../entities/p_cs/FreshPropertyIssuesCutoffWall';
import { StDensityCLSM } from '../../../entities/p_cs/StDensityCLSM';
import { StFlowConsistencyCLSM } from '../../../entities/p_cs/StFlowConsistencyCLSM';
import { PhotoVideo } from '../../../entities/p_gen/PhotoVideo';
import { StSoilSampleClassification } from '../../../entities/p_cs/StSoilSampleClassification';
import { StSoilNuclearGaugeTest } from '../../../entities/p_cs/StSoilNuclearGaugeTest';
import { StSoilSandConeTest } from '../../../entities/p_cs/StSoilSandConeTest';
import { StSoilGrainAnalysis } from '../../../entities/p_cs/StSoilGrainAnalysis';
import { StSoilMoistureContent } from '../../../entities/p_cs/StSoilMoistureContent';
import { StSoilOrganicContent } from '../../../entities/p_cs/StSoilOrganicContent';
import { StSoilPassing200Sieve } from '../../../entities/p_cs/StSoilPassing200Sieve';
import { StSoilProctorTest } from '../../../entities/p_cs/StSoilProctorTest';
import { Sample } from '../../../entities/p_cs/Sample';
import { StSoilAtterbergLimit } from '../../../entities/p_cs/StSoilAtterbergLimit';
import { StSoilAbsorptionSpecificGravity } from '../../../entities/p_cs/StSoilAbsorptionSpecificGravity';
import { StSoilGrainAnalysisWorksheet } from '../../../entities/p_cs/StSoilGrainAnalysisWorksheet';
import { StConcreteDensity } from '../../../entities/p_cs/StConcreteDensity';
import { StConcreteSlump } from '../../../entities/p_cs/StConcreteSlump';
import { StSoilRelativeDensity } from '../../../entities/p_cs/StSoilRelativeDensity';
import { StHydraulicConductivityTest } from '../../../entities/p_cs/StHydraulicConductivityTest';
import { Observation } from '../../../entities/p_cs/Observation';
import { StConcreteTemperature } from '../../../entities/p_cs/StConcreteGroutTemperature';
import { CTLog } from '../../../entities/p_gen/CTLog';
import { ProjectReportList } from '../../../entities/p_gen/ReportList';
import { StSoilAtterbergLimitWorksheet } from '../../../entities/p_cs/StSoilAtterbergLimitWorksheet';
import { StSoilProctorWorksheet } from '../../../entities/p_cs/StSoilProctorWorksheet';
import { PvCutMaster } from '../../../entities/p_cs/PvCutMaster';
import { PvCutInformation } from '../../../entities/p_cs/PvCutInformation';
import { StSoilOrganicContentWorksheet } from '../../../entities/p_cs/StSoilOrganicContentWorksheet';
import { PvCLSMPlacement } from '../../../entities/p_cs/PvCLSMPlacement ';
import { StSoilGrainAnalysisHydrometerReadings } from '../../../entities/p_cs/StSoilGrainAnalysisHydrometerReadings';
import { StCompressiveStrength } from '@entities/p_cs/StCompressiveStrength';
import { TestLogPrefixConfig } from '@entities/p_gen/TestLogPrefixConfig';
import { StHydraulicConductivityWorkSheet } from '@entities/p_cs/StHydraulicConductivityWorkSheet';
import { StSoilUCS } from '@entities/p_qms/StSoilUCS';
import { SampleSpecimen } from '@entities/p_cs/SampleSpecimen';
import { StSoilUCSSAC } from '@entities/p_cs/StSoilUCSSAC';
import { StAggregateSieveAnalysis } from '@entities/p_qms/StAggregateSieveAnalysis';
import { Surveying } from '@entities/p_cs/Surveying';
import { ClearingGrubbing } from '@entities/p_gen/ClearingGrubbing';


export interface EntityListInterface {
  compressiveStrength: EntityTarget<StCompressiveStrength>;
  unconfinedCompressiveStrength: EntityTarget<StSoilUCS>;
  slumpTest: EntityTarget<StConcreteSlump>;
  concreteDensity: EntityTarget<StConcreteDensity>;
  borehole: EntityTarget<Borehole>;
  dewatering: EntityTarget<Dewatering>;
  inclinometer: EntityTarget<Inclinometer>;
  turbidityTesting: EntityTarget<TurbidityTesting>;
  blastReport: EntityTarget<BlastReport>;
  blastHole: EntityTarget<BlastHole>;
  concreteMixDesign: EntityTarget<ConcreteMixDesign>;
  deckLoading: EntityTarget<DeckLoading>;
  explosiveTicket: EntityTarget<ExplosiveTicket>;
  groutMixDesign: EntityTarget<GroutMixDesign>;
  penetrationRate: EntityTarget<PenetrationRate>;
  remedialTreatment: EntityTarget<RemedialTreatment>;
  vibration: EntityTarget<Vibration>;
  seismograph: EntityTarget<Seismograph>;
  monitoringLocation: EntityTarget<MonitoringLocation>;
  blastEvaluation: EntityTarget<BlastEvaluation>;
  piezometer: EntityTarget<Piezometer>;
  fieldSurvey: EntityTarget<FieldSurvey>;
  reboundSettleGauge: EntityTarget<ReboundSettleGauge>;
  concreteBatchTicket: EntityTarget<ConcreteBatchTicket>;
  groutPlantTicket: EntityTarget<GroutPlantTicket>;
  concreteBleed: EntityTarget<StConcreteBleeding>;
  stConcreteBleedingWorksheet: EntityTarget<StConcreteBleedingWorksheet>;
  concreteSettingTime: EntityTarget<StConcreteSettingTime>;
  waterPressureTest: EntityTarget<WaterPressureTest>;
  surveyFeature: EntityTarget<SurveyFeature>;
  stBleedExpansion: EntityTarget<StBleedExpansion>;
  stDensitySlurry: EntityTarget<StDensitySlurry>;
  stViscosityMarshFunnel: EntityTarget<StViscosityMarshFunnel>;
  stWaterContentOfSoil: EntityTarget<StWaterContentOfSoil>;
  stSoilCementContent: EntityTarget<StSoilCementContent>;
  foundationPreparation: EntityTarget<FoundationPreparation>;
  materialHaulPlace: EntityTarget<MaterialHaulPlace>;
  postBlastVideo: EntityTarget<PostBlastVideo>;
  seepageBarrier: EntityTarget<SeepageBarrier>;
  seepageBarrierVerticality: EntityTarget<SeepageBarrierVerticality>;
  blastDamageSurvey: EntityTarget<BlastDamageSurvey>;
  surveying: EntityTarget<Surveying>;
  clearingGrubbing: EntityTarget<ClearingGrubbing>;
  shrinkage: EntityTarget<StCylindricalSpecimenHeightChange>;
  stHardenedSoilCementContent: EntityTarget<StHardenedSoilCementContent>;
  stSandContentBentSlurry: EntityTarget<StSandContentBentSlurry>;
  freshPropertyIssuesCutoffWall: EntityTarget<FreshPropertyIssuesCutoffWall>;
  concreteIssuesCutoffWall: EntityTarget<ConcreteIssuesCutoffWall>;
  stDensityCLSM: EntityTarget<StDensityCLSM>;
  stFLowConsistencyCLSM: EntityTarget<StFlowConsistencyCLSM>;
  photoVideo: EntityTarget<PhotoVideo>;
  sampleClassification: EntityTarget<StSoilSampleClassification>;
  nuclearGaugeTest: EntityTarget<StSoilNuclearGaugeTest>;
  sandConeTest: EntityTarget<StSoilSandConeTest>;
  grainSizeAnalysis: EntityTarget<StSoilGrainAnalysis>;
  sieveAnalysis: EntityTarget<StAggregateSieveAnalysis>;
  grainAnalysisWorksheet: EntityTarget<StSoilGrainAnalysisWorksheet>;
  moistureContent: EntityTarget<StSoilMoistureContent>;
  organicContent: EntityTarget<StSoilOrganicContent>;
  passing200Sieve: EntityTarget<StSoilPassing200Sieve>;
  proctor: EntityTarget<StSoilProctorTest>;
  proctorWorksheet: EntityTarget<StSoilProctorWorksheet>;
  sampleManagement: EntityTarget<Sample>;
  atterbergLimits: EntityTarget<StSoilAtterbergLimit>;
  atterbergLimitWorksheet: EntityTarget<StSoilAtterbergLimitWorksheet>;
  absorptionAndSpecificGravity: EntityTarget<StSoilAbsorptionSpecificGravity>;
  stSoilRelativeDensity: EntityTarget<StSoilRelativeDensity>;
  hydraulicConductivity: EntityTarget<StHydraulicConductivityTest>;
  hydraulicConductivityWorkSheet: EntityTarget<StHydraulicConductivityWorkSheet>;
  observation: EntityTarget<Observation>;
  concreteTemperature: EntityTarget<StConcreteTemperature>;
  ctLog: EntityTarget<CTLog>;
  reportList: EntityTarget<ProjectReportList>;
  cutMaster: EntityTarget<PvCutMaster>;
  clsmPlacement: EntityTarget<PvCLSMPlacement>;
  cutInformation: EntityTarget<PvCutInformation>;
  organicContentWorksheet: EntityTarget<StSoilOrganicContentWorksheet>;
  hydrometer: EntityTarget<StSoilGrainAnalysisHydrometerReadings>;
  testLogPrefixConfig: EntityTarget<TestLogPrefixConfig>;
  sampleSpecimen: EntityTarget<SampleSpecimen>;
  soilUCSSAC: EntityTarget<StSoilUCSSAC>;
}

export const entityList: EntityListInterface = {
  stConcreteBleedingWorksheet: StConcreteBleedingWorksheet,
  slumpTest: StConcreteSlump,
  compressiveStrength: StCompressiveStrength,
  unconfinedCompressiveStrength: StSoilUCS,
  concreteDensity: StConcreteDensity,
  borehole: Borehole,
  dewatering: Dewatering,
  inclinometer: Inclinometer,
  turbidityTesting: TurbidityTesting,
  blastReport: BlastReport,
  blastHole: BlastHole,
  concreteMixDesign: ConcreteMixDesign,
  deckLoading: DeckLoading,
  explosiveTicket: ExplosiveTicket,
  groutMixDesign: GroutMixDesign,
  penetrationRate: PenetrationRate,
  remedialTreatment: RemedialTreatment,
  vibration: Vibration,
  seismograph: Seismograph,
  monitoringLocation: MonitoringLocation,
  blastEvaluation: BlastEvaluation,
  piezometer: Piezometer,
  fieldSurvey: FieldSurvey,
  reboundSettleGauge: ReboundSettleGauge,
  concreteBatchTicket: ConcreteBatchTicket,
  groutPlantTicket: GroutPlantTicket,
  concreteBleed: StConcreteBleeding,
  concreteSettingTime: StConcreteSettingTime,
  waterPressureTest: WaterPressureTest,
  surveying: Surveying,
  clearingGrubbing: ClearingGrubbing,
  surveyFeature: SurveyFeature,
  stBleedExpansion: StBleedExpansion,
  stDensitySlurry: StDensitySlurry,
  stViscosityMarshFunnel: StViscosityMarshFunnel,
  stWaterContentOfSoil: StWaterContentOfSoil,
  stSoilCementContent: StSoilCementContent,
  foundationPreparation: FoundationPreparation,
  materialHaulPlace: MaterialHaulPlace,
  postBlastVideo: PostBlastVideo,
  seepageBarrier: SeepageBarrier,
  seepageBarrierVerticality: SeepageBarrierVerticality,
  blastDamageSurvey: BlastDamageSurvey,
  shrinkage: StCylindricalSpecimenHeightChange,
  stHardenedSoilCementContent: StHardenedSoilCementContent,
  stSandContentBentSlurry: StSandContentBentSlurry,
  concreteIssuesCutoffWall: ConcreteIssuesCutoffWall,
  freshPropertyIssuesCutoffWall: FreshPropertyIssuesCutoffWall,
  stDensityCLSM: StDensityCLSM,
  stFLowConsistencyCLSM: StFlowConsistencyCLSM,
  photoVideo: PhotoVideo,
  sampleClassification: StSoilSampleClassification,
  nuclearGaugeTest: StSoilNuclearGaugeTest,
  sandConeTest: StSoilSandConeTest,
  grainSizeAnalysis: StSoilGrainAnalysis,
  sieveAnalysis: StAggregateSieveAnalysis,
  moistureContent: StSoilMoistureContent,
  organicContent: StSoilOrganicContent,
  passing200Sieve: StSoilPassing200Sieve,
  proctor: StSoilProctorTest,
  proctorWorksheet: StSoilProctorWorksheet,
  sampleManagement: Sample,
  atterbergLimits: StSoilAtterbergLimit,
  atterbergLimitWorksheet: StSoilAtterbergLimitWorksheet,
  absorptionAndSpecificGravity: StSoilAbsorptionSpecificGravity,
  grainAnalysisWorksheet: StSoilGrainAnalysisWorksheet,
  stSoilRelativeDensity: StSoilRelativeDensity,
  hydraulicConductivity: StHydraulicConductivityTest,
  hydraulicConductivityWorkSheet: StHydraulicConductivityWorkSheet,
  observation: Observation,
  concreteTemperature: StConcreteTemperature,
  ctLog: CTLog,
  reportList: ProjectReportList,
  cutMaster: PvCutMaster,
  cutInformation: PvCutInformation,
  organicContentWorksheet: StSoilOrganicContentWorksheet,
  clsmPlacement: PvCLSMPlacement,
  hydrometer: StSoilGrainAnalysisHydrometerReadings,
  testLogPrefixConfig: TestLogPrefixConfig,
  sampleSpecimen: SampleSpecimen,
  soilUCSSAC: StSoilUCSSAC,
};
