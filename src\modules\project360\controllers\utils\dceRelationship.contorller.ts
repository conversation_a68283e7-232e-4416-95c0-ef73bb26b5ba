import { Request, Response } from 'express';
import dceRelationshipModel from '../../models/utils/dceRelationship.model';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import successResponse from '../../../../shared/middlewares/response/successResponse.middleware';
import getDataByDceAndTableId from '../../../../shared/utilities/dce/getDataByDceAndTableId';

class DCERelationshipController {
  findByDceAndTableId = async (req: Request, res: Response) => {
    try {
      const { projectId, dceId, tableId } = req.params;
      const data = await dceRelationshipModel.getRelationByDceAndTableId(dceId, tableId, projectId);
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      let finalData: any[] = [];
      await Promise.all(
        data.map(async (item) => {
          if (item?.relationshipType?.name) {
            let relationshipName = item?.relationshipType?.name;
            const tableIdToFetch =
              item.sourceDataId !== tableId ? item.sourceDataId : item.targetDataId;
            if (tableIdToFetch == item.sourceDataId) {
              relationshipName = item?.relationshipType?.sourceLabel;
              const values = await getDataByDceAndTableId(
                item?.sourceDCEId || '',
                tableIdToFetch || ''
              );
              delete item.targetDataId;
              delete item.targetDCEId;
              (item as any).tableValues = values;
              (item as any).dceId = item.sourceDCEId;
              (item as any).dce = item.sourceDCE;
              delete item.sourceDCEId;
              delete item.sourceDataId;
              delete item.sourceDCE;
            } else if (tableIdToFetch == item.targetDataId) {
              relationshipName = item?.relationshipType?.targetLabel;
              const values = await getDataByDceAndTableId(
                item?.targetDCEId || '',
                tableIdToFetch || ''
              );
              delete item.sourceDataId;
              delete item.sourceDCEId;
              (item as any).tableValues = values;
              (item as any).dceId = item.targetDCEId;
              (item as any).dce = item.targetDCE;
              delete item.targetDCEId;
              delete item.targetDCE;
            }
            const checkValue = finalData.find((value) => value.name == relationshipName);
            if (checkValue) {
              delete item.relationshipType;
              checkValue.relationship.push(item);
              finalData = finalData.map((datas) => {
                if (datas.name == relationshipName) {
                  return checkValue;
                }
                return datas;
              });
            } else {
              delete item.relationshipType;
              finalData.push({ name: relationshipName, relationship: [item] });
            }
          }
        })
      );
      return successResponse(finalData, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
  findByDceAndRelationshipType = async (req: Request, res: Response) => {
    try {
      const { projectId, dceId, relationshipTypeId } = req.params;
      const data = await dceRelationshipModel.getRelationByDceAndRelationshipType(
        dceId,
        relationshipTypeId,
        projectId
      );
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      let finalData: any[] = [];
      await Promise.all(
        data.map(async (item) => {
          if (item?.relationshipType?.name) {
            let relationshipName = item?.relationshipType?.name;
            const tableIdToFetch = item.sourceDCEId == dceId ? item.sourceDCEId : item.targetDCEId;
            if (tableIdToFetch == item.sourceDCEId) {
              relationshipName = item?.relationshipType?.sourceLabel;
              const values = await getDataByDceAndTableId(
                item?.sourceDCEId || '',
                tableIdToFetch || ''
              );
              delete item.targetDataId;
              delete item.targetDCEId;
              (item as any).tableValues = values;
              (item as any).dceId = item.sourceDCEId;
              (item as any).dce = item.sourceDCE;
              delete item.sourceDCEId;
              delete item.sourceDataId;
              delete item.sourceDCE;
            } else if (tableIdToFetch == item.targetDCEId) {
              relationshipName = item?.relationshipType?.targetLabel;
              const values = await getDataByDceAndTableId(
                item?.targetDCEId || '',
                tableIdToFetch || ''
              );
              delete item.sourceDataId;
              delete item.sourceDCEId;
              (item as any).tableValues = values;
              (item as any).dceId = item.targetDCEId;
              (item as any).dce = item.targetDCE;
              delete item.targetDCEId;
              delete item.targetDCE;
            }
            const checkValue = finalData.find((value) => value.name == relationshipName);
            if (checkValue) {
              delete item.relationshipType;
              checkValue.relationship.push(item);
              finalData = finalData.map((datas) => {
                if (datas.name == relationshipName) {
                  return checkValue;
                }
                return datas;
              });
            } else {
              delete item.relationshipType;
              finalData.push({ name: relationshipName, relationship: [item] });
            }
          }
        })
      );
      return successResponse(finalData, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  findByDcesAndProject = async (req: Request, res: Response) => {
    try {
      const { projectId } = req.params;
      const { dceIds } = req.query;
      const data = await dceRelationshipModel.getRelationByDceIds(projectId, dceIds as string);
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  // * Sample test related methods

  getSampleTestRelation = async (req: Request, res: Response) => {
    try {
      const { sampleId } = req.params;
      const data = await dceRelationshipModel.getRelationBySampleId(sampleId);
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getSampleTestSummary = async (req: Request, res: Response) => {
    try {
      const { sampleId } = req.params;
      const data = await dceRelationshipModel.getSampleTestsBySampleId(sampleId);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  getSampleTestRelationTestData = async (req: Request, res: Response) => {
    try {
      const { sampleId } = req.params;
      const data = await dceRelationshipModel.getRelationBySampleIdWithTestDetails(sampleId);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  addSampleTestRelation = async (req: Request, res: Response) => {
    try {
      const { sampleId, sampleTestDetails } = req.body;
      const createdUserId = (req as any).user.id;
      const createdUserName = (req as any).user.name;
      const data = await dceRelationshipModel.addSampleRelation(
        sampleId,
        sampleTestDetails,
        createdUserId,
        createdUserName
      );
      const message = data.length > 0 ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  editSampleTestRelation = async (req: Request, res: Response) => {
    try {
      const { sampleId, sampleTestDetails } = req.body;
      const { id } = req.params;
      const createdUserId = (req as any).user.id;
      const createdUserName = (req as any).user.name;
      const data = await dceRelationshipModel.editSampleRelation(
        sampleId,
        id,
        sampleTestDetails,
        createdUserId,
        createdUserName
      );
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };

  deleteSampleTestRelation = async (req: Request, res: Response) => {
    try {
      const { id, sampleId } = req.params;
      const data = await dceRelationshipModel.deleteSampleRelation(sampleId, id);
      const message = data ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return successResponse(data, message, res);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default DCERelationshipController;
