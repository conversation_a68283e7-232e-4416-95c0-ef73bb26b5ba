import { NextFunction, Request, Response } from 'express';
import errorMiddleware from './error/error.middleware';

export const dceConfigEditMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    if ('route' in req.body) {
      delete req.body.route;
    }
    if ('dropdownURL' in req.body) {
      delete req.body.dropdownURL;
    }
    if ('entity' in req.body) {
      delete req.body.entity;
    }
    next();
  } catch (error) {
    return errorMiddleware(error, 500, res, req);
  }
};
