import { get<PERSON>anager, <PERSON>Null, Not } from 'typeorm';
import { Clsm } from '../../../../entities/p_gen/Clsm';
import convertStationToNumber from '@utils/custom/convertStationToNumber';
class ClsmModel {
  private ClsmRepositry = getManager().getRepository(Clsm);

  getClsmsByStations = async (start: number, end: number) => {
    try {
      const startValue = start;
      const endValue = end;
      const clsms = await this.ClsmRepositry.find({
        where: [
          {
            stationStart: Not(IsNull()),
            stationEnd: Not(IsNull()),
            topElevation: Not(IsNull()),
            bottomElevation: Not(IsNull()),
            clsm: Not(IsNull()),
            clsmTop: Not(IsNull()),
          },
        ],
      });

      return clsms.filter((clsm) => {
        const clsmStartValue = convertStationToNumber(clsm.stationStart!);
        const clsmEndValue = convertStationToNumber(clsm.stationEnd!);

        return (
          (clsmStartValue <= endValue && clsmEndValue >= startValue) || // Case 1: Panel overlaps the given range
          (clsmStartValue >= startValue && clsmStartValue <= endValue) || // Case 2: Panel's start is within the range
          (clsmEndValue >= startValue && clsmEndValue <= endValue) // Case 3: Panel's end is within the range
        );
      });
    } catch (error) {
      throw error;
    }
  };
}

export default ClsmModel;
