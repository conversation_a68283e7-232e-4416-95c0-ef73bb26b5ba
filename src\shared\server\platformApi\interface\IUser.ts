import { IRole } from './IAppicaltionRole';
import { IOrganization } from './IOrganization';

export interface IUser {
  id: string;
  organizationId: string | null;
  organization?: IOrganization;
  subscriberId: string | null;
  email: string;
  passwordHash: string | null;
  firstName: string | null;
  lastName: string | null;
  dateOfBirth?: Date | null;
  phone?: string | null;
  gender?: string | null;
  roleId: string | null;
  role?: IRole;
  lastLogin?: Date | null;
  currentLogin?: Date | null;
  accountStatus: string | null;
  country?: string | null;
  address?: string | null;
  city?: string | null;
  state?: string | null;
  postalCode?: string | null;
  createdAt?: Date | null;
  createdBy?: string | null;
  createdUserId?: string | null;
  updatedAt?: Date | null;
  updatedBy: string | null;
  isDelete?: boolean;
  customMessage?: string;
  token: string | null;
  emailVerified: boolean;
}
