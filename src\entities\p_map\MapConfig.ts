import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

import { Project } from '../p_gen/Project';
import ProjectLogo from '../p_gen/ProjectLogo';

@Entity({ schema: 'p_map' })
export class MapConfig {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  // general settings
  @Column({ type: 'decimal', nullable: true })
  generalLng?: number;

  @Column({ type: 'decimal', nullable: true })
  generalLat?: number;

  @Column({ nullable: true })
  generalZoom?: string;

  @Column({ nullable: true })
  viewsEnabled?: string;

  // map settings
  @Column({ type: 'decimal', nullable: true })
  mapLng?: number;

  @Column({ type: 'decimal', nullable: true })
  mapLat?: number;

  @Column({ nullable: true })
  mapZoom?: string;

  @Column({ nullable: true })
  mapStyle?: string;

  @Column({ nullable: true })
  enableRuler?: boolean;

  @Column({ nullable: true })
  rulerUnits?: string;

  @Column({ nullable: true })
  rulerColor?: string;

  @Column({ nullable: true, default: false })
  enableTiles?: boolean;

  //profile settings
  @Column({ type: 'decimal', nullable: true })
  profileLng?: number;

  @Column({ type: 'decimal', nullable: true })
  profileLat?: number;

  @Column({ nullable: true })
  profileZoom?: string;

  @Column({ type: 'decimal', nullable: true })
  arrowSliderDistance?: number;

  @Column({ type: 'decimal', nullable: true })
  profileDisplayLength?: number;

  @Column({ nullable: true, default: false })
  enablePDFDownload?: boolean;

  @Column({ nullable: true })
  defaultAlignment?: string;

  @Column({ nullable: true })
  defaultStation?: string;

  //progress settings
  @Column({ type: 'decimal', nullable: true })
  progressLng?: number;

  @Column({ type: 'decimal', nullable: true })
  progressLat?: number;

  @Column({ nullable: true })
  progressZoom?: string;

  //miscaleous
  @Column({ type: 'decimal', nullable: true })
  projectBoundaryLength?: number;

  @Column({ nullable: true })
  srid?: number;

  @Column({ type: 'decimal', nullable: true })
  projectRadius?: number;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  logoId?: string;

  @ManyToOne(() => ProjectLogo, { nullable: true })
  @JoinColumn({ name: 'logoId' })
  logo?: ProjectLogo;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
