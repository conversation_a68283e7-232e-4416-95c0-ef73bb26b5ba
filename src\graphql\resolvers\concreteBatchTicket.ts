import { getManager, Between } from 'typeorm';
import { ConcreteBatchTicket } from '@entities/p_cs/ConcreteBatchTicket';

export const concreteBatchTicketResolvers = {
  Query: {
    batchTicketsByProject: async (
      _: any,
      {
        projectId,
        startDate,
        endDate,
        dateField = 'batchedDateTime',
      }: {
        projectId: string;
        startDate?: string;
        endDate?: string;
        dateField?: string;
      }
    ) => {
      try {
        const repository = getManager().getRepository(ConcreteBatchTicket);
        const whereClause: any = {
          projectId,
          isDelete: false,
        };

        // Add date filtering if both dates are provided
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          whereClause[dateField] = Between(start, end);
        }

        const results = await repository.find({
          where: whereClause,
          relations: ['concreteMixDesign'],
        });

        return results;
      } catch (error) {
        console.error('Error fetching batch ticket data:', error);
        throw new Error('Failed to fetch batch ticket data');
      }
    },
  },
};
