import { getManager } from 'typeorm';
import { WorkPackageActivity } from '../../../../entities/p_gen/WorkPackageActivity';
import { WorkPackage } from '../../../../entities/p_gen/WorkPackage';
import { ProjectEquipment } from '../../../../entities/p_gen/ProjectEquipment';
import { DataCaptureElements } from '../../../../entities/p_meta/DataCaptureElements';
import { WorkPackageActivityTechnician } from '../../../../entities/p_gen/WorkPackageActivityTechnician';

class WorkPackageModel {
  async getNextWorkPackageNo(): Promise<string> {
    try {
      const result = await getManager()
        .getRepository(WorkPackage)
        .createQueryBuilder('workPackage')
        .select('MAX(CAST(SUBSTRING(workPackage.workPackageNo, 4) AS INTEGER))', 'maxNumber')
        .where('workPackage.workPackageNo ~ :format', { format: '^WP-[0-9]+$' })
        .andWhere('workPackage.isDelete = :isDelete', { isDelete: false })
        .getRawOne();

      const maxNumber = result?.maxNumber ?? 0;
      const nextNumber = maxNumber + 1;
      const nextWorkPackageNo = `WP-${String(nextNumber).padStart(5, '0')}`;

      return nextWorkPackageNo;
    } catch (error) {
      console.error('Error generating workPackageNo:', error);
      throw error;
    }
  }

  async getById(id: string): Promise<WorkPackage | null> {
    try {
      const workPackageRepository = getManager().getRepository(WorkPackage);
      const workPackage = await workPackageRepository.findOne({
        where: { id: id, isDelete: false },
      });
      return workPackage;
    } catch (error) {
      throw error;
    }
  }

  async add(newWorkPackage: Partial<WorkPackage>, newWorkPackageActivity: any[]) {
    try {
      const entityManager = getManager();
      const final = await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const workPackageRepository = transactionalEntityManager.getRepository(WorkPackage);
          // const userRepository = transactionalEntityManager.getRepository(User);
          const equipmentRepository = transactionalEntityManager.getRepository(ProjectEquipment);
          const dceRepository = transactionalEntityManager.getRepository(DataCaptureElements);
          const workPackageActivityRepository =
            transactionalEntityManager.getRepository(WorkPackageActivity);

          // If workPackageNo is not provided, generate a new one
          if (!newWorkPackage.workPackageNo) {
            try {
              newWorkPackage.workPackageNo = await this.getNextWorkPackageNo();
            } catch (error) {
              console.error('Error generating workPackageNo:', error);
            }
          }

          const addedWorkPackage = await workPackageRepository.save(newWorkPackage);

          await Promise.all(
            newWorkPackageActivity.map(async (value) => {
              const inputItem: Partial<WorkPackageActivity> = { ...value.workPackageActivity };
              // const techniciansData = await userRepository.findByIds(value.technicianIds);
              const equipmentData = await equipmentRepository.findByIds(value.equipmentIds);
              const dceData = await dceRepository.findByIds(value.dceIds);

              // inputItem.technician = [...techniciansData];
              inputItem.equipment = [...equipmentData];
              inputItem.dce = [...dceData];
              inputItem.workPackageId = addedWorkPackage.id;
              inputItem.updatedBy = addedWorkPackage.updatedBy;
              inputItem.createdBy = addedWorkPackage.createdBy;
              const AddWorkPackageActivity = await workPackageActivityRepository.save(inputItem);

              const techniciansData = value.technicianIds.map((value: string) => {
                return { workPackageActivityId: AddWorkPackageActivity.id, userId: value };
              });

              await transactionalEntityManager
                .getRepository(WorkPackageActivityTechnician)
                .save(techniciansData);
            })
          );

          const result = await workPackageRepository.findOne({
            where: { id: addedWorkPackage.id },
            relations: [
              'site',
              'workPackageActivity',
              'workPackageActivity.activity',
              'workPackageActivity.dce',
              'workPackageActivity.technician',
              'workPackageActivity.equipment',
            ],
          });
          return result;
        } catch (error) {
          throw error;
        }
      });
      return final;
    } catch (error) {
      throw error;
    }
  }

  async edit(newWorkPackage: Partial<WorkPackage>, newWorkPackageActivity: any[], id: string) {
    try {
      const entityManager = getManager();
      const final = await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const workPackageRepository = transactionalEntityManager.getRepository(WorkPackage);
          // const userRepository = transactionalEntityManager.getRepository(User);
          const equipmentRepository = transactionalEntityManager.getRepository(ProjectEquipment);
          const dceRepository = transactionalEntityManager.getRepository(DataCaptureElements);
          const workPackageActivityRepository =
            transactionalEntityManager.getRepository(WorkPackageActivity);

          await workPackageRepository.update(id, newWorkPackage);

          await Promise.all(
            newWorkPackageActivity.map(async (value) => {
              const inputItem: Partial<WorkPackageActivity> = { ...value.workPackageActivity };
              // const techniciansData = await userRepository.findByIds(value.technicianIds);
              const equipmentData = await equipmentRepository.findByIds(value.equipmentIds);
              const dceData = await dceRepository.findByIds(value.dceIds);

              // inputItem.technician = [...techniciansData];
              inputItem.equipment = [...equipmentData];
              inputItem.dce = [...dceData];
              inputItem.workPackageId = id;
              inputItem.updatedBy = newWorkPackage.updatedBy;

              await workPackageActivityRepository.save(inputItem);
            })
          );

          const result = await workPackageRepository.findOne({
            where: { id: id },
            relations: [
              'site',
              'workPackageActivity',
              'workPackageActivity.activity',
              'workPackageActivity.dce',
              'workPackageActivity.technician',
              'workPackageActivity.equipment',
            ],
          });
          return result;
        } catch (error) {
          throw error;
        }
      });
      return final;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectId(projectId: string, fromDate?: string, toDate?: string) {
    try {
      const workPackageRepository = getManager().getRepository(WorkPackage);

      if (fromDate && toDate) {
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        if (fromDateObj < toDateObj) {
          const result = await workPackageRepository
            .createQueryBuilder('workPackage')
            .distinct()
            .leftJoinAndSelect('workPackage.site', 'site')
            .leftJoinAndSelect('workPackage.project', 'project')
            .leftJoinAndSelect('workPackage.workPackageActivity', 'workPackageActivity')
            .leftJoinAndSelect('workPackageActivity.activity', 'activity')
            .leftJoinAndSelect('workPackageActivity.dce', 'dce')
            .leftJoinAndSelect('workPackageActivity.technician', 'technician')
            .leftJoinAndSelect('workPackageActivity.equipment', 'equipment')
            .leftJoinAndSelect('workPackageActivity.eventLog', 'eventLog')
            .select([
              'workPackage',
              'project.name',
              'project.id',
              'site',
              'workPackageActivity',
              'activity',
              'technician',
              'dce.id',
              'dce.entity',
              'dce.name',
              'equipment',
              'eventLog',
            ])
            .where('workPackage.projectId = :projectId', { projectId })
            .andWhere('workPackage.isDelete = :delete', { delete: false })
            .andWhere('workPackage.isDelete = :delete', { delete: false })
            .andWhere('"endDate" BETWEEN :startDate AND :endDate', { fromDateObj, toDateObj })
            .andWhere('"startDate" BETWEEN :startDate AND :endDate', { fromDateObj, toDateObj })
            .getMany();
          return result;
        } else {
          throw new Error('Invalid fromDate and toDate');
        }
      }
      const result = await workPackageRepository
        .createQueryBuilder('workPackage')
        .leftJoinAndSelect('workPackage.site', 'site')
        .leftJoinAndSelect('workPackage.project', 'project')
        .leftJoinAndSelect('workPackage.workPackageActivity', 'workPackageActivity')
        .leftJoinAndSelect('workPackageActivity.activity', 'activity')
        .leftJoinAndSelect('workPackageActivity.dce', 'dce')
        .leftJoinAndSelect('workPackageActivity.technician', 'technician')
        .leftJoinAndSelect('workPackageActivity.equipment', 'equipment')
        .leftJoinAndSelect('workPackageActivity.eventLog', 'eventLog')
        .select([
          'workPackage',
          'project.name',
          'project.id',
          'site',
          'workPackageActivity',
          'activity',
          'technician',
          'dce.id',
          'dce.entity',
          'dce.name',
          'equipment',
          'eventLog',
        ])
        .where('workPackage.projectId = :projectId', { projectId })
        .andWhere('workPackage.isDelete = :delete', { delete: false })
        .getMany();
      return result;
    } catch (error) {
      throw error;
    }
  }

  async getByUserId(userId: string, fromDate?: string, toDate?: string) {
    try {
      const workPackageRepository = getManager().getRepository(WorkPackage);
      let final: WorkPackage[] = [];
      const result = await workPackageRepository
        .createQueryBuilder('workPackage')
        .leftJoinAndSelect('workPackage.site', 'site')
        .leftJoinAndSelect('workPackage.project', 'project')
        .leftJoinAndSelect('workPackage.workPackageActivity', 'workPackageActivity')
        .leftJoinAndSelect('workPackageActivity.activity', 'activity')
        .leftJoinAndSelect('workPackageActivity.dce', 'dce')
        .leftJoinAndSelect('workPackageActivity.technician', 'technician')
        .leftJoinAndSelect('workPackageActivity.equipment', 'equipment')
        .leftJoinAndSelect('workPackageActivity.eventLog', 'eventLog')
        .select([
          'workPackage',
          'project.name',
          'project.id',
          'site',
          'workPackageActivity',
          'activity',
          'technician',
          'dce.id',
          'dce.entity',
          'dce.name',
          'equipment',
          'eventLog',
        ])
        .where('technician.userId = :id', { id: userId })
        .andWhere('workPackage.isDelete = :delete', { delete: false })
        .getMany();

      final = result;
      if (fromDate && toDate) {
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        if (fromDateObj < toDateObj) {
          final = result.filter(
            (value) =>
              value.startDate &&
              new Date(value.startDate) > fromDateObj &&
              new Date(value.startDate) < toDateObj
          );
        } else {
          throw new Error('Invalid fromDate and toDate');
        }
      }
      return final;
    } catch (error) {
      throw error;
    }
  }

  async getByUserIdForMobile(userId: string, fromDate?: string, toDate?: string) {
    try {
      const workPackageRepository = getManager().getRepository(WorkPackage);
      let final: WorkPackage[] = [];
      const result = await workPackageRepository
        .createQueryBuilder('workPackage')
        .leftJoinAndSelect('workPackage.site', 'site')
        .leftJoinAndSelect('workPackage.project', 'project')
        .leftJoinAndSelect('workPackage.workPackageActivity', 'workPackageActivity')
        .leftJoinAndSelect('workPackageActivity.activity', 'activity')
        .leftJoinAndSelect('workPackageActivity.dce', 'dce')
        .leftJoinAndSelect('workPackageActivity.technician', 'technician')
        .leftJoinAndSelect('workPackageActivity.equipment', 'equipment')
        .leftJoinAndSelect('workPackageActivity.eventLog', 'eventLog')
        .select([
          'workPackage',
          // 'project.name',
          // 'project.id',
          // 'site',
          'workPackageActivity',
          'activity.name',
          'activity.description',
          'technician.userId',
          'dce.id',
          'dce.isMobile',
          // 'dce.entity',
          // 'dce.name',
          'equipment.id',
          // 'eventLog',
        ])
        .where('technician.userId = :id', { id: userId })
        .andWhere('workPackage.isDelete = :delete', { delete: false })
        .getMany();

      final = result;
      if (fromDate && toDate) {
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        if (fromDateObj < toDateObj) {
          final = result
            .map((value) => {
              value.workPackageActivity?.map((item) => {
                item.technician?.map((techUser) => {
                  delete techUser.user;
                  return techUser;
                });
                if (item?.activity) {
                  (item.activity as any).dce = item.dce || [];
                }
                delete item.dce;
                return item;
              });
              return value;
            })
            .filter(
              (value) =>
                value.startDate &&
                new Date(value.startDate) > fromDateObj &&
                new Date(value.startDate) < toDateObj
            );
        } else {
          throw new Error('Invalid fromDate and toDate');
        }
      }
      return final;
    } catch (error) {
      throw error;
    }
  }

  async getProjectIdsByWorkpackage(
    userId: string,
    recent?: number,
    fromDate?: Date,
    toDate?: Date
  ) {
    try {
      const workPackageRepository = getManager().getRepository(WorkPackage);
      const projectIds: string[] = [];

      const result = await workPackageRepository
        .createQueryBuilder('workPackage')
        .leftJoinAndSelect('workPackage.project', 'project')
        .leftJoinAndSelect('workPackage.workPackageActivity', 'workPackageActivity')
        .leftJoinAndSelect('workPackageActivity.technician', 'technician')
        .select(['workPackage'])
        .where('technician.userId = :id', { id: userId })
        .where('workPackage.isDelete = :id', { id: false })
        .getMany();

      if (fromDate && toDate) {
        const fromDateObj = new Date(fromDate);
        const toDateObj = new Date(toDate);
        if (fromDateObj < toDateObj) {
          result.map((value) => {
            if (
              value.startDate &&
              new Date(value.startDate) > fromDateObj &&
              new Date(value.startDate) < toDateObj &&
              value.projectId
            ) {
              if (projectIds.length == recent) {
                return projectIds;
              }
              if (!projectIds.includes(value.projectId)) {
                projectIds.push(value.projectId);
              }
            }
          });
          return projectIds;
        } else {
          throw new Error('Invalid fromDate and toDate');
        }
      }
    } catch (error) {
      throw error;
    }
  }
}

const workPackageModel = new WorkPackageModel();
export default workPackageModel;
