import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import soilGrainAnalysisController from '../../controllers/cs/soilGrainAnalysis.controller';
import { StSoilGrainAnalysis } from '../../../../entities/p_cs/StSoilGrainAnalysis';

const router: Router = express.Router();

// Create a generic router for the User entity
const CrudFunctionController = new CrudController<StSoilGrainAnalysis>(StSoilGrainAnalysis);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/', authenticateToken, soilGrainAnalysisController.create);
router.put('/:id', authenticateToken, (req, res) => CrudFunctionController.update(req, res));
router.get('/:id', authenticateToken, soilGrainAnalysisController.findById);
router.delete('/:id', authenticateToken, (req, res) => CrudFunctionController.softDelete(req, res));

export default router;
