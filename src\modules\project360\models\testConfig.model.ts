import { getManager } from 'typeorm';
import { TestConfiguration } from '../../../entities/p_gen/TestConfig';
import { TestConfigurationRule } from '../../../entities/p_gen/TestConfigRule';

class TestConfigModel {
  constructor() {}
  findByProjectId = async (projectId: string) => {
    try {
      const testConfigData = await getManager()
        .getRepository(TestConfiguration)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['feature', 'material', 'testMethod', 'test', 'structure'],
        });

      return testConfigData;
    } catch (error) {
      throw error;
    }
  };

  findByProjectMaterialFeature = async (
    projectId: string,
    materialId: string,
    featureId: string
  ) => {
    try {

      const testConfigData = await getManager()
        .getRepository(TestConfiguration)
        .findOne({
          where: {
            projectId: projectId,
            materialId: materialId,
            featureId: featureId,
            isDelete: false,
          },
          relations: [
            'feature',
            'material',
            'testMethod',
            'test',
            'structure',
            'test.testVariant',
            'test.testMethod',
          ],
        });

      return testConfigData;
    } catch (error) {
      throw error;
    }
  };
  findByProjectMaterialFeatureTest = async (
    projectId: string,
    materialId: string,
    featureId: string,
    testId: string
  ) => {
    try {
      // return await getManager()
      //   .getRepository(Feature)
      //   .find({ where: { proejctId: projectId } });

      const testConfigData = await getManager()
        .getRepository(TestConfiguration)
        .findOne({
          where: {
            projectId: projectId,
            materialId: materialId,
            featureId: featureId,
            isDelete: false,
            testId: testId,
          },
          relations: [
            'feature',
            'material',
            'testMethod',
            'test',
            'structure',
            'test.testVariant',
            'test.testMethod',
            'rule',
          ],
        });

      return testConfigData;
    } catch (error) {
      throw error;
    }
  };

  findByProjectInOrderId = async (projectId: string) => {
    try {
      const testConfigData = await getManager()
        .getRepository(TestConfiguration)
        .find({
          where: { projectId: projectId, isDelete: false },
          relations: ['feature', 'material', 'testMethod', 'test', 'structure', 'rule'],
        });
      const testMap = new Map();

      // Organize data in the map
      testConfigData.forEach((item) => {
        const { test, ...config } = item;

        // If the test is not already in the map, add it
        if (!testMap.has(test?.id)) {
          testMap.set(test?.id, {
            ...test,
            configurations: [],
          });
        }

        // Add the configuration to the corresponding test's configurations array
        const testObject = testMap.get(test?.id);
        if (testObject) {
          testObject.configurations.push(config);
        }
      });

      // Convert the map values back to an array
      const organizedTestConfigData = Array.from(testMap.values());

      return organizedTestConfigData;
    } catch (error) {
      throw error;
    }
  };
  findById = async (id: string) => {
    try {
      const testConfigData = await getManager()
        .getRepository(TestConfiguration)
        .find({
          where: { id, isDelete: false },
          relations: ['feature', 'material', 'testMethod', 'test', 'structure', 'rule'],
        });
      const testMap = new Map();

      // Organize data in the map
      testConfigData.forEach((item) => {
        const { test, ...config } = item;

        // If the test is not already in the map, add it
        if (!testMap.has(test?.id)) {
          testMap.set(test?.id, {
            ...test,
            configurations: [],
          });
        }

        // Add the configuration to the corresponding test's configurations array
        const testObject = testMap.get(test?.id);
        if (testObject) {
          testObject.configurations.push(config);
        }
      });

      // Convert the map values back to an array
      const organizedTestConfigData = Array.from(testMap.values());

      return organizedTestConfigData;
    } catch (error) {
      throw error;
    }
  };

  addTestConfig = async (
    testConfig: Partial<TestConfiguration>,
    rule?: TestConfigurationRule[]
  ) => {
    try {
      const entityManager = getManager();
      const result = await entityManager.transaction(async (transactionalEntityManager) => {
        const newTestConfig = await transactionalEntityManager.save(TestConfiguration, testConfig);
        if (rule) {
          const newRule = rule.map((value) => {
            if (value) {
              value.testConfigId = newTestConfig.id;
            }
            return value;
          });
          if (newRule && newRule.length > 0) {
            await transactionalEntityManager.save(TestConfigurationRule, newRule);
          }
        }
        const returnData = await transactionalEntityManager.findOne(TestConfiguration, {
          where: { id: testConfig.id },
        });
        return returnData;
      });
      return result;
    } catch (error) {
      throw error;
    }
  };
}

const testConfigModel = new TestConfigModel();
export default testConfigModel;
