import { FindOperator, getManager } from 'typeorm';
import { StSoilNuclearGaugeTest } from '../../../../entities/p_cs/StSoilNuclearGaugeTest';
import { StSoilSandConeTest } from '../../../../entities/p_cs/StSoilSandConeTest';
import { StSoilMoistureContent } from '../../../../entities/p_cs/StSoilMoistureContent';
import { Sample } from '../../../../entities/p_cs/Sample';

const getWhereCondition = (projectId: string, dataControl: any) => {
  if (Array.isArray(dataControl) && dataControl.length > 0) {
    return dataControl;
  }
  const whereCondition: any = {
    isDelete: false,
    projectId,
  };
  return whereCondition;
};

class FieldDensityModel {
  async getNuclearTests(
    projectId: string,
    dataControl: { purposeId: string; approvalStatusId: FindOperator<any> }[]
  ) {
    try {
      const entityManager = getManager();
      const query = entityManager
        .createQueryBuilder(StSoilNuclearGaugeTest, 'nuclear')
        .leftJoinAndSelect('nuclear.projectNuclearGauges', 'gauge')
        .leftJoinAndSelect('nuclear.sample', 'sample')
        .leftJoinAndSelect('nuclear.submittalVersion', 'submittalVersion')
        .leftJoinAndSelect('submittalVersion.submittal', 'submittal')
        .leftJoinAndSelect('submittal.specification', 'specification')
        .leftJoinAndSelect('nuclear.ctLog', 'testNoId')
        .leftJoinAndSelect('nuclear.testResult', 'testResultId')
        .select([
          'nuclear.id',
          'nuclear.testLocation',
          'testResultId.name',
          'nuclear.testedBy',
          'nuclear.dateTested',
          'gauge.gaugeNo',
          'nuclear.easting',
          'nuclear.northing',
          'testNoId.testNo',
          'sample.testNo',
          'nuclear.requiredCompaction',
          'nuclear.maximumLabDensity',
          'nuclear.optimumMoistureContent',
          'nuclear.elevation',
          'nuclear.wetDensity',
          'nuclear.moistureContent',
          'nuclear.dryDensity',
          'submittalVersion.version',
          'submittalVersion.id',
          'submittal.id',
          'submittal.itemNo',
          'specification.subSpecCode',
          'nuclear.approvalStatusId',
          'nuclear.purposeId',
        ])
        .where(getWhereCondition(projectId, dataControl));

      return query.getMany();
    } catch (error) {
      throw error;
    }
  }

  async getSandConeTests(projectId: string, dataControl: any) {
    try {
      const entityManager = getManager();
      const query = entityManager
        .createQueryBuilder(StSoilSandConeTest, 'sandCone')
        .leftJoinAndSelect('sandCone.sandConeInfo', 'sandConeInfo')
        .leftJoinAndSelect('sandCone.sample', 'sample')
        .leftJoinAndSelect('sandCone.submittalVersion', 'submittalVersion')
        .leftJoinAndSelect('submittalVersion.submittal', 'submittal')
        .leftJoinAndSelect('submittal.specification', 'specification')
        .leftJoinAndSelect('sandCone.ctLog', 'testNoId')
        .leftJoinAndSelect('sandCone.testResult', 'testResultId')
        .select([
          'sandCone.id',
          'sandCone.testLocation',
          'sandCone.testedBy',
          'sandCone.dateTested',
          'sandConeInfo.name',
          'sandCone.easting',
          'sandCone.northing',
          'testNoId.testNo',
          'sample.testNo',
          'sandCone.requiredCompaction',
          'sandCone.maximumLabDensity',
          'sandCone.optimumMoistureContent',
          'sandCone.elevation',
          'sandCone.IPWetDensity',
          'sandCone.moistureContent',
          'sandCone.IPDryDensity',
          'testResultId.name',
          'submittalVersion.version',
          'submittalVersion.id',
          'submittal.id',
          'submittal.itemNo',
          'specification.subSpecCode',
          'sandCone.approvalStatus',
          'sandCone.purposeId',
        ])
        .where(getWhereCondition(projectId, dataControl));
      return query.getMany();
    } catch (error) {
      throw error;
    }
  }

  async getMoistureContentTests(projectId: string, dataControl: any) {
    try {
      const entityManager = getManager();

      const sandConeTests = await entityManager
        .createQueryBuilder(StSoilSandConeTest, 'sandCone')
        .leftJoinAndSelect('sandCone.ctLog', 'testNoId')
        .select('testNoId.testNo')
        .where('sandCone.isDelete = :isDelete', { isDelete: false })
        .andWhere('sandCone.projectId = :projectId', { projectId })
        .getMany();

      const externalSampleNos = sandConeTests.map((test) => test.ctLog?.testNo).filter(Boolean);

      const nuclearGaugeTests = await entityManager
        .createQueryBuilder(StSoilNuclearGaugeTest, 'nuclear')
        .select('DATE(nuclear.dateTested)', 'dateTested')
        .where('nuclear.isDelete = :isDelete', { isDelete: false })
        .andWhere('nuclear.projectId = :projectId', { projectId })
        .distinct(true)
        .getRawMany();

      const testDates = nuclearGaugeTests.map((test) => test.dateTested);

      const samples = await entityManager
        .createQueryBuilder(Sample, 'sample')
        .select('sample.id')
        .where('DATE(sample.sampledDate) IN (:...testDates)', { testDates })
        .andWhere('sample.isDelete = :isDelete', { isDelete: false })
        .andWhere('sample.projectId = :projectId', { projectId })
        .getRawMany();

      const sampleIds = samples.map((sample) => sample.sample_id);

      const query = entityManager
        .createQueryBuilder(StSoilMoistureContent, 'moisture')
        .leftJoinAndSelect('moisture.submittalVersion', 'submittalVersion')
        .leftJoinAndSelect('moisture.sample', 'sample')
        .leftJoinAndSelect('submittalVersion.submittal', 'submittal')
        .leftJoinAndSelect('submittal.specification', 'specification')
        .leftJoinAndSelect('moisture.ctLog', 'testNoId')
        .select([
          'moisture.id',
          'testNoId.testNo',
          'moisture.testedBy',
          'moisture.dateTested',
          'sample.testNo',
          'sample.easting',
          'sample.northing',
          'moisture.moistureContent',
          'submittalVersion.version',
          'submittalVersion.id',
          'submittal.id',
          'submittal.itemNo',
          'specification.subSpecCode',
          'moisture.approvalStatus',
          'moisture.purposeId',
        ]);

      if (sampleIds && sampleIds.length > 0) {
        query.andWhere('moisture.sampleId IN (:...sampleIds)', { sampleIds });
      }
      if (externalSampleNos && externalSampleNos.length > 0) {
        query.andWhere('testNoId.testNo NOT IN (:...externalSampleNos)', { externalSampleNos });
      }
      query.andWhere(getWhereCondition(projectId, dataControl));

      return query.getMany();
    } catch (error) {
      throw error;
    }
  }
}

const fieldDensityModel = new FieldDensityModel();
export default fieldDensityModel;
