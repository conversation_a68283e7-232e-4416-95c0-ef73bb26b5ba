import { CTLog } from '../../../entities/p_gen/CTLog';
import { v4 as uuidv4 } from 'uuid';
import ctLogModel from '../../../modules/project360/models/gen/ctLog.model';
import testLogPrefixModel from '../../../modules/project360/models/gen/testLogPrefix.model';
import dceModel from '@models/meta/dce.model';

/**
 * Creates a CT Log entry for a nuclear gauge or sand cone test
 * @param data The test data
 * @returns A promise that resolves to an object containing the created CT Log ID and test number
 */
export async function createTestLogEntry(data: any): Promise<string | null> {
  try {
    if (!data.projectId || !data.purposeId) {
      console.error('Missing projectId or purposeId for CT Log creation');
      return null;
    }

    // Get the test log prefix based on project and purpose
    const testLogPrefix = await testLogPrefixModel.getByTestLogPrefixByProjectIdAndPurposeId(
      data.projectId,
      data.purposeId
    );

    // Get the next test number
    const currentTestNo = await ctLogModel.getTestNoByProjectIdAndPurposeId(
      data.projectId,
      data.purposeId
    );
    const nextTestNo = currentTestNo + 1;

    // Format the test number with padding
    const formattedTestNo = `${testLogPrefix}-${String(nextTestNo).padStart(5, '0')}`;

    // Create the CT Log entry
    const ctLogId = uuidv4();
    const ctLogData = {
      id: ctLogId,
      projectId: data.projectId,
      testNo: formattedTestNo,
      location: data.testLocation || data.location,
      materialId: data.materialId,
      materialTypeId: data.materialTypeId,
      purposeId: data.purposeId,
      sampleDate: data.dateTested || data.sampleDate || new Date(),
      comments: data.comments || '',
      latitude: data.latitude,
      longitude: data.longitude,
      easting: data.easting,
      northing: data.northing,
      elevation: data.elevation,
      structureId: data.structureId,
      featureId: data.featureId,
      generalProjectAreaId: data.generalProjectAreaId,
      createdBy: data.createdBy,
      updatedBy: data.updatedBy,
      createdUserId: data.createdUserId,
    };

    await dceModel.addDataByEntity(ctLogData, CTLog, data.createdUserId, 'ctLog');

    return ctLogId;
  } catch (error) {
    console.error('Error creating CT Log entry:', error);
    return null;
  }
}
