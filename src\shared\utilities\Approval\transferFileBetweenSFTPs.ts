import SFTP from 'ssh2-sftp-client';
import { Readable } from 'stream';
import { AzureBlobStorageService } from '../sftpBlobAndFiles/azureBlobStorageService';
import projectModel from '../../../modules/project360/models/project.model';
import { verifyTokenForSftpPassword } from '../jwt';
import SFTPPurposeConfigModel from '../../../modules/project360/models/gen/sftpPurposeConfig.model';

export async function transferFileBetweenSFTPs(
  sourceFilePath: string,
  destinationFilePath: string,
  projectId: string,
  purposeId: string
): Promise<void> {
  const destinationSftp = new SFTP();

  try {
    let sourceFileStream: Readable;
    // Get the file from the source SFTP server

    const config = await destinationConfig(purposeId, projectId);
    if (!config) {
      console.log('No SFTP setup for this purpose');
      return;
    }
    const azureBlobStorageService = new AzureBlobStorageService(
      process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
      process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
    );

    const sourceFileData = await azureBlobStorageService.downloadFile(projectId, sourceFilePath);

    if (typeof sourceFileData === 'string' || Buffer.isBuffer(sourceFileData)) {
      // If it's a string or buffer, create a readable stream
      sourceFileStream = new Readable();
      sourceFileStream.push(sourceFileData);
      sourceFileStream.push(null); // Signal the end of the stream
    } else {
      // If it's a writable stream, you may need to handle it accordingly
      console.error('Unsupported file data type:', typeof sourceFileData);
      return;
    }
    await destinationSftp.connect(config);
    await destinationSftp.put(sourceFileStream, destinationFilePath);
    // Connect to the destination SFTP server

    // Put the file onto the destination SFTP server

    console.log('File transfer successful!');
    return;
  } catch (err) {
    console.error('Error transferring file:', (err as any).message);
    throw err;
  } finally {
    // Close the SFTP connections
    await destinationSftp.end();
  }
}

const destinationConfig = async (purpose: string, projectId: string) => {
  try {
    const sftpPurposeConfigModel = new SFTPPurposeConfigModel();
    const container = await sftpPurposeConfigModel.getByProjectId(purpose, projectId);
    if (!container) {
      throw new Error('SFTP Container not found for this purpose');
    }
    const parts = process.env.A2_USERNAME?.split('.', 2);
    if (!parts || parts.length < 0) {
      throw new Error('Invalid SFTP Username');
    }
    const username = `${parts[0]}.${container}.${parts[1]}`;
    const config: { host: string; username: string; port: number; password: string } = {
      host: process.env.A2_HOST || '',
      username: username,
      password: process.env.A2_PASSWORD || '',
      port: process.env.A2_PORT ? Number(process.env.A2_PORT) : 22,
    };
    return config;
  } catch (error) {
    throw error;
  }
};

export async function ensureFolderExists(path: string, projectId: string, purposeId: string) {
  const sftp = new SFTP();
  try {
    const config = await destinationConfig(purposeId, projectId);
    if (!config) {
      console.log('No SFTP setup for this purpose');
      return;
    }
    // Connect to the SFTP server (replace host, username, and password with actual values)
    await sftp.connect(config);

    const folders = path.split('/').filter((folder) => folder); // Split path and remove empty strings
    let currentPath = '';

    // Iterate through each folder in the path
    for (const folder of folders) {
      currentPath += `/${folder}`;
      const folderExists = await sftp.exists(currentPath);

      // Create the folder if it doesn't exist
      if (!folderExists) {
        await sftp.mkdir(currentPath);
        console.log(`Folder '${currentPath}' created successfully.`);
      } else {
        console.log(`Folder '${currentPath}' already exists.`);
      }
    }
  } catch (error) {
    console.error(`Error: ${(error as Error).message}`);
  } finally {
    // Disconnect from the SFTP server
    await sftp.end();
  }
}

export async function deleteFileFromSFTP(filePath: string, projectId: string): Promise<void> {
  const sftp = new SFTP();

  try {
    // Retrieve project details
    const projectDetails = await projectModel.getProjectId(projectId);
    if (!projectDetails || !projectDetails.sftpToken) {
      throw new Error(`Project details or SFTP token not found for project ${projectId}`);
    }

    const destinationConfig = {
      host: process.env.A2_HOST || '',
      username: process.env.A2_USERNAME || '',
      port: process.env.A2_PORT ? parseInt(process.env.A2_PORT, 10) : undefined,
      password: process.env.A2_PASSWORD || '',
    };
    if (process.env.NODE_ENV !== 'dev' && process.env.NODE_ENV !== 'local') {
      if (projectDetails?.sftpToken) {
        const decodedToekn = verifyTokenForSftpPassword(projectDetails.sftpToken);
        destinationConfig.password = decodedToekn.password;
      }
      destinationConfig.username = `${process.env.A2_USERNAME}${projectId.split('-').join('')}`;

      // Verify and extract SFTP token
      const decodedToken = verifyTokenForSftpPassword(projectDetails.sftpToken);
      destinationConfig.username = `${process.env.A2_USERNAME}${projectId.split('-').join('')}`;
      destinationConfig.password = decodedToken.password;
    }

    // Connect to SFTP server
    await sftp.connect(destinationConfig);

    // Check if the file exists
    const fileExists = await sftp.exists(filePath);
    if (!fileExists) {
      throw new Error(`File ${filePath} does not exist on the SFTP server`);
    }

    // Delete the file
    await sftp.delete(filePath);

    console.log(`File ${filePath} deleted successfully.`);
  } catch (error) {
    console.error(`Error deleting file: ${(error as Error).message}`);
    throw error;
  } finally {
    // Disconnect from SFTP server
    await sftp.end();
  }
}

export async function renameFolderAndFileOnSFTP(
  oldFolderPath: string,
  newFolderPath: string,
  projectId: string,
  purposeId: string
): Promise<void> {
  const sftp = new SFTP();

  try {
    // If the environment is not dev or local, override with project-specific config
    const config = await destinationConfig(purposeId, projectId);
    if (!config) {
      console.log('No SFTP setup for this purpose');
      return;
    }

    // Connect to the SFTP server
    await sftp.connect(config);

    // Rename folder
    await sftp.rename(oldFolderPath, newFolderPath);
    console.log(`Folder renamed from ${oldFolderPath} to ${newFolderPath}`);

    console.log('Rename operation successful!');
  } catch (err) {
    console.error('Error renaming folder or file:', (err as any).message);
    throw err;
  } finally {
    // Close the SFTP connection
    await sftp.end();
  }
}
export async function deleteFileOnSFTP(
  path: string,
  projectId: string,
  purposeId: string
): Promise<void> {
  const sftp = new SFTP();

  try {
    // If the environment is not dev or local, override with project-specific config
    const config = await destinationConfig(purposeId, projectId);
    if (!config) {
      console.log('No SFTP setup for this purpose');
      return;
    }

    // Connect to the SFTP server
    await sftp.connect(config);

    // Rename folder
    await sftp.delete(path);
    console.log(`file delete ${path}`);

    console.log('SFTP Delete operation successful!');
  } catch (err) {
    console.error('Error renaming folder or file:', (err as any).message);
    throw err;
  } finally {
    // Close the SFTP connection
    await sftp.end();
  }
}
