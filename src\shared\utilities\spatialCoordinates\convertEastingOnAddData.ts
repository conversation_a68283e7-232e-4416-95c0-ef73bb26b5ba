import { convertEastingNorthingToLatLong } from './eastingAndNothingConversion';

const convertEastingAndNorthingToLatAndLong = (data: any) => {
  try {
    if ('easting' in data && 'northing' in data) {
      if (data.easting && data.northing) {
        const result: any = convertEastingNorthingToLatLong(
          Number(data.easting),
          Number(data.northing)
        );
        if (result && result.latitude && result.longitude) {
          data.longitude = result.longitude;
          data.latitude = result.latitude;
        }
      }
    }
    return data;
  } catch (error) {
    throw error;
  }
};

export default convertEastingAndNorthingToLatAndLong;
