import express, { Router } from 'express';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../generic/crudDriver.controller';
import testVariantController from '../../controllers/meta/testVariant.controller';
import { TestVariant } from '../../../../entities/p_meta/Testvariant';

const router: Router = express.Router();

// Create a generic router for the User entity
const TestVariantDetailController = new CrudController<TestVariant>(TestVariant);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/get/all', authenticateToken, testVariantController.all);
router.get('/get/by/test/:id', authenticateToken, testVariantController.getByTestId);
router.get('/get/by/testMethod/:id', authenticateToken, testVariantController.getByTestMethodId);
router.post('/', authenticateToken, (req, res) => TestVariantDetailController.create(req, res));
router.put('/:id', authenticateToken, (req, res) => TestVariantDetailController.update(req, res));

export default router;
