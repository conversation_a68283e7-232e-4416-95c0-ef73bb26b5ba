import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilSampleClassification } from '../../../../../../entities/p_cs/StSoilSampleClassification';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilSampleClassification>(StSoilSampleClassification);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'sampleClassification')
);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'sampleClassification')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'sampleClassification')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'sampleClassification')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'sampleClassification')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'sampleClassification')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'sampleClassification')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'sampleClassification')
);

export default router;
