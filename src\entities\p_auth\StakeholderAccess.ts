import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ProjectOrgStructure } from './OrgStructure'; // Import the ProjectOrgStructure entity
import { Stakeholder } from './Stakeholder'; // Import the Stakeholders entity

@Entity({ schema: 'p_auth' })
export class StakeholderAccess {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  orgStructureId?: string;

  @ManyToOne(() => ProjectOrgStructure, { nullable: true }) 
  @JoinColumn({ name: 'orgStructureId' })
  Organization?: ProjectOrgStructure;

  @Column()
  stakeholderId?: string;

  @ManyToOne(() => Stakeholder, { nullable: true }) 
  @JoinColumn({ name: 'stakeholderId' })
  Stakeholder?: Stakeholder;

  @Column()
  accessLevel?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
