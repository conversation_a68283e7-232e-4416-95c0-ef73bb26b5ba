import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Project } from './Project';
import { FeatureType } from '../p_domain/FeatureType';
import { FeatureLocation } from '../p_domain/FeatureLocation';
import { FeatureSubLocation } from '../p_domain/FeatureSubLocatoin';

@Entity({ schema: 'p_gen' })
export class Feature {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'varchar', nullable: true })
  name?: string;

  @Column({ nullable: true })
  featureTypeId?: string;

  @ManyToOne(() => FeatureType, { nullable: true }) 
  @JoinColumn({ name: 'featureTypeId' })
  featureType?: FeatureType;

  @Column({ nullable: true })
  featureLocationId?: string;

  @ManyToOne(() => FeatureLocation, { nullable: true }) 
  @JoinColumn({ name: 'featureLocationId' })
  featureLocation?: FeatureLocation;

  @Column({ nullable: true })
  featureSubLocationId?: string;

  @ManyToOne(() => FeatureSubLocation, { nullable: true }) 
  @JoinColumn({ name: 'featureSubLocationId' })
  featureSubLocation?: FeatureSubLocation;

  @Column({ type: 'varchar', nullable: true })
  description?: string;

  @Column({ type: 'varchar', nullable: true })
  parentId?: string;

  @Column({ type: 'json', nullable: true })
  customAttributes: any;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ type: 'uuid', nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;
}
