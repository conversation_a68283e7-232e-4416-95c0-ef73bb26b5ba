import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { SurveyType } from '../p_domain/SurveyType';
import { FeatureLocation } from '../p_domain/FeatureLocation';
import { FeatureSubLocation } from '../p_domain/FeatureSubLocatoin';
import { InstrumentType } from '../p_domain/InstrumentType';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class FieldSurvey extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Survey Name', // this column will be entered by the
      fieldName: 'surveyName',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column()
  surveyName?: string;

  @ColumnInfo({
    customData: {
      name: 'Feature Location',
      fieldName: 'featureLocationId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.feature_location WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  featureLocationId?: string;

  @ManyToOne(() => FeatureLocation, { nullable: true })
  @JoinColumn({ name: 'featureLocationId' })
  featureLocation?: FeatureLocation;

  @ColumnInfo({
    customData: {
      name: 'Feature Sub Location',
      fieldName: 'featureSubLocationId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domain.feature_sub_location WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  featureSubLocationId?: string;

  @ManyToOne(() => FeatureSubLocation, { nullable: true })
  @JoinColumn({ name: 'featureSubLocationId' })
  featureSubLocation?: FeatureSubLocation;

  @ColumnInfo({
    customData: {
      name: 'Survey Type',
      fieldName: 'surveyTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: 'SELECT id FROM p_domian.survey_type WHERE name = $1',
    },
  })
  @Column({ nullable: true })
  surveyTypeId?: string;

  @ManyToOne(() => SurveyType, { nullable: true })
  @JoinColumn({ name: 'surveyTypeId' })
  SurveyType?: SurveyType;

  @ColumnInfo({
    customData: {
      name: 'Instrument Type',
      fieldName: 'instrumentTypeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  instrumentTypeId?: string;

  @ManyToOne(() => InstrumentType, { nullable: true })
  @JoinColumn({ name: 'instrumentTypeId' })
  instrumentType?: InstrumentType;

  @ColumnInfo({
    customData: {
      name: 'Elevation',
      fieldName: 'elevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Survey Date',
      fieldName: 'dateSurveyed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  dateSurveyed?: Date;

  @ColumnInfo({
    customData: {
      name: 'Link to documentation',
      fieldName: 'link',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  link?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
