import sharp from 'sharp';

import { MediaConfig } from '../../../entities/p_gen/MediaConfig';
import { sihLogo, usaceLogo } from './svgLogo';
import { createCompassArrowBuffer } from './compassLogo';
import { createSVGAnnotation } from './annotationRect';
import fs from 'fs';

export async function createCompositeConfig(
  mediaConfig: MediaConfig | null, // Replace with actual mediaConfig type if available
  station: string | null,
  newDescriptionObj: { offset?: string; imageDirection?: string; [key: string]: any },
  formattedDate: string,
  newDescription: string,
  metadata: sharp.Metadata,
  size: { width: number; height: number }
): Promise<sharp.OverlayOptions[]> {
  if (!mediaConfig) {
    return [];
  }
  const compositeConfig: sharp.OverlayOptions[] = [];

  // Conditionally create and add the logo if enabled
  if (mediaConfig?.enableLogo && mediaConfig?.logoPosition) {
    const sihLogoBuffer = await sih<PERSON>ogo();
    compositeConfig.push({
      input: sihLogoBuffer,
      gravity: mediaConfig.logoPosition as sharp.Gravity,
      blend: 'over',
      top: 10,
      left: 0,
    });
  }

  if (
    mediaConfig?.projectId &&
    (mediaConfig?.projectId === 'd80f0b17-0b73-4d96-a629-94c2003ede7b' ||
      mediaConfig?.projectId === 'eb4cb7f5-eb15-47bf-8955-4d61659f9413')
  ) {
    const usaceLogoBuffer = await usaceLogo();
    compositeConfig.push({
      input: usaceLogoBuffer,
      gravity: mediaConfig.compassPosition as sharp.Gravity,
      blend: 'over',
    });
  }

  // Conditionally create and add the compass if enabled
  if (mediaConfig?.enableCompass && mediaConfig?.compassPosition) {
    const compassArrowBuffer = await createCompassArrowBuffer(
      newDescriptionObj?.imageDirection || 'North'
    );
    compositeConfig.push({
      input: compassArrowBuffer,
      gravity: mediaConfig.logoPosition as sharp.Gravity,
      top: 60,
      left: 0,
      blend: 'over',
    });
  }

  // Conditionally create and add the annotation if enabled
  if (mediaConfig?.enableAnnotation && mediaConfig?.annotationPosition) {
    const annotationImagePath = './annotation-image.png';
    await createSVGAnnotation(
      station,
      metadata,
      formattedDate,
      size,
      mediaConfig?.fontSize || 30,
      newDescriptionObj,
      annotationImagePath
    );
    const annotationBuffer = fs.readFileSync(annotationImagePath);
    compositeConfig.push({
      input: annotationBuffer,
      gravity: mediaConfig.annotationPosition as sharp.Gravity,
      blend: 'over',
    });
  }

  return compositeConfig;
}
