const compressiveStrengthQuery = `
SELECT
    COALESCE(s."sampleNo", 'N/A') AS "sampleNo",
    COALESCE(s."location", 'N/A') AS "location",
    COALESCE(cs."slumpAvg"::TEXT, 'N/A') AS "slumpAvg",
    COALESCE(cmd."mixId", 'N/A') AS "mixCode",
    COALESCE(cbt."truckId"::TEXT, 'N/A') AS "truckId",
    COALESCE(cbt."ticketNumber"::TEXT, 'N/A') AS "ticketNumber",
    COALESCE(sc."materialTypeId"::TEXT, 'N/A') AS "materialTypeId",
    COALESCE(sc."testNo", 'N/A') AS "testNo",
    COALESCE(sc."dateTested"::TEXT, 'N/A') AS "dateTested",
    COALESCE(sc."castDate"::TEXT, 'N/A') AS "castDate",
    COALESCE(sc."testedBy", 'N/A') AS "testedBy",
    COALESCE(sc."specimenAge"::TEXT, 'N/A') AS "specimenAge",
    COALESCE(sc."failureLoad"::TEXT, 'N/A') AS "failureLoad",
    COALESCE(sc."cylinderDiameter1"::TEXT, 'N/A') AS "cylinderDiameter1",
    COALESCE(sc."cylinderDiameter2"::TEXT, 'N/A') AS "cylinderDiameter2",
    COALESCE(sc."cylinderDiameter"::TEXT, 'N/A') AS "cylinderDiameter",
    COALESCE(sc."cylinderLength1"::TEXT, 'N/A') AS "cylinderLength1",
    COALESCE(sc."cylinderLength2"::TEXT, 'N/A') AS "cylinderLength2",
    COALESCE(sc."cylinderLength"::TEXT, 'N/A') AS "cylinderLength",
    COALESCE(sc."crossSectionalArea"::TEXT, 'N/A') AS "crossSectionalArea",
    COALESCE(sc."compressiveStrength"::TEXT, 'N/A') AS "compressiveStrength",
    COALESCE(sc."fractureTypeId"::TEXT, 'N/A') AS "fractureTypeId",
    COALESCE(sc."testResultId"::TEXT, 'N/A') AS "testResultId",
    COALESCE(sc."lengthDiameterRatio"::TEXT, 'N/A') AS "lengthDiameterRatio"
FROM
    p_cs.st_compressive_strength sc
JOIN
    p_cs.sample s ON sc."sampleId" = s.id  
LEFT JOIN
    p_cs.concrete_mix_design cmd ON s."mixId" = cmd.id
LEFT JOIN
    p_cs.concrete_batch_ticket cbt ON sc."concreteBatchId" = cbt."id"
LEFT JOIN
    p_cs.st_concrete_slump cs ON sc."sampleId" = cs."sampleId"
WHERE
    cs."isDelete" = false
    AND s."isDelete" = false
    AND s.id = {sampleId} ;
    
`
export { compressiveStrengthQuery };