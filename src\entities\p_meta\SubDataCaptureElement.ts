import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { DataCaptureElements } from './DataCaptureElements';

@Entity({ schema: 'p_meta' })
export class SubDataCaptureElements {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  parentColumn?: string;

  @Column()
  name?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  entity?: string;

  @Column({ default: false })
  isGeoJson?: boolean;

  @Column({ nullable: true })
  component?: string;

  @Column({ nullable: true })
  columnsToRemove?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ default: 'Point' })
  geoType?: string;
}
