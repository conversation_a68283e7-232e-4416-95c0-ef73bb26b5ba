import express, { Router } from 'express';
import { DefaultForm } from '../../../../../entities/p_meta/Form';
import CrudController from '../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import FormController from '../../../controllers/meta/formConfiguration/form.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const CRUD = new CrudController<DefaultForm>(DefaultForm);
const controller = new FormController();

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/', authenticateToken, (req, res) => CRUD.create(req, res));
router.put('/:id', authenticateToken, controller.update);
router.put('/edit/mode/:dceId', authenticateToken, controller.editFormMode);
router.delete('/:id', authenticateToken, (req, res) => CRUD.softDelete(req, res));
router.get('/by/dce/:dceId', authenticateToken, controller.findByDceId);
router.get('/create/by/dce/:dceId', authenticateToken, controller.createFormByDce);
router.post('/create/by/multiple/dce', authenticateToken, controller.createFormByMultipleDce);
router.get('/json/by/form/:dceId/:formId', authenticateToken, controller.getJSONByFormId);
router.get('/all/mode/by/dce/:dceId', authenticateToken, controller.getModeFormsByDCE);
router.get('/create/by/sub/dce/:dceId', authenticateToken, controller.createFormBySubDce);
router.get('/get/mode/by/form/:formId', authenticateToken, controller.getModeByFormId);
router.get('/get/custom/forms', authenticateToken, controller.getAllCustomForms);
router.get(
  '/get/custom/form/by/formKey/:formKey',
  authenticateToken,
  controller.getCustomFormByFormKey
);

export default router;
