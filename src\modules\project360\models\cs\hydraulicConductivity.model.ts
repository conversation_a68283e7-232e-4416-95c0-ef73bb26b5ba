import { getManager } from 'typeorm';
import { StHydraulicConductivityTest } from '../../../../entities/p_cs/StHydraulicConductivityTest';

class HydraulicConductivityModel {
  constructor() {}

  async findById(SampleId: string) {
    try {
      return await getManager()
        .getRepository(StHydraulicConductivityTest)
        .findOne({
          where: { id: SampleId, isDelete: false },
          relations: ['hydraulicConductivityCellInfo'],
        });
    } catch (error) {
      throw error;
    }
  }

  async add(newHCTest: StHydraulicConductivityTest) {
    try {
      const HCTestRepository = getManager().getRepository(StHydraulicConductivityTest);
      const addedHctest = HCTestRepository.create(newHCTest);
      await HCTestRepository.save(addedHctest);
      return addedHctest;
    } catch (error) {
      throw error;
    }
  }
}

const hydraulicConductivityModel = new HydraulicConductivityModel();
export default hydraulicConductivityModel;
