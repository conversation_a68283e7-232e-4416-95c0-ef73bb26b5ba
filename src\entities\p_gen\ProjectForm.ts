import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  ManyToMany,
} from 'typeorm';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { Project } from './Project';
import { DefaultForm } from '../p_meta/Form';
import { ProjectActivity } from './Activity';

@Entity({ schema: 'p_gen', name: 'form' })
export class ProjectForm {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true, type: 'uuid' })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true, type: 'uuid' })
  parentFormId?: string;

  @ManyToOne(() => DefaultForm, { nullable: true })
  @JoinColumn({ name: 'parentFormId' })
  parentForm?: DefaultForm;

  @Column({ type: 'varchar', nullable: true })
  mode?: string | null;

  @Column({ nullable: true, default: false })
  default?: boolean;

  @Column({ nullable: true })
  isCustom?: boolean;

  @Column({ nullable: true })
  isMultiple?: boolean;

  @Column({ nullable: true, default: true })
  enableMobile?: boolean;

  @Column({ nullable: true })
  name?: string;

  @Column({ type: 'json', nullable: true })
  form?: string;

  @Column({ nullable: true })
  description?: string;

  @Column({ nullable: true })
  dceId?: string;

  @Column({ nullable: true })
  formKey?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @ManyToMany(() => ProjectActivity, (activity) => activity.forms)
  activities?: ProjectActivity[];
}
