import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
  AfterLoad,
} from 'typeorm';
import { Section } from '../p_gen/Section';
import { Project } from '../p_gen/Project';
import { CutInformation } from './CutInformation';
import { Station } from '../p_map/Station';
import StationModel from '../../modules/project360/models/map/station.model';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { Purpose } from '../p_meta/Purpose';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class PanelInformation extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  projectId?: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'uuid', nullable: true })
  sectionId?: string;

  @ManyToOne(() => Section, { nullable: true })
  @JoinColumn({ name: 'sectionId' })
  section?: Section;

  @OneToMany(() => CutInformation, (cutInformation) => cutInformation.panelInformation)
  cutInformation?: CutInformation[];

  @Column()
  panelTechnology?: string;

  @Column()
  panelId?: string;

  @Column()
  panelType?: string;

  @Column()
  panelName?: string;

  @Column({ nullable: true })
  cuts?: number;

  @Column({ nullable: true })
  stationStart?: string;

  @Column({ nullable: true })
  stationEnd?: string;

  @Column({ nullable: true })
  stationCenterline?: string;

  @Column({ nullable: true })
  eastingCenterline?: number;

  @Column({ nullable: true })
  northingCenterline?: number;

  @Column({ nullable: true })
  latitudeCenterline?: number;

  @Column({ nullable: true })
  longitudeCenterline?: number;

  @Column({ nullable: true })
  offsetCenterline?: number;

  @Column({ nullable: true })
  panelLength?: number;

  @Column({ nullable: true })
  panelWidth?: number;

  @Column({ nullable: true })
  groundElevation?: number;

  @Column({ nullable: true })
  topElevation?: number;

  @Column({ nullable: true })
  bottomElevation?: number;

  @Column({ type: 'timestamp', nullable: true })
  PanelStartDate?: Date;

  @Column({ type: 'timestamp', nullable: true })
  dateExcavated?: Date;

  // @Column({ default: 'Approved', nullable: true })
  // approvalStatus?: string;

  @Column()
  createdBy?: string;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  startStationCoords?: Partial<Station> | null;
  endStationCoords?: Partial<Station> | null;

  @AfterLoad()
  async afterLoad() {
    try {
      const stationModel = new StationModel();

      if (this.stationStart && this.stationEnd && this.projectId) {
        const startData = await stationModel.getCoordinatesByStation(
          this.stationStart,
          this.projectId
        );

        this.startStationCoords = { latitude: startData.latitude, longitude: startData.longitude };
        const endData = await stationModel.getCoordinatesByStation(this.stationEnd, this.projectId);
        this.endStationCoords = { latitude: endData.latitude, longitude: endData.longitude };
      } else {
        this.startStationCoords = null;
        this.endStationCoords = null;
      }
    } catch (error) {
      this.startStationCoords = null;
      this.endStationCoords = null;
    }
  }
}
