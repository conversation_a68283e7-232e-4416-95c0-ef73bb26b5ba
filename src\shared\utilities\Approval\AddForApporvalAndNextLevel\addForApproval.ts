import { getManager } from 'typeorm';
import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import dceModel from '@models/meta/dce.model';
import ApprovalServices from './ApprovalServices';
import projectModel from '@models/project.model';
import notificaitonTrigger from '@utils/notification/notificaitonService';
import { camelCaseToNormalCase } from '@utils/custom/caseConversion';

const addForApproval = async (
  data: any[],
  entity: string,
  projectId: string,
  entityMetadata: any,
  currentLevel: number = 0,
  submittedUser: string,
  isMicroservice: boolean = false,
  token?: string
) => {
  const entityManager = getManager();
  const invalidDatas: any = [];
  try {
    const subModule = await dceModel.findByApprovalEntity(entity);
    const approvalStatusModel = new ApprovalStatusModel();
    const approvalServices = new ApprovalServices();
    const sendForApprovalAllowedStatusIds =
      await approvalStatusModel.getSendForApprovalCheckStatusId();
    const pendingApprovalStatusId = await approvalStatusModel.getByNameStatusId('pending');
    let userIdForNotification = new Set<string>();
    for (const value of data) {
      try {
        if (
          value.approvalStatusId &&
          sendForApprovalAllowedStatusIds.includes(value.approvalStatusId)
        ) {
          userIdForNotification = await approvalServices.processApproval(
            value,
            subModule,
            projectId,
            entity,
            entityMetadata,
            currentLevel,
            submittedUser,
            isMicroservice,
            token,
            entityManager,
            pendingApprovalStatusId || '',
            userIdForNotification
          );
        }
        if (userIdForNotification.size > 0) {
          try {
            const projectDetails = await projectModel.getProjectId(projectId);
            notificaitonTrigger({
              userIds: Array.from(userIdForNotification),
              message: `${camelCaseToNormalCase(
                entity || ''
              )} from ${projectDetails?.name} is waiting for your approval.`,
              triggerName: 'approval',
              projectId,
            });
          } catch (error) {
            console.error('Approval Notification Error');
          }
        }
      } catch (error) {
        throw error;
      }
    }
    return invalidDatas;
  } catch (error) {
    throw error;
  }
};

export default addForApproval;
