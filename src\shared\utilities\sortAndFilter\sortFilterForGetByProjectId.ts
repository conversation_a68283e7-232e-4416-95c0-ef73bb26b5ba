import { DataCaptureElements } from '../../../entities/p_meta/DataCaptureElements';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import { validateByOperatorAndType } from './filterOperators';

class SortAndFilter {
  sortFilterForGetByProjectId = async (
    query: queryInterface,
    data: any[],
    dceData?: DataCaptureElements
  ) => {
    try {
      const { filter, sort } = query;
      let finalData = data;

      if (filter && dceData) {
        const columnAndType = await dceModel.getColumnType(dceData);
        const parsedArray: IFilter[] = JSON.parse(filter);
        parsedArray.map((value) => {
          value.type = columnAndType[value.field];
        });
        const dataForFilter = finalData;
        finalData = dataForFilter.filter((value) => {
          return parsedArray.some((item) => {
            return validateByOperatorAndType(
              item.type,
              item.operator,
              item.value,
              value[item.field]
            );
          });
        });
      }

      if (sort) {
        const parsedArray: ISort[] = JSON.parse(sort);
        parsedArray.map((value) => {
          finalData = this.sortDataByCOlumn(value.sort, value.field, finalData);
        });
      }

      return finalData;
    } catch (error) {
      throw error;
    }
  };

  sortDataByCOlumn = (sort: string, sortBy: string, data: any[]) => {
    try {
      return data.sort((a, b) => {
        const aValue = a[sortBy];
        const bValue = b[sortBy];

        // Handle null or undefined values
        if (aValue == null && bValue == null) {
          return 0;
        } else if (aValue == null) {
          return sort === 'asc' ? -1 : 1;
        } else if (bValue == null) {
          return sort === 'asc' ? 1 : -1;
        }

        if (aValue instanceof Date && bValue instanceof Date) {
          // Both values are dates
          return sort === 'asc'
            ? aValue.getTime() - bValue.getTime()
            : bValue.getTime() - aValue.getTime();
        } else if (typeof aValue === 'number' && typeof bValue === 'number') {
          // Both values are numbers
          return sort === 'asc' ? aValue - bValue : bValue - aValue;
        } else if (typeof aValue === 'string' && typeof bValue === 'string') {
          // Both values are strings
          return sort === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
        } else if (aValue instanceof Date) {
          // If a[sortBy] is a date but b[sortBy] is not
          return sort === 'asc' ? -1 : 1;
        } else if (bValue instanceof Date) {
          // If b[sortBy] is a date but a[sortBy] is not
          return sort === 'asc' ? 1 : -1;
        } else if (typeof aValue === 'number') {
          // If a[sortBy] is a number but b[sortBy] is not
          return sort === 'asc' ? -1 : 1;
        } else if (typeof bValue === 'number') {
          // If b[sortBy] is a number but a[sortBy] is not
          return sort === 'asc' ? 1 : -1;
        } else {
          // Fallback case where neither are numbers, strings, or dates
          return 0;
        }
      });
    } catch (error) {
      throw error;
    }
  };

  getOnlySelectRows = (selectRows: string[], data: any[]) => {
    try {
      return data.filter((value) => selectRows.find((item) => item == value.id));
    } catch (error) {
      throw error;
    }
  };
}

export default SortAndFilter;

// export interface queryInterface {
//   sort?: {
//     sort: string;
//     field: string;
//   }[];
//   filter?: {
//     field: string;
//     operator: string;
//     value: string & string[];
//     type: string;
//   }[];
// }

export interface queryInterface {
  sort?: string;
  filter?: string;
}

interface IFilter {
  field: string;
  operator: string;
  value: string & string[];
  type: string;
}

interface ISort {
  sort: string;
  field: string;
}
