import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { TestMethod } from './TestMethod';
import { TestVariant } from './Testvariant';
import { DataCaptureElements } from './DataCaptureElements';

@Entity({ schema: 'p_meta' })
export class Test {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  name?: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  description?: string;

  @Column({
    nullable: true,
  })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true }) 
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => TestMethod, (testMethod) => testMethod.test)
  testMethod?: TestMethod[];

  @OneToMany(() => TestVariant, (testVariant) => testVariant.test)
  testVariant?: TestVariant[];
}
