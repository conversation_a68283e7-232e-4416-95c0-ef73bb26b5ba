import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>umn,
  CreateDateColumn,
  UpdateDateColumn,
  AfterLoad,
} from 'typeorm';
import { Stakeholder } from './Stakeholder';
// import { User } from '../g_auth/User';
import { ProjectRole } from './Role';
import { IUser } from '../../shared/server/platformApi/interface/IUser';
import { getUserById } from '../../shared/server/platformApi/user';

@Entity({ schema: 'p_auth' })
export class StakeUser {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column()
  stakeholderId?: string;

  @ManyToOne(() => Stakeholder, { nullable: true })
  @JoinColumn({ name: 'stakeholderId' })
  stakeholder?: Stakeholder;

  @Column({ nullable: true })
  userId?: string;

  @Column({ nullable: true })
  designation?: string;

  @Column({ nullable: true })
  roleId?: string;

  @ManyToOne(() => ProjectRole, { nullable: true })
  @JoinColumn({ name: 'roleId' })
  role?: ProjectRole;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  user?: IUser | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.userId) {
        const data = await getUserById(this.userId);
        this.user = data;
      } else {
        this.user = null;
      }
    } catch (error) {
      this.user = null;
    }
  }
}
