import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { Project } from '../p_gen/Project';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class StFlowConsistencyCLSM extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  testNo?: string;

  @ColumnInfo({
    customData: {
      name: 'Concrete Batch Ticket Id',
      fieldName: 'ticketId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column()
  ticketId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'ticketId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @ColumnInfo({
    customData: {
      name: 'Test Status (Pass/Fail)',
      fieldName: 'passFail',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'First Flow Measurement',
      fieldName: 'firstFlowMeasurement',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  firstFlowMeasurement?: number;

  @ColumnInfo({
    customData: {
      name: 'Second Flow Measurement',
      fieldName: 'secondFlowMeasurement',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  secondFlowMeasurement?: number;

  @ColumnInfo({
    customData: {
      name: 'Average Flow Measurement',
      fieldName: 'averageFlowMeasurement',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  averageFlowMeasurement?: number;

  @ColumnInfo({
    customData: {
      name: 'Tester',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  tester?: string;

  @ColumnInfo({
    customData: {
      name: 'Test date',
      fieldName: 'dateTimeTest',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'timestamp', nullable: true })
  dateTimeTest?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
