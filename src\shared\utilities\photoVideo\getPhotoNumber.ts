import photoVideoModel from '../../../modules/project360/models/photoVideo.model';

export const getPhotoNumberByPhotoDate = async (date: Date, projectId: string) => {
  try {
    const count = await photoVideoModel.getByprojectIdAndDateForPhotoNumber(projectId, date);
    return Number(count) + 1;
  } catch (error) {
    throw error;
  }
};

export const getPhotoNumberByProjectId = async (projectId: string) => {
  try {
    const photoNumber = await photoVideoModel.getByProjectIdForPhotoNumber(projectId);
    return photoNumber;
  } catch (error) {
    throw error;
  }
};
