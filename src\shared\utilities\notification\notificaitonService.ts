import { sendNotification } from '../../server/platformApi/notificaiton';

// Listen for the 'trigger_notification' event
const notificaitonTrigger = (data: {
  userIds: string[];
  message: string;
  triggerName: string;
  projectId: string;
}) => {
  try {
    const { userIds, message, triggerName, projectId } = data;
    const finalUserId = Array.isArray(userIds) ? userIds : [userIds];
    sendNotification(finalUserId, message, triggerName, projectId);
  } catch (error) {
    console.error(error);
  }
};

// Export the eventEmitter so it can be used in other modules
export default notificaitonTrigger;
