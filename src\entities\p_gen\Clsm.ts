import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  AfterLoad,
} from 'typeorm';
import { Project } from './Project';
import { Section } from './Section';
import { PanelInformation } from '../p_cs/PanelInformation';
import { CutInformation } from '../p_cs/CutInformation';
import { Station } from '../p_map/Station';
import StationModel from '../../modules/project360/models/map/station.model';

@Entity({ schema: 'p_gen' })
export class Clsm {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ type: 'uuid' })
  projectId?: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'uuid', nullable: true })
  panelInformationId?: string;

  @ManyToOne(() => PanelInformation)
  @JoinColumn({ name: 'panelInformationId' })
  paneInformation?: PanelInformation;

  @Column({ type: 'uuid', nullable: true })
  cutInformationId?: string;

  @ManyToOne(() => CutInformation)
  @JoinColumn({ name: 'cutInformationId' })
  cutInformation?: CutInformation;

  @Column({ type: 'uuid', nullable: true })
  sectionId?: string;

  @ManyToOne(() => Section)
  @JoinColumn({ name: 'sectionId' })
  section?: Section;

  @Column({ type: 'uuid', nullable: true }) //mixdesign two tables are there need clearity on this
  mixDesignId?: string;

  @Column()
  stationStart?: string;

  @Column()
  stationEnd?: string;

  @Column({ type: 'decimal', nullable: true })
  longitudeStart?: number;

  @Column({ type: 'decimal', nullable: true })
  longitudeEnd?: number;

  @Column({ type: 'decimal', nullable: true })
  latitudeStart?: number;

  @Column({ type: 'decimal', nullable: true })
  latitudeEnd?: number;

  @Column({ nullable: true })
  rockAverageElevation?: number;

  @Column({ nullable: true })
  averageElevation?: number;

  @Column({ nullable: true })
  targetElevation?: number;

  @Column({ nullable: true })
  topElevation?: number;

  @Column({ nullable: true })
  bottomElevation?: number;

  @Column({ nullable: true })
  clsm?: number;

  @Column({ nullable: true })
  clsmTop?: number;

  @Column()
  createdBy?: string;

  @Column({ nullable: true })
  qaqc?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  startStationCoords?: Partial<Station> | null;
  endStationCoords?: Partial<Station> | null;

  @AfterLoad()
  async afterLoad() {
    try {
      const stationModel = new StationModel();

      if (this.stationStart && this.stationEnd && this.projectId) {
        const startData = await stationModel.getCoordinatesByStation(
          this.stationStart,
          this.projectId
        );

        this.startStationCoords = { latitude: startData.latitude, longitude: startData.longitude };
        const endData = await stationModel.getCoordinatesByStation(this.stationEnd, this.projectId);
        this.endStationCoords = { latitude: endData.latitude, longitude: endData.longitude };
      } else {
        this.startStationCoords = null;
        this.endStationCoords = null;
      }
    } catch (error) {
      this.startStationCoords = null;
      this.endStationCoords = null;
    }
  }
}
