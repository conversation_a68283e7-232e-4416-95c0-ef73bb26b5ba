import express, { Router } from 'express';
// import { authenticateToken } from "../../../shared/middlewares/auth.middleware";
import CrudController from '../../generic/crudDriver.controller';
import { ProjectMaterial } from '../../../entities/p_gen/ProjectMaterial';
import projectMaterialController from '../controllers/projectMaterial.controller';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const ProjectMaterialDetailController = new CrudController<ProjectMaterial>(ProjectMaterial);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post('/', authenticateToken, projectMaterialController.addMaterial);
router.put('/:id', authenticateToken, (req, res) =>
  ProjectMaterialDetailController.update(req, res)
);
router.delete('/:id', authenticateToken, projectMaterialController.delete);
router.get('/by/:id', authenticateToken, projectMaterialController.findById);
router.get('/by/project/:id', authenticateToken, projectMaterialController.findByProjectId);
router.get(
  '/by/materialType/project/:materialTypeId/:projectId',
  authenticateToken,
  projectMaterialController.findByMaterialTypeAndProjectId
);
router.delete('/:id', authenticateToken, (req, res) =>
  ProjectMaterialDetailController.softDelete(req, res)
);

export default router;
