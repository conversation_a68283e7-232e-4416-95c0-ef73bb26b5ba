const getEntitiesFromMultipleDCEForms = (obj: any): string[] => {
  const unique = new Set<string>();
  const stack = [obj];

  while (stack.length > 0) {
    const current = stack.pop();

    if (Array.isArray(current)) {
      for (const item of current) {
        stack.push(item);
      }
    } else if (typeof current === 'object' && current !== null) {
      for (const [key, value] of Object.entries(current)) {
        if (key === 'name' && typeof value === 'string' && value.includes('.')) {
          unique.add(value.split('.')[0]);
        } else if (typeof value === 'object') {
          stack.push(value);
        }
      }
    }
  }

  return [...unique];
};

export default getEntitiesFromMultipleDCEForms;
