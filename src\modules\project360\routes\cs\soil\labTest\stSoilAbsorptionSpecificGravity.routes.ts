import express, { Router } from 'express';
import CrudController from '../../../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../../../shared/middlewares/auth.middleware';
import { StSoilAbsorptionSpecificGravity } from '../../../../../../entities/p_cs/StSoilAbsorptionSpecificGravity';

const router: Router = express.Router();

const GenricController = new CrudController<StSoilAbsorptionSpecificGravity>(
  StSoilAbsorptionSpecificGravity
);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'absorptionAndSpecificGravity')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'absorptionAndSpecificGravity')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'absorptionAndSpecificGravity')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'absorptionAndSpecificGravity')
);
router.post('/', authenticateToken, (req, res) =>
  GenricController.create(req, res, 'absorptionAndSpecificGravity')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'absorptionAndSpecificGravity')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'absorptionAndSpecificGravity')
);
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'absorptionAndSpecificGravity')
);

export default router;
