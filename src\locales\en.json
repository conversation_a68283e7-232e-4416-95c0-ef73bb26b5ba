{"welcomeMessage": "Welcome to Smarthub API!", "DataNotFoundMessage": "Data not found", "DataFoundMessage": "Data found", "DataInputSuccess": "Data input successful", "DataInputFail": "Data input unsuccessful", "UpdatedSuccess": "Updated data successfully", "UpdatedUnSuccess": "Error occurred while update", "InvalidInputDataError": "Invalid input data. Please check the request parameters.", "GetTestConfigError": "materialId and featureId not found in sample", "NuclearGaugeNumberInvalid": "Nuclear gauge number invalid or not found", "DeleteSuccess": "Deletion successful", "NoRecordFoundForDelete": "No record found for deletion", "InvalidStartAndEndDate": "Invalid start date and end date", "InvalidImportFile": "Invalid import file", "Unauthorized": "You do not have permission to access this resource", "AlreadyVerified": "Data is already verified"}