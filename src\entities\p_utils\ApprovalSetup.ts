import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ApprovalLevel } from './ApprovalLevel';
import { DataCaptureElements } from '../p_meta/DataCaptureElements';
import { ProjectActivity } from '../p_gen/Activity';
import { Purpose } from '../p_meta/Purpose';

@Entity({ schema: 'p_utils' })
export class ApprovalSetup {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  name?: string;

  @Column({ nullable: true })
  activityId?: string;

  @ManyToOne(() => ProjectActivity, { nullable: true }) 
  @JoinColumn({ name: 'activityId' })
  activity?: ProjectActivity;

  @Column({ nullable: true, type: 'uuid' })
  dceId?: string;

  @ManyToOne(() => DataCaptureElements, { nullable: true })
  @JoinColumn({ name: 'dceId' })
  dce?: DataCaptureElements;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  totalLevel?: string;

  @Column({ nullable: true })
  purposeTypeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeTypeId' })
  approvalPurposeType?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @OneToMany(() => ApprovalLevel, (approver) => approver.approval)
  levels?: ApprovalLevel[];
}
