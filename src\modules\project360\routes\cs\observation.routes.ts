import express, { Router } from 'express';

import { Observation } from '../../../../entities/p_cs/Observation';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';

const router: Router = express.Router();

const GenricController = new CrudController<Observation>(Observation);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.get('/by/project/:id', authenticateToken, (req, res) =>
  GenricController.findByProjectId(req, res, 'observation')
);
router.get('/:id', authenticateToken, GenricController.findById);

router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  GenricController.findByWorkActivityId(req, res, 'observation')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  GenricController.getDataCountByProjectId(req, res, 'observation')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  GenricController.sendForApproval(req, res, 'observation')
);
router.put('/:id', authenticateToken, (req, res) =>
  GenricController.update(req, res, 'observation')
);
router.post('/', authenticateToken, (req, res) => GenricController.create(req, res, 'observation'));
router.delete('/:id', authenticateToken, (req, res) =>
  GenricController.softDelete(req, res, 'observation')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  GenricController.multiSoftDelete(req, res, 'observation')
);

export default router;
