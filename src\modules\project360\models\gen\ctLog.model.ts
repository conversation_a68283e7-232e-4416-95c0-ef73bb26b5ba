import { CTLog } from '@entities/p_gen/CTLog';
import { getAuditDetails } from '@utils/auditLog/getAuditDetailsByEntity';
import { getManager } from 'typeorm';
import testLogPrefixModel from './testLogPrefix.model';
import { dceValidationBeforeApproval } from '@utils/Approval/dceValidation';
import { sendTestLogCalendarInvite } from '@utils/email/testLogCalendarInvite';
import { Sample } from '@entities/p_cs/Sample';
import { StSoilSandConeTest } from '@entities/p_cs/StSoilSandConeTest';
import { StSoilNuclearGaugeTest } from '@entities/p_cs/StSoilNuclearGaugeTest';
import { normalizeCtNumber, getCellValue } from '../../controllers/utils/ctLog';
class CTLogModel {
  private ctLogRepo = getManager().getRepository(CTLog);
  dataSource: any;

  async getTestNoByProjectIdAndPurposeId(projectId: string, purposeId: string): Promise<number> {
    try {
      const result = await this.ctLogRepo
        .createQueryBuilder('ctLog')
        .select(`MAX(CAST(SPLIT_PART(ctLog.testNo, '-', 2) AS INTEGER))`, 'maxTestNo')
        .where('ctLog.projectId = :projectId', { projectId })
        .andWhere('ctLog.purposeId = :purposeId', { purposeId })
        .andWhere('ctLog.isDelete = :isDelete', { isDelete: false })
        .andWhere('ctLog.testNo ~ :format', { format: '^[A-Za-z]+-[0-9]+$' })
        .getRawOne();

      // Return the maximum number or 0 if no records are found
      return result?.maxTestNo ? parseInt(result.maxTestNo, 10) : 0;
    } catch (error) {
      console.error('Error fetching Test No:', error);
      throw error;
    }
  }

  async addMultipleTests(testData: CTLog, noOfTests: number) {
    try {
      if (!testData.projectId || !testData.purposeId) {
        throw new Error('projectId and purposeId are required');
      }
      const testLogPrefix = await testLogPrefixModel.getByTestLogPrefixByProjectIdAndPurposeId(
        testData.projectId,
        testData.purposeId
      );
      const currentTestNo = await this.getTestNoByProjectIdAndPurposeId(
        testData.projectId,
        testData.purposeId
      );
      let nextTestNo = currentTestNo + 1;
      const testRecords: CTLog[] = [];

      // Store first and last test numbers for multiple tests
      const firstTestNo = `${testLogPrefix}-${String(nextTestNo).padStart(5, '0')}`;
      const lastTestNo = `${testLogPrefix}-${String(nextTestNo + noOfTests - 1).padStart(5, '0')}`;

      for (let i = 0; i < noOfTests; i++, nextTestNo++) {
        const formattedTestNo = `${testLogPrefix}-${String(nextTestNo).padStart(5, '0')}`;
        const newTestData = { ...testData, testNo: formattedTestNo };
        testRecords.push(this.ctLogRepo.create(newTestData));
      }
      const addedTests = await this.ctLogRepo.save(testRecords);

      if (testData.calendarInvite === true && testData.emailListId) {
        try {
          // Pass first and last test numbers to the calendar invite function
          const testDataWithRange = {
            ...testData,
            firstTestNo,
            lastTestNo,
            isMultipleTests: noOfTests > 1,
          };
          await sendTestLogCalendarInvite(testDataWithRange, testData.emailListId);
        } catch (error) {
          console.error('Error sending calendar invite:', error);
        }
      }

      // Audit logging in parallel (optional, depending on your needs)
      await Promise.all([
        ...addedTests.map((savedEntity) => dceValidationBeforeApproval('ctLog', [savedEntity])),
        ...addedTests.map((test) =>
          getAuditDetails(test.createdUserId || '', 'ctLog', test, 'add')
        ),
      ]);
      return addedTests;
    } catch (error) {
      throw error;
    }
  }
  async findCtnumberbyExcelDataAndUpdateDceTables(
    rawRows: Record<string, string>[],
    projectId: string
  ): Promise<{
    updatedRecords: {
      sampleManagement: Sample[];
      sandConeTest: StSoilSandConeTest[];
      nuclearGaugeTest: StSoilNuclearGaugeTest[];
      ctLog: CTLog[];
    };
  }> {
    try {
      if (!rawRows || rawRows.length === 0) throw new Error('Excel data is empty');
      const draftStatuses = ['In Progress', 'Ready for Submission', 'Not Started'];
      const updatedRecords: {
        sampleManagement: Sample[];
        sandConeTest: StSoilSandConeTest[];
        nuclearGaugeTest: StSoilNuclearGaugeTest[];
        ctLog: CTLog[];
      } = {
        sampleManagement: [],
        sandConeTest: [],
        nuclearGaugeTest: [],
        ctLog: [],
      };
      const filteredData: { id: string; [key: string]: string | undefined }[] = [];
      for (const rawRow of rawRows) {
        const rawCtNumber = getCellValue(rawRow, 'CT Number')?.toString() ?? '';
        if (!rawCtNumber) continue;
        const normalizedTestNo = normalizeCtNumber(rawCtNumber);
        if (!normalizedTestNo) continue;
        // Find matching ctLog entry with given test number and project, and not deleted
        const ctLog = await this.ctLogRepo.findOne({
          where: { testNo: normalizedTestNo, projectId, isDelete: false },
        });
        if (!ctLog) continue;

        // Extract only relevant fields from the Excel row and attach to ctLog.id
        const cellToJsonMap: Record<string, string> = {
          'Station (ft)': 'station',
          'Offset (ft)': 'offset',
          'Elevation (ft)': 'elevation',
          'Northing (ft)': 'northing',
          'Easting (ft)': 'easting',
          Latitude: 'latitude',
          Longitude: 'longitude',
          Alignment: 'alignment',
        };
        const obj: { id: string; [key: string]: string | undefined } = { id: ctLog.id };
        for (const [cellName, jsonKey] of Object.entries(cellToJsonMap)) {
          // getCellValue returns string | null, so map null to undefined here:
          const value = getCellValue(rawRow, cellName) ?? undefined;
          if (value) {
            obj[jsonKey] = value;
          }
        }
        filteredData.push(obj);
      }
      type EntityName = 'Sample' | 'StSoilSandConeTest' | 'StSoilNuclearGaugeTest' | 'CTLog';
      const entities: { repo: any; name: EntityName }[] = [
        { repo: getManager().getRepository(Sample), name: 'Sample' },
        { repo: getManager().getRepository(StSoilSandConeTest), name: 'StSoilSandConeTest' },
        {
          repo: getManager().getRepository(StSoilNuclearGaugeTest),
          name: 'StSoilNuclearGaugeTest',
        },
        { repo: getManager().getRepository(CTLog), name: 'CTLog' },
      ];

      for (const { repo, name } of entities) {
        const columnNames = repo.metadata.columns.map(
          (col: { propertyName: any }) => col.propertyName
        );
        for (const data of filteredData) {
          const alias = name.toLowerCase();
          // Find matching record with same testNoId and in a draft status
          let existing = null;
          if (alias == 'ctlog') {
            existing = await repo
              .createQueryBuilder(alias)
              .leftJoin(`${alias}.approvalStatus`, 'approvalStatus')
              .where(`${alias}.id = :id`, { id: data.id })
              .andWhere(`approvalStatus.name IN (:...statuses)`, { statuses: draftStatuses })
              .select([`${alias}.id`])
              .getOne();
          } else {
            existing = await repo
              .createQueryBuilder(alias)
              .leftJoin(`${alias}.approvalStatus`, 'approvalStatus')
              .where(`${alias}.testNoId = :id`, { id: data.id })
              .andWhere(`approvalStatus.name IN (:...statuses)`, { statuses: draftStatuses })
              .select([`${alias}.id`])
              .getOne();
          }
          if (!existing) continue;
          const updatePayload: Record<string, any> = {};
          // Prepare only the valid fields to be updated
          for (const [key, value] of Object.entries(data)) {
            if (key !== 'id' && value !== undefined && columnNames.includes(key)) {
              updatePayload[key] = value;
            }
          }
          // Only update if there's at least one changed field
          if (Object.keys(updatePayload).length > 0) {
            if (alias == 'ctlog') {
              await repo.update({ id: data.id }, updatePayload);
              const updated = await repo.findOne({ where: { id: data.id } });
              if (updated) updatedRecords.ctLog.push(updated);
            } else {
              await repo.update({ testNoId: data.id }, updatePayload);
              const updated = await repo.findOne({ where: { testNoId: data.id } });
              if (updated) {
                if (name === 'Sample') updatedRecords.sampleManagement.push(updated);
                else if (name === 'StSoilSandConeTest') updatedRecords.sandConeTest.push(updated);
                else if (name === 'StSoilNuclearGaugeTest')
                  updatedRecords.nuclearGaugeTest.push(updated);
              }
            }
          }
        }
      }
      return { updatedRecords };
    } catch (error) {
      console.error('Error updating DCE tables:', error);
      throw error;
    }
  }
}
const ctLogModel = new CTLogModel();
export default ctLogModel;
