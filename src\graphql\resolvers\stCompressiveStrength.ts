import { getManager, Between } from 'typeorm';
import { StCompressiveStrength } from '@entities/p_cs/StCompressiveStrength';

export const compressiveStrengthResolvers = {
  Query: {
    compressiveStrengthByMaterial: async (
      _: any,
      {
        materialId,
        startDate,
        endDate,
        projectId,
      }: { materialId: string; startDate: string; endDate: string; projectId: string }
    ) => {
      try {
        const repository = getManager().getRepository(StCompressiveStrength);

        // Convert string dates to Date objects
        const start = new Date(startDate);
        const end = new Date(endDate);

        // Query the database with date range and material ID
        const results = await repository.find({
          where: {
            materialId,
            projectId,
            dateTested: Between(start, end),
            isDelete: false,
          },
          relations: ['project', 'material', 'materialType', 'fractureType', 'cappingMethod'],
        });

        return results;
      } catch (error) {
        console.error('Error fetching compressive strength data:', error);
        throw new Error('Failed to fetch compressive strength data');
      }
    },

    compressiveStrengthByProject: async (
      _: any,
      {
        projectId,
        startDate,
        endDate,
        dateField = 'dateTested',
      }: {
        projectId: string;
        startDate?: string;
        endDate?: string;
        dateField?: string;
      }
    ) => {
      try {
        const repository = getManager().getRepository(StCompressiveStrength);
        const whereClause: any = {
          projectId,
          isDelete: false,
        };

        // Add date filtering if both dates are provided
        if (startDate && endDate) {
          const start = new Date(startDate);
          const end = new Date(endDate);
          whereClause[dateField] = Between(start, end);
        }

        const results = await repository.find({
          where: whereClause,
          relations: ['project', 'material', 'materialType', 'fractureType', 'cappingMethod'],
        });

        return results;
      } catch (error) {
        console.error('Error fetching compressive strength data:', error);
        throw new Error('Failed to fetch compressive strength data');
      }
    },
  },
};
