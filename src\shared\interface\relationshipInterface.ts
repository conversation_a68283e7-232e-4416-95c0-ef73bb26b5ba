import { DataCaptureElements } from '../../entities/p_meta/DataCaptureElements';

export interface ISampleSpecimenData {
  id: string;
  specimenAge?: number;
}

export interface ISampleTestRelation {
  id?: string;
  labId: string;
  testId: string;
  testMethodId: string;
  testVariantId: string;
  specimens: ISampleSpecimenData[];
  dce?: DataCaptureElements;
}
export interface ISampleTestRelationAddData {
  testId?: string;
  testMethodId?: string;
  testVariantId?: string;
  siteId?: string;
  sampleId: string;
  createdUserId: string;
  createdBy: string;
}
export interface ISampleTestRelationEditData {
  testId?: string;
  testMethodId?: string;
  testVariantId?: string;
  sampleId: string;
  updatedBy: string;
}
