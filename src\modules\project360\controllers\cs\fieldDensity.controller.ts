import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import fieldDensityModel from '../../models/cs/fieldDensity.model';
import { getUserRoleDetail } from '../../../../shared/utilities/role/getUserRoleDetails';
import { getUserById } from 'src/shared/server/platformApi/user';
import isUUID from '@utils/custom/isUUID';

class FieldDensityController {
  getAggregateData = async (req: Request, res: Response) => {
    try {
      const { id: projectId } = req.params;
      const dataControl = await getUserRoleDetail((req as any).user, projectId);

      const [nuclearTests, sandConeTests, moistureContentTests] = await Promise.all([
        fieldDensityModel.getNuclearTests(projectId, dataControl),
        fieldDensityModel.getSandConeTests(projectId, dataControl),
        fieldDensityModel.getMoistureContentTests(projectId, dataControl),
      ]);

      const allTests = [...nuclearTests, ...sandConeTests, ...moistureContentTests];

      const userIds = Array.from(
        new Set(
          allTests.map((test) => test.testedBy).filter((id): id is string => !!id && isUUID(id))
        )
      );

      const users = await Promise.all(userIds.map((id) => getUserById(id)));

      const userMap = new Map(users.map((u) => [u.id, `${u.firstName} ${u.lastName}`]));

      const resolveTestedBy = (value: string | null | undefined) => {
        if (!value) return '';
        if (isUUID(value)) return userMap.get(value) ?? value;
        return value;
      };
      const unifiedData = [
        ...nuclearTests.map((test) => ({
          id: test.id,
          testLocation: test.testLocation,
          status: test.testResult?.name ?? null,
          testedBy: resolveTestedBy(test.testedBy),
          testDate: test.dateTested,
          serialNo: test.projectNuclearGauges ? test.projectNuclearGauges.gaugeNo : null,
          easting: test.easting,
          northing: test.northing,
          testType: 'Nuclear Gauge',
          testNumber: test.ctLog?.testNo ?? null,
          proctorId: test.sample ? test.sample.testNo : null,
          requiredCompaction: test.requiredCompaction,
          maximumLabDensity: test.maximumLabDensity,
          optimumMoistureContent: test.optimumMoistureContent,
          elevation: test.elevation,
          wetDensity: test.wetDensity,
          moistureContent: test.moistureContent,
          dryDensity: test.dryDensity,
          submittalVersionId: test.submittalVersion ? test.submittalVersion.id : null,
          submittalVersion:
            test.submittalVersion &&
            test.submittalVersion.submittal &&
            test.submittalVersion.submittal.specification
              ? test.submittalVersion.submittal.specification.subSpecCode +
                '_' +
                test.submittalVersion.submittal.itemNo +
                '.' +
                test.submittalVersion.version
              : null,
          approvalStatus: test.approvalStatus,
        })),
        ...sandConeTests.map((test) => ({
          id: test.id,
          testLocation: test.testLocation,
          status: test.testResult?.name ?? null,
          testedBy: resolveTestedBy(test.testedBy),
          testDate: test.dateTested,
          serialNo: test.sandConeInfo ? test.sandConeInfo.name : null,
          easting: test.easting,
          northing: test.northing,
          testType: 'Sand Cone',
          testNumber: test.ctLog?.testNo ?? null,
          proctorId: test.sample ? test.sample.testNo : null,
          requiredCompaction: test.requiredCompaction,
          maximumLabDensity: test.maximumLabDensity,
          optimumMoistureContent: test.optimumMoistureContent,
          elevation: test.elevation,
          wetDensity: test.IPWetDensity,
          moistureContent: test.moistureContent,
          dryDensity: test.IPDryDensity,
          submittalVersionId: test.submittalVersion ? test.submittalVersion.id : null,
          submittalVersion:
            test.submittalVersion &&
            test.submittalVersion.submittal &&
            test.submittalVersion.submittal.specification
              ? test.submittalVersion.submittal.specification.subSpecCode +
                '_' +
                test.submittalVersion.submittal.itemNo +
                '.' +
                test.submittalVersion.version
              : null,
          approvalStatus: test.approvalStatus,
        })),
        ...moistureContentTests.map((test) => ({
          id: test.id,
          testLocation: '',
          testedBy: resolveTestedBy(test.testedBy),
          status: '',
          testDate: test.dateTested,
          testType: 'Moisture Content',
          testNumber: test.ctLog?.testNo ?? null,
          moistureContent: test.moistureContent,
          easting: test.sample ? test.sample.easting : null,
          northing: test.sample ? test.sample.northing : null,
          submittalVersionId: test.submittalVersion ? test.submittalVersion.id : null,
          submittalVersion:
            test.submittalVersion &&
            test.submittalVersion.submittal &&
            test.submittalVersion.submittal.specification
              ? test.submittalVersion.submittal.specification.subSpecCode +
                '_' +
                test.submittalVersion.submittal.itemNo +
                '.' +
                test.submittalVersion.version
              : null,
          approvalStatus: test.approvalStatus,
        })),
      ];

      unifiedData.sort((a, b) => {
        const dateA = a.testDate ? new Date(a.testDate).getTime() : 0; // Use 0 for undefined dates
        const dateB = b.testDate ? new Date(b.testDate).getTime() : 0; // Use 0 for undefined dates
        return dateB - dateA; // Change the order for descending sort
      });

      const message = unifiedData ? req.__('DataFoundMessage') : req.__('DataNotFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: unifiedData || {},
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  };
}

export default FieldDensityController;
