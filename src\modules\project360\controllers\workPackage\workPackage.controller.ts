import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import filterByUpdateAt from '../../../../shared/utilities/custom/filterDataByUpdateAt';
import workPackageModel from '@models/workPackage/workPackage.model';

class WorkpackageController {
  async add(req: Request, res: Response) {
    try {
      const { workPackage, activity } = req.body;
      if ((req as any).user) {
        workPackage.createdBy = (req as any).user.name;
        workPackage.updatedBy = (req as any).user.name;
      }
      const finalWorkPackageActivities: any[] = [];
      activity.map((value: any) => {
        const object: any = {};
        object.technicianIds = value.technicianIds;
        object.equipmentIds = value.equipmentIds;
        object.dceIds = value.dceIds;
        object.workPackageActivity = value.workPackageActivity;
        finalWorkPackageActivities.push(object);
      });
      const response = await workPackageModel.add(workPackage, finalWorkPackageActivities);
      return res.json({ isSucceed: true, data: response, msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async edit(req: Request, res: Response) {
    try {
      const { workPackage, activity } = req.body;
      const { id } = req.params;
      if ((req as any).user) {
        workPackage.createdBy = (req as any).user.name;
        workPackage.updatedBy = (req as any).user.name;
      }
      const finalWorkPackageActivities: any[] = [];
      activity.map((value: any) => {
        const object: any = {};
        object.technicianIds = value.technicianIds;
        object.equipmentIds = value.equipmentIds;
        object.dceIds = value.dceIds;
        object.workPackageActivity = value.workPackageActivity;
        finalWorkPackageActivities.push(object);
      });
      const response = await workPackageModel.edit(workPackage, finalWorkPackageActivities, id);
      return res.json({ isSucceed: true, data: response, msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getByProjectId(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const { fromDate, toDate }: { fromDate?: string; toDate?: string } = req.query;

      const response = await workPackageModel.getByProjectId(id, fromDate, toDate);
      return res.json({ isSucceed: true, data: response, msg: 'Data added' });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getByUserId(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const {
        type,
        fromDate,
        toDate,
        updatedAfter,
      }: { type?: string; fromDate?: string; toDate?: string; updatedAfter?: string } = req.query;

      if (type && type.toLowerCase() == 'mobile') {
        let response = await workPackageModel.getByUserIdForMobile(id, fromDate, toDate);
        if (updatedAfter) {
          const givenDate = new Date(updatedAfter);
          response = filterByUpdateAt(givenDate, response);
        }
        return res.json({ isSucceed: true, data: response, msg: 'Data found' });
      } else {
        const response = await workPackageModel.getByUserId(id, fromDate, toDate);
        return res.json({ isSucceed: true, data: response, msg: 'Data found' });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default WorkpackageController;
