import { AfterLoad, <PERSON>umn, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { Project } from '../p_gen/Project';
import { convertEastingNorthingToLatLongOnGetData } from '../../shared/utilities/spatialCoordinates/convertEastingOnGetData';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { PvCutMaster } from './PvCutMaster';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';
@Entity({ schema: 'p_cs' })
export class PvCutInformation extends SubDCEColumns {
  @Column({ type: 'uuid', nullable: true })
  projectId?: string;

  @ManyToOne(() => Project)
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ type: 'uuid', nullable: true })
  cutMasterId?: string;

  @ManyToOne(() => PvCutMaster)
  @JoinColumn({ name: 'cutMasterId' })
  cutMaster?: PvCutMaster;

  @ColumnInfo({
    customData: {
      name: 'Unique Label',
      fieldName: 'uniqueLabel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  uniqueLabel!: string;

  @ColumnInfo({
    customData: {
      name: 'Northing Start',
      fieldName: 'northingStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northingStart?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting Start',
      fieldName: 'eastingStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  eastingStart?: number;

  @ColumnInfo({
    customData: {
      name: 'Stn_Start',
      fieldName: 'stationStart',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'varchar', nullable: true })
  stationStart?: string;

  @ColumnInfo({
    customData: {
      name: 'Depth',
      fieldName: 'depth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  depth?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation',
      fieldName: 'elevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  elevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Panel Length',
      fieldName: 'panelLength',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  panelLength?: number;

  @ColumnInfo({
    customData: {
      name: 'Platform Level',
      fieldName: 'platformLevel',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  platformLevel?: number;

  @ColumnInfo({
    customData: {
      name: 'Contract Top Wall Elev',
      fieldName: 'contractTopWallElev',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  contractTopWallElev?: number;

  @ColumnInfo({
    customData: {
      name: 'Contract Bottom Wall Elev',
      fieldName: 'contractBottomWallElev',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  contractBottomWallElev?: number;

  longitudeStart?: string | null;

  latitudeStart?: string | null;

  @AfterLoad()
  async afterLoad() {
    try {
      if (this.eastingStart && this.northingStart) {
        const data = convertEastingNorthingToLatLongOnGetData(
          this.eastingStart,
          this.northingStart
        );
        if (data && data?.latitude && data.longitude) {
          this.latitudeStart = data?.latitude;
          this.longitudeStart = data.longitude;
        }
      }
    } catch (error) {
      this.latitudeStart = null;
      this.longitudeStart = null;
    }
  }
}
