import express, { Router } from 'express';
import CrudController from '../../../generic/crudDriver.controller';
import { authenticateToken } from '../../../../shared/middlewares/auth.middleware';
import { SieveConfig } from '@entities/p_meta/SieveConfig';
import sieveConfigController from '@controllers//meta/sieveConfig.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const genericController = new CrudController<SieveConfig>(SieveConfig);

// Mount the userRouter for CRUD operations at /auth/user/crud

router.post('/', authenticateToken, (req, res) => genericController.create(req, res));
router.get('/:id', authenticateToken, genericController.findById);
router.get(
  '/material/:materialId/testMethod/:testMethodId',
  authenticateToken,
  sieveConfigController.findByMateriaIdAndTestMethodId
);
router.get('/', authenticateToken, (req, res) => genericController.findAll(req, res));
router.put('/:id', authenticateToken, (req, res) => genericController.update(req, res));

export default router;
