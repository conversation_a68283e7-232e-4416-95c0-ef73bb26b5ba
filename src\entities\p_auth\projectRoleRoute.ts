import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  PrimaryColumn,
} from 'typeorm';

import { ProjectRole } from './Role';
import { Route } from './Route';

@Entity({ schema: 'p_auth' })
export class ProjectRoleRoute {
  @PrimaryColumn('uuid')
  projectRoleId!: string;

  @PrimaryColumn('uuid')
  routeId!: string;

  @Column({ nullable: true, type: 'varchar', default: '00000' })
  permission?: string;

  @ManyToOne(() => ProjectRole, { nullable: true }) 
  @JoinColumn({ name: 'projectRoleId' })
  projectRole?: ProjectRole;

  @ManyToOne(() => Route, { nullable: true }) 
  @JoinColumn({ name: 'routeId' })
  route?: Route;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcqa!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
