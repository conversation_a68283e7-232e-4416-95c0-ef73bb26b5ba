import { Request, Response } from 'express';
import errorMiddleware from '../../../../shared/middlewares/error/error.middleware';
import LayerParentModel from '../../models/map/layerParent.model';
import changeOrderSorting from '../../../../shared/utilities/custom/changeOrder';
import { LayerParent } from '../../../../entities/p_map/LayerParent';
import GeoJsonConfigurationModel from '../../models/map/geoJSONConfig.model';
import { Layer } from '../../../../entities/p_map/Layer';
import isUUID from '../../../../shared/utilities/custom/isUUID';
import LayerModel from '../../models/map/layer.model';

class LayerParentController {
  async getByProjectId(req: Request, res: Response) {
    try {
      const model = new LayerParentModel();
      const { id } = req.params;
      const { type } = req.query;
      let data: LayerParent[] = [];
      if (type && type === 'mobile') {
        const geoJsonConfigurationModel = new GeoJsonConfigurationModel();
        const layerModel = new LayerModel();
        const dataFromDB = await model.getByProjectIdForMobile(id);
        for (const item of dataFromDB) {
          const layers: Layer[] = [];
          for (const element of item.layer) {
            try {
              if (element.id) {
                try {
                  const columns = await layerModel.getColumnName(element.id);
                  if (columns && columns?.length > 0) {
                    const data = await geoJsonConfigurationModel.getData(
                      columns || [],
                      id,
                      element.id,
                      (req as any).user.name
                    );
                    element.popupConfig = data;
                  }
                } catch (error) {}
              }
              if (element.label) {
                const isUU = isUUID(element.label);
                if (isUU) {
                  const lableColumnName =
                    await geoJsonConfigurationModel.getByIdLayerLableConversion(element.label);
                  if (lableColumnName.name) {
                    element.label = lableColumnName.name;
                  }
                }
              }
              layers.push(element);
            } catch (error) {
              throw error;
            }
          }
          if (layers.length > 0) {
            item.layer = layers;
          }
          data.push(item);
        }
      } else {
        data = await model.getByProjectId(id);
      }
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: 'data found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async changeOrder(req: Request, res: Response) {
    try {
      const model = new LayerParentModel();
      const { oldIndex, targetIndex, projectId } = req.body;
      const data = await model.getByProjectId(projectId);
      const newOrderArray = changeOrderSorting(data, oldIndex, targetIndex);
      const orderChange = newOrderArray.map((value: any, index: number) => {
        return { id: value.id, order: index + 1 };
      });
      await model.changeOrder(orderChange);
      res.json({ isSucceed: true, data: [], msg: 'Order Changed' });
    } catch (error) {
      errorMiddleware(error, 500, res, req);
    }
  }
}

export default LayerParentController;
