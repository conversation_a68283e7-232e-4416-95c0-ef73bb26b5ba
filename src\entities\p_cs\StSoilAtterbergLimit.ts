// Import necessary modules from TypeORM
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Sample } from './Sample';
import { Test } from '../p_meta/Test';
import { TestMethod } from '../p_meta/TestMethod';
import { TestVariant } from '../p_meta/Testvariant';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Project } from '../p_gen/Project';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { StSoilAtterbergLimitWorksheet } from './StSoilAtterbergLimitWorksheet';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { EventLog } from '@entities/p_gen/EventLog';
import { SieveSize } from '@entities/p_domain/SieveSize';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

// Define the entity class
@Entity({ schema: 'p_cs' })
export class StSoilAtterbergLimit extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  passFail?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ nullable: true })
  maximumGrainSize?: string;

  @ManyToOne(() => SieveSize, { nullable: true })
  @JoinColumn({ name: 'maximumGrainSize' })
  sieveSize?: SieveSize;

  @Column({ type: 'decimal', nullable: true })
  no40RetainedPercentage?: number;

  @Column({ nullable: true })
  samplePreparation?: string;

  @Column({ nullable: true })
  equipmentUsed?: string;

  @Column({ type: 'decimal', nullable: true })
  sampleDepth?: number;

  @Column({ type: 'decimal', nullable: true })
  liquidLimit?: number;

  @Column({ type: 'decimal', nullable: true })
  plasticLimit?: number;

  @Column({ type: 'decimal', nullable: true })
  plasticityIndex?: number;

  @Column({
    type: 'boolean',
    nullable: true,
    transformer: {
      to: (value: boolean) => value,
      from: (value: string | boolean) => value === 'true' || value === true,
    },
  })
  nonPlasticSoil?: string;

  @Column({ nullable: true })
  symbolPlasticityChart?: string;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false, nullable: true })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @OneToMany(() => StSoilAtterbergLimitWorksheet, (worksheet) => worksheet.atterbergTest, {
    cascade: true,
  })
  atterbergLimitWorksheet?: StSoilAtterbergLimitWorksheet[];

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
