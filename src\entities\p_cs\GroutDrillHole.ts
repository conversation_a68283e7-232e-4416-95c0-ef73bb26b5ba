import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
  Point,
  BeforeInsert,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class GroutDrillHole extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Borehole Id',
      fieldName: 'boreholeNumber',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  boreholeNumber?: string;

  @ColumnInfo({
    customData: {
      name: 'Primary/ Secondary/ Tertiary',
      fieldName: 'boreholeType',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  boreholeType?: string;

  @ColumnInfo({
    customData: {
      name: 'Driller Name',
      fieldName: 'drillerName',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  drillerName?: string;

  @ColumnInfo({
    customData: {
      name: 'Drill Inspector',
      fieldName: 'drillInspector',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  drillInspector?: string;

  @ColumnInfo({
    customData: {
      name: 'Drill Rig',
      fieldName: 'drillRig',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  drillRig?: string;

  @ColumnInfo({
    customData: {
      name: 'Drill Start Time',
      fieldName: 'drillStartTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  drillStartTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Drill End Time',
      fieldName: 'drillEndTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  drillEndTime?: Date;

  @ColumnInfo({
    customData: {
      name: 'Longitude',
      fieldName: 'longitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  longitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Latitude',
      fieldName: 'latitude',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  latitude?: number;

  @ColumnInfo({
    customData: {
      name: 'Easting',
      fieldName: 'easting',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  easting?: number;

  @ColumnInfo({
    customData: {
      name: 'Northing',
      fieldName: 'northing',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  northing?: number;

  @ColumnInfo({
    customData: {
      name: 'Station',
      fieldName: 'station',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  station?: string;

  @ColumnInfo({
    customData: {
      name: 'Offset',
      fieldName: 'offset',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  offset?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation top of the rock',
      fieldName: 'rockTopElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  rockTopElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation top of the hole',
      fieldName: 'holeTopElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  holeTopElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Elevation bottom of the hole',
      fieldName: 'holeBottomElevation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  holeBottomElevation?: number;

  @ColumnInfo({
    customData: {
      name: 'Design (planned) depth of the hole',
      fieldName: 'designHoleDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  designHoleDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Final depth of the hole',
      fieldName: 'finalHoleDepth',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  finalHoleDepth?: number;

  @ColumnInfo({
    customData: {
      name: 'Top depth of the drilling interval',
      fieldName: 'topDepthInterval',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  topDepthInterval?: number;

  @ColumnInfo({
    customData: {
      name: 'Bottom depth of the drilling interval',
      fieldName: 'botDepthInterval',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  botDepthInterval?: number;

  @ColumnInfo({
    customData: {
      name: 'Angle',
      fieldName: 'angle',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  angle?: number;

  @ColumnInfo({
    customData: {
      name: 'Orientation',
      fieldName: 'orientation',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  orientation?: number;

  @ColumnInfo({
    customData: {
      name: 'Hole Diameter',
      fieldName: 'holeDiameter',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  holeDiameter?: number;

  @ColumnInfo({
    customData: {
      name: 'Section Line',
      fieldName: 'sectionLine',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  sectionLine?: string;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column('geometry', { spatialFeatureType: 'Point', srid: 4326, nullable: true })
  geom?: Point;

  @BeforeInsert()
  updateLocationGeometry() {
    if (this.latitude !== undefined && this.longitude !== undefined) {
      this.geom = {
        type: 'Point',
        coordinates: [this.longitude, this.latitude],
      };
    }
  }
}
