import { Request, Response } from 'express';
// import { Config } from '../../entities/g_utils/Config';
import SFTP from 'ssh2-sftp-client';
import { AzureBlobStorageService } from './azureBlobStorageService';
import errorMiddleware from '../../middlewares/error/error.middleware';

class SFTPGetFile {
  determineContentType(fileName: string): string {
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }

  getFilenameFromPath(filePath: string): string {
    // Extract filename from the path
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  }

  async serveSftpFile(req: Request, res: Response, remoteFilePath: string) {
    const sftp = new SFTP();
    try {
      // eslint-disable-next-line prefer-const
      // const configs = await getManager()
      //   .getRepository(Config)
      //   .find({
      //     where: {
      //       key: Like('sftp%'),
      //     },
      //   });

      // const host = configs.filter((conf) => {
      //   if (conf.key === 'sftp.host') return conf.value;
      // });
      // const user = configs.filter((conf) => {
      //   if (conf.key === 'sftp.username') return conf.value;
      // });
      // const pass = configs.filter((conf) => {
      //   if (conf.key === 'sftp.password') return conf.value;
      // });
      await sftp.connect({
        // host: host[0].value,
        // port: 22,
        // username: user[0].value,
        // password: pass[0].value,
        timeout: 60000,
      });
      const fileData = await sftp.get(remoteFilePath); // Use await to handle the promise
      const contentType = this.determineContentType(remoteFilePath);
      const filename = this.getFilenameFromPath(remoteFilePath);
      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
      sftp.end(); // Close the connection after getting the file

      return res.send(fileData);
    } catch (error) {
      sftp.end(); // Ensure the connection is closed in case of an error
      res.status(500).json({ error: 'Failed to get the file from SFTP server.' });
    } finally {
      sftp.end();
    }
  }

  async getBlobFile(req: Request, res: Response, remoteFilePath: string) {
    try {
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );

      const file = await azureBlobStorageService.downloadFile(req.params.projectId, remoteFilePath);
      const contentType = this.determineContentType(remoteFilePath);
      const filename = this.getFilenameFromPath(remoteFilePath);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);

      return res.send(file);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async getBlobFileByProject(remoteFilePath: string, projectId: string) {
    try {
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );

      const file = await azureBlobStorageService.downloadFile(projectId, remoteFilePath);

      return file;
    } catch (error) {
      throw error;
    }
  }

  async serveSftpFileAsStream(req: Request, res: Response, remoteFilePath: string) {
    // const sftp: SftpClient = await connectSftp();
    // try {
    //   const contentType = this.determineContentType(remoteFilePath);
    //   const filename = this.getFilenameFromPath(remoteFilePath);
    //   res.setHeader('Content-Type', contentType);
    //   res.setHeader('Content-Disposition', `inline; filename="${filename}"`);
    //   // Stream the file in chunks
    //   const readStream = await sftp.createReadStream(remoteFilePath, {
    //     highWaterMark: 1024 * 1024,
    //   });
    //   readStream.pipe(res);
    //   // Close the SFTP connection once the stream is finished
    //   readStream.on('end', () => {
    //     closeSftpConnection();
    //   });
    //   // Handle any errors during streaming
    //   readStream.on('error', (error: any) => {
    //     console.error(error);
    //     closeSftpConnection();
    //     res.status(500).json({ error: 'Failed to stream the file from SFTP server.' });
    //   });
    // } catch (error) {
    //   console.error(error);
    //   closeSftpConnection();
    //   res.status(500).json({ error: 'Failed to get the file from SFTP server.' });
    // }
  }
}

export default SFTPGetFile;
