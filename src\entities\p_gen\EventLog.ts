import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Project } from './Project'; // Import the Projects entity
import { Resource } from './Resource';
import { WorkPackage } from './WorkPackage';
import { WorkPackageActivity } from './WorkPackageActivity';
import { ProjectEquipment } from './ProjectEquipment';

@Entity({ schema: 'p_gen' })
export class EventLog {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column({ nullable: true })
  resourceId?: string;

  @Column({ nullable: true })
  userId?: string;

  @ManyToOne(() => Resource, { nullable: true })
  @JoinColumn({ name: 'resourceId' })
  resource?: Resource;

  @Column({ nullable: true })
  equipmentId?: string;

  @ManyToOne(() => ProjectEquipment, { nullable: true })
  @JoinColumn({ name: 'equipmentId' })
  equipment?: ProjectEquipment;

  @Column({ nullable: true })
  workPackageId?: string;

  @ManyToOne(() => WorkPackage, { nullable: true })
  @JoinColumn({ name: 'workPackageId' })
  workPackage?: WorkPackage;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ type: 'timestamp', nullable: true })
  startTime?: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime?: Date;

  @Column({ nullable: true })
  travelTime?: string;

  @Column({ nullable: true })
  travelTimeFromSite?: string;

  @Column({ nullable: true })
  travelTimeToSite?: string;

  @Column({ nullable: true })
  checkInDescription?: string;

  @Column({ nullable: true })
  checkOutDescription?: string;

  @Column({ nullable: true })
  truckNo?: string;

  @Column({ nullable: true })
  startMileage?: string;

  @Column({ nullable: true })
  endMileage?: string;

  @Column({ nullable: true })
  startWeatherDescription?: string;

  @Column({ nullable: true })
  endWeatherDescription?: string;

  @Column({ nullable: true, type: 'decimal' })
  startTemperature?: number;

  @Column({ nullable: true, type: 'decimal' })
  endTemperature?: number;

  @Column({ nullable: true })
  notes?: string;

  @Column({ nullable: true })
  startTemperatureUnit?: string;

  @Column({ nullable: true })
  endTemperatureUnit?: string;

  @Column({ type: 'decimal', nullable: true })
  startLongitude?: number;

  @Column({ type: 'decimal', nullable: true })
  startLatitude?: number;

  @Column({ type: 'decimal', nullable: true })
  endLongitude?: number;

  @Column({ type: 'decimal', nullable: true })
  endLatitude?: number;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
