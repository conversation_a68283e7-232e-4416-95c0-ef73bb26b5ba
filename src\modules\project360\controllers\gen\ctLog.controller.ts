import { Request, Response } from 'express';
import ctLogModel from '@models/gen/ctLog.model';
import testLogPrefixModel from '@models/gen/testLogPrefix.model';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';
import sampleModel from '@models/sample.model';
import dceRelationshipModel from '@models/utils/dceRelationship.model';
import {
  getTestSummaryForNuclearGauge,
  getTestSummaryForSandCone,
} from '@utils/relationship/testSummary';

class CTLogController {
  constructor() {}
  async getNextCTNoByProjectIdAndPurposedId(req: Request, res: Response) {
    try {
      const { projectId, purposeId } = req.params;

      if (!projectId || !purposeId) {
        // Handle missing parameters
        const errorMessage = req.__('InvalidInputDataError');
        return res.status(400).json({
          isSucceed: false,
          msg: errorMessage,
        });
      }
      const currentTestNo =
        (await ctLogModel.getTestNoByProjectIdAndPurposeId(projectId, purposeId)) ?? 0;
      const testLogPrefix = await testLogPrefixModel.getByTestLogPrefixByProjectIdAndPurposeId(
        projectId,
        purposeId
      );
      const nextTestNo = currentTestNo + 1;

      const formattedTestNo = `${testLogPrefix}-${String(nextTestNo).padStart(5, '0')}`;

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: formattedTestNo,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getTestSummary(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const nuclearGauge = await getTestSummaryForNuclearGauge(id);
      const sandCone = await getTestSummaryForSandCone(id);
      const sample = await sampleModel.getSampleByTestNo(id);

      const testSummary = [];

      if (sample) {
        const sampleTestSummary = await dceRelationshipModel.getSampleTestsBySampleId(sample?.id);
        testSummary.push(...sampleTestSummary);
      }
      if (nuclearGauge) testSummary.push(nuclearGauge);
      if (sandCone) testSummary.push(sandCone);

      return res.status(200).json({
        isSucceed: true,
        data: {
          testSummary,
        },
        msg: req.__('DataFoundMessage'),
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addMultipleTests(req: Request, res: Response) {
    try {
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
        req.body.createdUserId = (req as any).user.id;
      }

      const { noOfTests, ...testLogData } = req.body;

      const tests = await ctLogModel.addMultipleTests(testLogData, noOfTests);
      const message = req.__('DataFoundMessage');
      return res.status(200).json({
        isSucceed: true,
        data: tests,
        msg: message,
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

export default CTLogController;
