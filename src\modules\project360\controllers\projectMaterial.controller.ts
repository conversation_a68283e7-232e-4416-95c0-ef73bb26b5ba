import { Request, Response } from 'express';
import projectMaterialModel from '../models/projectMaterial.model';

class ProjectMaterialController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const projectMaterialData = await projectMaterialModel.findById(req.params.id);
      // checking if data is found with the id
      if (projectMaterialData) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: projectMaterialData,
          msg: 'project material found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project material data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByProjectId(req: Request, res: Response) {
    try {
      const projectMaterialData = await projectMaterialModel.findByProjectId(req.params.id);

      if (projectMaterialData && projectMaterialData.length > 0) {
        return res.status(200).json({
          isSucceed: true,
          data: projectMaterialData,
          msg: 'project material found',
        });
      } else {
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project material data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByMaterialTypeAndProjectId(req: Request, res: Response) {
    try {
      const projectMaterialData = await projectMaterialModel.findByMaterialTypeAndProjectId(
        req.params.materialTypeId,
        req.params.projectId
      );
      // getting the data from database with the given id

      if (projectMaterialData && projectMaterialData.length > 0) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: projectMaterialData,
          msg: 'project material found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No project material data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async delete(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const projectMaterialData = await projectMaterialModel.deleteById(req.params.id);

      // checking if data is found with the id
      if (projectMaterialData.affected == 1) {
        const message = req.__('DeleteSuccess');
        // if true data will send as response
        return res.status(204).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      } else {
        const message = req.__('NoRecordFoundForDelete');
        // if false send error as response
        return res.status(404).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async addMaterial(req: Request, res: Response) {
    try {
      req.body.createdBy = (req as any).user.name;
      req.body.updatedBy = (req as any).user.name;
      req.body.projectId = req.body.projectid;
      const projectData = await projectMaterialModel.addmaterial(req.body);

      return res.status(200).json({ isSucceed: true, data: projectData, msg: 'Data added' });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }
}

const projectMaterialController = new ProjectMaterialController();
export default projectMaterialController;
