import express, { Router } from 'express';
import CrudController from '../../generic/crudDriver.controller';
import { authenticateToken } from '../../../shared/middlewares/auth.middleware';
import { PhotoVideo } from '../../../entities/p_gen/PhotoVideo';
import PhotoVideoController from '../controllers/photoVideo.controller';

const router: Router = express.Router();

// Create a generic router for the User entity
const genericController = new CrudController<PhotoVideo>(PhotoVideo);
const photoVideoController = new PhotoVideoController();
// Mount the userRouter for CRUD operations at /auth/user/crud

router.post('/', authenticateToken, photoVideoController.addPhotoVideo);
router.get('/', authenticateToken, genericController.findAll);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  genericController.sendForApproval(req, res, 'photoVideo')
);
router.get('/:id', authenticateToken, photoVideoController.getByIdt);
router.put('/:id', authenticateToken, (req, res) => genericController.update(req, res));
router.get('/by/project/:id', authenticateToken, (req, res) =>
  genericController.findByProjectId(req, res, 'photoVideo')
);
router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  genericController.getDataCountByProjectId(req, res, 'photoVideo')
);
router.delete('/:id', authenticateToken, photoVideoController.deletePhotoVideoWithApproval);

export default router;
