import { Request, Response } from 'express';
import hydraulicConductivityWorksheetModel from '../../models/cs/hydraulicConductivityWorksheet.model';

class HydraulicConductivityWorksheetController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const hydraulicConductivityData = await hydraulicConductivityWorksheetModel.findById(
        req.params.id
      );

      // checking if data is found with the id
      if (hydraulicConductivityData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: hydraulicConductivityData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByIdWithCalculatedValues(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const hydraulicConductivityData =
        await hydraulicConductivityWorksheetModel.findByIdWithCalculatedValue(req.params.id);
      // checking if data is found with the id
      if (hydraulicConductivityData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: hydraulicConductivityData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findAllByHCTestId(req: Request, res: Response) {
    try {
      const worksheetData = await hydraulicConductivityWorksheetModel.findWorksheetByHCTestId(
        req.params.id
      );
      let CalcuTableData = [];
      const calcuUsedWorksheet = worksheetData.filter((value) => value.usedInCalc);
      if (calcuUsedWorksheet.length > 0) {
        // getting the data from database with the given id
        CalcuTableData =
          await hydraulicConductivityWorksheetModel.findByHCTestId(calcuUsedWorksheet);
      }

      // checking if data is found with the id
      if (worksheetData.length > 0 && CalcuTableData.length > 0) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: { worksheet: worksheetData, calculatedTable: CalcuTableData },
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: { worksheet: worksheetData, calculatedTable: CalcuTableData },
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }
}

const hydraulicConductivityWorksheetController = new HydraulicConductivityWorksheetController();
export default hydraulicConductivityWorksheetController;
