import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyTo<PERSON>ne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from './Project';
import { NuclearGaugesInfo } from '../p_domain/NuclearGaugesInfo';
import { Purpose } from '../p_meta/Purpose';

@Entity({ schema: 'p_gen' })
export class ProjectNuclearGauges {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true }) 
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  gaugeId?: string;

  @ManyToOne(() => NuclearGaugesInfo, { nullable: true }) 
  @JoinColumn({ name: 'gaugeId' })
  nuclearGaugesInfo?: NuclearGaugesInfo;

  @Column()
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true }) 
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;
}
