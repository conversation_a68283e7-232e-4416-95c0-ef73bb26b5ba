import {
  extractDataFromExcel,
  extractDataFromExcelWithGroupForEarthwork,
  extractDataFromExcelWithGroupForWater,
} from './getValuesFromExcel';
import importObjectConverter from './importObjectConverter';
import objectToDatabase from './objectToDatabase';

class ImportSpeceficFunction {
  async earthWorkForNCAndSC(data: Buffer, projectId: string) {
    try {
      if (data) {
        const dataFromExcel = await extractDataFromExcel(data);
        const contvertedData = await importObjectConverter.densityTest(dataFromExcel, projectId);
        await objectToDatabase.fieldDensityTestWithEntity(contvertedData);

        // importValidation.fieldDensityImportNuclearGaugeValidation(dataFromExcel);
        return contvertedData;
      }
    } catch (error) {
      // error response
      throw error;
    }
  }

  async earthWork(data: Buffer, projectId: string) {
    try {
      if (data) {
        const dataFromExcel = await extractDataFromExcelWithGroupForEarthwork(data);

        const contvertedData = await importObjectConverter.earthWork(dataFromExcel, projectId);
        await objectToDatabase.fieldWaterWithEntity(contvertedData);

        return contvertedData;
      }
    } catch (error) {
      // error response
      throw error;
    }
  }

  async water(data: Buffer, projectId: string) {
    try {
      if (data) {
        const dataFromExcel = await extractDataFromExcelWithGroupForWater(data);

        const contvertedData = await importObjectConverter.water(dataFromExcel, projectId);
        await objectToDatabase.fieldWaterWithEntity(contvertedData);
        return contvertedData;
      }
    } catch (error) {
      // error response
      throw error;
    }
  }

  async concrete(data: Buffer, projectId: string) {
    try {
      if (data) {
        const dataFromExcel = await extractDataFromExcel(data);

        const contvertedData = await importObjectConverter.concrete(dataFromExcel, projectId);
        return contvertedData;
      }
    } catch (error) {
      // error response
      throw error;
    }
  }
}

const importSpeceficFunction = new ImportSpeceficFunction();
export default importSpeceficFunction;
