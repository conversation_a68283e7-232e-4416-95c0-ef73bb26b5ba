import { getManager } from 'typeorm';

import { SFTPConfig } from '../../../entities/p_gen/SFTPConfig';

class SFTPConfidModel {
  constructor() {}

  async findByProject(projectId: string) {
    try {
      return await getManager()
        .getRepository(SFTPConfig)
        .find({
          where: { projectId, isDelete: false },
          relations: ['activity', 'dce'],
        });
    } catch (error) {
      throw error;
    }
  }

  async findByDCEId(dceId: string, projectId: string) {
    try {
      return await getManager()
        .getRepository(SFTPConfig)
        .findOne({
          where: { dceId, isDelete: false, projectId },
          relations: ['activity', 'dce'],
        });
    } catch (error) {
      throw error;
    }
  }
}

const sftpConfidModel = new SFTPConfidModel();
export default sftpConfidModel;
