import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  JoinColumn,
  ManyToOne,
} from 'typeorm';

import { Sample } from './Sample';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class StWaterTest extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @Column({ nullable: true, type: 'decimal' })
  pH?: number;

  @Column()
  testNo?: string;

  @Column({ nullable: true, type: 'decimal' })
  temperature?: number; // In Celsius

  @Column({ nullable: true, type: 'decimal' })
  conductivity?: number; // In µS/cm

  @Column({ nullable: true, type: 'decimal' })
  turbidity?: number; // In NTU

  @Column({ nullable: true, type: 'decimal' })
  chloride?: number; // In ppm

  @Column({ nullable: true, type: 'decimal' })
  sulfate?: number; // In ppm

  @Column({ nullable: true, type: 'decimal' })
  totalSolId?: string; // In ppm

  @Column({ nullable: true })
  tester?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
