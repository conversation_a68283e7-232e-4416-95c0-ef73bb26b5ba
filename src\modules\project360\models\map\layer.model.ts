import { In, <PERSON><PERSON><PERSON>, getManager } from 'typeorm';
import { Layer } from '../../../../entities/p_map/Layer';

import CreateTemplate from '../../../../shared/utilities/import/importByTable/createDownloadTemplate';
import GeomLayerModel from './geomLayer.model';
import { Legend } from '../../../../entities/p_map/Legend';
import { GeomLayer } from '../../../../entities/p_map/GeomLayer';
import { FeatureCollection, GeoJsonProperties, Geometry } from 'geojson';
import { EntityListInterface, entityList } from '../../../../shared/utilities/entity/entityList';
import dceModel from '../meta/dce.model';

class LayerModel {
  async getLayerGeoJson(
    layerId: string,
    user: any,
    type?: string,
    filters?: {
      date: {
        startDate: Date;
        endDate: Date;
      };
    }
  ) {
    try {
      const layerRepo = getManager().getRepository(Layer);

      const layerDetails = await layerRepo.findOne({
        where: {
          id: layerId,
        },
        relations: ['dce'],
      });
      if (layerDetails && layerDetails.projectId) {
        if (layerDetails.dce) {
          const keyVariable = layerDetails.dce.entity;
          if (keyVariable && keyVariable in entityList) {
            const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
            // Now 'entityValue' holds the entity type corresponding to the keyVariable
            const createTemplateObj = new CreateTemplate(entityValue);
            if (type && type === 'config') {
              const data = await createTemplateObj.getDataForMap(
                layerDetails.projectId,
                keyVariable,
                true,
                user
              );

              return data || {};
            } else {
              const data = await createTemplateObj.getData(
                layerDetails.projectId,
                keyVariable,
                true,
                user,
                filters,
                layerId
              );

              return data || {};
            }
          } else {
            throw new Error(`Key '${keyVariable}' not found in the object.`);
          }
        } else {
          const geomModel = new GeomLayerModel();
          const data = await geomModel.getLayerDataAsGeoJson(layerId);
          return data || {};
        }
      } else {
        throw new Error('Data not Found');
      }
    } catch (error) {
      throw error;
    }
  }

  async getLayerName(layerId: string): Promise<string> {
    try {
      const layerRepo = getManager().getRepository(Layer);
      const layerDetails = await layerRepo.findOne({ where: { id: layerId } });

      if (layerDetails && layerDetails.name) {
        return layerDetails.name;
      } else {
        throw new Error('Layer not found or name is missing');
      }
    } catch (error) {
      throw error;
    }
  }
  async getLayerGeoJsonByEnity(entity: string, projectId: string, user: any) {
    try {
      const layerRepo = getManager().getRepository(Layer);
      const dceData = await dceModel.findByEntity(entity);
      if (!dceData) {
        throw new Error('DCE not Found');
      }
      const layerDetails = await layerRepo.findOne({
        where: { projectId, dceId: dceData?.id, isDelete: false },
        relations: ['dce'],
      });
      if (layerDetails && layerDetails.projectId) {
        if (layerDetails.dce) {
          const keyVariable = layerDetails.dce.entity;
          if (keyVariable && keyVariable in entityList) {
            const entityValue: any = entityList[keyVariable as keyof EntityListInterface];
            // Now 'entityValue' holds the entity type corresponding to the keyVariable
            const createTemplateObj = new CreateTemplate(entityValue);

            const data = await createTemplateObj.getData(layerDetails.projectId, keyVariable, user);

            return data || {};
          } else {
            throw new Error(`Key '${keyVariable}' not found in the object.`);
          }
        } else {
          throw new Error('DCE not Found');
        }
      } else {
        throw new Error('Data not Found');
      }
    } catch (error) {
      throw error;
    }
  }

  async addLayerDataWithDce(layer: Partial<Layer>, legend: Partial<Legend>) {
    try {
      const entityManager = getManager();

      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const addedLayer = await transactionalEntityManager.save(Layer, layer);
          legend.layerId = addedLayer.id;
          await transactionalEntityManager.save(Legend, legend);
          return addedLayer;
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async addGeomDataWithLayer(
    shapefileData: FeatureCollection<Geometry, GeoJsonProperties>,
    projectId: string,
    newLayer: Partial<Layer>,
    legend: Partial<Legend>
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        try {
          const addedLayer = await transactionalEntityManager.save(Layer, newLayer);
          legend.layerId = addedLayer.id;
          await transactionalEntityManager.save(Legend, legend);
          await Promise.all(
            shapefileData.features.map(async (value) => {
              const geomLayer = new GeomLayer();
              const coordinates = (value.geometry as any).coordinates;

              geomLayer.projectId = projectId;
              geomLayer.layerId = addedLayer.id;
              geomLayer.properties = value.properties;
              geomLayer.type = value.type;

              geomLayer.geom = {
                type: value.geometry.type,
                coordinates,
              };
              await transactionalEntityManager.save(GeomLayer, geomLayer);
            })
          );
          return addedLayer;
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async addLayerWithoutDCEorShape(newLayer: Partial<Layer>, legend: Partial<Legend>) {
    try {
      const entityManager = getManager().getRepository(Layer);
      const addedLayer = await entityManager.save(newLayer);
      legend.layerId = addedLayer.id;
      await getManager().getRepository(Legend).save(legend);
    } catch (error) {
      throw error;
    }
  }

  async editLayerDataWithDce(
    layerId: string,
    legendId: string,
    layer: Partial<Layer>,
    legend: Partial<Legend>
  ) {
    try {
      const entityManager = getManager();

      return await entityManager.transaction(async (transactionalEntityManager) => {
        await transactionalEntityManager.update(Layer, { id: layerId }, layer);
        await transactionalEntityManager.update(Legend, { id: legendId }, legend);

        const updatedLayer = await transactionalEntityManager.findOne(Layer, {
          where: { id: layerId },
        });
        const updatedLegend = await transactionalEntityManager.findOne(Legend, {
          where: { id: legendId },
        });

        return { updatedLayer, updatedLegend };
      });
    } catch (error) {
      throw error;
    }
  }

  async editGeomDataWithLayer(
    shapefileData: FeatureCollection<Geometry, GeoJsonProperties>,
    layer: Partial<Layer>,
    legend: Partial<Legend>,
    layerId: string,
    geomId: string,
    legendId: string
  ) {
    try {
      const entityManager = getManager();
      return await entityManager.transaction(async (transactionalEntityManager) => {
        await transactionalEntityManager.update(Layer, { id: layerId }, layer);
        await transactionalEntityManager.update(Legend, { id: legendId }, legend);

        await Promise.all(
          shapefileData.features.map(async (value) => {
            const geomLayer = new GeomLayer();
            const coordinates = (value.geometry as any)?.coordinates;

            geomLayer.projectId = layer?.projectId;
            geomLayer.layerId = layer?.id;
            geomLayer.properties = value?.properties;
            geomLayer.type = value?.type;
            geomLayer.updatedBy = layer.updatedBy || '';
            geomLayer.geom = {
              type: value?.geometry?.type,
              coordinates,
            };

            await transactionalEntityManager.update(GeomLayer, { id: geomId }, geomLayer);
          })
        );

        const updatedLayer = await transactionalEntityManager.findOne(Layer, {
          where: { id: layerId },
        });
        const updatedLegend = await transactionalEntityManager.findOne(Legend, {
          where: { id: legendId },
        });

        return { updatedLayer, updatedLegend };
      });
    } catch (error) {
      throw error;
    }
  }

  async getByParentLayerId(parentLayerId: string) {
    try {
      const layerParentRepo = getManager().getRepository(Layer);

      const data = await layerParentRepo.find({
        where: { layerParentId: parentLayerId, isDelete: false },
        relations: ['legend', 'geomLayer', 'dce', 'filter'],
        order: { order: 'ASC' },
      });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async changeOrder(newOrder: { id: string; order: number }[]) {
    try {
      await getManager().transaction(async (transactionalEntityManager) => {
        try {
          Promise.all(
            newOrder.map(async (value) => {
              try {
                await transactionalEntityManager.update(
                  Layer,
                  { id: value.id },
                  { order: value.order }
                );
              } catch (error) {
                throw error;
              }
            })
          );
        } catch (error) {
          throw error;
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async getByProjectIdForMobile(projectId: string) {
    try {
      const layerParentRepo = getManager().getRepository(Layer);

      const data = await layerParentRepo
        .createQueryBuilder('l')
        .leftJoinAndSelect('l.legend', 'lg')
        .where('l.projectId = :projectId', { projectId })
        .andWhere('l.isDelete = :isDelete', { isDelete: false })
        .andWhere('l.enableMobile = :enableMobile', { enableMobile: true })
        .addOrderBy('l.order', 'ASC')
        .select([
          'l.id',
          'l.name',
          'l.dceId',
          'l.label',
          'l.mapLabelColor',
          'l.layerType',
          'l.order',
          'l.mapLayerZoom',
          'l.enablePopupForMap',
          'l.enableLabelForMap',
          'lg.color',
          'lg.path',
        ])
        .getMany();
      const final = data.map((value) => {
        if (value.legend && value.legend.length > 0) {
          const legend = value.legend?.filter(
            (fil) => fil.isDelete == false && fil.isDefault == true
          );
          if (legend && legend.length < 0) {
            value.legend = [value.legend[0]];
          }
        }

        return {
          labelColor: value?.mapLabelColor,
          zoomLevel: value?.mapLayerZoom,
          enablePopUp: value?.enablePopupForMap,
          enableLabel: value?.enableLabelForMap,
          ...value,
        };
      });
      return final;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectId(projectId: string) {
    try {
      const layerRepo = getManager().getRepository(Layer);

      const data = await layerRepo.find({
        where: { projectId, isDelete: false, legend: { isDelete: false } },
        relations: ['legend', 'geomLayer', 'dce'],
        order: { order: 'ASC' },
      });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectIdAndNoParent(projectId: string) {
    try {
      const layerRepo = getManager().getRepository(Layer);

      const data = await layerRepo.find({
        where: { projectId, isDelete: false, layerParentId: IsNull(), legend: { isDelete: false } },
        relations: ['legend', 'geomLayer', 'dce'],
        order: { order: 'ASC' },
      });

      return data;
    } catch (error) {
      throw error;
    }
  }

  async getByProjectIdAndLayerKeys(projectId: string, layerKeys: string[]) {
    try {
      const layerRepo = getManager().getRepository(Layer);

      const data = await layerRepo.find({
        where: { projectId, isDelete: false, layerKey: In(layerKeys) },
        relations: ['legend', 'geomLayer', 'dce'],
        order: { order: 'ASC' },
      });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getColumnName(layerId: string) {
    try {
      const layerRepo = getManager().getRepository(Layer);
      const layerDetails = await layerRepo.findOne({ where: { id: layerId }, relations: ['dce'] });
      if (layerDetails && layerDetails.projectId) {
        if (layerDetails.dce) {
          const data = await dceModel.getColumnsByDce(layerDetails.dce);

          return data || [];
        } else {
          const geomModel = new GeomLayerModel();
          const data = await geomModel.getPropertiesByLayerId(layerId);
          return data || {};
        }
      }
    } catch (error) {
      throw error;
    }
  }

  async getById(id: string) {
    try {
      const layerRepo = getManager().getRepository(Layer);

      const data = await layerRepo.findOne({
        where: { id, isDelete: false },
        relations: ['legend'],
      });

      return data;
    } catch (error) {
      throw error;
    }
  }
}

export default LayerModel;
