import { Request, Response } from 'express';
import { AzureBlobStorageService } from '../../../../shared/utilities/sftpBlobAndFiles/azureBlobStorageService';

class GetFileController {
  determineContentType(fileName: string): string {
    const fileExtension = fileName.split('.').pop()?.toLowerCase();

    switch (fileExtension) {
      case 'pdf':
        return 'application/pdf';
      case 'doc':
      case 'docx':
        return 'application/msword';
      case 'xls':
      case 'xlsx':
        return 'application/vnd.ms-excel';
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      default:
        return 'application/octet-stream';
    }
  }

  getFilenameFromPath(filePath: string): string {
    // Extract filename from the path
    const parts = filePath.split('/');
    return parts[parts.length - 1];
  }
  async getBlobFile(req: Request, res: Response) {
    try {
      const remoteFilePath = (req.query.path as string) || '';
      const azureBlobStorageService = new AzureBlobStorageService(
        process.env.BLOB_STOREAGE_ACCOUNT_NAME || '',
        process.env.BLOB_STOREAGE_ACCOUNT_KEY || ''
      );

      const file = await azureBlobStorageService.downloadFile(req.params.projectId, remoteFilePath);
      const contentType = this.determineContentType(remoteFilePath);
      const filename = this.getFilenameFromPath(remoteFilePath);

      res.setHeader('Content-Type', contentType);
      res.setHeader('Content-Disposition', `inline; filename="${filename}"`);

      return res.send(file);
    } catch (error) {
      res
        .status(500)
        .json({ error: (error as Error).message || 'Failed to get the file from SFTP server.' });
    }
  }
}

export default GetFileController;
