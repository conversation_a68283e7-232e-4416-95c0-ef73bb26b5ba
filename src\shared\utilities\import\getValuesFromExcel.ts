import * as ExcelJS from 'exceljs';

export function convertToCamelCase(str: string): string {
  // Convert to camel case without spaces
  return str.replace(/\s+/g, '').replace(/^(.)/, (match) => match.toLowerCase());
}

export async function extractDataFromExcel(buffer: Buffer): Promise<any[]> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);

  const worksheet = workbook.getWorksheet(1);

  if (!worksheet) {
    throw new Error('Worksheet not found in the Excel file');
  }

  const extractedData: any[] = [];
  const columns: string[] = [];

  worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    const rowData: any = {};

    row.eachCell((cell, colNumber) => {
      if (rowNumber === 1) {
        // First row is used as column names
        columns[colNumber] = convertToCamelCase(cell.value as string);
      } else {
        // Subsequent rows are represented as objects with keys in camel case
        rowData[columns[colNumber]] = cell.value !== undefined ? cell.value : '';
      }
    });

    // Skip the first row (it's used as column names)
    if (rowNumber > 1) {
      rowData.rowNumber = rowNumber;
      extractedData.push(rowData);
    }
  });

  return extractedData;
}

interface GroupedData {
  sample: Record<string, any>;
  testOne: Record<string, any>;
  testTwo: Record<string, any>;
  testThree: Record<string, any>;
  testFour: Record<string, any>;
  testFive: Record<string, any>;
  testSix: Record<string, any>;
  testSeven: Record<string, any>;
  testEight: Record<string, any>;
  testNine: Record<string, any>;
  rowNumber: number;
}

export async function extractDataFromExcelWithGroupForEarthwork(
  buffer: Buffer
): Promise<GroupedData[]> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);

  const worksheet = workbook.getWorksheet(1);

  if (!worksheet) {
    throw new Error('Worksheet not found in the Excel file');
  }

  const extractedData: any[] = [];
  const columns: string[] = [];

  worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    const rowData: any = {
      sample: {}, // Initialize the 'sample' property as an empty object
      testOne: {},
      testTwo: {},
      testThree: {},
      testFour: {},
      testFive: {},
      testSix: {},
      testSeven: {},
      testEight: {},
    };

    row.eachCell((cell, colNumber) => {
      if (rowNumber === 1) {
        // First row is used as column names
        columns[colNumber] = convertToCamelCase(String(cell.value));
      } else {
        // Subsequent rows are represented as objects with keys in camel case
        const columnName = columns[colNumber];

        // rowData[columnName] = cell.value !== undefined ? cell.value : '';
        if (colNumber >= 1 && colNumber <= 14) {
          rowData.sample[columnName] = cell.value;
        } else if (colNumber >= 15 && colNumber <= 21) {
          rowData.testOne[columnName] = cell.value;
        } else if (colNumber >= 22 && colNumber <= 28) {
          rowData.testTwo[columnName] = cell.value;
        } else if (colNumber >= 29 && colNumber <= 35) {
          rowData.testThree[columnName] = cell.value;
        } else if (colNumber >= 36 && colNumber <= 40) {
          rowData.testFour[columnName] = cell.value;
        } else if (colNumber >= 41 && colNumber <= 45) {
          rowData.testFive[columnName] = cell.value;
        } else if (colNumber >= 46 && colNumber <= 50) {
          rowData.testSix[columnName] = cell.value;
        } else if (colNumber >= 51 && colNumber <= 55) {
          rowData.testSeven[columnName] = cell.value;
        } else if (colNumber >= 56 && colNumber <= 60) {
          rowData.testEight[columnName] = cell.value;
        }
      }
    });

    // Skip the first row (it's used as column names)
    if (rowNumber > 1) {
      rowData.rowNumber = rowNumber;
      extractedData.push(rowData);
    }
  });

  return extractedData;
}

export async function extractDataFromExcelWithGroupForWater(
  buffer: Buffer
): Promise<GroupedData[]> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);

  const worksheet = workbook.getWorksheet(1);

  if (!worksheet) {
    throw new Error('Worksheet not found in the Excel file');
  }

  const extractedData: any[] = [];
  const columns: string[] = [];

  worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
    const rowData: any = {
      sample: {}, // Initialize the 'sample' property as an empty object
      testOne: {},
      testTwo: {},
      testThree: {},
      testFour: {},
      testFive: {},
      testSix: {},
    };

    row.eachCell((cell, colNumber) => {
      if (rowNumber === 1) {
        // First row is used as column names
        columns[colNumber] = convertToCamelCase(String(cell.value));
      } else {
        // Subsequent rows are represented as objects with keys in camel case
        const columnName = columns[colNumber];

        // rowData[columnName] = cell.value !== undefined ? cell.value : '';
        if (colNumber >= 1 && colNumber <= 12) {
          rowData.sample[columnName] = cell.value;
        } else if (colNumber >= 13 && colNumber <= 16) {
          rowData.testOne[columnName] = cell.value;
        } else if (colNumber >= 17 && colNumber <= 19) {
          rowData.testTwo[columnName] = cell.value;
        } else if (colNumber >= 20 && colNumber <= 22) {
          rowData.testThree[columnName] = cell.value;
        } else if (colNumber >= 23 && colNumber <= 25) {
          rowData.testFour[columnName] = cell.value;
        } else if (colNumber >= 26 && colNumber <= 28) {
          rowData.testFive[columnName] = cell.value;
        } else if (colNumber >= 29 && colNumber <= 31) {
          rowData.testSix[columnName] = cell.value;
        }
      }
    });

    // Skip the first row (it's used as column names)
    if (rowNumber > 1) {
      rowData.rowNumber = rowNumber;
      extractedData.push(rowData);
    }
  });

  return extractedData;
}

export const extractDataFromExcelByTable = async (
  buffer: Buffer
): Promise<{ entity: string; data: any[]; index: number }[]> => {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);
  let extractedData: { entity: string; data: any[]; index: number }[] = [];
  workbook.worksheets.forEach((worksheet, index) => {
    const columns: any[] = [];
    const entity = worksheet.name
      .toLowerCase() // Convert the string to lowercase
      .replace(
        /(?:^\w|[A-Z]|\b\w|\s+)/g, // Match the first letter of each word and spaces
        (match, index) => (index === 0 ? match.toLowerCase() : match.toUpperCase())
      ) // Convert to camel case
      .replace(/\s+/g, '');
    worksheet.eachRow({ includeEmpty: true }, (row, rowNumber) => {
      const rowData: any = {};

      row.eachCell((cell, colNumber) => {
        if (rowNumber === 1) {
          // First row is used as column names
          columns[colNumber] = cell.text as string;
        } else {
          // Subsequent rows are represented as objects with keys in camel case
          rowData[columns[colNumber]] = cell.text !== undefined ? cell.text : '';
        }
      });

      // Skip the first row (it's used as column names)
      if (rowNumber > 1) {
        rowData.rowNumber = rowNumber;
        if (extractedData.find((value) => value.entity === entity)) {
          extractedData = updateDataIfEntityExists(extractedData, entity, rowData);
        } else {
          extractedData.push({ entity, data: [rowData], index });
        }
      }
    });

    if (!worksheet) {
      throw new Error('Worksheet not found in the Excel file');
    }
  });
  return extractedData;
};

const updateDataIfEntityExists = (
  arr: { entity: string; data: any[]; index: number }[],
  entityToCheck: string,
  newValue: any
) => {
  // Iterate through each object in the array
  arr.forEach((obj) => {
    // Check if the 'entity' matches the entityToCheck
    if (obj.entity === entityToCheck) {
      // If entity matches, push the new value to the data array
      obj.data.push(newValue);
    }
  });

  return arr;
};

export async function checkWorkSheet(buffer: Buffer, name: string): Promise<boolean> {
  const workbook = new ExcelJS.Workbook();
  await workbook.xlsx.load(buffer);

  const worksheet = workbook.getWorksheet(1);

  if (!worksheet) {
    return false;
  }

  if (worksheet.name != name) {
    return false;
  }

  return true;
}
