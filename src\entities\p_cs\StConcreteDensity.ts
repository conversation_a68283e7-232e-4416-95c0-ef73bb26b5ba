import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { TestMethod } from '../p_meta/TestMethod';
import { Test } from '../p_meta/Test';
import { Sample } from './Sample';
import { SampleType } from '../p_meta/SampleType';
import { Purpose } from '../p_meta/Purpose';
import { Site } from '@entities/p_gen/Site';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StConcreteDensity extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Batch Id',
      fieldName: 'batchId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.concrete_batch_ticket WHERE "ticketNumber" = $1`,
      getListQuery: `SELECT id, "ticketNumber" as name FROM p_cs.concrete_batch_ticket WHERE "projectId" = $1 ORDER BY "updatedAt" DESC;`,
      listParams: 'id',
      listName: 'batchIdList',
    },
  })
  @Column({ nullable: true })
  batchId?: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Id', // this column will be entered by the
      fieldName: 'sampleId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.sample WHERE "QMSSampleYear" || '-' || "QMSLabSampleId" = $1`,
      getListQuery: `SELECT id,"QMSSampleYear" || '-' || "QMSLabSampleId" as name FROM p_cs.sample WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 75;`,
      listParams: 'id',
      listName: 'sampleList',
    },
  })
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  // TODO: Remove sampleTypeId
  @Column({ nullable: true })
  sampleTypeId?: string;

  @ManyToOne(() => SampleType, { nullable: true })
  @JoinColumn({ name: 'sampleTypeId' })
  sampleType?: SampleType;

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @ColumnInfo({
    customData: {
      name: 'Test No',
      fieldName: 'testNo',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'Volume',
      fieldName: 'volume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  volume?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Mass Batched',
      fieldName: 'totalMassBatched',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalMassBatched?: number;

  @ColumnInfo({
    customData: {
      name: 'Total Absolute Volume',
      fieldName: 'totalAbsoluteVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  totalAbsoluteVolume?: number;

  @ColumnInfo({
    customData: {
      name: 'CLSM Volume',
      fieldName: 'clsmVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  clsmVolume?: number;

  @ColumnInfo({
    customData: {
      name: 'Cement Mass',
      fieldName: 'cementMass',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementMass?: number;

  @ColumnInfo({
    customData: {
      name: 'Mold Weight',
      fieldName: 'moldWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moldWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Mold With Water Weight',
      fieldName: 'moldWithWaterWeight',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moldWithWaterWeight?: number;

  @ColumnInfo({
    customData: {
      name: 'Mold Volume',
      fieldName: 'moldVolume',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  moldVolume?: number;

  @ColumnInfo({
    customData: {
      name: 'Unit Weight Mold With Sample',
      fieldName: 'unitWeightMoldWithSample',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  unitWeightMoldWithSample?: number;

  @ColumnInfo({
    customData: {
      name: 'Density',
      fieldName: 'density',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  density?: number;

  @ColumnInfo({
    customData: {
      name: 'Theoretical Density',
      fieldName: 'theoreticalDensity',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  theoreticalDensity?: number;

  @ColumnInfo({
    customData: {
      name: 'Yield',
      fieldName: 'yield',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  yield?: number;

  @ColumnInfo({
    customData: {
      name: 'Relative Yield',
      fieldName: 'relativeYield',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  relativeYield?: number;

  @ColumnInfo({
    customData: {
      name: 'Cement Content',
      fieldName: 'cementContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementContent?: number;

  @Column({ type: 'decimal', nullable: true })
  airContent?: number;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @Column({ nullable: true })
  comments?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ type: 'timestamp', nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
