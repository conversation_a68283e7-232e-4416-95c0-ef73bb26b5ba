import {
  DeepPartial,
  EntityTarget,
  ObjectLiteral,
  getConnection,
  getManager,
  getRepository,
} from 'typeorm';
import { DataCaptureElements } from '../../../../entities/p_meta/DataCaptureElements';
import { ProjectActivity } from '../../../../entities/p_gen/Activity';
import { EntityListInterface, entityList } from '../../../../shared/utilities/entity/entityList';
import { typeConversionNormal } from '../../../../shared/utilities/surveyJs/convertTableMetaDataToSurveyJsJson';
import { getAuditDetails } from '../../../../shared/utilities/auditLog/getAuditDetailsByEntity';
import { ProjectEquipment } from '../../../../entities/p_gen/ProjectEquipment';
import { Observation } from '../../../../entities/p_cs/Observation';
import { entityRelationList } from '../../../../shared/utilities/entity/entityRelation';
import stationConversionOnAddData from '../../../../shared/utilities/spatialCoordinates/stationConversionOnAddData';
import convertEastingAndNorthingToLatAndLong from '../../../../shared/utilities/spatialCoordinates/convertEastingOnAddData';
import SubDCEModel from './SubDce.model';
import editDataWithSubDCE from '@utils/custom/editDataWithSubDCE';
import { addNewRelation, editRelation } from '@utils/relationship/relationshipChangesOnDropdown';
import { dceValidationBeforeApproval } from '@utils/Approval/dceValidation';
import sampleModel from '@models/sample.model';
import {
  moveUnknownPrimitivesToMetadata,
  sanitizeRequestData,
} from '@utils/validation/sanitizeRequestData';
import { Sample } from '@entities/p_cs/Sample';
import { sendTestLogCalendarInvite } from '@utils/email/testLogCalendarInvite';
import { createTestLogEntry } from '@utils/testLog/createTestLogEntry';

class DCEModel {
  private dceToSubDceMap: Record<string, { entity: string; parentColumn: string }[]> = {};

  async findById(id: string) {
    try {
      return await getManager()
        .getRepository(DataCaptureElements)
        .findOne({ where: { id, isDelete: false }, relations: [] });
    } catch (error) {
      throw error;
    }
  }
  async findByMicroserviceId(id: string) {
    try {
      return await getManager()
        .getRepository(DataCaptureElements)
        .findOne({ where: { microserviceDceId: id, isDelete: false }, relations: [] });
    } catch (error) {
      throw error;
    }
  }
  async findAll() {
    try {
      return await getManager()
        .getRepository(DataCaptureElements)
        .find({ where: { isDelete: false }, order: { name: 'ASC' } });
    } catch (error) {
      throw error;
    }
  }

  async findByEntity(entity: string) {
    try {
      return await getManager()
        .getRepository(DataCaptureElements)
        .findOne({ where: { entity, isDelete: false }, relations: ['route'] });
    } catch (error) {
      throw error;
    }
  }

  async findByApprovalEntity(entity: string) {
    try {
      return await getManager()
        .getRepository(DataCaptureElements)
        .findOne({ where: { entity, isDelete: false } });
    } catch (error) {
      throw error;
    }
  }

  async findByActivityId(activityId: string) {
    try {
      const data = await getManager()
        .getRepository(ProjectActivity)
        .findOne({
          where: { id: activityId, isDelete: false, dataCaptureElements: { isDelete: false } },
          relations: ['dataCaptureElements'],
        });
      return data?.dataCaptureElements;
    } catch (error) {
      throw error;
    }
  }
  async findByGeoJson() {
    try {
      const data = await getManager()
        .getRepository(DataCaptureElements)
        .find({
          where: { isDelete: false, isGeoJson: true },
        });
      return data;
    } catch (error) {
      throw error;
    }
  }

  async getColumnsByDce(dce: DataCaptureElements) {
    try {
      if (dce?.entity && dce?.entity in entityList) {
        const entityClass: any = entityList[dce.entity as keyof EntityListInterface];
        const entityMetadata = getRepository(entityClass).metadata;
        return entityMetadata.columns.map((column) => {
          return column.propertyName;
        });
      }
    } catch (error) {
      throw error;
    }
  }
  checkColumnExistByDce = async <T extends ObjectLiteral>(
    entity: EntityTarget<T>,
    columnName: string
  ) => {
    try {
      const entityMetadata = getRepository(entity).metadata;
      const check = entityMetadata.columns.find((column) => column.propertyName === columnName);
      if (check) {
        return true;
      }
      return false;
    } catch (error) {
      throw error;
    }
  };

  private async getSubDceEntitiesForDce(
    dceId: string
  ): Promise<{ entity: string; parentColumn: string }[]> {
    // Check if we already have the mapping cached
    if (this.dceToSubDceMap[dceId]) {
      return this.dceToSubDceMap[dceId];
    }

    // If not cached, fetch and cache it
    try {
      const subDCEModel = new SubDCEModel();
      const subDCEs = await subDCEModel.findByDCEId(dceId);

      const subDceEntities = subDCEs.map((subDce) => ({
        entity: subDce.entity || '',
        parentColumn: subDce.parentColumn || '',
      }));

      // Cache the result
      this.dceToSubDceMap[dceId] = subDceEntities;

      return subDceEntities;
    } catch (error) {
      console.error('Error fetching SubDCE entities:', error);
      return [];
    }
  }

  private addOrderToSubDCEEntities = async (data: any, entityString: string): Promise<any> => {
    try {
      const dce = await this.findByEntity(entityString);
      if (!dce || !dce.id) return data;

      // Get the SubDCE entities from our mapping function
      const subDceEntities = await this.getSubDceEntitiesForDce(dce.id);

      // Create a copy of the data to avoid modifying the original
      const updatedData = { ...data };

      // Process each subdce entity
      for (const subDce of subDceEntities) {
        const subEntityName = subDce.entity;

        if (subEntityName && updatedData[subEntityName]) {
          // Handle case where data is a string that needs to be parsed
          let subEntityData = updatedData[subEntityName];

          // If it's a string that looks like JSON, try to parse it
          if (
            typeof subEntityData === 'string' &&
            (subEntityData.startsWith('[') || subEntityData.startsWith('{'))
          ) {
            subEntityData = JSON.parse(subEntityData);
            subEntityData = sanitizeRequestData(subEntityData);
          }

          if (Array.isArray(subEntityData)) {
            updatedData[subEntityName] = subEntityData.map((item: any, index: number) => ({
              ...item,
              order: index + 1, // Use index + 1 for 1-based ordering
            }));
          }
        }
      }

      return updatedData;
    } catch (error) {
      console.error('Error adding order to subDCE entities:', error);
      return data; // Return original data if there's an error
    }
  };

  addDataByEntity = async <T extends ObjectLiteral>(
    data: any,
    entity: EntityTarget<T>,
    createdUserId: string,
    entityString: string
  ) => {
    try {
      const repository = getConnection().getRepository<T>(entity);
      if (entityString == 'observation') {
        const newObservation = Object.assign(new Observation(), data);
        const equipmentId = data?.equipment;
        if (equipmentId) {
          const equipmentList = await getManager()
            .getRepository(ProjectEquipment)
            .findByIds(equipmentId);
          newObservation.equipment = equipmentList;
        }
        const response = await repository.save(newObservation);
        if (entityString) {
          await addNewRelation(entityString, response);
          await dceValidationBeforeApproval(entityString, [response]);
          await getAuditDetails(createdUserId, entityString, response, 'add');
        }
        return response;
      }
      if (entityString == 'sampleManagement') {
        const nextSampleNo = await sampleModel.getNextSampleNo(data?.sampledDate);
        data.sampleNo = nextSampleNo;
      }
      if (entityString == 'ctLog') {
        if (
          (data?.calendarInvite === true || data?.calendarInvite === 'true') &&
          data?.emailListId
        ) {
          try {
            const calendarResponse = await sendTestLogCalendarInvite(data, data.emailListId);
            if (calendarResponse && calendarResponse.workPackageId) {
              data.workPackageId = calendarResponse.workPackageId;
            }
          } catch (error) {
            console.error('Error sending calendar invite:', error);
          }
        }
      }

      if (entityString == 'nuclearGaugeTest' || entityString == 'sandConeTest') {
        if (data.generateTestNo === true || data.generateTestNo === 'true') {
          try {
            data.testNoId = await createTestLogEntry(data);
          } catch (error) {
            console.error('Error creating CT Log entry:', error);
          }
        }
      }
      let dataToAdd = data;
      dataToAdd = convertEastingAndNorthingToLatAndLong(dataToAdd);
      if (entity) {
        dataToAdd = await stationConversionOnAddData(
          (data as any)?.projectId || '',
          dataToAdd,
          entity
        );
      }

      dataToAdd = await this.addOrderToSubDCEEntities(dataToAdd, entityString);

      const dataWithMetadata = await moveUnknownPrimitivesToMetadata(dataToAdd, entity);
      const createdData = await repository.create(dataWithMetadata as DeepPartial<T>);
      const response = await repository.save(createdData);
      if (entity) {
        await addNewRelation(entityString, response);
        await dceValidationBeforeApproval(entityString, [response]);
        await getAuditDetails(createdUserId, entityString, response, 'add');
      }
      return response;
    } catch (error) {
      throw error;
    }
  };
  addDataByMultipleEntity = async (
    data: { entity: string; data: any }[],
    createdUserId: string,
    createdUserName: string,
    projectId: string
  ) => {
    try {
      const response: any[] = [];
      const sample = data.find((value) => value.entity === 'sampleManagement');
      let addedSampleId: string | null = null;

      if (sample) {
        Object.assign(sample.data, {
          createdBy: createdUserName,
          updatedBy: createdUserName,
          createdUserId,
          projectId,
        });

        const addedSample = await this.addDataByEntity(
          sample.data,
          Sample,
          createdUserId,
          sample.entity
        );
        addedSampleId = addedSample?.id || null;
      }

      for (const element of data.filter((value) => value.entity !== 'sampleManagement')) {
        if (element.entity in entityList) {
          const entity: any = entityList[element.entity as keyof EntityListInterface];

          Object.assign(element.data, {
            createdBy: createdUserName,
            updatedBy: createdUserName,
            createdUserId,
            projectId,
          });

          if (sample && addedSampleId && this.columnExistsInEntity(entity, 'sampleId')) {
            element.data.sampleId = addedSampleId;
            element.data.materialId = sample.data.materialId;
            element.data.materialTypeId = sample.data.materialTypeId;
            element.data.purposeId = sample.data.purposeId;
            element.data.batchId = sample.data.concreteBatchId;
            element.data.dateTested = sample.data.sampledDate;
            element.data.testNoId = sample.data.testNoId;
          }

          response.push(
            await this.addDataByEntity(element.data, entity, createdUserId, element.entity)
          );
        }
      }

      return response;
    } catch (error) {
      throw error;
    }
  };
  editDataByEntity = async <T extends ObjectLiteral>(
    id: string,
    data: any,
    entity: EntityTarget<T>,
    createdUserId: string,
    createdUserName: string,
    entityString: string
  ) => {
    try {
      const repository = getConnection().getRepository<T>(entity);
      let dataToAdd = data;
      const dce = await this.findByEntity(entityString);
      const dataBeforeEdit = await repository.findOne({ where: { id, isDelete: false } as any });

      // Use the cached mapping instead of querying every time
      const subDCEList: { entity: string; parentColumn: string }[] = [];
      if (dce && dce.id) {
        const subDceEntities = await this.getSubDceEntitiesForDce(dce.id);
        subDCEList.push(...subDceEntities);
      }

      if (entityString === 'ctLog' && dataBeforeEdit) {
        const scheduleChanged =
          (data.scheduleFrom && data.scheduleFrom !== dataBeforeEdit.scheduleFrom) ||
          (data.scheduleTo && data.scheduleTo !== dataBeforeEdit.scheduleTo);

        if (scheduleChanged && (data.calendarInvite || dataBeforeEdit.calendarInvite)) {
          const calendarData = {
            ...dataBeforeEdit,
            ...data,
            updatedBy: createdUserName,
            updatedUserId: createdUserId,
          };

          const emailListId = data.emailListId || dataBeforeEdit.emailListId;

          if (emailListId) {
            await sendTestLogCalendarInvite(calendarData, emailListId, true);
          }
        }
      }

      dataToAdd = convertEastingAndNorthingToLatAndLong(data);

      if (entity) {
        dataToAdd = await stationConversionOnAddData(
          (data as any)?.projectId || '',
          dataToAdd,
          entity
        );
      }

      // Apply order to subDCE entities in the data
      dataToAdd = await this.addOrderToSubDCEEntities(dataToAdd, entityString);

      if (subDCEList.length > 0) {
        const subDCEData: { entity: string; data: any[] }[] = [];

        for (const element of subDCEList) {
          const key = element.entity;
          const parentColumn = element.parentColumn;
          if (!parentColumn) throw new Error(`Parent column not found for ${key}`);
          if (key in dataToAdd && dataToAdd[key]) {
            const subData = dataToAdd[key].map((record: any) => ({
              ...record,
              [parentColumn]: id,
            }));

            // Deletion logic for subDCE
            const subEntity: EntityTarget<any> = entityList[key as keyof EntityListInterface];
            if (subEntity) {
              const requestIds = subData.map((item: { id?: string }) => item.id).filter(Boolean);
              const existingRows = await getRepository(subEntity).find({
                where: { [parentColumn]: id },
              });
              const toDelete = existingRows.filter((row) => !requestIds.includes(row.id));
              if (toDelete.length > 0) {
                const toDeleteIds = toDelete.map((row) => row.id);
                await getRepository(subEntity).delete(toDeleteIds);
              }

              const cleanedSubData = subData.filter(
                (item: { id?: string }) => !toDelete.some((del) => del.id === item.id)
              );
              if (cleanedSubData.length > 0) {
                subDCEData.push({ entity: key, data: cleanedSubData });
              }
            }
            delete dataToAdd[key];
          }
        }
        await editDataWithSubDCE(subDCEData, dataToAdd, entity, id);
      } else {
        dataToAdd = await moveUnknownPrimitivesToMetadata(dataToAdd, entity);
        await repository.update(id, dataToAdd as any);
      }

      if (entityString) {
        const updatedEntity = await repository.findOne({ where: { id, isDelete: false } as any });
        await editRelation(entityString, updatedEntity, dataBeforeEdit);
        await getAuditDetails(createdUserId, entityString, updatedEntity, 'edit');
        await dceValidationBeforeApproval(entityString, [updatedEntity]);
      }
      const response = await repository.findOne({ where: { id } as any });
      return response;
    } catch (error) {
      throw error;
    }
  };
  getDataByEntity = async <T extends ObjectLiteral>(
    value: string,
    column: string,
    entity: EntityTarget<T>,
    single?: boolean
  ) => {
    try {
      const repository = getConnection().getRepository<T>(entity);
      if (single) {
        const response = await repository.findOne({ where: { [column]: value } as any });
        return response;
      }
      const response = await repository.find({ where: { [column]: value } as any });
      return response;
    } catch (error) {
      throw error;
    }
  };

  getColumnType = async (dceData: DataCaptureElements) => {
    try {
      const entity = dceData.entity;

      if (entity && entity in entityList) {
        const entityClass: any = entityList[entity as keyof EntityListInterface];
        const entityMetadata = getRepository(entityClass).metadata;
        const query = `SELECT column_name as name, udt_name as type
                            FROM information_schema.columns
                            WHERE table_name = '${entityMetadata.tableName}' and table_schema = '${entityMetadata.schema}'`;

        const out: { name: string; type: string }[] = await getManager().query(query);

        const finalOut: any = {};
        for (const iterator of out) {
          const type = typeConversionNormal(iterator.type);
          finalOut[iterator.name] = type;
        }

        return finalOut;
      }
    } catch (error) {
      throw error;
    }
  };

  async getTableDataByDceAndTableId<T extends ObjectLiteral>(
    id: string,
    entity: EntityTarget<T>,
    entityName: string
  ) {
    try {
      const repository = getConnection().getRepository<T>(entity);
      if (entityName in entityRelationList) {
        const output = await repository.find({
          where: { id, isDelete: false } as any,
          relations: entityRelationList[entityName].relation,
        });

        if (output !== null) {
          return output;
        }
      }
    } catch (error) {
      throw error;
    }
  }
  async getTableDataByDceAndTableIdForDropdown<T extends ObjectLiteral>(
    id: string,
    entity: EntityTarget<T>,
    entityColumn: string
  ) {
    try {
      const repository = getConnection().getRepository<T>(entity);
      const output = await repository.findOne({
        where: { id, isDelete: false } as any,
      });

      if (output !== null) {
        return output[entityColumn];
      }
    } catch (error) {
      throw error;
    }
  }

  async getForeignKeyRelatedTable(tableName: string, columnName: string): Promise<string | null> {
    const entityManager = getManager();
    const [schema, table] = tableName.split('.');
    const result = await entityManager.query(
      `
      SELECT
          ccu.table_schema AS "referencedSchema",
          ccu.table_name AS "referencedTableName"
      FROM
          information_schema.table_constraints AS tc
      JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
      JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE
          tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = $1
          AND tc.table_name = $2
          AND kcu.column_name = $3;
      `,
      [schema, table, columnName]
    );

    return result.length > 0
      ? `${columnName}.${result[0].referencedSchema}.${result[0].referencedTableName}`
      : null;
  }

  columnExistsInEntity(entity: any, columnName: string): boolean {
    const metadata = getManager().getRepository(entity).metadata;
    return metadata.columns.some((column) => column.propertyName === columnName);
  }
}

const dceModel = new DCEModel();

export default dceModel;
