import {
  BlobDownloadResponseModel,
  BlobGetPropertiesResponse,
  BlobServiceClient,
  ContainerCreateResponse,
  StorageSharedKeyCredential,
} from '@azure/storage-blob';
import projectModel from '../../../modules/project360/models/project.model';

export class AzureBlobStorageService {
  private readonly blobServiceClient: BlobServiceClient;

  constructor(accountName: string, accountKey: string) {
    const sharedKeyCredential = new StorageSharedKeyCredential(accountName, accountKey);
    this.blobServiceClient = new BlobServiceClient(
      `https://${accountName}.blob.core.windows.net`,
      sharedKeyCredential
    );
  }

  private async getProjectDetails(projectId: string) {
    try {
      const project = await projectModel.getProjectId(projectId);
      if (project) {
        return project.container;
      } else {
        throw new Error(`Project with Id ${projectId} not found`);
      }
    } catch (error) {}
  }

  getContainerClient(containerName: string) {
    return this.blobServiceClient.getContainerClient(containerName);
  }

  async listContainers() {
    const containersIterator = this.blobServiceClient.listContainers();
    const containers = [];

    for await (const container of containersIterator) {
      containers.push(container.name);
    }

    return containers;
  }

  async deleteContainer(containerName: string) {
    try {
      const deleteContainerResponse = await this.blobServiceClient.deleteContainer(containerName);
      console.log(`Container "${containerName}" deleted successfully.`, deleteContainerResponse);
    } catch (error: any) {
      console.error('Error deleting container:', error.message);
    }
  }

  getBlockBlobClient(containerName: string, blobName: string) {
    const containerClient = this.getContainerClient(containerName);
    return containerClient.getBlockBlobClient(blobName);
  }

  async createContainer(containerName: string): Promise<ContainerCreateResponse> {
    const containerClient = this.getContainerClient(containerName);

    try {
      return await containerClient.create();
    } catch (error: any) {
      throw new Error(`Error creating container "${containerName}": ${error.message}`);
    }
  }

  async listBlobs(projectId: string, delimiter: string, folderPrefix?: string) {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || '';
      const blobsIterator = await this.blobServiceClient
        .getContainerClient(containerName)
        .listBlobsByHierarchy(delimiter, { prefix: folderPrefix });
      const blobs = [];

      for await (const blob of blobsIterator) {
        blobs.push(blob);
      }

      return blobs;
    } catch (error: any) {
      console.error('Error listing blobs:', error.message);
      throw error;
    }
  }

  async downloadFile(projectId: string, blobName: string) {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || 'test-by-nihal';
      const blockBlobClient = this.getBlockBlobClient(containerName, blobName);
      // Upload the file to Azure Blob Storage
      const data = await blockBlobClient.downloadToBuffer();

      return data;
    } catch (error: any) {
      console.error('Error listing blobs:', error.message);
      throw error;
    }
  }

  async deleteBlob(projectId: string, blobName: string) {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || '';
      const deleteBlobResponse = await this.blobServiceClient
        .getContainerClient(containerName)
        .getBlockBlobClient(blobName)
        .delete();
      console.log(`Blob "${blobName}" deleted successfully.`, deleteBlobResponse);
    } catch (error: any) {
      console.error('Error deleting blob:', error.message);
      throw error;
    }
  }

  async uploadFileToBlob(projectId: string, blobName: string, file: any): Promise<string> {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || '';
      const blockBlobClient = this.getBlockBlobClient(containerName, blobName);
      // Upload the file to Azure Blob Storage
      await blockBlobClient.upload(file.data, file.data.length);

      console.log(`File "${blobName}" uploaded to container "${containerName}" successfully.`);
      return blobName;
    } catch (error: any) {
      throw new Error(`Error uploading file "${blobName}": ${error.message}`);
    }
  }

  async uploadFileFromRequest(projectId: string, blobName: string, file: any): Promise<string> {
    if (!file) {
      throw new Error('No file provided in the request.');
    }
    const newBlobName = `${projectId}/${blobName}`;

    return await this.uploadFileToBlob(projectId, newBlobName, file);
  }

  async uploadFileToBlobUploadInput(
    projectId: string,
    blobName: string,
    file: any
  ): Promise<string> {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || 'test-container';
      const blockBlobClient = this.getBlockBlobClient(containerName, blobName);
      // Upload the file to Azure Blob Storage
      await blockBlobClient.upload(file.data, file.data.length);

      console.log(`File "${blobName}" uploaded to container "${containerName}" successfully.`);
      return blobName;
    } catch (error: any) {
      throw new Error(`Error uploading file "${blobName}": ${error.message}`);
    }
  }

  async uploadFileToBlobUploadBufferInput(
    projectId: string,
    blobName: string,
    file: any
  ): Promise<string> {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || 'test-container';
      const blockBlobClient = this.getBlockBlobClient(containerName, blobName);

      // Convert Uint8Array to Buffer
      const buffer = Buffer.from(file);

      // Upload the buffer to Azure Blob Storage
      await blockBlobClient.upload(buffer, buffer.length);

      console.log(`File "${blobName}" uploaded to container "${containerName}" successfully.`);
      return blobName;
    } catch (error: any) {
      throw new Error(`Error uploading file "${blobName}": ${error.message}`);
    }
  }

  async uploadFileFromRequestUploadInput(
    projectId: string,
    blobName: string,
    file: any
  ): Promise<string> {
    try {
      if (!file) {
        throw new Error('No file provided in the request.');
      }
      const newBlobName = `${projectId}/${blobName}`;

      return await this.uploadFileToBlobUploadInput(projectId, newBlobName, file);
    } catch (error) {
      throw error;
    }
  }

  async uploadFileToBlobForProjectFile(
    projectId: string,
    blobName: string,
    file: any
  ): Promise<string> {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || '';
      const blockBlobClient = this.getBlockBlobClient(containerName, blobName);
      // Upload the file to Azure Blob Storage
      await blockBlobClient.upload(file.data, file.data.length);

      console.log(`File "${blobName}" uploaded to container "${containerName}" successfully.`);
      return blobName;
    } catch (error: any) {
      throw new Error(`Error uploading file "${blobName}": ${error.message}`);
    }
  }

  async uploadFileFromRequestForProjectFile(
    projectId: string,
    blobName: string,
    file: any
  ): Promise<string> {
    if (!file) {
      throw new Error('No file provided in the request.');
    }
    const newBlobName = `${projectId}/${blobName}`;
    return await this.uploadFileToBlobForProjectFile(projectId, newBlobName, file);
  }

  async containerExists(containerName: string): Promise<boolean> {
    try {
      const containerClient = this.blobServiceClient.getContainerClient(containerName);
      const exists = await containerClient.exists();
      return exists;
    } catch (error: any) {
      console.error(`Error checking if container "${containerName}" exists:`, error.message);
      throw error;
    }
  }

  async getBlobDetails(projectId: string, blobName: string): Promise<BlobDownloadResponseModel> {
    try {
      const containerName = (await this.getProjectDetails(projectId)) || '';
      const blobClient = this.getBlockBlobClient(containerName, blobName);

      // Get blob properties
      const propertiesResponse: BlobGetPropertiesResponse = await blobClient.getProperties();
      const metadata = propertiesResponse.metadata;

      // You can retrieve other details as needed

      return { metadata, ...propertiesResponse }; // Include other properties as needed
    } catch (error: any) {
      console.error('Error getting blob details:', error.message);
      throw error;
    }
  }
}
