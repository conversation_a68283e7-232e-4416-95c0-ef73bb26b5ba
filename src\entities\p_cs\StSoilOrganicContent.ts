import {
  <PERSON><PERSON><PERSON>,
  Column,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
} from 'typeorm';

import { Sample } from './Sample';
import { TestMethod } from '../p_meta/TestMethod';
import { TestVariant } from '../p_meta/Testvariant';
import { Test } from '../p_meta/Test';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { Purpose } from '../p_meta/Purpose';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { StSoilOrganicContentWorksheet } from './StSoilOrganicContentWorksheet';
import { Site } from '@entities/p_gen/Site';
import { ProjectMaterial } from '@entities/p_gen/ProjectMaterial';
import { MaterialType } from '@entities/p_meta/MaterialType';
import { EventLog } from '@entities/p_gen/EventLog';
import { TestAndDCEColumns } from '@entities/common/TestColumns';

@Entity({ schema: 'p_cs' })
export class StSoilOrganicContent extends TestAndDCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'Sample Id', // this column will be entered by the
      fieldName: 'sampleId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_cs.sample WHERE "id" = $1`,
      getListQuery: `SELECT id,"QMSSampleYear" || '-' || "QMSLabSampleId" as name FROM p_cs.sample WHERE "projectId" = $1 ORDER BY "updatedAt" DESC LIMIT 75;`,
      listParams: 'id',
      listName: 'sampleList',
    },
  })
  @Column({ nullable: true })
  sampleId?: string;

  @ManyToOne(() => Sample, { nullable: true })
  @JoinColumn({ name: 'sampleId' })
  sample?: Sample;

  @Column({ nullable: true })
  testNo?: string;

  @Column({ nullable: true })
  testId?: string;

  @ManyToOne(() => Test, { nullable: true })
  @JoinColumn({ name: 'testId' })
  test?: Test;

  @Column({ nullable: true })
  testMethodId?: string;

  @ManyToOne(() => TestMethod, { nullable: true })
  @JoinColumn({ name: 'testMethodId' })
  testMethod?: TestMethod;

  @ColumnInfo({
    customData: {
      name: 'Test Variant', // this column will be entered by the
      fieldName: 'testVariantId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.test_variant WHERE "standardCode" = $1`,
      getListQuery: `SELECT id,"variantAlias" as name FROM p_meta.test_variant where "testId" = 'c5e96c8d-345d-40f3-809f-210da0c4668a' ORDER BY "updatedAt";`,
      listName: 'testVariantList',
    },
  })
  @Column({ nullable: true })
  testVariantId?: string;

  @ManyToOne(() => TestVariant, { nullable: true })
  @JoinColumn({ name: 'testVariantId' })
  testVariant?: TestVariant;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'Sample Purpose',
      fieldName: 'samplePurpose',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'Preparation Process',
      fieldName: 'preparationProcess',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  preparationProcess?: string; //Any special selection or preparation process such as removal of gravel or other materials

  @Column({ nullable: true })
  materialId?: string;

  @ManyToOne(() => ProjectMaterial, { nullable: true })
  @JoinColumn({ name: 'materialId' })
  material?: ProjectMaterial;

  @Column({ nullable: true })
  materialTypeId?: string;

  @ManyToOne(() => MaterialType, { nullable: true })
  @JoinColumn({ name: 'materialTypeId' })
  materialType?: MaterialType;

  @ColumnInfo({
    customData: {
      name: 'Furnace Temperature',
      fieldName: 'furnaceTemperature',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  furnaceTemperature?: number;

  @ColumnInfo({
    customData: {
      name: 'Specimen Duration In furnace',
      fieldName: 'specimenDurationInfurnace',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  specimenDurationInfurnace?: number;

  // //Test Variant - Method A

  // @ColumnInfo({
  //   customData: {
  //     name: 'Container Mass',
  //     fieldName: 'containerMass',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // containerMass?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Moist Mass With Container',
  //     fieldName: 'moistMassWithContainer',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // moistMassWithContainer?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Oven Dry Mass With Container',
  //     fieldName: 'ovenDryMassWithContainer',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // ovenDryMassWithContainer?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Air Dry Mass With Container',
  //     fieldName: 'airDryMassWithContainer',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // airDryMassWithContainer?: number;

  // //Test Variant - Method B

  // @ColumnInfo({
  //   customData: {
  //     name: 'Pan Mass',
  //     fieldName: 'panMass',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // panMass?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Moist Mass With Pan',
  //     fieldName: 'moistMassWithPan',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // moistMassWithPan?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Air Dry Mass With Pan',
  //     fieldName: 'airDryMassWithPan',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // airDryMassWithPan?: number;

  // @ColumnInfo({
  //   customData: {
  //     name: 'Container Mass With Ash',
  //     fieldName: 'containerMassWithAsh',
  //     needed: true,
  //     inTemplate: true,
  //     type: 'decimal',
  //     fkey: false,
  //   },
  // })
  // @Column({ type: 'decimal', nullable: true })
  // containerMassWithAsh?: number;

  @ColumnInfo({
    customData: {
      name: 'Ash Content',
      fieldName: 'ashContent',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  ashContent?: number;

  @ColumnInfo({
    customData: {
      name: 'Organic Content',
      fieldName: 'organicContent',
      needed: true,
      inTemplate: true,
      type: 'decimal',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  averageOrganicContent?: number;

  @Column({ nullable: true })
  testedBy?: string;

  @Column({ type: 'timestamp', nullable: true })
  dateTested?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', nullable: true })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
    nullable: true,
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  qaVerifier?: string;

  @Column({ nullable: true })
  qaDate?: Date;

  @Column({ default: false, nullable: true })
  isDelete?: boolean;

  @Column({ nullable: true })
  currentStep?: string;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;

  @OneToMany(() => StSoilOrganicContentWorksheet, (worksheet) => worksheet.organicTest, {
    cascade: true,
  })
  organicContentWorksheet?: StSoilOrganicContentWorksheet[];

  @Column({ nullable: true })
  siteId?: string;

  @ManyToOne(() => Site, { nullable: true })
  @JoinColumn({ name: 'siteId' })
  site?: Site;
}
