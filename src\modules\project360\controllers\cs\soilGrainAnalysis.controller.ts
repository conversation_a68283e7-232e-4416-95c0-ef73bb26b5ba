import { Request, Response } from 'express';
import { soilGrainAnalysisCalculation } from '../../../../shared/utilities/cs/testCalculationFunctions';
import soilGrainAnalysisModel from '../../models/cs/soilGrainAnalysis.model';

class SoilGrainAnalysisController {
  constructor() {}

  // find by id
  async findById(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const soilGrainAnalysisData = await soilGrainAnalysisModel.findById(req.params.id);
      // checking if data is found with the id
      if (soilGrainAnalysisData) {
        // if true data will send as response
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: soilGrainAnalysisData,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as any).message });
    }
  }

  async create(req: Request, res: Response) {
    try {
      const {
        wetSampleWithPan,
        drySampleWithPan,
        weightOfPan,
        drySampleWithBulkPan,
        weightOfBulkPan,
        isBulkSample,
      } = req.body;
      if ((req as any).user) {
        req.body.createdBy = (req as any).user.name;
        req.body.updatedBy = (req as any).user.name;
      }
      if (isBulkSample == true) {
        if (!drySampleWithBulkPan || !weightOfBulkPan) {
          const message = req.__('InvalidInputDataError');
          return res.status(500).json({ isSucceed: false, data: [], msg: message });
        }
        const weightOfDrySampleInBulkPa =
          soilGrainAnalysisCalculation.CalculateWeightOfDrySampleInBulkPan(
            drySampleWithBulkPan,
            weightOfBulkPan
          );
        req.body.weightOfDrySampleInBulkPa = weightOfDrySampleInBulkPa;
      }

      if (!wetSampleWithPan || !drySampleWithPan || !weightOfPan) {
        const message = req.__('InvalidInputDataError');
        return res.status(500).json({ isSucceed: false, data: [], msg: message });
      }

      const weightOfWater = soilGrainAnalysisCalculation.CalculateWeightOfWater(
        wetSampleWithPan,
        drySampleWithPan
      );
      req.body.weightOfWater = weightOfWater;

      const weightOfDrySample = soilGrainAnalysisCalculation.CalculateInitialWeightOfDrySample(
        drySampleWithPan,
        weightOfPan
      );
      req.body.weightOfDrySample = weightOfDrySample;

      // getting the data from database with the given id
      const soilGrainAnalysisData = await soilGrainAnalysisModel.add(req.body);
      // checking if data is found with the id
      // if true data will send as response
      const message = req.__('DataInputSuccess');
      return res.status(200).json({
        isSucceed: true,
        data: soilGrainAnalysisData,
        msg: message,
      });
    } catch (error) {
      const message = req.__('DataInputFail');
      // error response
      return res
        .status(500)
        .json({ isSucceed: false, data: [], msg: (error as any).message || message });
    }
  }
}

const soilGrainAnalysisController = new SoilGrainAnalysisController();
export default soilGrainAnalysisController;
