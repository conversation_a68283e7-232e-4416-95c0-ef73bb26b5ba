import { Request, Response } from 'express';
import { v4 as uuidv4 } from 'uuid';
import reportModel from '../models/report.model';
import ReportVersion from '../../../entities/p_gen/ReportVersion';
// import Report from '../../../entities/p_gen/Report';
import ReportDocument from '../../../entities/p_gen/ReportDocument';
import errorMiddleware from 'src/shared/middlewares/error/error.middleware';
import axios from 'axios';
import querystring from 'querystring';
import crypto from 'crypto';
import createExcel from '@utils/excel/createExcel';
// import { getUserRoleDetail } from '@utils/role/getUserRoleDetails';
// import Submittal from '../../../entities/p_gen/Submittal';

class ReportController {
  constructor() {}

  async findByIdAndVersion(req: Request, res: Response) {
    try {
      const data = await reportModel.findByIdAndVersion(req.params.id, Number(req.params.version));

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findById(req: Request, res: Response) {
    try {
      const data = await reportModel.findByIdAndVersion(req.params.id, Number(req.params.version));

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByProjectId(req: Request, res: Response) {
    try {
      const data = await reportModel.findByProjectId(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async getAllVersionOfReprot(req: Request, res: Response) {
    try {
      const data = await reportModel.findVersionbyReport(req.params.id);

      // getting the data from database with the given id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async addReprotVersionWithDocument(req: Request, res: Response) {
    res.send('Coming soons...');
    // try {
    //   const createdBy = (req as any).user.name;
    //   const updatedBy = (req as any).user.name;
    //   const id = uuidv4();
    //   let version = 1;
    //   if (req.body.reportId) {
    //     const submital = await reportModel.findByIdLastVersion(req.body.reportId);
    //     if (submital && submital.version) {
    //       version = submital.version + 1;
    //     }
    //   }
    //   const newSubmital: ReportVersion = {
    //     id,
    //     updatedBy,
    //     createdBy,
    //     reportId: req.body.reportId,
    //     submissionDate: req.body.submissionDate || new Date(),
    //     reportDate: req.body.reportDate,
    //     status: 'Pending',
    //     version: version,
    //   };
    //   const submittalChange: Partial<Submittal> = {
    //     id: req.body.reportId,
    //     status: 'Pending',
    //     reportDate: req.body.reportDate,
    //   };
    //   const files: string[] = req.body.filePath || [];
    //   const document = files.map((value) => {
    //     const newDocument: ReportDocument = {
    //       updatedBy,
    //       createdBy,
    //       documentURL: value,
    //       reportVersionId: id,
    //     };
    //     return newDocument;
    //   });
    //   // await reportModel.addSubmittalAndDocumentAndChangeStatus(
    //   //   submittalChange,
    //   //   document,
    //   //   newSubmital
    //   // );
    //   // getting the data from database with the given id
    //   if (newSubmital) {
    //     const message = req.__('DataFoundMessage');
    //     // if true data will send as response
    //     return res.status(200).json({
    //       isSucceed: true,
    //       data: newSubmital,
    //       msg: message,
    //     });
    //   } else {
    //     const message = req.__('DataNotFoundMessage');
    //     // if false send error as response
    //     return res.status(200).json({
    //       isSucceed: true,
    //       data: [],
    //       msg: message,
    //     });
    //   }
    // } catch (error) {
    //   // error response
    //   return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    // }
  }

  async addReprot(req: Request, res: Response) {
    try {
      const createdBy = (req as any).user.name;
      const updatedBy = (req as any).user.name;
      const id = uuidv4();
      const version = 1;

      // const submittalChange: Partial<Report> = {
      //   reportTypeId: req.body.reportTypeId,
      //   comment: req.body.comment,
      //   reportDate: req.body.targetDate,
      //   projectId: req.body.projectId,
      //   name: req.body.name,
      //   status: 'Pending',
      // };

      const newSubmital: ReportVersion = {
        id,
        updatedBy,
        createdBy,
        submissionDate: req.body.submissionDate || new Date(),
        status: 'Pending',
        version: version,
      };

      const files: string[] = req.body.filePath || [];

      const document = files.map((value) => {
        const newDocument: ReportDocument = {
          updatedBy,
          createdBy,
          documentURL: value,
          reportVersionId: id,
        };
        return newDocument;
      });

      await reportModel.addReport(document, newSubmital);

      // getting the data from database with the given id
      if (newSubmital) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: newSubmital,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async countByStatus(req: Request, res: Response) {
    try {
      const data = await reportModel.countByStatus(req.params.id, req.params.status);

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async qcReport(req: Request, res: Response) {
    try {
      const { projectId } = req.params;
      const { from, to } = req.query;

      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);

      // Format dates as "YYYY-MM-DD"

      const formatDate = (date: Date) => date.toISOString().split('T')[0];
      // Set default values if empty
      const fromDate = from ? formatDate(new Date(from as string)) : formatDate(yesterday);
      const toDate = to ? formatDate(new Date(to as string)) : formatDate(today);

      const data = await reportModel.qcReport(projectId, fromDate, toDate);

      const excelBuffer = await createExcel(data);

      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      );
      const fileName = `QC Report ${fromDate} to ${toDate}`;
      res.setHeader('Content-Disposition', `attachment; filename=${fileName}.xlsx`);
      res.send(excelBuffer);
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async inPlaceDensityReport(req: Request, res: Response) {
    try {
      const { projectId } = req.params;
      const { from, to } = req.query;
      const today = new Date();
      const yesterday = new Date();
      yesterday.setDate(today.getDate() - 1);

      // Format dates as "YYYY-MM-DD"
      const formatDate = (date: Date) => date.toISOString().split('T')[0];

      // Set default values if empty
      const fromDate = from ? formatDate(new Date(from as string)) : formatDate(yesterday);
      const toDate = to ? formatDate(new Date(to as string)) : formatDate(today);
      // const userRole = await getUserRoleDetail((req as any).user, projectId);
      const data = await reportModel.inPlaceDensity(
        projectId,
        'ca8d6c51-1bdc-4ff2-8f33-de5261a857c7',
        fromDate,
        toDate
      );

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async proctorReport(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;

      const data = await reportModel.proctorReport(sampleId, projectId);

      // getting the data from database with the given id
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: data,
        msg: message,
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }
  async flowConsistencyReports(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;
      const data = await reportModel.flowConsistencyReports(sampleId, projectId);
      if (data) {
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async ucsReport(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;
      const data = await reportModel.ucsReport(sampleId, projectId);
      if (data) {
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      }
    } catch (error) {
      res.send(error);
    }
  }

  async compressiveStrengthReport(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;
      const data = await reportModel.compressiveStrengthReport(sampleId, projectId);
      if (data) {
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      }
    } catch (error) {
      res.send(error);
    }
  }

  async clsmReport(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;
      const data = await reportModel.clsmReport(sampleId, projectId);
      if (data) {
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      }
    } catch (error) {
      res.send(error);
    }
  }
  async clsmLabAndFieldTestingReport(req: Request, res: Response) {
    try {
      const { projectId, sampleId } = req.params;
      const data = await reportModel.clsmLabAndFieldTestingReport(sampleId, projectId);
      if (data) {
        const message = req.__('DataFoundMessage');
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      }
    } catch (error) {
      res.send(error);
    }
  }

  async getBoldAccessToken(req: Request, res: Response) {
    try {
      const nonce = crypto.randomUUID(); // Generate a unique nonce
      const timeStamp = Math.floor(Date.now() / 1000).toString(); // Get Unix timestamp

      const embedMessage = `embed_nonce=${nonce}&user_email=${process.env.BOLD_EMAIL}&timestamp=${timeStamp}`;
      const signature = signURL(embedMessage, process.env.BOLD_SECRET || '');

      const postData = querystring.stringify({
        grant_type: 'embed_secret',
        username: process.env.BOLD_EMAIL,
        embed_nonce: nonce,
        embed_signature: signature,
        timestamp: timeStamp,
      });

      const response = await axios.post(
        'https://dev.smartinfrahub.com//reporting/api/site/site1/token',
        postData,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      if (response.data.error) {
        throw new Error(`Error retrieving token: ${response.data.error_description}`);
      }

      res.send(response.data); // Return the token data
    } catch (error) {
      res.send(error);
    }
  }

  }

function signURL(embedMessage: string, secretCode: string) {
  return crypto.createHmac('sha256', secretCode).update(embedMessage).digest('base64');
}

const reportController = new ReportController();
export default reportController;
