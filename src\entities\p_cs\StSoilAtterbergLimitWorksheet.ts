import { Entity, Column, ManyToOne, JoinColumn } from 'typeorm';
import { StSoilAtterbergLimit } from './StSoilAtterbergLimit';
import { SubDCEColumns } from '@entities/common/SubDCEColumns';

// Define the entity class
@Entity({ schema: 'p_cs' })
export class StSoilAtterbergLimitWorksheet extends SubDCEColumns {
  @Column({ nullable: true })
  atterbergTestId?: string;

  @ManyToOne(() => StSoilAtterbergLimit, { nullable: true })
  @JoinColumn({ name: 'atterbergTestId' })
  atterbergTest?: StSoilAtterbergLimit;

  @Column({ nullable: true })
  testType?: string;

  @Column({ nullable: true })
  tareId?: string;

  @Column({ type: 'decimal', nullable: true })
  tareWeight?: number;

  @Column({ type: 'decimal', nullable: true })
  moistSoilWeightWithTare?: number;

  @Column({ type: 'decimal', nullable: true })
  drySoilWeightWithTare?: number;

  @Column({ type: 'decimal', nullable: true })
  weightOfWater?: number; // weightOfWater = moistSoilWeightWithTare - drySoilWeightWithTare

  @Column({ type: 'decimal', nullable: true })
  weightOfSoil?: number; // weightOfSoil = drySoilWeightWithTare - tareWeight

  @Column({ type: 'decimal', nullable: true })
  waterContent?: number; // waterContent = weightOfWater/weightOfSoil * 100

  @Column({ type: 'decimal', nullable: true })
  blowsCount?: number;

  @Column({ nullable: true })
  comment?: string;
}
