import { getManager } from 'typeorm';
import { DCERelationship } from '../../../entities/p_utils/DCERelationship';
import { entityList, EntityListInterface } from '../entity/entityList';
import { entityRelationList } from '../entity/entityRelation';
import testResultConfigModel from '@models/meta/tesResultConfig.model';
import isUUID from '@utils/custom/isUUID';
import { getUserById } from 'src/shared/server/platformApi/user';
import stSoilNuclearGaugeTestModel from '@models/cs/stSoilNuclearGaugeTest.model';
import dceModel from '@models/meta/dce.model';
import stSoilSandConeTestModel from '@models/cs/stSoilSandConeTest.model';
import { Test } from '@entities/p_meta/Test';
import { TestMethod } from '@entities/p_meta/TestMethod';
import { TestResult } from '@entities/p_meta/TestResult';
import { Site } from '@entities/p_gen/Site';

interface TestData {
  id: string;
  test?: Test;
  testMethod?: TestMethod;
  testResult?: TestResult;
  dateTested?: Date;
  testedBy?: string;
  site?: Site;
  createdAt?: Date;
  [key: string]: unknown;
}

const createTestSummaryResult = (
  data: TestData,
  entity: string,
  resultValues: Record<string, unknown>
) => ({
  id: data.id,
  entity,
  testName: data?.test?.name || '',
  testMethod: data?.testMethod?.standardCode || '',
  testResult: data?.testResult?.name || '',
  dateTested: data?.dateTested,
  testedBy: data?.testedBy || '',
  testLab: data?.site?.name || '',
  resultValues,
  createdAt: data?.createdAt,
});

const processResultColumns = async (dceId: string, data: any) => {
  const resultColumns = await testResultConfigModel.findColumnsByDCEId(dceId);
  const resultValues: { [key: string]: any } = {};
  if (Array.isArray(resultColumns)) {
    resultColumns.forEach((column) => {
      resultValues[column] = data[column] || 'N/A';
    });
  }
  return resultValues;
};

const resolveTestedBy = async (testedByIds: Set<string>) => {
  const userIds = Array.from(testedByIds);
  const users = await Promise.all(userIds.map((id) => getUserById(id)));
  return new Map(users.map((u) => [u.id, `${u.firstName} ${u.lastName}`]));
};

const getTestSummaryForSample = async (sampleId: string, relationShipData: DCERelationship[]) => {
  try {
    const result: { testName: string; testMethod: string; [key: string]: any }[] = [];
    const processedEntities = new Set<string>();

    const testedByIds: Set<string> = new Set();

    await Promise.all(
      relationShipData.map(async (value) => {
        if (value.targetDCE) {
          const entityString = value.targetDCE.entity;
          const resultColumns = await testResultConfigModel.findColumnsByDCEId(value.targetDCE.id);
          if (entityString && entityString in entityList && !processedEntities.has(entityString)) {
            processedEntities.add(entityString);
            const entity = entityList[entityString as keyof EntityListInterface] as any;

            const repository = getManager().getRepository(entity);
            if (entityString in entityRelationList) {
              const output = await repository.find({
                where: { sampleId, isDelete: false } as any,
                relations: entityRelationList[entityString].relation,
              });
              if (output !== null && output.length > 0) {
                output.forEach((row) => {
                  const resultValues: { [key: string]: any } = {};

                  if (Array.isArray(resultColumns)) {
                    resultColumns.forEach((column) => {
                      resultValues[column] = row[column] || 'N/A';
                    });
                  }

                  const testedById = row?.testedBy;

                  if (testedById && isUUID(testedById)) {
                    testedByIds.add(testedById);
                  }
                  result.push(createTestSummaryResult(row as TestData, entityString, resultValues));
                });
              }
            }
          }
        }
      })
    );

    const userMap = await resolveTestedBy(testedByIds);
    result.forEach((entry) => {
      if (entry.testedBy && isUUID(entry.testedBy)) {
        entry.testedBy = userMap.get(entry.testedBy) ?? entry.testedBy;
      }
    });

    result.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
    return result;
  } catch (error) {
    throw error;
  }
};

export const getTestSummaryForNuclearGauge = async (testNoId: string) => {
  try {
    const nuclearGauge = await stSoilNuclearGaugeTestModel.getNuclearGaugeByTestNo(testNoId);
    if (!nuclearGauge) {
      return null;
    }
    const dceData = await dceModel.findByEntity('nuclearGaugeTest');
    if (!dceData) {
      console.log('DCE Data not found');
      return null;
    }

    const resultValues = await processResultColumns(dceData.id, nuclearGauge);
    const result = createTestSummaryResult(
      nuclearGauge as unknown as TestData,
      'nuclearGaugeTest',
      resultValues
    );

    if (result.testedBy && isUUID(result.testedBy)) {
      const userMap = await resolveTestedBy(new Set([result.testedBy]));
      result.testedBy = userMap.get(result.testedBy) ?? result.testedBy;
    }

    return result;
  } catch (error) {
    throw error;
  }
};

export const getTestSummaryForSandCone = async (testNoId: string) => {
  try {
    const sandCone = await stSoilSandConeTestModel.getSandConeByTestNo(testNoId);
    if (!sandCone) {
      return null;
    }
    const dceData = await dceModel.findByEntity('sandConeTest');
    if (!dceData) {
      console.log('DCE Data not found');
      return null;
    }

    const resultValues = await processResultColumns(dceData.id, sandCone);
    const result = createTestSummaryResult(
      sandCone as unknown as TestData,
      'sandConeTest',
      resultValues
    );

    if (result.testedBy && isUUID(result.testedBy)) {
      const userMap = await resolveTestedBy(new Set([result.testedBy]));
      result.testedBy = userMap.get(result.testedBy) ?? result.testedBy;
    }

    return result;
  } catch (error) {
    throw error;
  }
};

export default getTestSummaryForSample;
