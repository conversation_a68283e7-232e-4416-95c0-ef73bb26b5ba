import { Request, Response } from 'express';
import approvalSetupModel from '../models/approvalSetup.model';
import approvalinstanceModel from '../models/approval.model';

import { EntityListInterface, entityList } from '../../../shared/utilities/entity/entityList';
import {
  getSensorDataByDCEIdAndTableIds,
  getSensorTableColumnDataByDceID,
} from '../../../shared/server/sensorApi/dce';
import dceModel from '../models/meta/dce.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';
import updateApprovalAndSendToNextLevel from '@utils/Approval/AddForApporvalAndNextLevel/updateApprovalNextLevel';
import addForApproval from '@utils/Approval/AddForApporvalAndNextLevel/addForApproval';

class ApprovalInstanceController {
  constructor() {}

  async findByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const approvalSetup = await approvalSetupModel.findByProjectId(req.params.id);
      // checking if data is found with the id
      if (approvalSetup) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: approvalSetup,
          msg: 'approval setup found',
        });
      } else {
        // if false send error as response
        return res.status(404).json({
          isSucceed: false,
          data: [],
          msg: 'No approval setup data found',
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByUserId(req: Request, res: Response) {
    try {
      const { type } = req.params;
      // getting the data from database with the given id
      const data = await approvalinstanceModel.getByApprovalListByuserId(req.params.id, type);
      // checking if data is found with the id
      if (data) {
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(404).json({
          isSucceed: false,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  async findByUserIdAndProjectId(req: Request, res: Response) {
    try {
      const { type, projectId } = req.params;
      // getting the data from database with the given id
      let data = await approvalinstanceModel.getByApprovalListByUserIdAndProjectId(
        req.params.id,
        projectId
      );
      // checking if data is found with the id
      if (data) {
        if (type == 'pending') {
          data = data.filter((value) => value.status == 'Pending');
        }
        if (type == 'approved') {
          data = data.filter((value) => value.status == 'Approved');
        }

        if (type == 'rejected') {
          data = data.filter((value) => value.status == 'Rejected');
        }

        if (type == 'history') {
          data = data.filter((value) => value.status == 'Rejected' || value.status == 'Approved');
        }
        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: data,
          msg: message,
        });
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(404).json({
          isSucceed: false,
          data: [],
          msg: message,
        });
      }
    } catch (error) {
      // error response
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }

  microserviceSendForApproval = async (req: Request, res: Response) => {
    try {
      const { dceId, ids, projectId } = req.body;
      const userName = (req as any).user.name;
      const data = await getSensorDataByDCEIdAndTableIds(dceId, ids, req.headers.authorization);
      const entitydata = await getSensorTableColumnDataByDceID(dceId, req.headers.authorization);
      const dceData = await dceModel.findByMicroserviceId(dceId);
      if (!dceData || !dceData.entity) {
        throw new Error('DCE not found');
      }

      await addForApproval(
        data,
        dceData?.entity,
        projectId,
        entitydata,
        0,
        userName,
        true,
        req.headers.authorization
      );
      const message = req.__('DataFoundMessage');
      // if true data will send as response
      return res.status(200).json({
        isSucceed: true,
        data: [],
        msg: message,
      });
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  };

  async findTableData(req: Request, res: Response) {
    try {
      const { id } = req.params;
      const approvalData = await approvalinstanceModel.getById(id);

      // getting the data from database with the given id
      if (approvalData && approvalData.tableName && approvalData.tableId && approvalData?.entity) {
        if (approvalData.entity in entityList) {
          const entityValue: any = entityList[approvalData.entity as keyof EntityListInterface];

          const data = await approvalinstanceModel.findTableData(
            approvalData.tableName,
            approvalData.tableId,
            entityValue,
            approvalData.entity
          );

          if (data) {
            const message = req.__('DataFoundMessage');
            // if true data will send as response
            return res.status(200).json({
              isSucceed: true,
              data: data,
              msg: message,
            });
          } else {
            const message = req.__('DataNotFoundMessage');
            // if false send error as response
            return res.status(404).json({
              isSucceed: true,
              data: [],
              msg: message,
            });
          }
        } else {
          const message = req.__('DataNotFoundMessage');

          return res.status(200).json({
            isSucceed: false,
            data: [],
            msg: message,
          });
        }
      } else {
        const message = req.__('DataNotFoundMessage');
        // if false send error as response
        return res.status(200).json({
          isSucceed: false,
          data: [],
          msg: message,
        });
      }
      // checking if data is found with the id
    } catch (error) {
      // error response
      return errorMiddleware(error, 500, res, req);
    }
  }

  async editMultipleApprovalStatus(req: Request, res: Response) {
    try {
      const { status, approvalIds, comment } = req.body;
      if (Array.isArray(approvalIds)) {
        await updateApprovalAndSendToNextLevel(
          status,
          approvalIds,
          comment,
          (req as any).user.name,
          (req as any).user.id
        );
        return res.status(200).json({ isSucceed: true, data: [], msg: `Item ${status}` });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async getApprovalWithData(req: Request, res: Response) {
    try {
      const { id, type } = req.params;
      const data = await approvalinstanceModel.getByApprovalListByuserId(id, type);
      if (data) {
        const finalData: any[] = [];
        for (const value of data) {
          if (value.tableName && value.tableId && value?.entity) {
            const dceData = value.dce;
            if (dceData?.isMicroservice) {
              const result = await getSensorDataByDCEIdAndTableIds(
                dceData?.microserviceDceId || '',
                [value.tableId],
                req.headers.authorization
              );
              (value as any).data = result[0];
            } else {
              if (value.entity in entityList) {
                const entityValue: any = entityList[value.entity as keyof EntityListInterface];

                const result = await approvalinstanceModel.findTableData(
                  value.tableName,
                  value.tableId,
                  entityValue,
                  value.entity
                );
                (value as any).data = result;
              }
            }
            finalData.push(value);
          }
        }
        const obj: any = {};
        finalData.map((value) => {
          if (value.entity) {
            const tableData = (value as any).data;
            const approvalDetails = value.approvers?.find((value: any) => value.approverId == id);
            if (tableData) {
              tableData.approvalId = value.id;
              tableData.tableId = tableData.id;
              tableData.id = value.id;
              tableData.status = approvalDetails?.status;
              tableData.projectName = value.project?.name;
              tableData.levelInstanceId = approvalDetails?.id;

              if (value.entity?.charAt(0).toUpperCase() + value.entity?.slice(1) in obj) {
                obj[value.entity?.charAt(0).toUpperCase() + value.entity?.slice(1)].push(tableData);
              } else {
                obj[value.entity?.charAt(0).toUpperCase() + value.entity?.slice(1)] = [tableData];
              }
            }
          }
        });

        const message = req.__('DataFoundMessage');
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: obj,
          msg: message,
        });
      }
    } catch (error) {
      return res.status(500).json({ isSucceed: false, data: [], msg: (error as Error).message });
    }
  }
}

const approvalInstanceController = new ApprovalInstanceController();
export default approvalInstanceController;
