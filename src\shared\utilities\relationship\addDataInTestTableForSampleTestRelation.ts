import ApprovalStatusModel from '@models/meta/approvalStatus.model';
import dceModel from '../../../modules/project360/models/meta/dce.model';
import sampleModel from '../../../modules/project360/models/sample.model';
import {
  ISampleTestRelation,
  ISampleTestRelationAddData,
} from '../../interface/relationshipInterface';
import { entityList, EntityListInterface } from '../entity/entityList';

const addDataInTestTableForSampleTestRelation = async (
  sampleId: string,
  sampleTestDetails: ISampleTestRelation[],
  createdUserId: string,
  createdBy: string
): Promise<{ dceId: string; dataId: string; additionalDetails: any }[]> => {
  try {
    const sampleDetails = await sampleModel.findById(sampleId);
    const finalOut: { dceId: string; dataId: string; additionalDetails: any }[] = [];
    const approvalStatusModel = new ApprovalStatusModel();
    const approvalStatusId = approvalStatusModel.getSystemStatusId();
    await Promise.all(
      sampleTestDetails.map(async (sampleTest) => {
        if (sampleTest.dce && sampleTest.dce?.entity) {
          const entityString = sampleTest?.dce?.entity;
          const dceId = sampleTest?.dce?.id;
          const data: ISampleTestRelationAddData = { sampleId, createdUserId, createdBy };
          const columnsToCheck = ['testId', 'testMethodId', 'testVariantId', 'siteId'];
          if (entityString in entityList) {
            const entityValue: any = entityList[entityString as keyof EntityListInterface];
            const results = await Promise.all(
              columnsToCheck.map((column) => dceModel.checkColumnExistByDce(entityValue, column))
            );
            const [
              columnTestIdExist,
              columnTestMethodIdExist,
              columnTestVariantIdExist,
              columnSiteIdExist,
            ] = results;
            if (columnTestIdExist) {
              data.testId = sampleTest.testId;
            }
            if (columnTestMethodIdExist) {
              data.testMethodId = sampleTest.testMethodId;
            }
            if (columnTestVariantIdExist) {
              data.testVariantId = sampleTest.testVariantId;
            }
            if (columnSiteIdExist) {
              data.siteId = sampleTest.labId;
            }
            if (!sampleTest.specimens || sampleTest.specimens.length === 0) {
              const response = await dceModel.addDataByEntity(
                {
                  purposeId: sampleDetails?.purposeId,
                  projectId: sampleDetails?.projectId,
                  testNoId: sampleDetails?.testNoId,
                  materialId: sampleDetails?.materialId,
                  materialTypeId: sampleDetails?.materialTypeId,
                  ...data,
                },
                entityList[entityString as keyof EntityListInterface] as any,
                createdUserId,
                entityString
              );
              delete sampleTest.dce;
              finalOut.push({
                dataId: response.id as string,
                dceId,
                additionalDetails: sampleTest,
              });
            } else {
              await Promise.all(
                sampleTest.specimens.map(async (specimen) => {
                  const response = await dceModel.addDataByEntity(
                    {
                      purposeId: sampleDetails?.purposeId,
                      projectId: sampleDetails?.projectId,
                      specimenId: specimen.id,
                      specimenAge: specimen.specimenAge,
                      approvalStatusId: approvalStatusId,
                      ...data,
                    },
                    entityValue,
                    createdUserId,
                    entityString
                  );
                  finalOut.push({
                    dataId: response.id as string,
                    dceId,
                    additionalDetails: { ...sampleTest, specimen },
                  });
                })
              );
            }
          }
        }
      })
    );
    return finalOut;
  } catch (error) {
    throw error;
  }
};

export default addDataInTestTableForSampleTestRelation;
