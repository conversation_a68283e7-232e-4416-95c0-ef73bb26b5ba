import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { Project } from '../p_gen/Project';
import { ConcreteBatchTicket } from './ConcreteBatchTicket';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { Purpose } from '../p_meta/Purpose';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class StHardenedSoilCementContent extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ColumnInfo({
    customData: {
      name: 'projectId',
      fieldName: 'projectId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @Column()
  testNo?: string;

  @ColumnInfo({
    customData: {
      name: 'Batch Id',
      fieldName: 'batchId',
      needed: true,
      inTemplate: false,
      type: 'uuid',
      fkey: false,
    },
  })
  @Column()
  batchId?: string;

  @ManyToOne(() => ConcreteBatchTicket, { nullable: true })
  @JoinColumn({ name: 'batchId' })
  concreteBatchTicket?: ConcreteBatchTicket;

  @ColumnInfo({
    customData: {
      name: 'Test Status (Pass/Fail)',
      fieldName: 'passFail',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  passFail?: string;

  @ColumnInfo({
    customData: {
      name: 'Raw Soil CaO',
      fieldName: 'rawSoilCaO',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  rawSoilCaO?: number;

  @ColumnInfo({
    customData: {
      name: 'Cement CaO',
      fieldName: 'cementCaO',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementCaO?: number;

  @ColumnInfo({
    customData: {
      name: 'Soil Cement Mixture Combined CaO',
      fieldName: 'soilCementMixtureCaO',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  soilCementMixtureCaO?: number;

  @ColumnInfo({
    customData: {
      name: 'Cement Content',
      fieldName: 'cementContent',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ type: 'decimal', nullable: true })
  cementContent?: string;

  @ColumnInfo({
    customData: {
      name: 'Tester',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  tester?: string;

  @ColumnInfo({
    customData: {
      name: 'Test date',
      fieldName: 'dateTimeTest',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  dateTimeTest?: Date;

  @ColumnInfo({
    customData: {
      name: 'Comments',
      fieldName: 'comments',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  comments?: string;

  @ColumnInfo({
    customData: {
      name: 'QC Verifier',
      fieldName: 'qcVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @ColumnInfo({
    customData: {
      name: 'Purpose', // this column will be entered by the
      fieldName: 'purposeId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
      query: `SELECT id FROM p_meta.purpose WHERE "name" = $1`,
      getListQuery: `SELECT id,"name" as name FROM p_meta.purpose;`,
      listName: 'purposeList',
    },
  })
  @Column({ nullable: true })
  purposeId?: string;

  @ManyToOne(() => Purpose, { nullable: true })
  @JoinColumn({ name: 'purposeId' })
  purpose?: Purpose;

  @ColumnInfo({
    customData: {
      name: 'QC Date',
      fieldName: 'qcDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qcDate?: Date;

  @ColumnInfo({
    customData: {
      name: 'QA Verifier',
      fieldName: 'qaVerifier',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaVerifier?: string;

  @ColumnInfo({
    customData: {
      name: 'QA Date',
      fieldName: 'qaDate',
      needed: false,
      inTemplate: false,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  qaDate?: Date;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
