import { Request, Response } from 'express';
import approvalSetupModel from '../models/approvalSetup.model';
import errorMiddleware from '../../../shared/middlewares/error/error.middleware';

class ApprovalSetupController {
  constructor() {}

  async findByProjectId(req: Request, res: Response) {
    try {
      // getting the data from database with the given id
      const approvalSetup = await approvalSetupModel.findByProjectId(req.params.id);

      // checking if data is found with the id
      if (approvalSetup) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: approvalSetup,
          msg: 'approval setup found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No approval setup data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async allApprovalSetupByPurpose(req: Request, res: Response) {
    try {
      const { id, projectId } = req.params;
      // getting the data from database with the given id
      const approvalSetup = await approvalSetupModel.checkSubmoduleWithFOrAllPurpose(id, projectId);

      // checking if data is found with the id
      if (approvalSetup) {
        // if true data will send as response
        return res.status(200).json({
          isSucceed: true,
          data: approvalSetup,
          msg: 'approval setup found',
        });
      } else {
        // if false send error as response
        return res.status(200).json({
          isSucceed: true,
          data: [],
          msg: 'No approval setup data found',
        });
      }
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }

  async addApproval(req: Request, res: Response) {
    try {
      const approval = req.body.approval;
      const levels = req.body.levels;
      approval.createdBy = (req as any).user.name;
      approval.updatedBy = (req as any).user.name;
      const approvalCheck = await approvalSetupModel.approvalSetupCheck(
        req.body.approval.activityId,
        req.body.approval.dceId,
        req.body.projectId,
        req.body.approval.purposeTypeId
      );

      if (approvalCheck) {
        throw new Error('Approval setup already exist');
      }

      const result = await approvalSetupModel.addApprovalSetup(approval, levels);
      return res.status(200).json({
        isSucceed: true,
        data: result,
        msg: 'approval setup found',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
  async editApproval(req: Request, res: Response) {
    try {
      const { approval, editLevels, newLevels } = req.body;
      const { id } = req.params;
      approval.id = id;
      const result = await approvalSetupModel.editApprovalSetup(
        approval,
        newLevels,
        editLevels,
        (req as any).user.name
      );
      return res.status(200).json({
        isSucceed: true,
        data: result,
        msg: 'Updated data successfully',
      });
    } catch (error) {
      return errorMiddleware(error, 500, res, req);
    }
  }
}

const approvalSetupController = new ApprovalSetupController();
export default approvalSetupController;
