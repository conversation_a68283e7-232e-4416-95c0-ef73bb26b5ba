import {
  <PERSON>tity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
  CreateDateColumn,
} from 'typeorm';
import { Project } from '../p_gen/Project';
import { ColumnInfo } from '../../shared/utilities/entity/customDecorator';
import { StConcreteBleeding } from './StConcreteBleeding';
import { ApprovalStatus } from '@entities/p_meta/ApprovalStatus';
import { WorkPackageActivity } from '../p_gen/WorkPackageActivity';
import SubmittalVersion from '../p_gen/SubmittalVersion';
import { EventLog } from '@entities/p_gen/EventLog';
import { DCEColumns } from '@entities/common/DCEColumns';

@Entity({ schema: 'p_cs' })
export class StConcreteBleedingWorksheet extends DCEColumns {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({ nullable: true })
  projectId?: string;

  @ManyToOne(() => Project, { nullable: true })
  @JoinColumn({ name: 'projectId' })
  project?: Project;

  @ColumnInfo({
    customData: {
      name: 'BleedId',
      fieldName: 'bleedId',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: true,
    },
  })
  @Column({ nullable: true })
  bleedId?: string;

  @ManyToOne(() => StConcreteBleeding, { nullable: true })
  @JoinColumn({ name: 'bleedId' })
  concreteBleeding?: StConcreteBleeding;

  @ColumnInfo({
    customData: {
      name: 'Tester',
      fieldName: 'tester',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  tester?: string;

  @ColumnInfo({
    customData: {
      name: 'DateTime Test',
      fieldName: 'dateTimeTest',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  dateTimeTest?: Date;

  @ColumnInfo({
    customData: {
      name: 'Recorded Time',
      fieldName: 'recordedTime',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  recordedTime?: string;

  @ColumnInfo({
    customData: {
      name: 'Nth Minute Duration',
      fieldName: 'nthMinuteDuration',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  nthMinuteDuration?: number;

  @ColumnInfo({
    customData: {
      name: 'Water Collected',
      fieldName: 'waterCollected',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  waterCollected?: number;

  @ColumnInfo({
    customData: {
      name: 'Cumulative Water Collected',
      fieldName: 'cumulativeWaterCollected',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  cumulativeWaterCollected?: number;

  @ColumnInfo({
    customData: {
      name: 'Volume Bleed',
      fieldName: 'volumeBleed',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  volumeBleed?: number;

  @ColumnInfo({
    customData: {
      name: 'Net Specimen Water',
      fieldName: 'netSpecimenWater',
      needed: false,
      inTemplate: false,
      type: 'date',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  netSpecimenWater?: Date;

  @ColumnInfo({
    customData: {
      name: 'Bleeding',
      fieldName: 'bleeding',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  bleeding?: string;

  @ColumnInfo({
    customData: {
      name: 'Cumulative Bleeding',
      fieldName: 'cumulativeBleeding',
      needed: true,
      inTemplate: true,
      type: 'string',
      fkey: false,
    },
  })
  @Column({ nullable: true })
  cumulativeBleeding?: string;

  @Column({ nullable: true })
  workPackageActivityId?: string;

  @ManyToOne(() => WorkPackageActivity, { nullable: true })
  @JoinColumn({ name: 'workPackageActivityId' })
  workPackageActivity?: WorkPackageActivity;

  @Column({ nullable: true })
  eventLogId?: string;

  @ManyToOne(() => EventLog, { nullable: true })
  @JoinColumn({ name: 'eventLogId' })
  eventLog?: EventLog;

  @CreateDateColumn({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt?: Date;

  @Column({ nullable: true })
  createdBy?: string;

  @Column({ nullable: true })
  createdUserId?: string;

  @Column({ nullable: true })
  qcVerifier?: string;

  @Column({ nullable: true })
  qcDate?: Date;

  @Column({ nullable: true })
  approvalStatusId?: string;

  @ManyToOne(() => ApprovalStatus, { nullable: true })
  @JoinColumn({ name: 'approvalStatusId' })
  approvalStatus?: ApprovalStatus;

  @UpdateDateColumn({
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  updatedAt?: Date;

  @Column({ nullable: true })
  updatedBy!: string;

  @Column({ default: false })
  isDelete?: boolean;

  @Column({ nullable: true })
  submittalVersionId?: string;

  @ManyToOne(() => SubmittalVersion, { nullable: true })
  @JoinColumn({ name: 'submittalVersionId' })
  submittalVersion?: SubmittalVersion;
}
