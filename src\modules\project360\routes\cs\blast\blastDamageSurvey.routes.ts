import express, { Router } from 'express';
import { authenticateToken } from '../../../../../shared/middlewares/auth.middleware';
import CrudController from '../../../../generic/crudDriver.controller';
import { sftpUploadProjectReportForBlastSurveyMiddleware } from '../../../../../shared/middlewares/uploadFileSftp.middleware';
import { BlastDamageSurvey } from '../../../../../entities/p_cs/BlastDamageSurvey';
import { createSubmittalBeforeFormMiddleware } from '../../../../../shared/middlewares/addSubmittalVersion.middleware';

const router: Router = express.Router();

// Create a generic router for the User entity
const CrudFunctionController = new CrudController<BlastDamageSurvey>(BlastDamageSurvey);

// Mount the userRouter for CRUD operations at /auth/user/crud
router.post(
  '/',
  authenticateToken,
  sftpUploadProjectReportForBlastSurveyMiddleware,
  createSubmittalBeforeFormMiddleware,
  (req, res) => CrudFunctionController.create(req, res, 'blastDamageSurvey')
);
router.put('/:id', authenticateToken, sftpUploadProjectReportForBlastSurveyMiddleware, (req, res) =>
  CrudFunctionController.update(req, res, 'blastDamageSurvey')
);

router.get('/data/count/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.getDataCountByProjectId(req, res, 'blastDamageSurvey')
);
router.get('/:id', authenticateToken, CrudFunctionController.findById);
router.delete('/:id', authenticateToken, (req, res) =>
  CrudFunctionController.softDelete(req, res, 'blastDamageSurvey')
);
router.patch('/multiple', authenticateToken, (req, res) =>
  CrudFunctionController.multiSoftDelete(req, res, 'blastDamageSurvey')
);
router.get('/by/project/:id', authenticateToken, (req, res) =>
  CrudFunctionController.findByProjectId(req, res, 'blastDamageSurvey')
);
router.get('/by/workActivity/:id/:projectId', authenticateToken, (req, res) =>
  CrudFunctionController.findByWorkActivityId(req, res, 'blastDamageSurvey')
);
router.patch('/send/for/approval', authenticateToken, (req, res) =>
  CrudFunctionController.sendForApproval(req, res, 'blastDamageSurvey')
);
router.get('/', authenticateToken, CrudFunctionController.findAll);

export default router;
